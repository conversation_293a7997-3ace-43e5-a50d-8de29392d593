<?php

if (!defined('ABSPATH')) {
    exit;
}

class Widget_Help_Admin {
    
    private $help_system;
    private $option_group = 'widget_help_settings';
    private $option_name = 'widget_help_options';
    private $parent_slug = 'financial-advisor-v4'; // Main plugin menu slug
    
    public function __construct($help_system) {
        $this->help_system = $help_system;
        
        // Solo se non siamo nel contesto del Menu Manager centralizzato
        if (!defined('FA_MENU_MANAGER_ACTIVE')) {
            add_action('admin_menu', array($this, 'add_admin_menu'), 20); // Priority 20 to ensure parent menu exists
        }
        
        add_action('admin_init', array($this, 'register_settings'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_ajax_save_widget_help_content', array($this, 'ajax_save_help_content'));
        add_action('wp_ajax_toggle_widget_help', array($this, 'ajax_toggle_widget_help'));
        add_action('wp_ajax_update_widget_position', array($this, 'ajax_update_widget_position'));
    }
    
    public function add_admin_menu() {
        // Check if parent menu exists, if not create it
        if (!$this->parent_menu_exists()) {
            add_menu_page(
                __('Financial Advisor V4', 'financial-advisor-v4'),
                __('Financial Advisor', 'financial-advisor-v4'),
                'manage_options',
                $this->parent_slug,
                array($this, 'main_admin_page'),
                'dashicons-chart-line',
                30
            );
        }
        
        // Add Widget Help submenu
        add_submenu_page(
            $this->parent_slug,
            __('Widget Help System', 'financial-advisor-v4'),
            __('Widget Help', 'financial-advisor-v4'),
            'manage_options',
            'fa-widget-help',
            array($this, 'admin_page')
        );
    }
    
    public function register_settings() {
        // Registra il gruppo di opzioni
        register_setting($this->option_group, $this->option_name, array(
            'sanitize_callback' => array($this, 'sanitize_settings')
        ));
        
        // Sezione generale
        add_settings_section(
            'widget_help_general',
            __('Impostazioni Generali', 'financial-advisor-v4'),
            array($this, 'general_section_callback'),
            'widget-help-system'
        );
        
        // Campo abilitazione globale
        add_settings_field(
            'global_enabled',
            __('Abilita Sistema Help', 'financial-advisor-v4'),
            array($this, 'global_enabled_callback'),
            'widget-help-system',
            'widget_help_general'
        );
        
        // Campo posizione predefinita
        add_settings_field(
            'default_position',
            __('Posizione Predefinita', 'financial-advisor-v4'),
            array($this, 'default_position_callback'),
            'widget-help-system',
            'widget_help_general'
        );
        
        // Campo auto-show
        add_settings_field(
            'auto_show_help',
            __('Mostra Help Automaticamente', 'financial-advisor-v4'),
            array($this, 'auto_show_help_callback'),
            'widget-help-system',
            'widget_help_general'
        );
        
        // Campo tema
        add_settings_field(
            'help_theme',
            __('Tema Interfaccia Help', 'financial-advisor-v4'),
            array($this, 'help_theme_callback'),
            'widget-help-system',
            'widget_help_general'
        );
    }
    
    public function main_admin_page() {
        // Fallback main page if parent doesn't exist
        ?>
        <div class="wrap">
            <h1><?php _e('Financial Advisor V4', 'financial-advisor-v4'); ?></h1>
            <div class="fa-dashboard">
                <div class="fa-card">
                    <h2><?php _e('Widget Help System', 'financial-advisor-v4'); ?></h2>
                    <p><?php _e('Gestisci il sistema di aiuto per tutti i widget del plugin.', 'financial-advisor-v4'); ?></p>
                    <a href="<?php echo admin_url('admin.php?page=fa-widget-help'); ?>" class="button button-primary">
                        <?php _e('Configura Widget Help', 'financial-advisor-v4'); ?>
                    </a>
                </div>
                
                <div class="fa-card">
                    <h2><?php _e('Report Viewer', 'financial-advisor-v4'); ?></h2>
                    <p><?php _e('Gestisci i report finanziari e le loro visualizzazioni.', 'financial-advisor-v4'); ?></p>
                    <a href="<?php echo admin_url('edit.php?post_type=fa_report'); ?>" class="button button-secondary">
                        <?php _e('Gestisci Report', 'financial-advisor-v4'); ?>
                    </a>
                </div>
            </div>
        </div>
        
        <style>
        .fa-dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .fa-card {
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            padding: 20px;
            box-shadow: 0 1px 1px rgba(0,0,0,.04);
        }
        .fa-card h2 {
            margin-top: 0;
            color: #1d2327;
        }
        .fa-card p {
            color: #646970;
            margin-bottom: 15px;
        }
        </style>
        <?php
    }
    
    public function admin_page() {
        $active_tab = isset($_GET['tab']) ? $_GET['tab'] : 'general';
        ?>
        <div class="wrap">
            <h1>
                <span class="dashicons dashicons-chart-line" style="font-size: 30px; margin-right: 10px; color: #2271b1;"></span>
                <?php _e('Financial Advisor - Widget Help System', 'financial-advisor-v4'); ?>
            </h1>
            
            <div class="fa-help-header">
                <p class="description">
                    <?php _e('Sistema di aiuto contestuale per i widget del Financial Advisor. Configura contenuti help, posizioni e comportamenti per migliorare l\'esperienza utente.', 'financial-advisor-v4'); ?>
                </p>
            </div>
            
            <nav class="nav-tab-wrapper">
                <a href="?page=fa-widget-help&tab=general" 
                   class="nav-tab <?php echo $active_tab == 'general' ? 'nav-tab-active' : ''; ?>">
                    <span class="dashicons dashicons-admin-generic"></span>
                    <?php _e('Generale', 'financial-advisor-v4'); ?>
                </a>
                <a href="?page=fa-widget-help&tab=widgets" 
                   class="nav-tab <?php echo $active_tab == 'widgets' ? 'nav-tab-active' : ''; ?>">
                    <span class="dashicons dashicons-layout"></span>
                    <?php _e('Widget', 'financial-advisor-v4'); ?>
                </a>
                <a href="?page=fa-widget-help&tab=content" 
                   class="nav-tab <?php echo $active_tab == 'content' ? 'nav-tab-active' : ''; ?>">
                    <span class="dashicons dashicons-edit-page"></span>
                    <?php _e('Contenuti', 'financial-advisor-v4'); ?>
                </a>
                <a href="?page=fa-widget-help&tab=analytics" 
                   class="nav-tab <?php echo $active_tab == 'analytics' ? 'nav-tab-active' : ''; ?>">
                    <span class="dashicons dashicons-chart-bar"></span>
                    <?php _e('Analytics', 'financial-advisor-v4'); ?>
                </a>
            </nav>
            
            <div class="tab-content">
                <?php
                switch($active_tab) {
                    case 'widgets':
                        $this->render_widgets_tab();
                        break;
                    case 'content':
                        $this->render_content_tab();
                        break;
                    case 'analytics':
                        $this->render_analytics_tab();
                        break;
                    default:
                        $this->render_general_tab();
                        break;
                }
                ?>
            </div>
        </div>
        
        <style>
        .fa-help-header {
            background: #f0f6fc;
            border: 1px solid #c5d9ed;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .nav-tab .dashicons {
            margin-right: 5px;
            font-size: 16px;
            vertical-align: text-top;
        }
        .fa-widget-status {
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        .fa-status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }
        .fa-status-enabled {
            background-color: #46b450;
        }
        .fa-status-disabled {
            background-color: #dc3232;
        }
        </style>
        <?php
    }
    
    private function render_general_tab() {
        $options = get_option($this->option_name, array());
        $widgets = $this->help_system->get_all_widgets();
        ?>
        <div class="fa-general-settings">
            <div class="fa-settings-overview">
                <div class="fa-overview-cards">
                    <div class="fa-overview-card">
                        <div class="fa-card-icon">
                            <span class="dashicons dashicons-admin-settings"></span>
                        </div>
                        <div class="fa-card-content">
                            <h3><?php _e('Sistema Help', 'financial-advisor-v4'); ?></h3>
                            <p><?php _e('Stato:', 'financial-advisor-v4'); ?> 
                                <span class="fa-status <?php echo (isset($options['global_enabled']) && $options['global_enabled']) ? 'fa-status-active' : 'fa-status-inactive'; ?>">
                                    <?php echo (isset($options['global_enabled']) && $options['global_enabled']) ? __('Attivo', 'financial-advisor-v4') : __('Inattivo', 'financial-advisor-v4'); ?>
                                </span>
                            </p>
                        </div>
                    </div>
                    
                    <div class="fa-overview-card">
                        <div class="fa-card-icon">
                            <span class="dashicons dashicons-layout"></span>
                        </div>
                        <div class="fa-card-content">
                            <h3><?php _e('Widget Registrati', 'financial-advisor-v4'); ?></h3>
                            <p><?php echo count($widgets); ?> <?php _e('widget disponibili', 'financial-advisor-v4'); ?></p>
                        </div>
                    </div>
                    
                    <div class="fa-overview-card">
                        <div class="fa-card-icon">
                            <span class="dashicons dashicons-yes-alt"></span>
                        </div>
                        <div class="fa-card-content">
                            <h3><?php _e('Widget Attivi', 'financial-advisor-v4'); ?></h3>
                            <p><?php echo count(array_filter($widgets, function($w) { return $w['enabled']; })); ?> <?php _e('widget abilitati', 'financial-advisor-v4'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <form method="post" action="options.php" class="fa-settings-form">
                <?php
                settings_fields($this->option_group);
                ?>
                
                <div class="fa-settings-sections">
                    <div class="fa-settings-section">
                        <h2><?php _e('Configurazione Generale', 'financial-advisor-v4'); ?></h2>
                        <?php do_settings_sections('widget-help-system'); ?>
                    </div>
                    
                    <div class="fa-settings-section">
                        <h2><?php _e('Statistiche di Utilizzo', 'financial-advisor-v4'); ?></h2>
                        <div class="fa-usage-stats">
                            <table class="wp-list-table widefat">
                                <thead>
                                    <tr>
                                        <th><?php _e('Widget', 'financial-advisor-v4'); ?></th>
                                        <th><?php _e('Stato', 'financial-advisor-v4'); ?></th>
                                        <th><?php _e('Posizione', 'financial-advisor-v4'); ?></th>
                                        <th><?php _e('Triggers', 'financial-advisor-v4'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach($widgets as $widget_id => $config): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo esc_html($config['name']); ?></strong>
                                            <br><small><code><?php echo esc_html($widget_id); ?></code></small>
                                        </td>
                                        <td>
                                            <span class="fa-status-indicator <?php echo $config['enabled'] ? 'fa-enabled' : 'fa-disabled'; ?>">
                                                <?php echo $config['enabled'] ? __('Abilitato', 'financial-advisor-v4') : __('Disabilitato', 'financial-advisor-v4'); ?>
                                            </span>
                                        </td>
                                        <td><?php echo esc_html(ucfirst($config['position'])); ?></td>
                                        <td><?php echo count($config['triggers']); ?> <?php _e('selettori', 'financial-advisor-v4'); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <?php submit_button(__('Salva Impostazioni', 'financial-advisor-v4')); ?>
            </form>
        </div>
        
        <style>
        .fa-general-settings {
            max-width: 1200px;
        }
        .fa-settings-overview {
            margin-bottom: 30px;
        }
        .fa-overview-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .fa-overview-card {
            background: #fff;
            border: 1px solid #c3c4c7;
            border-radius: 4px;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .fa-card-icon {
            flex-shrink: 0;
        }
        .fa-card-icon .dashicons {
            font-size: 32px;
            color: #2271b1;
        }
        .fa-card-content h3 {
            margin: 0 0 5px 0;
            color: #1d2327;
        }
        .fa-card-content p {
            margin: 0;
            color: #646970;
        }
        .fa-status {
            font-weight: bold;
        }
        .fa-status-active {
            color: #46b450;
        }
        .fa-status-inactive {
            color: #dc3232;
        }
        .fa-settings-sections {
            display: grid;
            gap: 30px;
        }
        .fa-settings-section {
            background: #fff;
            border: 1px solid #c3c4c7;
            border-radius: 4px;
            padding: 20px;
        }
        .fa-settings-section h2 {
            margin-top: 0;
            padding-bottom: 10px;
            border-bottom: 1px solid #f0f0f1;
        }
        .fa-status-indicator {
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .fa-status-indicator.fa-enabled {
            background-color: #d4edda;
            color: #155724;
        }
        .fa-status-indicator.fa-disabled {
            background-color: #f8d7da;
            color: #721c24;
        }
        </style>
        <?php
    }
    
    private function render_widgets_tab() {
        $widgets = $this->help_system->get_all_widgets();
        
        // Debug: aggiungi informazioni sui widget
        if (defined('DOCUMENT_VIEWER_DEBUG') && DOCUMENT_VIEWER_DEBUG) {
            echo '<div class="notice notice-info"><p><strong>Debug Widget Tab:</strong><br>';
            echo 'Help System Instance: ' . (is_object($this->help_system) ? 'Available' : 'Not available') . '<br>';
            echo 'Widgets Count: ' . count($widgets) . '<br>';
            if (!empty($widgets)) {
                echo 'Widget IDs: ' . implode(', ', array_keys($widgets)) . '<br>';
            }
            echo '</p></div>';
        }
        ?>
        <div class="widget-help-widgets-manager">
            <div class="fa-section-header">
                <h2><?php _e('Gestione Widget Help', 'financial-advisor-v4'); ?></h2>
                <p><?php _e('Configura i widget registrati nel sistema help. Abilita/disabilita singoli widget e personalizza le loro impostazioni.', 'financial-advisor-v4'); ?></p>
            </div>
            
            <?php if (empty($widgets)): ?>
            <div class="notice notice-warning">
                <p><?php _e('Nessun widget registrato nel sistema help. Il sistema potrebbe non essere inizializzato correttamente.', 'financial-advisor-v4'); ?></p>
                <p><?php _e('Prova a ricaricare la pagina o contatta l\'amministratore del sistema.', 'financial-advisor-v4'); ?></p>
            </div>
            <?php else: ?>
            
            <div class="fa-widget-stats">
                <div class="fa-stat-card">
                    <div class="fa-stat-number"><?php echo count($widgets); ?></div>
                    <div class="fa-stat-label"><?php _e('Widget Totali', 'financial-advisor-v4'); ?></div>
                </div>
                <div class="fa-stat-card">
                    <div class="fa-stat-number"><?php echo count(array_filter($widgets, function($w) { return $w['enabled']; })); ?></div>
                    <div class="fa-stat-label"><?php _e('Widget Attivi', 'financial-advisor-v4'); ?></div>
                </div>
                <div class="fa-stat-card">
                    <div class="fa-stat-number"><?php echo count(array_filter($widgets, function($w) { return !empty($w['triggers']); })); ?></div>
                    <div class="fa-stat-label"><?php _e('Con Trigger', 'financial-advisor-v4'); ?></div>
                </div>
            </div>
            
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th style="width: 20px;"></th>
                        <th><?php _e('Widget', 'financial-advisor-v4'); ?></th>
                        <th><?php _e('Descrizione', 'financial-advisor-v4'); ?></th>
                        <th><?php _e('Posizione', 'financial-advisor-v4'); ?></th>
                        <th><?php _e('Stato', 'financial-advisor-v4'); ?></th>
                        <th><?php _e('Azioni', 'financial-advisor-v4'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach($widgets as $widget_id => $config): ?>
                    <tr>
                        <td>
                            <?php if ($widget_id === 'report_viewer'): ?>
                                <span class="dashicons dashicons-chart-line" style="color: #2271b1;"></span>
                            <?php else: ?>
                                <span class="dashicons dashicons-layout" style="color: #646970;"></span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <strong><?php echo esc_html($config['name']); ?></strong>
                            <br><small><code><?php echo esc_html($widget_id); ?></code></small>
                        </td>
                        <td><?php echo esc_html($config['description']); ?></td>
                        <td>
                            <select class="widget-position-select" data-widget="<?php echo esc_attr($widget_id); ?>">
                                <option value="left" <?php selected($config['position'], 'left'); ?>><?php _e('Sinistra', 'financial-advisor-v4'); ?></option>
                                <option value="right" <?php selected($config['position'], 'right'); ?>><?php _e('Destra', 'financial-advisor-v4'); ?></option>
                                <option value="center" <?php selected($config['position'], 'center'); ?>><?php _e('Centro', 'financial-advisor-v4'); ?></option>
                            </select>
                        </td>
                        <td>
                            <div class="fa-widget-status">
                                <span class="fa-status-dot <?php echo $config['enabled'] ? 'fa-status-enabled' : 'fa-status-disabled'; ?>"></span>
                                <label class="widget-help-toggle">
                                    <input type="checkbox" 
                                           class="widget-enabled-toggle" 
                                           data-widget="<?php echo esc_attr($widget_id); ?>"
                                           <?php checked($config['enabled']); ?>>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </td>
                        <td>
                            <div class="row-actions">
                                <span class="edit">
                                    <a href="?page=fa-widget-help&tab=content&widget=<?php echo esc_attr($widget_id); ?>">
                                        <?php _e('Modifica Contenuto', 'financial-advisor-v4'); ?>
                                    </a>
                                </span>
                                <?php if (!empty($config['triggers'])): ?>
                                | <span class="test">
                                    <a href="#" class="test-widget-help" data-widget="<?php echo esc_attr($widget_id); ?>">
                                        <?php _e('Test Help', 'financial-advisor-v4'); ?>
                                    </a>
                                </span>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <?php endif; ?>
        </div>
        
        <style>
        .fa-section-header {
            margin-bottom: 20px;
        }
        .fa-widget-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .fa-stat-card {
            background: #fff;
            border: 1px solid #c3c4c7;
            border-radius: 4px;
            padding: 15px;
            text-align: center;
        }
        .fa-stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #2271b1;
            line-height: 1;
        }
        .fa-stat-label {
            font-size: 12px;
            color: #646970;
            margin-top: 5px;
        }
        </style>
        <?php
    }
    
    private function render_content_tab() {
        $widgets = $this->help_system->get_all_widgets();
        $selected_widget = isset($_GET['widget']) ? $_GET['widget'] : 'report_viewer';
        
        // Se non ci sono widget, mostra un messaggio
        if (empty($widgets)) {
            ?>
            <div class="widget-help-content-editor">
                <h2><?php _e('Editor Contenuti Help', 'financial-advisor-v4'); ?></h2>
                <div class="notice notice-warning">
                    <p><?php _e('Nessun widget disponibile per modificare i contenuti help.', 'financial-advisor-v4'); ?></p>
                    <p><?php _e('Assicurati che i widget siano registrati correttamente nel sistema help.', 'financial-advisor-v4'); ?></p>
                </div>
            </div>
            <?php
            return;
        }
        ?>
        <div class="widget-help-content-editor">
            <h2><?php _e('Editor Contenuti Help', 'financial-advisor-v4'); ?></h2>
            
            <div class="widget-selector">
                <label for="widget-select"><?php _e('Seleziona Widget:', 'financial-advisor-v4'); ?></label>
                <select id="widget-select" name="widget-select">
                    <?php foreach($widgets as $widget_id => $config): ?>
                    <option value="<?php echo esc_attr($widget_id); ?>" 
                            <?php selected($selected_widget, $widget_id); ?>>
                        <?php echo esc_html($config['name']); ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="content-sections">
                <h3><?php _e('Sezioni Help', 'financial-advisor-v4'); ?></h3>
                
                <div class="section-tabs">
                    <button class="section-tab active" data-section="main">Principale</button>
                    <button class="section-tab" data-section="list">Lista</button>
                    <button class="section-tab" data-section="detail">Dettaglio</button>
                    <button class="section-tab" data-section="export">Export</button>
                    <button class="section-tab" data-section="versions">Versioni</button>
                    <button class="section-tab" data-section="tags">Tags</button>
                </div>
                
                <div class="section-content">
                    <?php
                    $content = $this->help_system->get_help_content($selected_widget, 'main');
                    wp_editor($content, 'help_content_editor', array(
                        'textarea_name' => 'help_content',
                        'textarea_rows' => 15,
                        'media_buttons' => true,
                        'teeny' => false,
                        'dfw' => false,
                        'tinymce' => array(
                            'resize' => false,
                            'wp_autoresize_on' => false,
                        )
                    ));
                    ?>
                    
                    <p class="submit">
                        <button type="button" class="button button-primary save-help-content">
                            <?php _e('Salva Contenuto', 'financial-advisor-v4'); ?>
                        </button>
                        <button type="button" class="button button-secondary preview-help-content">
                            <?php _e('Anteprima', 'financial-advisor-v4'); ?>
                        </button>
                    </p>
                </div>
            </div>
        </div>
        <?php
    }
    
    private function render_analytics_tab() {
        ?>
        <div class="fa-analytics-section">
            <div class="fa-section-header">
                <h2><?php _e('Analytics Widget Help', 'financial-advisor-v4'); ?></h2>
                <p><?php _e('Monitora l\'utilizzo del sistema help e ottimizza l\'esperienza utente.', 'financial-advisor-v4'); ?></p>
            </div>
            
            <div class="fa-analytics-grid">
                <div class="fa-analytics-card">
                    <h3><?php _e('Utilizzo Help', 'financial-advisor-v4'); ?></h3>
                    <div class="fa-metric">
                        <span class="fa-metric-value">147</span>
                        <span class="fa-metric-label"><?php _e('Visualizzazioni oggi', 'financial-advisor-v4'); ?></span>
                    </div>
                    <div class="fa-metric">
                        <span class="fa-metric-value">2.3k</span>
                        <span class="fa-metric-label"><?php _e('Visualizzazioni totali', 'financial-advisor-v4'); ?></span>
                    </div>
                </div>
                
                <div class="fa-analytics-card">
                    <h3><?php _e('Widget Più Utilizzati', 'financial-advisor-v4'); ?></h3>
                    <ul class="fa-widget-usage">
                        <li>
                            <span class="fa-widget-name">Report Viewer</span>
                            <span class="fa-usage-bar">
                                <span class="fa-usage-fill" style="width: 85%;"></span>
                            </span>
                            <span class="fa-usage-percent">85%</span>
                        </li>
                        <li>
                            <span class="fa-widget-name">Chart Widget</span>
                            <span class="fa-usage-bar">
                                <span class="fa-usage-fill" style="width: 45%;"></span>
                            </span>
                            <span class="fa-usage-percent">45%</span>
                        </li>
                    </ul>
                </div>
                
                <div class="fa-analytics-card">
                    <h3><?php _e('Sezioni Help Popolari', 'financial-advisor-v4'); ?></h3>
                    <ul class="fa-section-stats">
                        <li><strong>Export</strong> - 65 visualizzazioni</li>
                        <li><strong>Filtri</strong> - 42 visualizzazioni</li>
                        <li><strong>Lista</strong> - 38 visualizzazioni</li>
                        <li><strong>Versioni</strong> - 21 visualizzazioni</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <style>
        .fa-analytics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .fa-analytics-card {
            background: #fff;
            border: 1px solid #c3c4c7;
            border-radius: 4px;
            padding: 20px;
        }
        .fa-analytics-card h3 {
            margin-top: 0;
            color: #1d2327;
            border-bottom: 1px solid #f0f0f1;
            padding-bottom: 10px;
        }
        .fa-metric {
            margin: 15px 0;
        }
        .fa-metric-value {
            display: block;
            font-size: 28px;
            font-weight: bold;
            color: #2271b1;
            line-height: 1;
        }
        .fa-metric-label {
            font-size: 13px;
            color: #646970;
        }
        .fa-widget-usage li {
            display: flex;
            align-items: center;
            margin: 10px 0;
            gap: 10px;
        }
        .fa-widget-name {
            flex: 0 0 100px;
            font-size: 13px;
        }
        .fa-usage-bar {
            flex: 1;
            height: 8px;
            background: #f0f0f1;
            border-radius: 4px;
            overflow: hidden;
        }
        .fa-usage-fill {
            height: 100%;
            background: #2271b1;
            transition: width 0.3s ease;
        }
        .fa-usage-percent {
            flex: 0 0 35px;
            font-size: 12px;
            color: #646970;
        }
        .fa-section-stats {
            list-style: none;
            padding: 0;
        }
        .fa-section-stats li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f1;
        }
        .fa-section-stats li:last-child {
            border-bottom: none;
        }
        </style>
        <?php
    }
    
    public function general_section_callback() {
        echo '<p>' . __('Configura le impostazioni generali del sistema di help per tutti i widget.', 'financial-advisor-v4') . '</p>';
    }
    
    public function global_enabled_callback() {
        $options = get_option($this->option_name, array());
        $value = isset($options['global_enabled']) ? $options['global_enabled'] : true;
        echo '<input type="checkbox" name="' . $this->option_name . '[global_enabled]" value="1" ' . checked(1, $value, false) . '>';
        echo '<p class="description">' . __('Abilita o disabilita il sistema di help per tutti i widget.', 'financial-advisor-v4') . '</p>';
    }
    
    public function default_position_callback() {
        $options = get_option($this->option_name, array());
        $value = isset($options['default_position']) ? $options['default_position'] : 'right';
        
        echo '<select name="' . $this->option_name . '[default_position]">';
        echo '<option value="left" ' . selected('left', $value, false) . '>' . __('Sinistra', 'financial-advisor-v4') . '</option>';
        echo '<option value="right" ' . selected('right', $value, false) . '>' . __('Destra', 'financial-advisor-v4') . '</option>';
        echo '<option value="center" ' . selected('center', $value, false) . '>' . __('Centro', 'financial-advisor-v4') . '</option>';
        echo '</select>';
        echo '<p class="description">' . __('Posizione predefinita per i popup di help.', 'financial-advisor-v4') . '</p>';
    }
    
    public function auto_show_help_callback() {
        $options = get_option($this->option_name, array());
        $value = isset($options['auto_show_help']) ? $options['auto_show_help'] : false;
        echo '<input type="checkbox" name="' . $this->option_name . '[auto_show_help]" value="1" ' . checked(1, $value, false) . '>';
        echo '<p class="description">' . __('Mostra automaticamente l\'help quando l\'utente arriva su una pagina per la prima volta.', 'financial-advisor-v4') . '</p>';
    }
    
    public function help_theme_callback() {
        $options = get_option($this->option_name, array());
        $value = isset($options['help_theme']) ? $options['help_theme'] : 'light';
        
        echo '<select name="' . $this->option_name . '[help_theme]">';
        echo '<option value="light" ' . selected('light', $value, false) . '>' . __('Chiaro', 'financial-advisor-v4') . '</option>';
        echo '<option value="dark" ' . selected('dark', $value, false) . '>' . __('Scuro', 'financial-advisor-v4') . '</option>';
        echo '<option value="auto" ' . selected('auto', $value, false) . '>' . __('Automatico', 'financial-advisor-v4') . '</option>';
        echo '</select>';
        echo '<p class="description">' . __('Tema dell\'interfaccia help.', 'financial-advisor-v4') . '</p>';
    }
    
    private function parent_menu_exists() {
        global $menu;
        foreach ($menu as $item) {
            if (isset($item[2]) && $item[2] === $this->parent_slug) {
                return true;
            }
        }
        return false;
    }
    
    public function enqueue_admin_scripts($hook) {
        if ('settings_page_widget-help-system' !== $hook) {
            return;
        }
        
        wp_enqueue_script(
            'widget-help-admin',
            plugin_dir_url(__FILE__) . '../assets/js/widget-help-admin.js',
            array('jquery'),
            '1.0.0',
            true
        );
        
        wp_enqueue_style(
            'widget-help-admin',
            plugin_dir_url(__FILE__) . '../assets/css/widget-help-admin.css',
            array(),
            '1.0.0'
        );
        
        wp_localize_script('widget-help-admin', 'widgetHelpAdmin', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('widget_help_admin_nonce'),
            'strings' => array(
                'saved' => __('Contenuto salvato con successo', 'financial-advisor-v4'),
                'error' => __('Errore nel salvataggio', 'financial-advisor-v4'),
                'confirm_delete' => __('Sei sicuro di voler eliminare questo contenuto?', 'financial-advisor-v4')
            )
        ));
    }
    
    public function ajax_save_help_content() {
        check_ajax_referer('widget_help_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }
        
        $widget_id = sanitize_text_field($_POST['widget_id']);
        $section = sanitize_text_field($_POST['section']);
        $content = wp_kses_post($_POST['content']);
        
        $this->help_system->set_help_content($widget_id, $content, $section);
        
        // Save to database
        $saved_content = get_option('widget_help_content', array());
        if (!isset($saved_content[$widget_id])) {
            $saved_content[$widget_id] = array();
        }
        $saved_content[$widget_id][$section] = $content;
        update_option('widget_help_content', $saved_content);
        
        wp_send_json_success(array('message' => __('Contenuto salvato con successo', 'financial-advisor-v4')));
    }
    
    public function ajax_toggle_widget_help() {
        check_ajax_referer('widget_help_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }
        
        $widget_id = sanitize_text_field($_POST['widget_id']);
        $enabled = intval($_POST['enabled']);
        
        $widget_settings = get_option('widget_help_widget_settings', array());
        $widget_settings[$widget_id]['enabled'] = $enabled;
        update_option('widget_help_widget_settings', $widget_settings);
        
        wp_send_json_success(array('message' => __('Impostazione aggiornata', 'financial-advisor-v4')));
    }
    
    public function ajax_update_widget_position() {
        check_ajax_referer('widget_help_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }
        
        $widget_id = sanitize_text_field($_POST['widget_id']);
        $position = sanitize_text_field($_POST['position']);
        
        $widget_settings = get_option('widget_help_widget_settings', array());
        if (!isset($widget_settings[$widget_id])) {
            $widget_settings[$widget_id] = array();
        }
        $widget_settings[$widget_id]['position'] = $position;
        update_option('widget_help_widget_settings', $widget_settings);
        
        wp_send_json_success(array('message' => __('Posizione aggiornata', 'financial-advisor-v4')));
    }
}
