<?php
/**
 * Classe Document_Management
 * 
 * Gestisce tutte le funzionalità relative al processamento di documenti:
 * - Caricamento e processamento di file PDF, Word e immagini
 * - Conversione di documenti Word in PDF
 * - Estrazione del testo tramite diversi metodi (PDF Parser, Imagick, PHPWord)
 * - Pulizia e normalizzazione del testo estratto
 * 
 * @package Financial_Advisor
 */

if (!defined('ABSPATH')) {
    exit;
}

class Document_Management {
    /**
     * Costruttore
     */
    public function __construct() {
        // Inizializza dipendenze necessarie
        $this->initialize_upload_directory();
    }

    /**
     * Inizializza la directory di upload per l'elaborazione dei documenti
     */
    public function initialize_upload_directory() {
        // Create upload directory for document processing
        $upload_dir = wp_upload_dir();
        $plugin_upload_dir = $upload_dir['basedir'] . '/document-viewer';
        
        // Check if directory exists
        if (!file_exists($plugin_upload_dir)) {
            // Create directory
            wp_mkdir_p($plugin_upload_dir);
            
            // Create .htaccess file to protect uploads
            $htaccess_file = $plugin_upload_dir . '/.htaccess';
            if (!file_exists($htaccess_file)) {
                $htaccess_content = "# Protect directory\nDeny from all\n";
                file_put_contents($htaccess_file, $htaccess_content);
            }
            
            // Create index.php to prevent directory listing
            $index_file = $plugin_upload_dir . '/index.php';
            if (!file_exists($index_file)) {
                file_put_contents($index_file, "<?php\n// Silence is golden.");
            }
        }
    }

    /**
     * Converte un documento Word in PDF
     * 
     * @param array $file Il file caricato da processare
     * @return array Informazioni sul PDF convertito
     */
    public function convert_word_to_pdf($file) {
        // Verifica se l'utente è un subscriber non WordPress
        $is_subscriber_logged_in = isset($_COOKIE['fa_subscriber_login']) && !empty($_COOKIE['fa_subscriber_login']);

        // Verifica permessi: utenti WordPress con upload_files o utenti esterni autenticati
        if (!current_user_can('upload_files') && !$is_subscriber_logged_in) {
            return array(
                'success' => false,
                'message' => __('Permission denied.', 'document-viewer-plugin')
            );
        }

        if (empty($file) || $file['error'] !== UPLOAD_ERR_OK) {
            return array(
                'success' => false,
                'message' => __('No Word file uploaded or upload error.', 'document-viewer-plugin')
            );
        }

        $tmp_name = $file['tmp_name'];
        $file_name = sanitize_file_name($file['name']);
        $ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

        if (!in_array($ext, ['doc', 'docx'])) {
            return array(
                'success' => false,
                'message' => __('Unsupported file type.', 'document-viewer-plugin')
            );
        }

        // Prepare upload directory for converted PDFs
        $upload_dir = wp_upload_dir();
        $pdf_dir = $upload_dir['basedir'] . '/document-viewer/converted';
        if (!file_exists($pdf_dir)) {
            wp_mkdir_p($pdf_dir);
        }

        $pdf_file_name = preg_replace('/\.(docx?|DOCX?)$/', '.pdf', $file_name);
        $pdf_path = $pdf_dir . '/' . $pdf_file_name;
        $pdf_url = $upload_dir['baseurl'] . '/document-viewer/converted/' . $pdf_file_name;

        try {
            // Set PDF renderer for PHPWord (required for PDF export)
            \PhpOffice\PhpWord\Settings::setPdfRendererName('MPDF');
            
            // Determina il percorso corretto alla libreria mPDF
            $vendor_dir = dirname(dirname(__FILE__)) . '/vendor';
            
            // Verifica tutti i possibili percorsi per mPDF
            $possible_paths = [
                $vendor_dir . '/mpdf/mpdf',                 // Percorso standard
                $vendor_dir . '/vendor/mpdf/mpdf',          // Percorso nidificato
                WP_PLUGIN_DIR . '/financial-advisor-V4/vendor/mpdf/mpdf', // Percorso assoluto
                plugin_dir_path(dirname(__FILE__)) . 'vendor/mpdf/mpdf'   // Percorso relativo al plugin
            ];
            
            $mpdf_path = null;
            
            foreach ($possible_paths as $path) {
                if (file_exists($path)) {
                    $mpdf_path = $path;
                    dv_debug_log('mPDF trovato in: ' . $mpdf_path);
                    break;
                }
            }
            
            // Log per debug
            if ($mpdf_path) {
                dv_debug_log('Percorso mPDF impostato a: ' . $mpdf_path);
            } else {
                dv_debug_log('mPDF non trovato in nessuno dei percorsi controllati');
                throw new \Exception('Directory della libreria mPDF non trovata. Verificare l\'installazione di Composer.');
            }
            
            // Imposta il percorso corretto
            \PhpOffice\PhpWord\Settings::setPdfRendererPath($mpdf_path);

            // Load Word document
            if (!class_exists('\PhpOffice\PhpWord\PhpWord')) {
                throw new \Exception('PHPWord library not available.');
            }
            $phpWord = \PhpOffice\PhpWord\IOFactory::load($tmp_name);

            // Extract text for display
            $text = '';
            foreach ($phpWord->getSections() as $section) {
                foreach ($section->getElements() as $element) {
                    $text .= $this->extract_element_text($element);
                }
                $text .= "\n\n";
            }

            // Save as PDF using mPDF
            if (!class_exists('\PhpOffice\PhpWord\Writer\PDF')) {
                throw new \Exception('PHPWord PDF writer not available.');
            }
            $pdfWriter = \PhpOffice\PhpWord\IOFactory::createWriter($phpWord, 'PDF');
            $pdfWriter->save($pdf_path);

            return array(
                'success' => true,
                'pdf_url' => $pdf_url,
                'pdf_content' => $text
            );
        } catch (\Exception $e) {
            dv_debug_log('Word conversion error: ' . $e->getMessage());
            return array(
                'success' => false,
                'message' => 'Conversion error: ' . $e->getMessage()
            );
        }
    }

    /**
     * Estrae testo da un elemento PHPWord
     *
     * @param object $element L'elemento da cui estrarre il testo
     * @return string Il testo estratto
     */
    private function extract_element_text($element) {
        $text = '';
        
        try {
            // Log per debug
            dv_debug_log('Estrazione testo da elemento di tipo: ' . get_class($element));
            
            if ($element instanceof \PhpOffice\PhpWord\Element\Text) {
                $text .= $element->getText() . ' ';
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\TextRun) {
                foreach ($element->getElements() as $subElement) {
                    $text .= $this->extract_element_text($subElement);
                }
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\Table) {
                foreach ($element->getRows() as $row) {
                    foreach ($row->getCells() as $cell) {
                        foreach ($cell->getElements() as $cellElement) {
                            $text .= $this->extract_element_text($cellElement);
                        }
                        $text .= ' | '; // Separa le celle con un delimitatore
                    }
                    $text .= "\n"; // Nuova riga per ogni riga della tabella
                }
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\ListItem) {
                // Controlla se l'elemento contiene un TextRun o un Text direttamente
                if (method_exists($element, 'getTextObject')) {
                    $text .= '• ' . $element->getTextObject()->getText() . "\n";
                } else {
                    $text .= '• ';
                    foreach ($element->getElements() as $subElement) {
                        $text .= $this->extract_element_text($subElement);
                    }
                    $text .= "\n";
                }
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\Title) {
                $text .= "\n" . $element->getText() . "\n";
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\Footnote) {
                if (method_exists($element, 'getElement')) {
                    $text .= ' [Nota: ' . $this->extract_element_text($element->getElement()) . ']';
                }
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\Header) {
                foreach ($element->getElements() as $headerElement) {
                    $text .= $this->extract_element_text($headerElement);
                }
                $text .= "\n";
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\Footer) {
                foreach ($element->getElements() as $footerElement) {
                    $text .= $this->extract_element_text($footerElement);
                }
                $text .= "\n";
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\TextBreak) {
                $text .= "\n";
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\Image || 
                      $element instanceof \PhpOffice\PhpWord\Element\Chart ||
                      $element instanceof \PhpOffice\PhpWord\Element\Shape) {
                // Per elementi grafici, aggiungiamo un placeholder
                $text .= ' [Immagine/Grafico] ';
            } elseif ($element instanceof \PhpOffice\PhpWord\Element\Link) {
                $text .= $element->getText() . ' ';
            }
        } catch (Exception $e) {
            dv_debug_log('Errore durante l\'estrazione del testo: ' . $e->getMessage());
        }
        
        return $text;
    }

    /**
     * Verifica se PDF Parser è disponibile
     * 
     * @return bool
     */
    public function check_pdf_parser() {
        // Verifica se la classe esiste
        if (!class_exists('Smalot\PdfParser\Parser')) {
            dv_debug_log('PDF Parser non disponibile: classe Smalot\PdfParser\Parser non trovata', 'analysis');
            return false;
        }
        
        // Verifica se è possibile creare un'istanza
        try {
            $parser = new \Smalot\PdfParser\Parser();
            return true;
        } catch (\Exception $e) {
            dv_debug_log('PDF Parser non funzionante: ' . $e->getMessage(), 'analysis');
            return false;
        }
    }
    
    /**
     * Estrae il contenuto da un file PDF
     * 
     * @param string $file_path Il percorso del file PDF
     * @return string Il testo estratto
     * @throws Exception Se l'estrazione fallisce
     */
    public function extract_pdf_content($file_path) {
        try {
            // Verifico che il file esista e sia leggibile
            if (!file_exists($file_path)) {
                throw new Exception('Il file PDF non esiste: ' . $file_path);
            }
            
            if (!is_readable($file_path)) {
                throw new Exception('Il file PDF non è leggibile: ' . $file_path);
            }
            
            // Log per debug
            $file_size_kb = round(filesize($file_path) / 1024, 2);
            dv_debug_log("Estrazione testo PDF: inizio processo per " . basename($file_path) . " (" . $file_size_kb . " KB)", 'analysis');
            
            // Verifico che il parser PDF sia disponibile
            if (!class_exists('Smalot\PdfParser\Parser')) {
                throw new Exception('Smalot PDF Parser non è disponibile. Verificare l\'installazione di Composer.');
            }
            
            // Set execution time limit for large files
            $original_time_limit = ini_get('max_execution_time');
            set_time_limit(300); // 5 minutes for PDF extraction
            
            // Increase memory limit temporarily for large PDFs
            $original_memory_limit = ini_get('memory_limit');
            ini_set('memory_limit', '768M');
            
           
            // Initialize parser
            $parser = new \Smalot\PdfParser\Parser();
            
            $text = '';
            dv_debug_log('PDF: inizializzazione estrazione testo', 'analysis');
            
            // Set timeout parameters
            $timeout =180; // 3 minutes timeout (increased from 2)
            $start_time = time();
            
            // Custom error handler for memory issues
            set_error_handler(function($severity, $message, $file, $line) {
                if (strpos($message, 'memory') !== false || strpos($message, 'allowed memory size') !== false) {                  
                    dv_debug_log("Errore di memoria durante l'estrazione: $message", 'analysis');
                    throw new Exception("Memoria insufficiente durante l'estrazione PDF: $message");
                }
                return false; // Let PHP handle other errors
            });
            
            // Try different extraction methods in sequence
            $extraction_successful = false;
            // First attempt: Try using PDFParser directly on the entire document
            try {
                dv_debug_log("PDF: tentativo di estrazione diretto in corso...", 'analysis');
                $pdf = $parser->parseFile($file_path);
                if ($pdf) {
                    $text = $pdf->getText();
                    if (!empty($text)) {
                        dv_debug_log("PDF: estrazione diretta completata con successo", 'analysis');
                        $extraction_successful = true;
                    }
                }
            } catch (\Exception $e) {
                dv_debug_log('PDF: estrazione diretta fallita: ' . $e->getMessage(), 'analysis');
                // Continue to the next method
            }
            
            // Second attempt: Page-by-page extraction if first method failed
            if (!$extraction_successful) {
                try {
                    dv_debug_log("PDF: tentativo di estrazione pagina per pagina in corso...", 'analysis');
                    $pdf = $parser->parseFile($file_path);
                    
                    if ($pdf) {
                        // Get pages and extract text page by page
                        $pages = $pdf->getPages();
                        $total_pages = count($pages);
                        
                        dv_debug_log("PDF: documento contiene $total_pages pagine, estrazione in corso", 'analysis');
                        $text = '';
                        
                        foreach ($pages as $pageNumber => $page) {
                            // Check for timeout on each page
                            if (time() - $start_time > $timeout) {
                                dv_debug_log("PDF: timeout durante l'estrazione della pagina " . ($pageNumber + 1), 'analysis');
                                if (!empty($text)) {
                                    $text .= "\n\n[NOTA: L'estrazione è stata interrotta dopo " . ($pageNumber) . " pagine a causa di timeout]";
                                    $extraction_successful = true;
                                    break;
                                } else {
                                    throw new Exception("Timeout durante l'estrazione del PDF alla pagina " . ($pageNumber + 1));
                                }
                            }
                            
                            try {
                                $pageText = $page->getText();
                                $text .= $pageText . "\n\n";

                                // Report progress every few pages
                                if ($pageNumber % 5 == 0 || $pageNumber == $total_pages - 1) {
                                    dv_debug_log("PDF: estratta pagina " . ($pageNumber + 1) . " di $total_pages", 'analysis');
                                }
                            } catch (\Exception $e) {
                                dv_debug_log("PDF: errore nell'estrazione della pagina " . ($pageNumber + 1) . ": " . $e->getMessage(), 'analysis');
                                $text .= "[ERRORE NELLA PAGINA " . ($pageNumber + 1) . "]\n\n";
                            }
                            
                            // Force garbage collection every few pages
                            if ($pageNumber % 5 == 0) {
                                gc_collect_cycles();
                            }
                        }
                        
                        if (!empty($text)) {
                            dv_debug_log("PDF: estrazione pagina per pagina completata con successo", 'analysis');
                            $extraction_successful = true;
                        }
                    }
                } catch (\Exception $e) {
                    dv_debug_log('PDF: estrazione pagina per pagina fallita: ' . $e->getMessage(), 'analysis');
                    // Continue to the next method
                }
            }
            
            // Third attempt: Try alternate PDF libraries if available
            if (!$extraction_successful && extension_loaded('imagick')) {
                try {
                    dv_debug_log("PDF: tentativo di estrazione con metodo Imagick in corso...", 'analysis');
                    $text = $this->extract_pdf_with_imagick($file_path);
                    if (!empty($text)) {
                        dv_debug_log("PDF: estrazione con Imagick completata con successo", 'analysis');
                        $extraction_successful = true;
                    }
                } catch (\Exception $e) {
                    dv_debug_log('PDF: estrazione con Imagick fallita: ' . $e->getMessage(), 'analysis');
                    // Continue to the next method
                }
            }
            
            // Fourth attempt: As a last resort, try with basic file content extraction
            if (!$extraction_successful) {
                try {
                    dv_debug_log("PDF: tentativo con metodo di fallback base...", 'analysis');
                    $content = file_get_contents($file_path);
                    
                    // Basic text extraction from PDF
                    $text = '';
                    $pattern = "/\/Text[^>]*>([^<]*)</";
                    if (preg_match_all($pattern, $content, $matches)) {
                        foreach ($matches[1] as $piece) {
                            $text .= $piece . ' ';
                        }
                    }
                    
                    // If we found something, mark as successful
                    if (!empty($text)) {
                        dv_debug_log("PDF: estrazione di fallback riuscita", 'analysis');
                        $extraction_successful = true;
                    } else {
                        // Try an even more basic pattern
                        $pattern = "/(\\(\\w+\\))/";
                        if (preg_match_all($pattern, $content, $matches)) {
                            foreach ($matches[1] as $piece) {
                                // Remove parentheses and add space
                                $text .= substr($piece, 1, -1) . ' ';
                            }
                        }
                        
                        if (!empty($text)) {
                            dv_debug_log("PDF: estrazione di fallback alternativa riuscita", 'analysis');
                            $extraction_successful = true;
                        }
                    }
                } catch (\Exception $e) {
                    dv_debug_log('PDF: estrazione di fallback fallita: ' . $e->getMessage(), 'analysis');
                }
            }
            
            // Restore normal error handler
            restore_error_handler();
            
            // If all extraction methods failed
            if (!$extraction_successful || empty($text)) {
                dv_debug_log("PDF: tutti i metodi diestrazione hanno fallito", 'analysis');
                throw new Exception('Non è stato possibile estrarre il testo dal PDF. Il file potrebbe essere protetto, danneggiato o in un formato non supportato.');
            }
            
            // Clean up the extracted text
            $text = $this->clean_extracted_text($text);
            
            // Restore original settings
            set_time_limit($original_time_limit);
            ini_set('memory_limit', $original_memory_limit);
            
            // Log un estratto del testo (primi 300 caratteri)
            $max_preview_length = 300;
            $text_preview = substr($text, 0, $max_preview_length);
            if (strlen($text) > $max_preview_length) {
                $text_preview .= '...';
            }
            dv_debug_log('PDF: estrazione completata. Anteprima del testo estratto: ' . PHP_EOL . $text_preview, 'analysis');
            dv_debug_log('PDF: lunghezza totale del testo estratto: ' . strlen($text) . ' caratteri', 'analysis');
            
            return $text;
        } catch (Exception $e) {
            dv_debug_log('PDF: errore durante l\'estrazione: ' . $e->getMessage(), 'analysis');
            
            // Restore settings in case of error
            if (isset($original_time_limit)) {
                set_time_limit($original_time_limit);
            }
            
            if (isset($original_memory_limit)) {
                ini_set('memory_limit', $original_memory_limit);
            }
            
            // Rethrow with a more user-friendly message
            throw new Exception('Errore durante l\'estrazione del testo: ' . $e->getMessage());
        }
    }
    
    /**
     * Estrae PDF con ImageMagick se disponibile
     * 
     * @param string $file_path Il percorso del file PDF
     * @return string Il testo estratto
     */
    private function extract_pdf_with_imagick($file_path) {
        $text = '';
        if (!extension_loaded('imagick')) {
            return $text;
        }
        
        try {
            $imagick = new \Imagick();
            $imagick->setResolution(300, 300);
            $imagick->readImage($file_path);
            
            $num_pages = $imagick->getNumberImages();
            dv_debug_log("Imagick: Elaborazione di $num_pages pagine", 'analysis');
            
            // Traverse each page
            for ($i = 0; $i < $num_pages; $i++) {
                // Select the page and convert to image
                $current_page = new \Imagick();
                $current_page->setResolution(300, 300);
                $current_page->readImage($file_path . "[$i]");
                
                // Set image format for OCR
                $current_page->setImageFormat('png');
                
                // If we have Tesseract OCR installed, use it
                if (function_exists('exec')) {
                    $temp_img = tempnam(sys_get_temp_dir(), 'pdf_ocr') . '.png';
                    $current_page->writeImage($temp_img);
                    
                    $temp_txt = tempnam(sys_get_temp_dir(), 'pdf_ocr') . '.txt';
                    exec("tesseract $temp_img $temp_txt 2>&1", $output, $return_var);
                    
                    if ($return_var === 0 && file_exists($temp_txt)) {
                        $page_text = file_get_contents($temp_txt);
                        $text .= $page_text . "\n\n";
                        @unlink($temp_txt);
                    }
                    
                    @unlink($temp_img);
                } else {
                    // If we can't use external commands, just extract any text in the image metadata
                    $page_text = $current_page->getImageProperties();
                    if (isset($page_text['comment'])) {
                        $text .= $page_text['comment'] . "\n";
                    }
                }
                
                $current_page->clear();
                $current_page->destroy();
                
                dv_debug_log("Imagick: Elaborata pagina " . ($i + 1) . " di $num_pages", 'analysis');
            }
            
            $imagick->clear();
            $imagick->destroy();
        } catch (\Exception $e) {
            dv_debug_log("Errore Imagick: " . $e->getMessage(), 'analysis');
        }
        
        return $text;
    }
    
    /**
     * Pulisce il testo estratto per migliorare l'analisi
     * 
     * @param string $text Il testo da pulire
     * @return string Il testo pulito
     */
    public function clean_extracted_text($text) {
        // Basic cleanup - Remove control characters
        $text = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $text);
        
        // Remove excessive whitespace but preserve paragraph breaks
        $text = preg_replace('/[ \t]+/', ' ', $text);
        $text = preg_replace('/\n{3,}/', "\n\n", $text);
        
        // Normalize newlines
        $text = str_replace(["\r\n", "\r"], "\n", $text);
        
        // Fix common PDF extraction artifacts
        $text = str_replace(['�', '…'], ['', '...'], $text);
        
        // Try to detect and fix words broken across lines
        $text = preg_replace('/(\w)-\n(\w)/', '$1$2', $text);
        
        // Remove page numbers that may appear in various formats
        $text = preg_replace('/\n\s*\d+\s*\n/', "\n", $text);
        
        // Break lines at reasonable points for LLM processing
        $text = wordwrap($text, 80, "\n", false);
        
        return trim($text);
    }
    
    /**
     * Verifica se PHPWord è disponibile
     * 
     * @return bool
     */
    public function check_phpword() {
        return class_exists('PhpOffice\PhpWord\PhpWord');
    }
    
    /**
     * Estrae contenuto da un documento DOCX
     * 
     * @param string $file_path Il percorso del file DOCX
     * @return string Il testo estratto
     * @throws Exception Se l'estrazione fallisce
     */
    public function extract_docx_content($file_path) {
        try {
            // Verifico che il file esista e sia leggibile
            if (!file_exists($file_path)) {
                throw new Exception('Il file DOCX non esiste: ' . $file_path);
            }
            
            if (!is_readable($file_path)) {
                throw new Exception('Il file DOCX non è leggibile: ' . $file_path);
            }
            
            // Log per debug
            dv_debug_log('Estrazione del contenuto dal documento Word: ' . $file_path);
            dv_debug_log('File size: ' . filesize($file_path) . ' bytes');
            
            // Verifico che PHPWord sia disponibile
            if (!class_exists('\PhpOffice\PhpWord\PhpWord')) {
                throw new Exception('PHPWord non è disponibile. Verificare l\'installazione di Composer.');
            }
            
            // Carico il documento
            try {
                $phpWord = \PhpOffice\PhpWord\IOFactory::load($file_path);
            } catch (\Exception $e) {
                dv_debug_log('Errore nel caricamento del documento Word: ' . $e->getMessage());
                throw new Exception('Impossibile caricare il documento Word: ' . $e->getMessage());
            }
            
            $text = '';
            
            // Estrai il testo da tutte le sezioni
            foreach ($phpWord->getSections() as $section) {
                foreach ($section->getElements() as $element) {
                    $text .= $this->extract_element_text($element);
                }
                // Aggiungi spaziatura tra le sezioni
                $text .= "\n\n";
            }
            
            if (empty($text)) {
                dv_debug_log('Nessun testo estratto dal documento Word');
                throw new Exception('Nessun testo estratto dal documento Word. Il file potrebbe essere protetto o danneggiato.');
            }
            
            dv_debug_log('Contenuto Word estratto con successo, lunghezza: ' . strlen($text) . ' caratteri');
            
            return $this->clean_extracted_text($text);
        } catch (\PhpOffice\PhpWord\Exception\Exception $e) {
            dv_debug_log('Errore PHPWord nell\'estrazione del documento: ' . $e->getMessage());
            throw new Exception('Errore nel processare il documento Word: ' . $e->getMessage());
        } catch (Exception $e) {
            dv_debug_log('Errore nell\'estrazione del documento Word: ' . $e->getMessage());
            throw new Exception('Errore nell\'analisi del documento Word: ' . $e->getMessage());
        }
    }

    /**
     * Ottiene messaggio di errore di upload
     * 
     * @param int $error_code Il codice di errore dell'upload
     * @return string Il messaggio di errore
     */
    public function get_upload_error_message($error_code) {
        switch ($error_code) {
            case UPLOAD_ERR_INI_SIZE:
                return __('The uploaded file exceeds the upload_max_filesize directive in php.ini.', 'document-viewer-plugin');
            case UPLOAD_ERR_FORM_SIZE:
                return __('The uploaded file exceeds the MAX_FILE_SIZE directive specified in the HTML form.', 'document-viewer-plugin');
            case UPLOAD_ERR_PARTIAL:
                return __('The uploaded file was only partially uploaded.', 'document-viewer-plugin');
            case UPLOAD_ERR_NO_FILE:
                return __('No file was uploaded.', 'document-viewer-plugin');
            case UPLOAD_ERR_NO_TMP_DIR:
                return __('Missing a temporary folder.', 'document-viewer-plugin');
            case UPLOAD_ERR_CANT_WRITE:
                return __('Failed to write file to disk.', 'document-viewer-plugin');
            case UPLOAD_ERR_EXTENSION:
                return __('A PHP extension stopped the file upload.', 'document-viewer-plugin');
            default:
                return __('Unknown upload error.', 'document-viewer-plugin');
        }
    }
}

// Funzione helper per creare un'istanza singleton della classe
function document_management() {
    static $instance = null;
    if ($instance === null) {
        $instance = new Document_Management();
    }
    return $instance;
}