/* Livello di formattazione avanzata per il testo dell'analisi */
.enhanced-text-formatting {
  font-family: 'Source Sans Pro', sans-serif;
  line-height: 1.6;
}

/* Miglioramento della leggibilità dei paragrafi */
.enhanced-text-formatting p {
  margin-bottom: 1.2em;
  text-align: justify;
  color: #333;
  font-size: 15px;
}

/* Evidenziazione di concetti chiave */
.enhanced-text-formatting .key-concept {
  background-color: rgba(255, 247, 212, 0.7);
  padding: 0 3px;
  border-radius: 3px;
  font-weight: 500;
}

/* Blocchi di citazione con stile migliorato */
.enhanced-text-formatting blockquote {
  border-left: 4px solid #0073aa;
  padding: 10px 15px;
  margin: 15px 0;
  background-color: #f7fafc;
  font-style: italic;
}

/* Box informativi */
.enhanced-text-formatting .info-box {
  background-color: #e8f4fd;
  border: 1px solid #bde0fe;
  border-radius: 5px;
  padding: 12px 15px;
  margin: 15px 0;
  position: relative;
}

.enhanced-text-formatting .info-box::before {
  content: "ℹ️";
  margin-right: 8px;
  font-size: 16px;
}

/* Box di avvertimento */
.enhanced-text-formatting .warning-box {
  background-color: #fff3e6;
  border: 1px solid #ffcca5;
  border-radius: 5px;
  padding: 12px 15px;
  margin: 15px 0;
}

.enhanced-text-formatting .warning-box::before {
  content: "⚠️";
  margin-right: 8px;
  font-size: 16px;
}

/* Tabelle più eleganti */
.enhanced-text-formatting table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  overflow: hidden;
  margin: 20px 0;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.enhanced-text-formatting th {
  background-color: #f8fafc;
  padding: 12px 15px;
  font-weight: 600;
  text-align: left;
  border-bottom: 2px solid #e2e8f0;
}

.enhanced-text-formatting td {
  padding: 10px 15px;
  border-bottom: 1px solid #e2e8f0;
}

.enhanced-text-formatting tr:last-child td {
  border-bottom: none;
}

.enhanced-text-formatting tr:hover {
  background-color: rgba(0,0,0,0.01);
}

/* Intestazioni con linea decorativa */
.enhanced-text-formatting h2 {
  position: relative;
  padding-bottom: 10px;
  margin-top: 25px;
  margin-bottom: 20px;
}

.enhanced-text-formatting h2::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100px;
  height: 3px;
  background: linear-gradient(90deg, #0073aa, transparent);
}

/* Evidenziazione termini finanziari */
.enhanced-text-formatting .financial-term {
  color: #2c5282;
  font-weight: 500;
  border-bottom: 1px dotted #2c5282;
  cursor: help;
}

/* Animazione per elementi che appaiono nel viewport */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.enhanced-text-formatting .animate-in {
  opacity: 0;
  animation: fadeInUp 0.5s ease forwards;
}

/* Miglioramento delle liste */
.enhanced-text-formatting ul, .enhanced-text-formatting ol {
  padding-left: 1.5em;
  margin-bottom: 1.2em;
}

.enhanced-text-formatting li {
  margin-bottom: 0.5em;
  line-height: 1.5;
}

/* Miglioramento del contrasto per i link */
.enhanced-text-formatting a {
  color: #0073aa;
  text-decoration: none;
  border-bottom: 1px solid rgba(0, 115, 170, 0.3);
  transition: border-color 0.2s ease, color 0.2s ease;
}

.enhanced-text-formatting a:hover {
  color: #005177;
  border-bottom-color: rgba(0, 81, 119, 0.7);
}

/* Stili per i punti chiave */
.key-points-list {
  list-style-type: none;
  padding-left: 0;
  margin-top: 15px;
}

.key-points-list li {
  position: relative;
  padding-left: 28px;
  margin-bottom: 15px;
  line-height: 1.5;
}

.key-points-list li::before {
  content: "✓";
  position: absolute;
  left: 0;
  top: 0;
  color: #0073aa;
  font-weight: bold;
  font-size: 18px;
}

/* Stile per codici o valori numerici */
.enhanced-text-formatting code {
  background: rgba(0, 115, 170, 0.08);
  padding: 2px 5px;
  border-radius: 3px;
  font-family: monospace;
  font-size: 0.9em;
  color: #333;
}

/* Stile per termini tecnici */
.enhanced-text-formatting .technical-term {
  font-style: italic;
  color: #555;
}

/* Stile per evidenziare importi e percentuali */
.enhanced-text-formatting .amount,
.enhanced-text-formatting .percentage {
  font-weight: 600;
  color: #2c5282;
}

/* Ottimizzazione per dispositivi mobili */
@media (max-width: 768px) {
  .enhanced-text-formatting p {
    font-size: 14px;
    text-align: left;
  }
  
  .enhanced-text-formatting table {
    font-size: 13px;
  }
  
  .enhanced-text-formatting th,
  .enhanced-text-formatting td {
    padding: 8px 10px;
  }
}