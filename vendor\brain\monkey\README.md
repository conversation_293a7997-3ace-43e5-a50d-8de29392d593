# README

## Brain Monkey

[![PHP Quality Assurance](https://github.com/Brain-WP/BrainMonkey/workflows/PHP%20Quality%20Assurance/badge.svg)](https://github.com/Brain-WP/BrainMonkey/actions?query=workflow%3A%22PHP+Quality+Assurance%22)
[![codecov](https://codecov.io/gh/Brain-WP/BrainMonkey/branch/master/graph/badge.svg)](https://codecov.io/gh/Brain-WP/BrainMonkey)

Brain Monkey is a tests utility for PHP.

It provides **two set of helpers**:

* the first are framework-agnostic tools that allow to mock \(or _monkey patch_\) and to test behavior of any **PHP function**
* the second are **specific to WordPress** and make unit testing of WordPress extensions a no-brainer.

## Requirements

* PHP 5.6+
* [Composer](https://getcomposer.org/) to install

Via Composer following packages are required:

* [mockery/mockery](https://packagist.org/packages/mockery/mockery) version 1 \(BSD-3-Clause\)
* [antecedent/patchwork](https://packagist.org/packages/antecedent/patchwork) version 2 \(MIT\)

When installed for development, following packages are also required:

* [phpunit/phpunit](https://packagist.org/packages/phpunit/phpunit) version 5.7 \(BSD-3-Clause\)

## License

Brain Monkey is open source and released under MIT license. See LICENSE file for more info.

## Question? Issues?

Brain Monkey is hosted on GitHub. Feel free to open issues there for suggestions, questions and real issues.

## Who's Behind

I'm Giuseppe, I deal with PHP since 2005. For questions, rants or chat ping me on Twitter \([@gmazzap](https://twitter.com/gmazzap)\) or on ["The Loop"](https://chat.stackexchange.com/rooms/6/the-loop) \(Stack Exchange\) chat.

Well, it's possible I'll ignore rants.

