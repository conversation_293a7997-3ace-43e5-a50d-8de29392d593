<?php
/**
 * Office Add-in Cache Manager
 *
 * This class provides caching functionality for Office Add-in to improve performance
 * and reduce API calls.
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

class Office_Addin_Cache_Manager {
    
    /**
     * Cache group for Office Add-in
     */
    private $cache_group = 'office_addin_cache';
    
    /**
     * Default cache durations (in seconds)
     */
    private $cache_durations = [
        'settings' => 300,           // 5 minutes for settings
        'queries' => 600,            // 10 minutes for predefined queries
        'api_test' => 60,            // 1 minute for API connection tests
        'analysis_result' => 1800    // 30 minutes for analysis results
    ];
    
    /**
     * Constructor
     */
    public function __construct() {
        // Initialize object cache group
        wp_cache_add_global_groups([$this->cache_group]);
    }
    
    /**
     * Get cached data
     *
     * @param string $key Cache key
     * @param string $type Cache type (affects duration)
     * @return mixed Cached data or false if not found
     */
    public function get($key, $type = 'default') {
        try {
            $cache_key = $this->generate_cache_key($key);
            $cached_data = wp_cache_get($cache_key, $this->cache_group);
            
            if ($cached_data !== false) {
                if (function_exists('dv_debug_log')) {
                    dv_debug_log("Cache hit for key: {$key} (type: {$type})", 'office_addin');
                }
                
                // Check if cached data has expiration info
                if (is_array($cached_data) && isset($cached_data['expires'])) {
                    if (time() > $cached_data['expires']) {
                        // Data has expired
                        $this->delete($key);
                        return false;
                    }
                    return $cached_data['data'];
                }
                
                return $cached_data;
            }
            
            return false;
            
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Cache get exception: ' . $e->getMessage(), 'office_addin');
            }
            return false;
        }
    }
    
    /**
     * Set cached data
     *
     * @param string $key Cache key
     * @param mixed $data Data to cache
     * @param string $type Cache type (affects duration)
     * @param int $custom_duration Custom cache duration in seconds
     * @return bool Success status
     */
    public function set($key, $data, $type = 'default', $custom_duration = null) {
        try {
            $cache_key = $this->generate_cache_key($key);
            $duration = $custom_duration ?: $this->get_cache_duration($type);
            
            // Add expiration timestamp to cached data
            $cached_data = [
                'data' => $data,
                'expires' => time() + $duration,
                'type' => $type,
                'created' => time()
            ];
            
            $result = wp_cache_set($cache_key, $cached_data, $this->cache_group, $duration);
            
            if (function_exists('dv_debug_log')) {
                dv_debug_log("Cache set for key: {$key} (type: {$type}, duration: {$duration}s)", 'office_addin');
            }
            
            return $result;
            
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Cache set exception: ' . $e->getMessage(), 'office_addin');
            }
            return false;
        }
    }
    
    /**
     * Delete cached data
     *
     * @param string $key Cache key
     * @return bool Success status
     */
    public function delete($key) {
        try {
            $cache_key = $this->generate_cache_key($key);
            $result = wp_cache_delete($cache_key, $this->cache_group);
            
            if (function_exists('dv_debug_log')) {
                dv_debug_log("Cache deleted for key: {$key}", 'office_addin');
            }
            
            return $result;
            
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Cache delete exception: ' . $e->getMessage(), 'office_addin');
            }
            return false;
        }
    }
    
    /**
     * Get cached settings
     *
     * @return array|false Cached settings or false
     */
    public function get_settings() {
        return $this->get('office_addin_settings', 'settings');
    }
    
    /**
     * Set cached settings
     *
     * @param array $settings Settings data
     * @return bool Success status
     */
    public function set_settings($settings) {
        return $this->set('office_addin_settings', $settings, 'settings');
    }
    
    /**
     * Get cached predefined queries
     *
     * @return array|false Cached queries or false
     */
    public function get_queries() {
        return $this->get('predefined_queries', 'queries');
    }
    
    /**
     * Set cached predefined queries
     *
     * @param array $queries Queries data
     * @return bool Success status
     */
    public function set_queries($queries) {
        return $this->set('predefined_queries', $queries, 'queries');
    }
    
    /**
     * Get cached API test result
     *
     * @return array|false Cached result or false
     */
    public function get_api_test_result() {
        return $this->get('api_test_result', 'api_test');
    }
    
    /**
     * Set cached API test result
     *
     * @param array $result Test result
     * @return bool Success status
     */
    public function set_api_test_result($result) {
        return $this->set('api_test_result', $result, 'api_test');
    }
    
    /**
     * Get cached analysis result
     *
     * @param string $text_hash Hash of the analyzed text
     * @param string $query_hash Hash of the query
     * @return array|false Cached result or false
     */
    public function get_analysis_result($text_hash, $query_hash) {
        $key = "analysis_{$text_hash}_{$query_hash}";
        return $this->get($key, 'analysis_result');
    }
    
    /**
     * Set cached analysis result
     *
     * @param string $text_hash Hash of the analyzed text
     * @param string $query_hash Hash of the query
     * @param array $result Analysis result
     * @return bool Success status
     */
    public function set_analysis_result($text_hash, $query_hash, $result) {
        $key = "analysis_{$text_hash}_{$query_hash}";
        return $this->set($key, $result, 'analysis_result');
    }
    
    /**
     * Generate hash for text content
     *
     * @param string $text Text content
     * @return string Hash
     */
    public function generate_text_hash($text) {
        return hash('sha256', trim($text));
    }
    
    /**
     * Generate hash for query
     *
     * @param mixed $query Query (can be ID or text)
     * @return string Hash
     */
    public function generate_query_hash($query) {
        return hash('sha256', serialize($query));
    }
    
    /**
     * Clear all cached data
     *
     * @return bool Success status
     */
    public function clear_all() {
        try {
            // Since WordPress doesn't provide a direct way to clear cache group,
            // we'll use a timestamp-based invalidation approach
            $cache_version = time();
            update_option('office_addin_cache_version', $cache_version);
            
            if (function_exists('dv_debug_log')) {
                dv_debug_log('All cache cleared with version: ' . $cache_version, 'office_addin');
            }
            
            return true;
            
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Clear all cache exception: ' . $e->getMessage(), 'office_addin');
            }
            return false;
        }
    }
    
    /**
     * Clear specific cache type
     *
     * @param string $type Cache type
     * @return bool Success status
     */
    public function clear_by_type($type) {
        try {
            $type_version = time();
            update_option("office_addin_cache_version_{$type}", $type_version);
            
            if (function_exists('dv_debug_log')) {
                dv_debug_log("Cache type '{$type}' cleared with version: {$type_version}", 'office_addin');
            }
            
            return true;
            
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Clear cache by type exception: ' . $e->getMessage(), 'office_addin');
            }
            return false;
        }
    }
    
    /**
     * Get cache duration for specific type
     *
     * @param string $type Cache type
     * @return int Duration in seconds
     */
    private function get_cache_duration($type) {
        // Allow customization via options
        $custom_durations = get_option('office_addin_cache_durations', []);
        
        if (isset($custom_durations[$type])) {
            return intval($custom_durations[$type]);
        }
        
        return isset($this->cache_durations[$type]) ? $this->cache_durations[$type] : 300;
    }
    
    /**
     * Generate cache key with version for invalidation
     *
     * @param string $key Base cache key
     * @return string Versioned cache key
     */
    private function generate_cache_key($key) {
        $global_version = get_option('office_addin_cache_version', 1);
        return "v{$global_version}_{$key}";
    }
    
    /**
     * Update cache durations
     *
     * @param array $durations New cache durations
     * @return bool Success status
     */
    public function update_cache_durations($durations) {
        try {
            // Validate durations
            $validated_durations = [];
            
            foreach ($durations as $type => $duration) {
                if (is_numeric($duration) && $duration >= 0) {
                    $validated_durations[sanitize_key($type)] = intval($duration);
                }
            }
            
            update_option('office_addin_cache_durations', $validated_durations);
            
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Cache durations updated: ' . print_r($validated_durations, true), 'office_addin');
            }
            
            return true;
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Update cache durations exception: ' . $e->getMessage(), 'office_addin');
            }
            return false;
        }
    }
    
    /**
     * Get cache statistics
     *
     * @return array Cache statistics
     */
    public function get_cache_stats() {
        try {
            $stats = [
                'cache_group' => $this->cache_group,
                'cache_durations' => $this->cache_durations,
                'global_version' => get_option('office_addin_cache_version', 1),
                'custom_durations' => get_option('office_addin_cache_durations', []),
                'hit_ratio' => 'Not available', // Would require more sophisticated tracking
                'total_size' => 'Not available' // Would require more sophisticated tracking
            ];
            
            return $stats;
            
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Get cache stats exception: ' . $e->getMessage(), 'office_addin');
            }
            return ['error' => 'Unable to get cache statistics'];
        }
    }
    
    /**
     * Warm up cache with commonly used data
     *
     * @return bool Success status
     */
    public function warm_up_cache() {
        try {
            // Pre-load settings
            $api_key = get_option('document_viewer_api_key', '');
            $api_endpoint = get_option('document_viewer_api_endpoint', '');
            $model = get_option('document_viewer_model', '');
            
            $settings = [
                'model' => $model,
                'api_configured' => !empty($api_key) && !empty($api_endpoint),
                'endpoint_configured' => !empty($api_endpoint)
            ];
            
            $this->set_settings($settings);
            
            // Pre-load predefined queries
            global $wpdb;
            $table_name = $wpdb->prefix . 'document_preset_queries';
            
            if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name) {
                $queries = $wpdb->get_results(
                    "SELECT id, query_text FROM $table_name ORDER BY id ASC",
                    ARRAY_A
                );
                
                if (!empty($queries)) {
                    $this->set_queries($queries);
                }
            }
            
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Cache warmed up successfully', 'office_addin');
            }
            
            return true;
            
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Cache warm up exception: ' . $e->getMessage(), 'office_addin');
            }
            return false;
        }
    }
}
