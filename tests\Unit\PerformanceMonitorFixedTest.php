<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;

/**
 * Unit tests for Office_Addin_Performance_Monitor class
 * Fixed to use actual class API methods and timer_id-based operations
 */
class PerformanceMonitorFixedTest extends TestCase
{
    private $performance_monitor;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Clear test data before each test
        \TestOptions::clear();
        \TestCache::clear();
        
        // Include the performance monitor class
        require_once __DIR__ . '/../../includes/class-office-addin-performance-monitor.php';
        
        $this->performance_monitor = new \Office_Addin_Performance_Monitor();
    }

    protected function tearDown(): void
    {
        // Clear test data after each test
        \TestOptions::clear();
        \TestCache::clear();
        
        parent::tearDown();
    }

    /**
     * Helper method to access private properties via reflection
     */
    private function getProperty($object, $property)
    {
        $reflection = new \ReflectionClass($object);
        $prop = $reflection->getProperty($property);
        $prop->setAccessible(true);
        return $prop->getValue($object);
    }

    /**
     * Helper method to set private properties via reflection
     */
    private function setProperty($object, $property, $value)
    {
        $reflection = new \ReflectionClass($object);
        $prop = $reflection->getProperty($property);
        $prop->setAccessible(true);
        $prop->setValue($object, $value);
    }

    public function testStartTimerReturnsTimerId(): void
    {
        $metric_name = 'test_operation';
        
        $timer_id = $this->performance_monitor->start_timer($metric_name);
        
        $this->assertIsString($timer_id);
        $this->assertStringStartsWith('timer_', $timer_id);
        $this->assertNotEmpty($timer_id);
    }

    public function testStartTimerWithContext(): void
    {
        $metric_name = 'api_request';
        $context = ['endpoint' => '/test', 'method' => 'GET'];
        
        $timer_id = $this->performance_monitor->start_timer($metric_name, $context);
        
        $this->assertIsString($timer_id);
        $this->assertStringStartsWith('timer_', $timer_id);
    }

    public function testStopTimerReturnsMetricData(): void
    {
        $metric_name = 'test_operation';
        
        $timer_id = $this->performance_monitor->start_timer($metric_name);
        usleep(1000); // Sleep 1ms to ensure measurable time
        $result = $this->performance_monitor->stop_timer($timer_id);
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('metric_name', $result);
        $this->assertArrayHasKey('duration_ms', $result);
        $this->assertArrayHasKey('memory_usage_bytes', $result);
        $this->assertArrayHasKey('timestamp', $result);
        $this->assertArrayHasKey('context', $result);
        $this->assertArrayHasKey('timer_id', $result);
        
        $this->assertEquals($metric_name, $result['metric_name']);
        $this->assertIsFloat($result['duration_ms']);
        $this->assertGreaterThan(0, $result['duration_ms']);
        $this->assertEquals($timer_id, $result['timer_id']);
    }

    public function testStopTimerWithInvalidIdReturnsFalse(): void
    {
        $invalid_timer_id = 'nonexistent_timer_id';
        
        $result = $this->performance_monitor->stop_timer($invalid_timer_id);
        
        $this->assertFalse($result);
    }

    public function testStopTimerWithAdditionalContext(): void
    {
        $metric_name = 'api_request';
        $initial_context = ['endpoint' => '/test'];
        $additional_context = ['status_code' => 200, 'response_size' => 1024];
        
        $timer_id = $this->performance_monitor->start_timer($metric_name, $initial_context);
        usleep(1000);
        $result = $this->performance_monitor->stop_timer($timer_id, $additional_context);
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('context', $result);
        $this->assertArrayHasKey('endpoint', $result['context']);
        $this->assertArrayHasKey('status_code', $result['context']);
        $this->assertArrayHasKey('response_size', $result['context']);
        $this->assertEquals('/test', $result['context']['endpoint']);
        $this->assertEquals(200, $result['context']['status_code']);
        $this->assertEquals(1024, $result['context']['response_size']);
    }

    public function testRecordMetricStoresBasicMetric(): void
    {
        $metric_name = 'user_count';
        $value = 150.5;
        $unit = 'count';
        
        $result = $this->performance_monitor->record_metric($metric_name, $value, $unit);
        
        $this->assertTrue($result);
    }

    public function testRecordMetricWithContext(): void
    {
        $metric_name = 'cache_hit_rate';
        $value = 85.7;
        $unit = 'percentage';
        $context = ['cache_type' => 'redis', 'region' => 'us-east-1'];
        
        $result = $this->performance_monitor->record_metric($metric_name, $value, $unit, $context);
        
        $this->assertTrue($result);
    }

    public function testRecordMetricWithDefaultUnit(): void
    {
        $metric_name = 'login_attempts';
        $value = 42;
        
        $result = $this->performance_monitor->record_metric($metric_name, $value);
        
        $this->assertTrue($result);
    }

    public function testGetMetricsReturnsArray(): void
    {
        // Record some test metrics first
        $this->performance_monitor->record_metric('test_metric_1', 100);
        $this->performance_monitor->record_metric('test_metric_2', 200);
        
        $metrics = $this->performance_monitor->get_metrics();
        
        $this->assertIsArray($metrics);
        // Note: metrics might be empty if database table doesn't exist or is empty
        // This is acceptable behavior
    }

    public function testGetMetricsWithFiltering(): void
    {
        $metric_name = 'specific_metric';
        
        // Record test metric
        $this->performance_monitor->record_metric($metric_name, 75);
        
        $metrics = $this->performance_monitor->get_metrics($metric_name, 10, 1);
        
        $this->assertIsArray($metrics);
    }

    public function testGetMetricsWithLimitAndTimeFilter(): void
    {
        $metrics = $this->performance_monitor->get_metrics(null, 5, 24);
        
        $this->assertIsArray($metrics);
        $this->assertLessThanOrEqual(5, count($metrics));
    }

    public function testGetPerformanceStatsReturnsArray(): void
    {
        // Start and stop a timer to generate some data
        $timer_id = $this->performance_monitor->start_timer('test_operation');
        usleep(1000);
        $this->performance_monitor->stop_timer($timer_id);
        
        $stats = $this->performance_monitor->get_performance_stats(24);
        
        $this->assertIsArray($stats);
        $this->assertArrayHasKey('response_times', $stats);
        $this->assertArrayHasKey('memory_usage', $stats);
        $this->assertArrayHasKey('hourly_requests', $stats);
        $this->assertArrayHasKey('threshold_violations', $stats);
        $this->assertArrayHasKey('period_hours', $stats);
        $this->assertArrayHasKey('thresholds', $stats);
    }

    public function testGetPerformanceStatsWithDifferentTimeFrames(): void
    {
        $stats_1h = $this->performance_monitor->get_performance_stats(1);
        $stats_24h = $this->performance_monitor->get_performance_stats(24);
        $stats_7d = $this->performance_monitor->get_performance_stats(168); // 7 days
        
        $this->assertIsArray($stats_1h);
        $this->assertIsArray($stats_24h);
        $this->assertIsArray($stats_7d);
        
        $this->assertEquals(1, $stats_1h['period_hours']);
        $this->assertEquals(24, $stats_24h['period_hours']);
        $this->assertEquals(168, $stats_7d['period_hours']);
    }

    public function testGetPerformanceRecommendationsReturnsArray(): void
    {
        $recommendations = $this->performance_monitor->get_performance_recommendations(24);
        
        $this->assertIsArray($recommendations);
        // Recommendations might be empty if no performance issues detected
        // This is acceptable behavior
    }

    public function testGetPerformanceRecommendationsWithDifferentTimeFrames(): void
    {
        $recommendations_1h = $this->performance_monitor->get_performance_recommendations(1);
        $recommendations_24h = $this->performance_monitor->get_performance_recommendations(24);
        
        $this->assertIsArray($recommendations_1h);
        $this->assertIsArray($recommendations_24h);
    }

    public function testCleanupOldMetrics(): void
    {
        // This method doesn't return anything, just ensure it doesn't throw exceptions
        $this->performance_monitor->cleanup_old_metrics();
        
        // If we get here without exceptions, the test passes
        $this->assertTrue(true);
    }

    public function testExportPerformanceDataReturnsArray(): void
    {
        $export_data = $this->performance_monitor->export_performance_data(24);
        
        $this->assertIsArray($export_data);
        $this->assertArrayHasKey('export_timestamp', $export_data);
        $this->assertArrayHasKey('export_period_hours', $export_data);
        $this->assertArrayHasKey('metrics', $export_data);
        $this->assertArrayHasKey('statistics', $export_data);
        $this->assertArrayHasKey('recommendations', $export_data);
        $this->assertArrayHasKey('thresholds', $export_data);
        
        $this->assertEquals(24, $export_data['export_period_hours']);
        $this->assertIsInt($export_data['export_timestamp']);
    }

    public function testExportPerformanceDataWithDifferentTimeFrames(): void
    {
        $export_1h = $this->performance_monitor->export_performance_data(1);
        $export_24h = $this->performance_monitor->export_performance_data(24);
        
        $this->assertIsArray($export_1h);
        $this->assertIsArray($export_24h);
        
        $this->assertEquals(1, $export_1h['export_period_hours']);
        $this->assertEquals(24, $export_24h['export_period_hours']);
    }

    public function testTimerWorkflowEndToEnd(): void
    {
        $metric_name = 'end_to_end_test';
        $context = ['test_type' => 'integration'];
        
        // Start timer
        $timer_id = $this->performance_monitor->start_timer($metric_name, $context);
        $this->assertIsString($timer_id);
        
        // Simulate some work
        usleep(2000); // 2ms
        
        // Stop timer
        $metric_data = $this->performance_monitor->stop_timer($timer_id);
        $this->assertIsArray($metric_data);
        $this->assertEquals($metric_name, $metric_data['metric_name']);
        $this->assertGreaterThan(1, $metric_data['duration_ms']); // Should be > 1ms
        $this->assertArrayHasKey('test_type', $metric_data['context']);
        $this->assertEquals('integration', $metric_data['context']['test_type']);
    }

    public function testMultipleTimersSimultaneously(): void
    {
        $operations = ['op1', 'op2', 'op3'];
        $timer_ids = [];
        
        // Start all timers
        foreach ($operations as $op) {
            $timer_id = $this->performance_monitor->start_timer($op);
            $timer_ids[$op] = $timer_id;
            $this->assertIsString($timer_id);
            usleep(500); // Small delay between starts
        }
        
        // Stop timers in reverse order
        foreach (array_reverse($operations) as $op) {
            $metric_data = $this->performance_monitor->stop_timer($timer_ids[$op]);
            $this->assertIsArray($metric_data);
            $this->assertEquals($op, $metric_data['metric_name']);
            $this->assertGreaterThan(0, $metric_data['duration_ms']);
        }
    }

    public function testMetricDataTypes(): void
    {
        // Test integer values
        $result1 = $this->performance_monitor->record_metric('int_metric', 42);
        $this->assertTrue($result1);
        
        // Test float values
        $result2 = $this->performance_monitor->record_metric('float_metric', 3.14159);
        $this->assertTrue($result2);
        
        // Test string values (should be converted to float)
        $result3 = $this->performance_monitor->record_metric('string_metric', '123.45');
        $this->assertTrue($result3);
        
        // Test zero values
        $result4 = $this->performance_monitor->record_metric('zero_metric', 0);
        $this->assertTrue($result4);
        
        // Test negative values
        $result5 = $this->performance_monitor->record_metric('negative_metric', -5.5);
        $this->assertTrue($result5);
    }

    public function testPerformanceThresholds(): void
    {
        // Get current thresholds by checking private property
        $thresholds = $this->getProperty($this->performance_monitor, 'thresholds');
        
        $this->assertIsArray($thresholds);
        $this->assertArrayHasKey('api_request', $thresholds);
        $this->assertArrayHasKey('database_query', $thresholds);
        $this->assertArrayHasKey('cache_operation', $thresholds);
        $this->assertArrayHasKey('total_request', $thresholds);
          // Verify threshold values are reasonable
        $this->assertIsNumeric($thresholds['api_request']);
        $this->assertGreaterThan(0, $thresholds['api_request']);
    }

    public function testCacheGroupConfiguration(): void
    {
        $cache_group = $this->getProperty($this->performance_monitor, 'cache_group');
        
        $this->assertEquals('office_addin_performance', $cache_group);
    }

    public function testRetentionPeriodConfiguration(): void
    {
        $retention_period = $this->getProperty($this->performance_monitor, 'retention_period');
        
        $this->assertIsInt($retention_period);
        $this->assertEquals(2592000, $retention_period); // 30 days in seconds
        $this->assertEquals(30 * 24 * 60 * 60, $retention_period);
    }
}
