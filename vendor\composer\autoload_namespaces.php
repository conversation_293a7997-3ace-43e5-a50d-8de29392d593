<?php

// autoload_namespaces.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'cli' => array($vendorDir . '/wp-cli/php-cli-tools/lib'),
    'WP_CLI\\' => array($vendorDir . '/wp-cli/wp-cli/php'),
    'Smalot\\PdfParser\\' => array($vendorDir . '/smalot/pdfparser/src'),
    'Oxymel' => array($vendorDir . '/nb/oxymel'),
    'Mustache' => array($vendorDir . '/wp-cli/mustache/src'),
);
