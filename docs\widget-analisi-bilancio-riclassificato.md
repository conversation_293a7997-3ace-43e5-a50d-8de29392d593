# Widget Analisi Bilancio Riclassificato

## Descrizione tecnica
Modulo calcolo indici, generazione grafici (Chart.js/ApexCharts), analisi AI (OpenAI API), confronto benchmark.

---

## Fasi di sviluppo e test

### 1. Calcolo indici
- Implementazione formule indici su dati riclassificati.
- API REST per calcolo.

**Prompt sviluppo:**  
- Implementa calcolo indici e API.
- Scrivi test unitari su formule.

**Test:**  
- Calcolo indici, confronto manuale.

---

### 2. Grafici dinamici
- Componente grafici KPI/trend.
- Export immagini.

**Prompt sviluppo:**  
- Crea UI grafici dinamici e export immagini.

**Test:**  
- Visualizzazione grafici, export immagini.

---

### 3. Analisi AI e suggerimenti
- Integrazione OpenAI API per commenti/alert.
- UI suggerimenti e alert soglie.

**Prompt sviluppo:**  
- Integra AI per generazione commenti e alert.

**Test:**  
- Output AI, verifica coerenza, alert soglie.

---

### 4. Benchmark settore
- DB benchmark, confronto automatico.
- Visualizzazione gap.

**Prompt sviluppo:**  
- Implementa confronto automatico con benchmark.

**Test:**  
- Visualizzazione confronto, alert gap.

---

### 5. Export e notifiche
- Export PDF/Excel, invio notifiche email/Telegram.

**Prompt sviluppo:**  
- Implementa export e notifiche.

**Test:**  
- Download, ricezione notifiche.

---

## Checklist validazione fase
- Ogni fase deve essere testabile singolarmente (mock/demo).
- Test unitari e funzionali completati.
- Documentazione aggiornata.
