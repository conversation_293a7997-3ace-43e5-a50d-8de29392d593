<?php
/**
 * User Subscription Manager
 * 
 * Class per gestire gli utenti e le sottoscrizioni
 * 
 * @package Financial_Advisor
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class User_Subscription_Manager {
    /**
     * Table names for users and subscription types
     * @var string
     */
    private $users_table;
    private $subscription_types_table;

    /**
     * Constructor
     */
    public function __construct() {
        global $wpdb;
        $this->users_table = 'wpcd_user_subscription';
        $this->subscription_types_table = $wpdb->prefix . 'type_subscription';

        // Create tables on plugin activation
        register_activation_hook(plugin_dir_path(dirname(__FILE__)) . 'document-advisor-plugin.php', array($this, 'create_user_tables'));

        // AJAX handlers
        add_action('wp_ajax_get_user_subscriptions', array($this, 'get_user_subscriptions_ajax'));
        add_action('wp_ajax_add_user_subscription', array($this, 'add_user_subscription_ajax'));
        add_action('wp_ajax_update_user_subscription', array($this, 'update_user_subscription_ajax'));
        add_action('wp_ajax_delete_user_subscription', array($this, 'delete_user_subscription_ajax'));
        
        add_action('wp_ajax_get_subscription_types', array($this, 'get_subscription_types_ajax'));
        add_action('wp_ajax_add_subscription_type', array($this, 'add_subscription_type_ajax'));
        add_action('wp_ajax_update_subscription_type', array($this, 'update_subscription_type_ajax'));
        add_action('wp_ajax_delete_subscription_type', array($this, 'delete_subscription_type_ajax'));

        // Check if tables exist, create if not
        add_action('plugins_loaded', array($this, 'check_tables_exist'));
    }

    /**
     * Check if the tables exist, and create them if not
     */
    public function check_tables_exist() {
        global $wpdb;

        if ($wpdb->get_var("SHOW TABLES LIKE '{$this->users_table}'") != $this->users_table) {
            $this->create_user_subscription_table();
        }
        
        if ($wpdb->get_var("SHOW TABLES LIKE '{$this->subscription_types_table}'") != $this->subscription_types_table) {
            $this->create_subscription_types_table();
        }
    }

    /**
     * Create the user subscription and subscription types tables
     */
    public function create_user_tables() {
        $this->create_user_subscription_table();
        $this->create_subscription_types_table();
    }

    /**
     * Create the User_subscription table
     */
    public function create_user_subscription_table() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE IF NOT EXISTS {$this->users_table} (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            username VARCHAR(191) NOT NULL UNIQUE,
            name VARCHAR(100) NOT NULL,
            surname VARCHAR(100) NOT NULL,
            phone VARCHAR(20),
            email VARCHAR(191) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            tipo_subscription VARCHAR(100) NOT NULL,
            credit DECIMAL(10,2) DEFAULT 0.00,
            analysis_count INT(11) DEFAULT 0,
            tokens_used INT(11) DEFAULT 0,
            actual_cost DECIMAL(10,2) DEFAULT 0.00,
            tot_cost DECIMAL(10,2) DEFAULT 0.00,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";

        require_once ABSPATH . 'wp-admin/includes/upgrade.php';
        dbDelta($sql);
        
        dv_debug_log('User subscription table created or updated');
    }

    /**
     * Create the Type_subscription table
     */
    public function create_subscription_types_table() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE IF NOT EXISTS {$this->subscription_types_table} (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            type_sub VARCHAR(100) NOT NULL UNIQUE,
            link_redirect VARCHAR(255),
            cost_per_token DECIMAL(10,6) NOT NULL DEFAULT 0.001,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";

        require_once ABSPATH . 'wp-admin/includes/upgrade.php';
        dbDelta($sql);
        
        dv_debug_log('Subscription types table created or updated');
    }

    /**
     * Get all user subscriptions
     */
    public function get_user_subscriptions() {
        global $wpdb;
        return $wpdb->get_results("SELECT * FROM {$this->users_table} ORDER BY surname, name", ARRAY_A);
    }

    /**
     * AJAX handler for getting user subscriptions
     */    public function get_user_subscriptions_ajax() {
        // Check nonce
        // Verifica nonce - accettiamo sia il nonce unificato che quello precedente per retrocompatibilità
        $valid_nonce = false;
        if (isset($_POST['nonce'])) {
            $valid_nonce = wp_verify_nonce($_POST['nonce'], 'unified_access_nonce') || 
                          wp_verify_nonce($_POST['nonce'], 'document_viewer_nonce');
        }
        
        if (!$valid_nonce) {
            wp_send_json_error(['message' => __('Security check failed.', 'document-viewer-plugin')]);
        }

        // Check permissions using FA access control
        if (!apply_filters('fa_user_can_access', false, 'admin')) {
            wp_send_json_error(['message' => __('You do not have permission to perform this action.', 'document-viewer-plugin')]);
        }

        $users = $this->get_user_subscriptions();
        wp_send_json_success(['users' => $users]);
    }

    /**
     * Add a new user subscription
     */
    public function add_user_subscription($data) {
        global $wpdb;

        // Validate required fields
        if (empty($data['username']) || empty($data['name']) || empty($data['surname']) || 
            empty($data['email']) || empty($data['password']) || empty($data['tipo_subscription'])) {
            return ['success' => false, 'message' => __('All required fields must be filled.', 'document-viewer-plugin')];
        }

        // Check if username or email already exists
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$this->users_table} WHERE username = %s OR email = %s",
            $data['username'], $data['email']
        ));

        if ($existing > 0) {
            return ['success' => false, 'message' => __('Username or email already exists.', 'document-viewer-plugin')];
        }

        // Hash password
        $data['password'] = wp_hash_password($data['password']);

        // Insert user
        $result = $wpdb->insert(
            $this->users_table,
            [
                'username' => $data['username'],
                'name' => $data['name'],
                'surname' => $data['surname'],
                'phone' => isset($data['phone']) ? $data['phone'] : '',
                'email' => $data['email'],
                'password' => $data['password'],
                'tipo_subscription' => $data['tipo_subscription'],
                'credit' => isset($data['credit']) ? $data['credit'] : 0.00
            ],
            ['%s', '%s', '%s', '%s', '%s', '%s', '%s', '%f']
        );

        if ($result === false) {
            return ['success' => false, 'message' => __('Failed to add user. Database error.', 'document-viewer-plugin')];
        }

        return ['success' => true, 'message' => __('User added successfully.', 'document-viewer-plugin'), 'user_id' => $wpdb->insert_id];
    }

    /**
     * AJAX handler for adding user subscription
     */    public function add_user_subscription_ajax() {
        // Check nonce
        // Verifica nonce - accettiamo sia il nonce unificato che quello precedente per retrocompatibilità
        $valid_nonce = false;
        if (isset($_POST['nonce'])) {
            $valid_nonce = wp_verify_nonce($_POST['nonce'], 'unified_access_nonce') || 
                          wp_verify_nonce($_POST['nonce'], 'document_viewer_nonce');
        }
        
        if (!$valid_nonce) {
            wp_send_json_error(['message' => __('Security check failed.', 'document-viewer-plugin')]);
        }

        // Check permissions using FA access control
        if (!fa_user_can_access('admin')) {
            wp_send_json_error(['message' => __('You do not have permission to perform this action.', 'document-viewer-plugin')]);
        }

        // Get and sanitize data
        $data = [
            'username' => isset($_POST['username']) ? sanitize_user($_POST['username']) : '',
            'name' => isset($_POST['name']) ? sanitize_text_field($_POST['name']) : '',
            'surname' => isset($_POST['surname']) ? sanitize_text_field($_POST['surname']) : '',
            'phone' => isset($_POST['phone']) ? sanitize_text_field($_POST['phone']) : '',
            'email' => isset($_POST['email']) ? sanitize_email($_POST['email']) : '',
            'password' => isset($_POST['password']) ? $_POST['password'] : '',
            'tipo_subscription' => isset($_POST['tipo_subscription']) ? sanitize_text_field($_POST['tipo_subscription']) : '',
            'credit' => isset($_POST['credit']) ? floatval($_POST['credit']) : 0.00
        ];

        $result = $this->add_user_subscription($data);
        
        if ($result['success']) {
            wp_send_json_success(['message' => $result['message'], 'user_id' => $result['user_id']]);
        } else {
            wp_send_json_error(['message' => $result['message']]);
        }
    }

    /**
     * Update a user subscription
     */
    public function update_user_subscription($user_id, $data) {
        global $wpdb;

        // Validate user_id
        if (empty($user_id)) {
            return ['success' => false, 'message' => __('User ID is required.', 'document-viewer-plugin')];
        }

        // Prepare update data
        $update_data = [];
        $formats = [];

        // Only update fields that are provided
        if (!empty($data['name'])) {
            $update_data['name'] = $data['name'];
            $formats[] = '%s';
        }

        if (!empty($data['surname'])) {
            $update_data['surname'] = $data['surname'];
            $formats[] = '%s';
        }

        if (isset($data['phone'])) { // Allow empty phone
            $update_data['phone'] = $data['phone'];
            $formats[] = '%s';
        }

        if (!empty($data['email'])) {
            // Check if email already exists for another user
            $existing = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM {$this->users_table} WHERE email = %s AND id != %d",
                $data['email'], $user_id
            ));

            if ($existing > 0) {
                return ['success' => false, 'message' => __('Email already exists for another user.', 'document-viewer-plugin')];
            }

            $update_data['email'] = $data['email'];
            $formats[] = '%s';
        }

        if (!empty($data['password'])) {
            $update_data['password'] = wp_hash_password($data['password']);
            $formats[] = '%s';
        }

        if (!empty($data['tipo_subscription'])) {
            $update_data['tipo_subscription'] = $data['tipo_subscription'];
            $formats[] = '%s';
        }

        if (isset($data['credit'])) {
            $update_data['credit'] = floatval($data['credit']);
            $formats[] = '%f';
        }

        // If no data to update
        if (empty($update_data)) {
            return ['success' => false, 'message' => __('No data provided for update.', 'document-viewer-plugin')];
        }

        // Update user
        $result = $wpdb->update(
            $this->users_table,
            $update_data,
            ['id' => $user_id],
            $formats,
            ['%d']
        );

        if ($result === false) {
            return ['success' => false, 'message' => __('Failed to update user. Database error.', 'document-viewer-plugin')];
        }

        return ['success' => true, 'message' => __('User updated successfully.', 'document-viewer-plugin')];
    }

    /**
     * AJAX handler for updating user subscription
     */    public function update_user_subscription_ajax() {
        // Check nonce
        // Verifica nonce - accettiamo sia il nonce unificato che quello precedente per retrocompatibilità
        $valid_nonce = false;
        if (isset($_POST['nonce'])) {
            $valid_nonce = wp_verify_nonce($_POST['nonce'], 'unified_access_nonce') || 
                          wp_verify_nonce($_POST['nonce'], 'document_viewer_nonce');
        }
        
        if (!$valid_nonce) {
            wp_send_json_error(['message' => __('Security check failed.', 'document-viewer-plugin')]);
        }

        // Check permissions using FA access control
        if (!fa_user_can_access('admin')) {
            wp_send_json_error(['message' => __('You do not have permission to perform this action.', 'document-viewer-plugin')]);
        }

        // Get and validate user_id
        $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
        if (empty($user_id)) {
            wp_send_json_error(['message' => __('User ID is required.', 'document-viewer-plugin')]);
        }

        // Get and sanitize data
        $data = [
            'name' => isset($_POST['name']) ? sanitize_text_field($_POST['name']) : '',
            'surname' => isset($_POST['surname']) ? sanitize_text_field($_POST['surname']) : '',
            'phone' => isset($_POST['phone']) ? sanitize_text_field($_POST['phone']) : '',
            'email' => isset($_POST['email']) ? sanitize_email($_POST['email']) : '',
            'password' => isset($_POST['password']) ? $_POST['password'] : '',
            'tipo_subscription' => isset($_POST['tipo_subscription']) ? sanitize_text_field($_POST['tipo_subscription']) : '',
            'credit' => isset($_POST['credit']) ? floatval($_POST['credit']) : null
        ];

        $result = $this->update_user_subscription($user_id, $data);
        
        if ($result['success']) {
            wp_send_json_success(['message' => $result['message']]);
        } else {
            wp_send_json_error(['message' => $result['message']]);
        }
    }

    /**
     * Delete a user subscription
     */
    public function delete_user_subscription($user_id) {
        global $wpdb;

        // Validate user_id
        if (empty($user_id)) {
            return ['success' => false, 'message' => __('User ID is required.', 'document-viewer-plugin')];
        }

        // Delete user
        $result = $wpdb->delete(
            $this->users_table,
            ['id' => $user_id],
            ['%d']
        );

        if ($result === false) {
            return ['success' => false, 'message' => __('Failed to delete user. Database error.', 'document-viewer-plugin')];
        }

        return ['success' => true, 'message' => __('User deleted successfully.', 'document-viewer-plugin')];
    }

    /**
     * AJAX handler for deleting user subscription
     */    public function delete_user_subscription_ajax() {
        // Check nonce
        // Verifica nonce - accettiamo sia il nonce unificato che quello precedente per retrocompatibilità
        $valid_nonce = false;
        if (isset($_POST['nonce'])) {
            $valid_nonce = wp_verify_nonce($_POST['nonce'], 'unified_access_nonce') || 
                          wp_verify_nonce($_POST['nonce'], 'document_viewer_nonce');
        }
        
        if (!$valid_nonce) {
            wp_send_json_error(['message' => __('Security check failed.', 'document-viewer-plugin')]);
        }

        // Check permissions using FA access control
        if (!fa_user_can_access('admin')) {
            wp_send_json_error(['message' => __('You do not have permission to perform this action.', 'document-viewer-plugin')]);
        }

        // Get and validate user_id
        $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
        if (empty($user_id)) {
            wp_send_json_error(['message' => __('User ID is required.', 'document-viewer-plugin')]);
        }

        $result = $this->delete_user_subscription($user_id);
        
        if ($result['success']) {
            wp_send_json_success(['message' => $result['message']]);
        } else {
            wp_send_json_error(['message' => $result['message']]);
        }
    }

    /**
     * Get all subscription types
     */
    public function get_subscription_types() {
        global $wpdb;
        return $wpdb->get_results("SELECT * FROM {$this->subscription_types_table} ORDER BY type_sub", ARRAY_A);
    }

    /**
     * AJAX handler for getting subscription types
     */    public function get_subscription_types_ajax() {
        // Check nonce
        // Verifica nonce - accettiamo sia il nonce unificato che quello precedente per retrocompatibilità
        $valid_nonce = false;
        if (isset($_POST['nonce'])) {
            $valid_nonce = wp_verify_nonce($_POST['nonce'], 'unified_access_nonce') || 
                          wp_verify_nonce($_POST['nonce'], 'document_viewer_nonce');
        }
        
        if (!$valid_nonce) {
            wp_send_json_error(['message' => __('Security check failed.', 'document-viewer-plugin')]);
        }

        // Check permissions using FA access control
        if (!fa_user_can_access('admin')) {
            wp_send_json_error(['message' => __('You do not have permission to perform this action.', 'document-viewer-plugin')]);
        }

        $types = $this->get_subscription_types();
        wp_send_json_success(['types' => $types]);
    }

    /**
     * Add a new subscription type
     */
    public function add_subscription_type($data) {
        global $wpdb;

        // Validate required fields
        if (empty($data['type_sub'])) {
            return ['success' => false, 'message' => __('Subscription type is required.', 'document-viewer-plugin')];
        }

        // Check if type already exists
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$this->subscription_types_table} WHERE type_sub = %s",
            $data['type_sub']
        ));

        if ($existing > 0) {
            return ['success' => false, 'message' => __('Subscription type already exists.', 'document-viewer-plugin')];
        }        // Insert type
        $result = $wpdb->insert(
            $this->subscription_types_table,
            [
                'type_sub' => $data['type_sub'],
                'link_redirect' => isset($data['link_redirect']) ? $data['link_redirect'] : '',
                'cost_per_token' => isset($data['cost_per_token']) ? $data['cost_per_token'] : 0.001
            ],
            ['%s', '%s', '%f']
        );

        if ($result === false) {
            return ['success' => false, 'message' => __('Failed to add subscription type. Database error.', 'document-viewer-plugin')];
        }

        return ['success' => true, 'message' => __('Subscription type added successfully.', 'document-viewer-plugin'), 'type_id' => $wpdb->insert_id];
    }

    /**
     * AJAX handler for adding subscription type
     */
    public function add_subscription_type_ajax() {
        // Check nonce
        // Verifica nonce - accettiamo sia il nonce unificato che quello precedente per retrocompatibilità
        $valid_nonce = false;
        if (isset($_POST['nonce'])) {
            $valid_nonce = wp_verify_nonce($_POST['nonce'], 'unified_access_nonce') || 
                          wp_verify_nonce($_POST['nonce'], 'document_viewer_nonce');
        }
        
        if (!$valid_nonce) {
            wp_send_json_error(['message' => __('Security check failed.', 'document-viewer-plugin')]);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have permission to perform this action.', 'document-viewer-plugin')]);
        }        // Get and sanitize data
        $data = [
            'type_sub' => isset($_POST['type_sub']) ? sanitize_text_field($_POST['type_sub']) : '',
            'link_redirect' => isset($_POST['link_redirect']) ? esc_url_raw($_POST['link_redirect']) : '',
            'cost_per_token' => isset($_POST['cost_per_token']) ? floatval($_POST['cost_per_token']) : 0.001
        ];

        $result = $this->add_subscription_type($data);
        
        if ($result['success']) {
            wp_send_json_success(['message' => $result['message'], 'type_id' => $result['type_id']]);
        } else {
            wp_send_json_error(['message' => $result['message']]);
        }
    }

    /**
     * Update a subscription type
     */
    public function update_subscription_type($type_id, $data) {
        global $wpdb;

        // Validate type_id
        if (empty($type_id)) {
            return ['success' => false, 'message' => __('Type ID is required.', 'document-viewer-plugin')];
        }

        // Prepare update data
        $update_data = [];
        $formats = [];

        // Only update fields that are provided
        if (!empty($data['type_sub'])) {
            // Check if type already exists for another entry
            $existing = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM {$this->subscription_types_table} WHERE type_sub = %s AND id != %d",
                $data['type_sub'], $type_id
            ));

            if ($existing > 0) {
                return ['success' => false, 'message' => __('Subscription type already exists.', 'document-viewer-plugin')];
            }

            $update_data['type_sub'] = $data['type_sub'];
            $formats[] = '%s';
        }        if (isset($data['link_redirect'])) { // Allow empty link_redirect
            $update_data['link_redirect'] = $data['link_redirect'];
            $formats[] = '%s';
        }
        
        if (isset($data['cost_per_token'])) {
            $update_data['cost_per_token'] = $data['cost_per_token'];
            $formats[] = '%f';
        }

        // If no data to update
        if (empty($update_data)) {
            return ['success' => false, 'message' => __('No data provided for update.', 'document-viewer-plugin')];
        }

        // Update type
        $result = $wpdb->update(
            $this->subscription_types_table,
            $update_data,
            ['id' => $type_id],
            $formats,
            ['%d']
        );

        if ($result === false) {
            return ['success' => false, 'message' => __('Failed to update subscription type. Database error.', 'document-viewer-plugin')];
        }

        return ['success' => true, 'message' => __('Subscription type updated successfully.', 'document-viewer-plugin')];
    }

    /**
     * AJAX handler for updating subscription type
     */    public function update_subscription_type_ajax() {
        // Check nonce
        // Verifica nonce - accettiamo sia il nonce unificato che quello precedente per retrocompatibilità
        $valid_nonce = false;
        if (isset($_POST['nonce'])) {
            $valid_nonce = wp_verify_nonce($_POST['nonce'], 'unified_access_nonce') || 
                          wp_verify_nonce($_POST['nonce'], 'document_viewer_nonce');
        }
        
        if (!$valid_nonce) {
            wp_send_json_error(['message' => __('Security check failed.', 'document-viewer-plugin')]);
        }

        // Check permissions using FA access control
        if (!fa_user_can_access('admin')) {
            wp_send_json_error(['message' => __('You do not have permission to perform this action.', 'document-viewer-plugin')]);
        }

        // Get and validate type_id
        $type_id = isset($_POST['type_id']) ? intval($_POST['type_id']) : 0;
        if (empty($type_id)) {
            wp_send_json_error(['message' => __('Type ID is required.', 'document-viewer-plugin')]);
        }        // Get and sanitize data
        $data = [
            'type_sub' => isset($_POST['type_sub']) ? sanitize_text_field($_POST['type_sub']) : '',
            'link_redirect' => isset($_POST['link_redirect']) ? esc_url_raw($_POST['link_redirect']) : '',
            'cost_per_token' => isset($_POST['cost_per_token']) ? floatval($_POST['cost_per_token']) : 0.001
        ];

        $result = $this->update_subscription_type($type_id, $data);
        
        if ($result['success']) {
            wp_send_json_success(['message' => $result['message']]);
        } else {
            wp_send_json_error(['message' => $result['message']]);
        }
    }

    /**
     * Delete a subscription type
     */
    public function delete_subscription_type($type_id) {
        global $wpdb;

        // Validate type_id
        if (empty($type_id)) {
            return ['success' => false, 'message' => __('Type ID is required.', 'document-viewer-plugin')];
        }

        // Check if this type is being used by any users
        $users_using = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$this->users_table} u 
            JOIN {$this->subscription_types_table} t ON u.tipo_subscription = t.type_sub 
            WHERE t.id = %d",
            $type_id
        ));

        if ($users_using > 0) {
            return ['success' => false, 'message' => __('Cannot delete this subscription type because it is being used by users.', 'document-viewer-plugin')];
        }

        // Delete type
        $result = $wpdb->delete(
            $this->subscription_types_table,
            ['id' => $type_id],
            ['%d']
        );

        if ($result === false) {
            return ['success' => false, 'message' => __('Failed to delete subscription type. Database error.', 'document-viewer-plugin')];
        }

        return ['success' => true, 'message' => __('Subscription type deleted successfully.', 'document-viewer-plugin')];
    }

    /**
     * AJAX handler for deleting subscription type
     */    public function delete_subscription_type_ajax() {
        // Check nonce
        // Verifica nonce - accettiamo sia il nonce unificato che quello precedente per retrocompatibilità
        $valid_nonce = false;
        if (isset($_POST['nonce'])) {
            $valid_nonce = wp_verify_nonce($_POST['nonce'], 'unified_access_nonce') || 
                          wp_verify_nonce($_POST['nonce'], 'document_viewer_nonce');
        }
        
        if (!$valid_nonce) {
            wp_send_json_error(['message' => __('Security check failed.', 'document-viewer-plugin')]);
        }

        // Check permissions using FA access control
        if (!fa_user_can_access('admin')) {
            wp_send_json_error(['message' => __('You do not have permission to perform this action.', 'document-viewer-plugin')]);
        }

        // Get and validate type_id
        $type_id = isset($_POST['type_id']) ? intval($_POST['type_id']) : 0;
        if (empty($type_id)) {
            wp_send_json_error(['message' => __('Type ID is required.', 'document-viewer-plugin')]);
        }

        $result = $this->delete_subscription_type($type_id);
        
        if ($result['success']) {
            wp_send_json_success(['message' => $result['message']]);
        } else {
            wp_send_json_error(['message' => $result['message']]);
        }
    }
}

// Initialize the User Subscription Manager
function dv_user_subscription_manager() {
    static $instance = null;
    if ($instance === null) {
        $instance = new User_Subscription_Manager();
    }
    return $instance;
}

// Create an instance
dv_user_subscription_manager();
