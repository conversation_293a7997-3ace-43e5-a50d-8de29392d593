# Widget Caricamento Excel & Riclassificazione

## Descrizione tecnica
Widget React/Vue per upload file Excel, parsing SheetJS, mapping conti su schema IV CEE, salvataggio e validazione.

---

## Fasi di sviluppo e test

### 1. Upload e parsing
- UI drag&drop, validazione formato, parsing SheetJS.
- Estrazione intestazioni e dati.

**Prompt sviluppo:**  
- Implementa upload e parsing file Excel.
- Scrivi test unitari su parsing e validazione formato.

**Test:**  
- Upload file, parsing colonne, errori formato.

---

### 2. UI mapping conti
- Componente drag&drop per mappatura colonne/conti.
- Salvataggio mapping utente.

**Prompt sviluppo:**  
- Crea UI mapping conti drag&drop.
- Gestisci salvataggio mapping.

**Test:**  
- Mapping manuale/automatico, correzione mapping.

---

### 3. Riclassificazione automatica
- Algoritmo mapping su schema IV CEE.
- Output dati riclassificati.

**Prompt sviluppo:**  
- Implementa algoritmo di riclassificazione automatica.

**Test:**  
- Output riclassificato, verifica correttezza.

---

### 4. Salvataggio e storico
- Salvataggio report riclassificato in DB.
- Visualizzazione storico importazioni.

**Prompt sviluppo:**  
- Salva report riclassificato e storico import.

**Test:**  
- Visualizzazione storico, ripristino import.

---

### 5. Validazione e gestione errori
- Validazione dati (tipi, duplicati, campi obbligatori).
- UI gestione errori e messaggi.

**Prompt sviluppo:**  
- Implementa validazione dati e gestione errori.

**Test:**  
- Errori simulati, messaggi utente.

---

## Checklist validazione fase
- Ogni fase deve essere testabile singolarmente (mock/demo).
- Test unitari e funzionali completati.
- Documentazione aggiornata.
