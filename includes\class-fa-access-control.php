<?php
/**
 * Financial Advisor - Access Control Class
 * 
 * Gestisce il controllo degli accessi alle funzionalità del plugin,
 * distinguendo tra accesso back-end (admin) e front-end (sottoscrittori)
 * 
 * @package Financial_Advisor
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class FA_Access_Control {
    /**
     * Istanza singleton
     * @var FA_Access_Control
     */
    private static $instance = null;
    
    /**
     * Constructor
     */
    private function __construct() {
        // Aggiungi i filter per il controllo degli accessi
        add_filter('fa_user_can_access', array($this, 'check_user_access'), 10, 2);
    }
    
    /**
     * Ottieni l'istanza singleton della classe
     * 
     * @return FA_Access_Control
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        
        return self::$instance;
    }

    /**
     * Verifica se l'utente corrente ha accesso a una determinata area o funzionalità
     *
     * @param bool $default_access Accesso predefinito (generalmente false)
     * @param string $area L'area o funzionalità a cui si sta tentando di accedere
     * @return bool True se l'utente può accedere, false altrimenti
     */
    public function check_user_access($default_access = false, $area = 'frontend') {
        // Amministratori hanno sempre accesso a tutto
        if (current_user_can('manage_options')) {
            return true;
        }
        
        // Controllo per le aree amministrative
        if ($area === 'admin') {
            return current_user_can('manage_options');
        }
        
        // Controllo per le aree frontend - MODIFICATO per garantire accesso a tutti gli utenti
        if ($area === 'frontend') {
            // Tutti gli utenti hanno accesso alle funzionalità frontend
            return true;
        }
        
        // Se siamo qui, l'utente non ha accesso
        return $default_access;
    }
    
    /**
     * Funzione di supporto per verificare se l'utente corrente è un sottoscrittore
     *
     * @return bool True se l'utente corrente è un sottoscrittore
     */
    public function is_current_user_subscriber() {
        if (!isset($_COOKIE['fa_subscriber_login'])) {
            return false;
        }
        
        try {
            $subscriber_data = json_decode(base64_decode($_COOKIE['fa_subscriber_login']), true);
            return isset($subscriber_data['id']) && !empty($subscriber_data['id']);
        } catch (Exception $e) {
            return false;
        }
    }
      /**
     * Ottieni i dati dell'utente sottoscrittore corrente
     *
     * @return array|null I dati dell'utente sottoscrittore o null se non è un sottoscrittore
     */
    public function get_current_subscriber_data() {
        if (!$this->is_current_user_subscriber()) {
            return null;
        }
        
        try {
            return json_decode(base64_decode($_COOKIE['fa_subscriber_login']), true);
        } catch (Exception $e) {
            return null;
        }
    }
    
    /**
     * Pulisci i cookie di autenticazione del sottoscrittore
     * 
     * @return boolean True se l'operazione è andata a buon fine
     */
    public function clear_subscriber_cookies() {
        if (isset($_COOKIE['fa_subscriber_login'])) {
            // Elimina il cookie impostandone la data di scadenza nel passato
            $past = time() - 3600;
            $secure = is_ssl();
            $http_only = true;
            
            // Ottieni il dominio per impostare correttamente il cookie
            $parsed_url = parse_url(get_site_url());
            $domain = isset($parsed_url['host']) ? $parsed_url['host'] : '';
            
            // Per garantire che il cookie venga eliminato, proviamo sia con dominio che senza
            setcookie('fa_subscriber_login', '', $past, '/', $domain, $secure, $http_only);
            setcookie('fa_subscriber_login', '', $past, '/', '', $secure, $http_only);
            
            if (isset($_COOKIE['fa_subscriber_data'])) {
                setcookie('fa_subscriber_data', '', $past, '/', $domain, $secure, $http_only);
                setcookie('fa_subscriber_data', '', $past, '/', '', $secure, $http_only);
            }
            
            // Registra il logout nel log di debug
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Cookie di autenticazione sottoscrittore eliminati');
            }
            
            return true;
        }
        
        return false;
    }
}

/**
 * Funzione per accedere all'istanza del Access Control
 * 
 * @return FA_Access_Control
 */
function fa_access_control() {
    return FA_Access_Control::get_instance();
}

// Inizializza il controllo degli accessi
fa_access_control();
