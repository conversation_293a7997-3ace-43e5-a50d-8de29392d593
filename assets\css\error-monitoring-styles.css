/* 
 * Advanced Error Monitoring & Payment Gateway Checker Styles
 * Integrated CSS for debugging tools and monitoring interfaces
 */

/* Debug Panel Animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Advanced Debug Panel */
#advanced-debug-panel {
    animation: slideInRight 0.3s ease-out;
    backdrop-filter: blur(10px);
    border: 1px solid #444;
}

#advanced-debug-panel button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    transition: all 0.2s ease;
}

#advanced-debug-panel button:active {
    transform: translateY(0);
}

/* Error Severity Indicators */
.error-severity-high {
    border-left-color: #f44336 !important;
    background: rgba(244, 67, 54, 0.1);
}

.error-severity-medium {
    border-left-color: #ff9800 !important;
    background: rgba(255, 152, 0, 0.1);
}

.error-severity-low {
    border-left-color: #2196f3 !important;
    background: rgba(33, 150, 243, 0.1);
}

/* Credit Update Animation */
.credit-updating {
    animation: pulse 0.6s ease-in-out;
    color: #4CAF50 !important;
    font-weight: bold !important;
}

/* Fast Modal Overlay */
.fast-modal-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.6) !important;
    z-index: 10000 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    animation: fadeIn 0.2s ease-out;
}

.fast-modal-content {
    background: white !important;
    padding: 30px !important;
    border-radius: 12px !important;
    max-width: 450px !important;
    text-align: center !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
    animation: slideInRight 0.3s ease-out;
    position: relative;
}

.fast-modal-message {
    font-size: 16px !important;
    margin-bottom: 25px !important;
    color: #333 !important;
    line-height: 1.5;
}

.fast-modal-buttons {
    margin-top: 25px !important;
    display: flex !important;
    gap: 15px !important;
    justify-content: center !important;
}

.fast-btn {
    padding: 12px 24px !important;
    margin: 0 !important;
    border: none !important;
    border-radius: 6px !important;
    cursor: pointer !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    min-width: 100px !important;
    transition: all 0.2s ease !important;
}

.fast-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

.fast-btn:active {
    transform: translateY(0) !important;
}

.fast-btn-confirm {
    background: linear-gradient(135deg, #4CAF50, #45a049) !important;
    color: white !important;
}

.fast-btn-confirm:hover {
    background: linear-gradient(135deg, #45a049, #3d8b40) !important;
}

.fast-btn-cancel {
    background: linear-gradient(135deg, #f44336, #d32f2f) !important;
    color: white !important;
}

.fast-btn-cancel:hover {
    background: linear-gradient(135deg, #d32f2f, #b71c1c) !important;
}

/* Feedback Message Enhanced Styles */
#subscriber-feedback-message {
    position: fixed !important;
    top: 20px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    z-index: 9999 !important;
    padding: 15px 25px !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    animation: slideInRight 0.3s ease-out;
    max-width: 90% !important;
    text-align: center !important;
}

#subscriber-feedback-message.success {
    background: linear-gradient(135deg, #4CAF50, #45a049) !important;
    color: white !important;
    border-left: 4px solid #2E7D32 !important;
}

#subscriber-feedback-message.error {
    background: linear-gradient(135deg, #f44336, #d32f2f) !important;
    color: white !important;
    border-left: 4px solid #C62828 !important;
}

#subscriber-feedback-message.warning {
    background: linear-gradient(135deg, #ff9800, #f57c00) !important;
    color: white !important;
    border-left: 4px solid #E65100 !important;
}

/* Console Commands Helper */
.console-commands-helper {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: #263238;
    color: #B0BEC5;
    padding: 15px;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    max-width: 300px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 9998;
    transition: all 0.3s ease;
    opacity: 0.8;
}

.console-commands-helper:hover {
    opacity: 1;
    transform: scale(1.02);
}

.console-commands-helper h4 {
    margin: 0 0 10px 0;
    color: #4CAF50;
    font-size: 14px;
}

.console-commands-helper ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.console-commands-helper li {
    margin: 5px 0;
    padding: 3px 0;
    border-bottom: 1px solid #37474F;
}

.console-commands-helper code {
    color: #81C784;
    background: #37474F;
    padding: 2px 4px;
    border-radius: 3px;
}

/* Loading States */
.processing {
    position: relative;
    pointer-events: none;
}

.processing::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.7);
    border-radius: inherit;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Error Simulation Styles */
.error-simulation-active {
    border: 2px dashed #f44336 !important;
    background: rgba(244, 67, 54, 0.1) !important;
    animation: pulse 2s infinite;
}

/* Gateway Status Indicators */
.gateway-status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
    position: relative;
}

.gateway-status-indicator.online {
    background: #4CAF50;
    box-shadow: 0 0 6px rgba(76, 175, 80, 0.6);
}

.gateway-status-indicator.offline {
    background: #f44336;
    box-shadow: 0 0 6px rgba(244, 67, 54, 0.6);
}

.gateway-status-indicator.warning {
    background: #ff9800;
    box-shadow: 0 0 6px rgba(255, 152, 0, 0.6);
}

.gateway-status-indicator.online::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px solid #4CAF50;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    #advanced-debug-panel {
        width: calc(100% - 40px) !important;
        left: 20px !important;
        right: 20px !important;
        top: 10px !important;
    }
    
    .fast-modal-content {
        margin: 20px !important;
        max-width: calc(100% - 40px) !important;
    }
    
    .console-commands-helper {
        display: none;
    }
    
    #subscriber-feedback-message {
        left: 10px !important;
        right: 10px !important;
        transform: none !important;
        max-width: calc(100% - 20px) !important;
    }
}

/* Print Styles */
@media print {
    #advanced-debug-panel,
    .fast-modal-overlay,
    .console-commands-helper {
        display: none !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    #advanced-debug-panel {
        border: 2px solid #fff !important;
        background: #000 !important;
    }
    
    .fast-modal-content {
        border: 2px solid #000 !important;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .fast-modal-content {
        background: #2e2e2e !important;
        color: #fff !important;
    }
    
    .fast-modal-message {
        color: #fff !important;
    }
}
