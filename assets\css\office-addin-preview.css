/**
 * Office Add-in Preview CSS
 *
 * Stili per la funzionalità di anteprima live dell'Office Add-in
 * nel pannello di amministrazione WordPress.
 */

/* Contenitore principale dell'anteprima */
.addin-preview-container {
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
    margin: 20px 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Loading indicator */
.addin-preview-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 100;
    text-align: center;
}

.addin-preview-loading .spinner {
    margin: 0 auto 10px;
    float: none;
}

.addin-preview-loading p {
    margin: 0;
    color: #555;
    font-size: 14px;
}

/* Intestazione dell'anteprima */
.addin-preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #ddd;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

.addin-preview-title {
    font-weight: bold;
    color: #333;
}

.addin-preview-controls {
    display: flex;
    gap: 5px;
}

/* Contenitore dell'iframe */
.addin-preview-frame-container {
    height: 800px;
    width: 400px;
    overflow: hidden;
    position: relative;
    background-color: #fff;
    transition: all 0.3s ease;
    margin: 0 auto;
    border: 1px solid #ddd;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    /* Simula l'aspetto di un task pane di Excel */
    border-radius: 2px;
    box-sizing: border-box;
}

/* Iframe di anteprima */
#addin-preview-frame {
    width: 100%;
    height: 100%;
    border: none;
    background-color: #fff;
}

/* Informazioni sull'anteprima */
.addin-preview-info {
    margin-top: 20px;
    padding: 15px;
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.addin-preview-info h3 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #333;
}

.addin-preview-info ul {
    margin-left: 20px;
}

.addin-preview-info li {
    margin-bottom: 5px;
}

/* Stili per i pulsanti di controllo */
.addin-preview-controls .button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    width: 30px;
    height: 30px;
}

.addin-preview-controls .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Stili per la modalità di anteprima grande */
.addin-preview-large .addin-preview-frame-container {
    height: 900px;
    width: 450px;
}

/* Stili per le schede */
.office-addin-tabs .nav-tab-wrapper {
    margin-bottom: 0;
    border-bottom: 0;
}

.office-addin-tabs .tab-content {
    display: none;
    padding: 20px;
    background-color: #fff;
    border: 1px solid #ccc;
    border-top: none;
}

.office-addin-tabs .tab-content.active {
    display: block;
}

/* Stili per l'indicatore di caricamento */
.addin-preview-frame-container:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.7);
    z-index: 1;
    display: none;
}

.addin-preview-frame-container.loading:before {
    display: block;
}

.addin-preview-frame-container.loading:after {
    content: "Caricamento anteprima...";
    position: absolute;
    top: 35%;
    left: 65%;
    transform: translate(-35%, -65%);
    z-index: 2;
    font-weight: bold;
    color: #333;
}

/* Stili per il pulsante di anteprima nella tab dei contenuti */
#preview-changes {
    margin-left: 10px;
}

/* Stili per la visualizzazione responsive */
@media screen and (max-width: 782px) {
    .addin-preview-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .addin-preview-controls {
        margin-top: 10px;
    }

    .addin-preview-frame-container {
        height: 700px;
        width: 380px;
        max-width: 100%;
    }
}

/* Stili per il tema di WordPress */
.wp-admin .addin-preview-container {
    max-width: 100%;
}

/* Stili per il tema scuro di WordPress */
.wp-admin.admin-color-midnight .addin-preview-header,
.wp-admin.admin-color-ocean .addin-preview-header,
.wp-admin.admin-color-sunrise .addin-preview-header,
.wp-admin.admin-color-ectoplasm .addin-preview-header {
    background-color: #32373c;
    border-color: #23282d;
}

.wp-admin.admin-color-midnight .addin-preview-title,
.wp-admin.admin-color-ocean .addin-preview-title,
.wp-admin.admin-color-sunrise .addin-preview-title,
.wp-admin.admin-color-ectoplasm .addin-preview-title {
    color: #f1f1f1;
}

.wp-admin.admin-color-midnight .addin-preview-container,
.wp-admin.admin-color-ocean .addin-preview-container,
.wp-admin.admin-color-sunrise .addin-preview-container,
.wp-admin.admin-color-ectoplasm .addin-preview-container {
    border-color: #23282d;
}

/* Stili per l'indicatore di caricamento */
.loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background-color: rgba(255, 255, 255, 0.9);
    z-index: 10;
}

.loading-spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top: 4px solid #3498db;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin-bottom: 10px;
}

.loading-text {
    color: #333;
    font-size: 14px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Modalità full-height per utilizzare l'intera altezza dello schermo */
.addin-preview-fullheight .addin-preview-frame-container {
    height: calc(100vh - 200px);
    min-height: 800px;
    max-height: 1200px;
}

/* Miglioramenti per simulare meglio l'ambiente Excel */
.addin-preview-frame-container::before {
    content: "";
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    height: 3px;
    background: linear-gradient(to right, #0078d4, #106ebe);
    z-index: 10;
    border-radius: 2px 2px 0 0;
}

/* Indicatore del tipo di task pane */
.addin-preview-header::after {
    content: "Task Pane Simulation";
    position: absolute;
    top: 5px;
    right: 50px;
    font-size: 10px;
    color: #666;
    background: #f0f0f0;
    padding: 2px 6px;
    border-radius: 3px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Miglioramento dei controlli */
.addin-preview-controls .button:hover {
    background-color: #f0f0f0;
    border-color: #0078d4;
}

.addin-preview-controls .button:active {
    background-color: #e0e0e0;
}

/* Excel-like Grid Styles */
.excel-grid-container {
    margin: 15px 0;
    border: 1px solid #d0d0d0;
    border-radius: 4px;
    background: #fff;
}

.excel-grid-header {
    background: #f8f9fa;
    padding: 10px 15px;
    border-bottom: 1px solid #d0d0d0;
    font-weight: 600;
    color: #333;
}

.excel-grid-controls {
    padding: 10px 15px;
    background: #f8f9fa;
    border-bottom: 1px solid #d0d0d0;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.grid-button {
    background: #0078d4;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.2s;
}

.grid-button:hover {
    background: #106ebe;
}

.grid-button:active {
    background: #005a9e;
}

.excel-grid-wrapper {
    max-height: 400px;
    overflow: auto;
    border-bottom: 1px solid #d0d0d0;
}

.excel-grid {
    width: 100%;
    border-collapse: collapse;
    font-family: 'Segoe UI', Arial, sans-serif;
    font-size: 12px;
}

.excel-grid th,
.excel-grid td {
    border: 1px solid #d0d0d0;
    padding: 0;
    margin: 0;
    text-align: center;
    vertical-align: middle;
}

.excel-grid th.row-header,
.excel-grid td.row-header {
    background: #f8f9fa;
    color: #666;
    font-weight: 600;
    width: 40px;
    min-width: 40px;
    max-width: 40px;
    font-size: 11px;
    user-select: none;
}

.excel-grid th.col-header {
    background: #f8f9fa;
    color: #666;
    font-weight: 600;
    height: 25px;
    font-size: 11px;
    user-select: none;
    width: 80px;
    min-width: 80px;
}

.excel-grid .grid-cell {
    padding: 0;
    position: relative;
    width: 80px;
    min-width: 80px;
    height: 22px;
}

.excel-grid .grid-cell.selected {
    background: #cce7ff !important;
    border: 2px solid #0078d4 !important;
}

.excel-grid .cell-input {
    width: 100%;
    height: 100%;
    border: none;
    outline: none;
    padding: 2px 4px;
    font-family: inherit;
    font-size: inherit;
    background: transparent;
    box-sizing: border-box;
}

.excel-grid .cell-input:focus {
    background: #fff;
    border: 2px solid #0078d4;
    z-index: 10;
    position: relative;
}

.grid-selection-info {
    padding: 8px 15px;
    background: #f8f9fa;
    font-size: 11px;
    color: #666;
    border-top: 1px solid #e0e0e0;
}

/* Hover effects */
.excel-grid tbody tr:hover .grid-cell:not(.selected) {
    background: #f5f5f5;
}

.excel-grid .grid-cell:hover:not(.selected) {
    background: #f0f0f0;
}

/* Selection range styles */
.excel-grid .grid-cell.range-start {
    border-top-left-radius: 3px;
}

.excel-grid .grid-cell.range-end {
    border-bottom-right-radius: 3px;
}

/* Copy/paste feedback */
.excel-grid .grid-cell.copied {
    border: 2px dashed #28a745 !important;
    background: #d4edda !important;
}

/* Responsive adjustments for grid */
@media (max-width: 600px) {
    .excel-grid-controls {
        flex-direction: column;
    }

    .grid-button {
        width: 100%;
        margin-bottom: 5px;
    }

    .excel-grid th.col-header,
    .excel-grid .grid-cell {
        width: 60px;
        min-width: 60px;
    }
}

/* Side-by-Side Layout Container */
.preview-layout-container {
    display: flex;
    gap: 20px;
    margin: 20px 0;
    min-height: 800px;
}

.excel-grid-side {
    flex: 1;
    min-width: 0; /* Allows flex item to shrink below content size */
}

.addin-panel-side {
    flex: 1;
    min-width: 0;
}

/* External Excel Grid Container (Left Side) */
.excel-grid-external-container {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    height: 100%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.excel-grid-external-container h3 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #2c3e50;
    font-size: 18px;
    border-bottom: 2px solid #3498db;
    padding-bottom: 8px;
}

.excel-grid-external-container p {
    margin-bottom: 15px;
    color: #666;
    font-size: 14px;
}

#external-excel-grid-container {
    margin: 15px 0;
    border: 2px solid #3498db;
    border-radius: 6px;
    background: #f8f9fa;
    overflow: hidden;
}

/* Grid Actions Section */
.grid-actions {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
}

.grid-actions .button {
    margin-right: 10px;
    margin-bottom: 10px;
}

.grid-actions .button-primary {
    background: #28a745;
    border-color: #28a745;
    font-weight: 600;
}

.grid-actions .button-primary:hover {
    background: #218838;
    border-color: #1e7e34;
}

.grid-actions .button-secondary {
    background: #6c757d;
    border-color: #6c757d;
    color: white;
    font-weight: 600;
}

.grid-actions .button-secondary:hover {
    background: #5a6268;
    border-color: #545b62;
    color: white;
}

/* Extraction Results */
.extraction-results {
    margin-top: 15px;
    padding: 15px;
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    border-left: 4px solid #28a745;
}

.extraction-results h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #28a745;
    font-size: 14px;
    font-weight: 600;
}

.extracted-text-display {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 12px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.5;
    max-height: 200px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* Enhanced Grid Styles for External Container */
.excel-grid-external-container .excel-grid-container {
    border: none;
    border-radius: 0;
    margin: 0;
}

.excel-grid-external-container .excel-grid-header {
    background: #3498db;
    color: white;
    font-weight: 600;
    text-align: center;
}

.excel-grid-external-container .excel-grid-controls {
    background: #e9ecef;
    border-bottom: 2px solid #3498db;
}

.excel-grid-external-container .grid-button {
    background: #3498db;
    font-weight: 600;
}

.excel-grid-external-container .grid-button:hover {
    background: #2980b9;
}

/* Selection Enhancement */
.excel-grid-external-container .excel-grid .grid-cell.selected {
    background: #007bff !important;
    border: 2px solid #0056b3 !important;
    box-shadow: 0 0 0 1px #80bdff;
}

.excel-grid-external-container .excel-grid .grid-cell.selected .cell-input {
    background: rgba(255, 255, 255, 0.9);
    color: #000;
    font-weight: 600;
}

/* Grid Selection Info Enhancement */
.excel-grid-external-container .grid-selection-info {
    background: #3498db;
    color: white;
    font-weight: 600;
    text-align: center;
    padding: 8px 15px;
}

/* Responsive Design for Side-by-Side Layout */
@media (max-width: 1200px) {
    .preview-layout-container {
        flex-direction: column;
        gap: 15px;
    }

    .excel-grid-side,
    .addin-panel-side {
        flex: none;
    }

    .addin-preview-frame-container {
        width: 100%;
        max-width: 500px;
        margin: 0 auto;
    }
}

@media (max-width: 768px) {
    .excel-grid-external-container {
        padding: 15px;
    }

    .grid-actions .button {
        width: 100%;
        margin-right: 0;
        margin-bottom: 10px;
    }

    .extracted-text-display {
        font-size: 11px;
    }

    .addin-preview-frame-container {
        height: 600px;
        width: 100%;
        max-width: 400px;
    }
}
