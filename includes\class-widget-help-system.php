<?php

if (!defined('ABSPATH')) {
    exit;
}

class Widget_Help_System {
    
    private $registered_widgets = array();
    private $help_contents = array();
    private $admin;
    
    public function __construct() {
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_ajax_get_widget_help', array($this, 'ajax_get_widget_help'));
        add_action('wp_ajax_nopriv_get_widget_help', array($this, 'ajax_get_widget_help'));
        
        // Controlla se esiste Widget_Help_Manager (nuovo sistema)
        if (class_exists('Widget_Help_Manager')) {
            // Delega al nuovo sistema
            $this->register_default_widgets_via_manager();
        } else {
            // Sistema legacy
            $this->register_default_widgets();
        }
        
        $this->load_saved_content();
        
        // L'admin interface viene gestita dal Menu Manager centralizzato
        // Non inizializzarla qui per evitare conflitti
    }
    
    public function register_widget($widget_id, $config) {
        $default_config = array(
            'name' => '',
            'description' => '',
            'default_content' => '',
            'triggers' => array(),
            'position' => 'right',
            'enabled' => true,
            'auto_show' => false
        );
        
        $this->registered_widgets[$widget_id] = wp_parse_args($config, $default_config);
    }
    
    public function get_widget_config($widget_id) {
        return isset($this->registered_widgets[$widget_id]) ? $this->registered_widgets[$widget_id] : null;
    }
    
    public function get_all_widgets() {
        return $this->registered_widgets;
    }
    
    public function ajax_get_widget_help() {
        check_ajax_referer('widget_help_nonce', 'nonce');
        
        $widget_id = sanitize_text_field($_POST['widget_id']);
        $section = sanitize_text_field($_POST['section'] ?? 'main');
        
        $config = $this->get_widget_config($widget_id);
        if (!$config) {
            wp_die('Widget not found');
        }
        
        $content = $this->get_help_content($widget_id, $section);
        
        wp_send_json_success(array(
            'content' => $content,
            'widget_name' => $config['name'],
            'position' => $config['position']
        ));
    }
    
    public function get_help_content($widget_id, $section = 'main') {
        if (isset($this->help_contents[$widget_id][$section])) {
            return $this->help_contents[$widget_id][$section];
        }
        
        $config = $this->get_widget_config($widget_id);
        return $config ? $config['default_content'] : '';
    }
    
    public function set_help_content($widget_id, $content, $section = 'main') {
        if (!isset($this->help_contents[$widget_id])) {
            $this->help_contents[$widget_id] = array();
        }
        $this->help_contents[$widget_id][$section] = $content;
    }
    
    public function enqueue_scripts() {
        wp_enqueue_script(
            'widget-help-system',
            plugin_dir_url(__FILE__) . '../assets/js/widget-help-system.js',
            array('jquery'),
            '1.0.0',
            true
        );
        
        wp_enqueue_style(
            'widget-help-system',
            plugin_dir_url(__FILE__) . '../assets/css/widget-help-system.css',
            array(),
            '1.0.0'
        );
        
        wp_localize_script('widget-help-system', 'widgetHelpAjax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('widget_help_nonce'),
            'widgets' => $this->get_all_widgets()
        ));
    }
    
    private function register_default_widgets() {
        // Report Viewer Widget
        $this->register_widget('report_viewer', array(
            'name' => __('Report Viewer', 'financial-advisor-v4'),
            'description' => __('Widget per visualizzazione e gestione report finanziari', 'financial-advisor-v4'),
            'default_content' => $this->get_report_viewer_default_help(),
            'triggers' => array('.report-viewer-widget', '[data-widget="report-viewer"]'),
            'position' => 'right',
            'enabled' => true
        ));
        
        // Document Analyzer Widget
        $this->register_widget('document_analyzer', array(
            'name' => __('Document Analyzer', 'financial-advisor-v4'),
            'description' => __('Widget per l\'analisi automatica di documenti finanziari', 'financial-advisor-v4'),
            'default_content' => $this->get_document_analyzer_default_help(),
            'triggers' => array('.document-analyzer-widget', '[data-widget="document-analyzer"]'),
            'position' => 'right',
            'enabled' => true
        ));
        
        // Financial Academy Widget
        $this->register_widget('financial_academy', array(
            'name' => __('Financial Academy', 'financial-advisor-v4'),
            'description' => __('Widget per la gestione delle domande e risposte educative', 'financial-advisor-v4'),
            'default_content' => $this->get_financial_academy_default_help(),
            'triggers' => array('.financial-academy-widget', '[data-widget="financial-academy"]'),
            'position' => 'left',
            'enabled' => true
        ));
        
        // Payment Gateway Widget
        $this->register_widget('payment_gateway', array(
            'name' => __('Payment Gateway', 'financial-advisor-v4'),
            'description' => __('Widget per la gestione dei pagamenti e abbonamenti', 'financial-advisor-v4'),
            'default_content' => $this->get_payment_gateway_default_help(),
            'triggers' => array('.payment-gateway-widget', '[data-widget="payment-gateway"]'),
            'position' => 'center',
            'enabled' => true
        ));
    }
    
    private function register_default_widgets_via_manager() {
        // Delega la registrazione dei widget al nuovo Widget_Help_Manager
        // Questo evita conflitti e mantiene compatibilità
        if (class_exists('Widget_Help_Manager')) {
            $manager = Widget_Help_Manager::get_instance();
            
            // Copia i widget registrati dal manager al sistema legacy per compatibilità
            $manager_widgets = $manager->get_registered_widgets();
            foreach ($manager_widgets as $widget_id => $config) {
                $this->registered_widgets[$widget_id] = $config;
            }
        }
    }
    
    private function get_report_viewer_default_help() {
        return '
        <div class="widget-help-content">
            <h3>Report Viewer - Guida Rapida</h3>
            <div class="help-sections">
                <div class="help-section" data-section="list">
                    <h4>📋 Gestione Lista Report</h4>
                    <ul>
                        <li><strong>Ricerca:</strong> Usa la barra di ricerca per trovare report specifici</li>
                        <li><strong>Filtri:</strong> Filtra per azienda, periodo o tag</li>
                        <li><strong>Ordinamento:</strong> Clicca sulle intestazioni per ordinare</li>
                    </ul>
                </div>
                
                <div class="help-section" data-section="detail">
                    <h4>📊 Visualizzazione Dettaglio</h4>
                    <ul>
                        <li><strong>Temi:</strong> Switcha tra tema chiaro e scuro</li>
                        <li><strong>Modifica:</strong> Clicca su modifica per aggiornare i dati</li>
                        <li><strong>Condivisione:</strong> Usa i pulsanti di condivisione social</li>
                    </ul>
                </div>
                
                <div class="help-section" data-section="export">
                    <h4>📥 Export e Download</h4>
                    <ul>
                        <li><strong>PDF:</strong> Scarica report in formato PDF</li>
                        <li><strong>Excel:</strong> Esporta dati in foglio di calcolo</li>
                        <li><strong>Formati:</strong> Scegli tra diversi template di export</li>
                    </ul>
                </div>
                
                <div class="help-section" data-section="versions">
                    <h4>🕐 Gestione Versioni</h4>
                    <ul>
                        <li><strong>Cronologia:</strong> Visualizza tutte le versioni precedenti</li>
                        <li><strong>Ripristino:</strong> Ripristina una versione specifica</li>
                        <li><strong>Confronto:</strong> Confronta differenze tra versioni</li>
                    </ul>
                </div>
                
                <div class="help-section" data-section="tags">
                    <h4>🏷️ Organizzazione Tags</h4>
                    <ul>
                        <li><strong>Aggiunta:</strong> Aggiungi tag per categorizzare i report</li>
                        <li><strong>Ricerca:</strong> Cerca report per tag specifici</li>
                        <li><strong>Gestione:</strong> Modifica o elimina tag esistenti</li>
                    </ul>
                </div>
            </div>
        </div>';
    }
    
    private function get_document_analyzer_default_help() {
        return '
        <div class="widget-help-content">
            <h3>Document Analyzer - Guida Rapida</h3>
            <div class="help-sections">
                <div class="help-section" data-section="upload">
                    <h4>📁 Caricamento Documenti</h4>
                    <ul>
                        <li><strong>Formati supportati:</strong> PDF, DOC, DOCX, XLS, XLSX</li>
                        <li><strong>Dimensione massima:</strong> 10MB per file</li>
                        <li><strong>Drag & Drop:</strong> Trascina i file direttamente nell\'area di caricamento</li>
                    </ul>
                </div>
                
                <div class="help-section" data-section="analysis">
                    <h4>🔍 Analisi Automatica</h4>
                    <ul>
                        <li><strong>AI Analysis:</strong> Analisi automatica con intelligenza artificiale</li>
                        <li><strong>Extraction:</strong> Estrazione di dati chiave e metriche</li>
                        <li><strong>Validation:</strong> Verifica della coerenza dei dati</li>
                    </ul>
                </div>
                
                <div class="help-section" data-section="results">
                    <h4>📊 Risultati e Report</h4>
                    <ul>
                        <li><strong>Summary:</strong> Riassunto esecutivo dell\'analisi</li>
                        <li><strong>Charts:</strong> Grafici e visualizzazioni interattive</li>
                        <li><strong>Export:</strong> Esportazione in PDF o Excel</li>
                    </ul>
                </div>
            </div>
        </div>';
    }
    
    private function get_financial_academy_default_help() {
        return '
        <div class="widget-help-content">
            <h3>Financial Academy - Guida Rapida</h3>
            <div class="help-sections">
                <div class="help-section" data-section="questions">
                    <h4>❓ Gestione Domande</h4>
                    <ul>
                        <li><strong>Creazione:</strong> Aggiungi nuove domande educative</li>
                        <li><strong>Categorie:</strong> Organizza per argomenti finanziari</li>
                        <li><strong>Difficoltà:</strong> Imposta livelli di complessità</li>
                    </ul>
                </div>
                
                <div class="help-section" data-section="learning">
                    <h4>📚 Percorsi di Apprendimento</h4>
                    <ul>
                        <li><strong>Moduli:</strong> Crea percorsi strutturati</li>
                        <li><strong>Progress:</strong> Traccia i progressi degli utenti</li>
                        <li><strong>Certification:</strong> Rilascia certificati di completamento</li>
                    </ul>
                </div>
                
                <div class="help-section" data-section="analytics">
                    <h4>📈 Analytics e Reportistica</h4>
                    <ul>
                        <li><strong>Performance:</strong> Monitora le performance degli utenti</li>
                        <li><strong>Engagement:</strong> Analizza l\'engagement sui contenuti</li>
                        <li><strong>Feedback:</strong> Raccogli feedback e valutazioni</li>
                    </ul>
                </div>
            </div>
        </div>';
    }
    
    private function get_payment_gateway_default_help() {
        return '
        <div class="widget-help-content">
            <h3>Payment Gateway - Guida Rapida</h3>
            <div class="help-sections">
                <div class="help-section" data-section="setup">
                    <h4>⚙️ Configurazione Gateway</h4>
                    <ul>
                        <li><strong>Provider:</strong> Configura Stripe, PayPal, o altri provider</li>
                        <li><strong>API Keys:</strong> Inserisci le chiavi API in modo sicuro</li>
                        <li><strong>Webhooks:</strong> Configura gli endpoint per le notifiche</li>
                    </ul>
                </div>
                
                <div class="help-section" data-section="subscriptions">
                    <h4>💳 Gestione Abbonamenti</h4>
                    <ul>
                        <li><strong>Plans:</strong> Crea e gestisci piani di abbonamento</li>
                        <li><strong>Billing:</strong> Automatizza la fatturazione ricorrente</li>
                        <li><strong>Upgrades:</strong> Gestisci upgrade e downgrade di piani</li>
                    </ul>
                </div>
                
                <div class="help-section" data-section="monitoring">
                    <h4>📊 Monitoraggio Transazioni</h4>
                    <ul>
                        <li><strong>Dashboard:</strong> Visualizza transazioni in tempo reale</li>
                        <li><strong>Failed Payments:</strong> Gestisci pagamenti falliti</li>
                        <li><strong>Refunds:</strong> Processa rimborsi e dispute</li>
                    </ul>
                </div>
            </div>
        </div>';
    }
    
    private function load_saved_content() {
        // Verifica e migra le chiavi di opzione se necessario
        // Prima controlla se esistono dati nella vecchia chiave
        $legacy_content = get_option('widget_help_contents', array());
        $current_content = get_option('widget_help_content', array());
        
        // Se abbiamo dati legacy ma non dati correnti, migra
        if (!empty($legacy_content) && empty($current_content)) {
            update_option('widget_help_content', $legacy_content);
            $this->help_contents = $legacy_content;
        } else {
            // Usa i dati correnti
            $this->help_contents = $current_content;
        }
        
        // Esegui la migrazione completa se la classe è disponibile
        if (class_exists('Widget_Help_Migration')) {
            Widget_Help_Migration::migrate_option_keys();
        }
        
        // Load widget settings
        $widget_settings = get_option('widget_help_widget_settings', array());
        foreach ($widget_settings as $widget_id => $settings) {
            if (isset($this->registered_widgets[$widget_id])) {
                $this->registered_widgets[$widget_id] = array_merge(
                    $this->registered_widgets[$widget_id],
                    $settings
                );
            }
        }
    }
    
    public function get_admin_instance() {
        // L'admin viene gestito dal Menu Manager centralizzato
        // Ritorna null se non è stato inizializzato
        return $this->admin;
    }
}

// Initialize the Widget Help System
global $widget_help_system;
$widget_help_system = new Widget_Help_System();

/**
 * Helper function to get the Widget Help System instance
 * @return Widget_Help_System|null
 */
function widget_help_system() {
    global $widget_help_system;
    return $widget_help_system;
}
