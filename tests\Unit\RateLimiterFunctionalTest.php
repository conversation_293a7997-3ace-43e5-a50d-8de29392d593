<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;

/**
 * Comprehensive Rate Limiter Tests
 * 
 * Tests all functionality of the Rate Limiter without Brain Monkey conflicts
 */
class RateLimiterFunctionalTest extends TestCase
{
    private $rate_limiter;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // Reset superglobals
        $_SERVER['REMOTE_ADDR'] = '*************';
        $_SERVER['HTTP_X_FORWARDED_FOR'] = '';
        $_SERVER['HTTP_X_REAL_IP'] = '';
        
        // Clear test cache
        \TestCache::clear();
        \TestOptions::clear();
        
        // Require the Rate Limiter class
        require_once dirname(dirname(__DIR__)) . '/includes/class-office-addin-rate-limiter.php';
        
        // Create new instance for each test
        $this->rate_limiter = new \Office_Addin_Rate_Limiter();
    }
    
    protected function tearDown(): void
    {
        // Clean up
        \TestCache::clear();
        \TestOptions::clear();
        $this->rate_limiter = null;
        parent::tearDown();
    }
    
    public function testConstructorSetsUpRateLimiter()
    {
        $this->assertInstanceOf(\Office_Addin_Rate_Limiter::class, $this->rate_limiter);
    }
    
    public function testIsRequestAllowedReturnsTrueForNewClient()
    {
        $result = $this->rate_limiter->is_request_allowed('test_action');
        
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('remaining', $result);
        $this->assertArrayHasKey('reset_time', $result);
    }
    
    public function testGetRateLimitStatusReturnsCorrectData()
    {
        // Make a request first
        $this->rate_limiter->is_request_allowed('test_action');
        
        $status = $this->rate_limiter->get_rate_limit_status('test_action');
        
        $this->assertIsArray($status);
        $this->assertArrayHasKey('requests_made', $status);
        $this->assertArrayHasKey('limit', $status);
        $this->assertArrayHasKey('remaining', $status);
        $this->assertArrayHasKey('reset_time', $status);
    }
    
    public function testRateLimitingWorksWithMultipleRequests()
    {
        $action = 'test_action';
        
        // First request should be allowed
        $result1 = $this->rate_limiter->is_request_allowed($action);
        $this->assertTrue($result1['success']);
        
        // Second request should also be allowed (within limits)
        $result2 = $this->rate_limiter->is_request_allowed($action);
        $this->assertTrue($result2['success']);
        
        // The remaining count should decrease
        $this->assertLessThanOrEqual($result1['remaining'], $result2['remaining']);
    }
    
    public function testResetRateLimitClearsCounters()
    {
        $action = 'test_action';
        
        // Make some requests
        $this->rate_limiter->is_request_allowed($action);
        $this->rate_limiter->is_request_allowed($action);
        
        // Get status before reset
        $status_before = $this->rate_limiter->get_rate_limit_status($action);
        $this->assertGreaterThan(0, $status_before['requests_made']);
        
        // Reset the rate limit
        $reset_result = $this->rate_limiter->reset_rate_limit($action);
        $this->assertTrue($reset_result['success']);
        
        // Check status after reset
        $status_after = $this->rate_limiter->get_rate_limit_status($action);
        $this->assertEquals(0, $status_after['requests_made']);
    }
    
    public function testUpdateRateLimitsChangesConfiguration()
    {
        $new_limits = [
            'test_action' => [
                'requests' => 5,
                'period' => 300
            ]
        ];
        
        $result = $this->rate_limiter->update_rate_limits($new_limits);
        $this->assertTrue($result['success']);
        
        // Test that the new limit is applied
        $status = $this->rate_limiter->get_rate_limit_status('test_action');
        $this->assertEquals(5, $status['limit']);
    }
    
    public function testClearAllRateLimitsWorks()
    {
        // Make some requests for different actions
        $this->rate_limiter->is_request_allowed('action1');
        $this->rate_limiter->is_request_allowed('action2');
        
        // Clear all rate limits
        $result = $this->rate_limiter->clear_all_rate_limits();
        $this->assertTrue($result['success']);
        
        // Check that counters are reset
        $status1 = $this->rate_limiter->get_rate_limit_status('action1');
        $status2 = $this->rate_limiter->get_rate_limit_status('action2');
        
        $this->assertEquals(0, $status1['requests_made']);
        $this->assertEquals(0, $status2['requests_made']);
    }
    
    public function testDifferentActionTypesAreTrackedSeparately()
    {
        // Make requests for different actions
        $result1 = $this->rate_limiter->is_request_allowed('upload');
        $result2 = $this->rate_limiter->is_request_allowed('download');
        
        $this->assertTrue($result1['success']);
        $this->assertTrue($result2['success']);
        
        // Get status for each action
        $status_upload = $this->rate_limiter->get_rate_limit_status('upload');
        $status_download = $this->rate_limiter->get_rate_limit_status('download');
        
        // Each should have their own counter
        $this->assertEquals(1, $status_upload['requests_made']);
        $this->assertEquals(1, $status_download['requests_made']);
    }
    
    public function testGetClientIdentifierUsesIpAddress()
    {
        // Use reflection to test the private method
        $reflection = new \ReflectionClass($this->rate_limiter);
        $method = $reflection->getMethod('get_client_identifier');
        $method->setAccessible(true);
        
        $identifier = $method->invoke($this->rate_limiter);
        
        // Should use IP address format for anonymous users
        $this->assertStringContains('ip_', $identifier);
        $this->assertStringContains('*************', $identifier);
    }
    
    public function testCacheKeyGenerationIsConsistent()
    {
        // Use reflection to test the private method
        $reflection = new \ReflectionClass($this->rate_limiter);
        $method = $reflection->getMethod('get_cache_key');
        $method->setAccessible(true);
        
        $key1 = $method->invoke($this->rate_limiter, 'test_action', 'client123');
        $key2 = $method->invoke($this->rate_limiter, 'test_action', 'client123');
        
        $this->assertEquals($key1, $key2);
        $this->assertStringContains('rate_limit', $key1);
        $this->assertStringContains('test_action', $key1);
        $this->assertStringContains('client123', $key1);
    }
    
    public function testErrorHandlingForInvalidActions()
    {
        // Test with empty action
        $result = $this->rate_limiter->is_request_allowed('');
        $this->assertFalse($result['success']);
        $this->assertArrayHasKey('message', $result);
        
        // Test get status for non-existent action
        $status = $this->rate_limiter->get_rate_limit_status('non_existent_action');
        $this->assertIsArray($status);
        $this->assertEquals(0, $status['requests_made']);
    }
    
    public function testRateLimitPerformanceWithManyRequests()
    {
        $action = 'performance_test';
        $start_time = microtime(true);
        
        // Make 10 rapid requests
        for ($i = 0; $i < 10; $i++) {
            $result = $this->rate_limiter->is_request_allowed($action);
            $this->assertIsArray($result);
            $this->assertArrayHasKey('success', $result);
        }
        
        $end_time = microtime(true);
        $execution_time = $end_time - $start_time;
        
        // Should complete in reasonable time (less than 1 second)
        $this->assertLessThan(1.0, $execution_time);
        
        // Check final status
        $status = $this->rate_limiter->get_rate_limit_status($action);
        $this->assertEquals(10, $status['requests_made']);
    }
}
