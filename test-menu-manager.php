<?php
/**
 * Test specifico per il Menu Manager
 * Verifica se la classe Financial_Advisor_Menu_Manager funziona correttamente
 */

// Prevenire accesso diretto
if (!defined('ABSPATH')) {
    exit('Accesso diretto non consentito');
}

// Verifica permessi admin
if (!current_user_can('manage_options')) {
    wp_die('Non hai i permessi per accedere a questa pagina.');
}

echo '<div style="font-family: Arial, sans-serif; margin: 20px; background: #f9f9f9; padding: 20px; border-radius: 8px;">';
echo '<h1 style="color: #2271b1;">🔧 Test Menu Manager</h1>';

// 1. Verifica esistenza classe
echo '<h2>📚 Verifica Classe</h2>';
echo '<div style="background: white; padding: 15px; border-radius: 5px; margin: 10px 0;">';

if (class_exists('Financial_Advisor_Menu_Manager')) {
    echo '<p style="color: #00a32a;">✅ Classe Financial_Advisor_Menu_Manager caricata</p>';
    
    // 2. Verifica istanza singleton
    echo '<h3>🔄 Test Singleton</h3>';
    try {
        $instance1 = Financial_Advisor_Menu_Manager::get_instance();
        $instance2 = Financial_Advisor_Menu_Manager::get_instance();
        
        if ($instance1 === $instance2) {
            echo '<p style="color: #00a32a;">✅ Pattern Singleton funziona correttamente</p>';
        } else {
            echo '<p style="color: #d63638;">❌ Pattern Singleton non funziona</p>';
        }
        
        // 3. Verifica proprietà
        echo '<h3>⚙️ Proprietà Istanza</h3>';
        if (property_exists($instance1, 'main_menu_slug')) {
            echo '<p style="color: #00a32a;">✅ Proprietà main_menu_slug: ' . $instance1->main_menu_slug . '</p>';
        } else {
            echo '<p style="color: #d63638;">❌ Proprietà main_menu_slug mancante</p>';
        }
        
        // 4. Verifica metodi
        echo '<h3>🔧 Metodi Disponibili</h3>';
        $methods_to_check = array(
            'register_all_menus',
            'render_widget_help_admin_page',
            'render_test_help_page',
            'render_repair_help_page',
            'render_settings_page'
        );
        
        foreach ($methods_to_check as $method) {
            if (method_exists($instance1, $method)) {
                echo '<p style="color: #00a32a;">✅ Metodo ' . $method . ' disponibile</p>';
            } else {
                echo '<p style="color: #d63638;">❌ Metodo ' . $method . ' mancante</p>';
            }
        }
        
    } catch (Exception $e) {
        echo '<p style="color: #d63638;">❌ Errore nell\'istanziazione: ' . $e->getMessage() . '</p>';
    }
    
} else {
    echo '<p style="color: #d63638;">❌ Classe Financial_Advisor_Menu_Manager non trovata</p>';
    
    // Verifica se il file esiste
    $file_path = plugin_dir_path(__FILE__) . 'includes/class-menu-manager.php';
    if (file_exists($file_path)) {
        echo '<p style="color: #72aee6;">ℹ️ File class-menu-manager.php esiste</p>';
        echo '<p style="color: #dba617;">⚠️ Il file potrebbe avere errori di sintassi o non essere incluso correttamente</p>';
    } else {
        echo '<p style="color: #d63638;">❌ File class-menu-manager.php non trovato</p>';
    }
}

echo '</div>';

// 5. Verifica funzione helper
echo '<h2>🔧 Funzione Helper</h2>';
echo '<div style="background: white; padding: 15px; border-radius: 5px; margin: 10px 0;">';

if (function_exists('financial_advisor_menu_manager')) {
    echo '<p style="color: #00a32a;">✅ Funzione financial_advisor_menu_manager() disponibile</p>';
    
    try {
        $helper_instance = financial_advisor_menu_manager();
        if ($helper_instance instanceof Financial_Advisor_Menu_Manager) {
            echo '<p style="color: #00a32a;">✅ Funzione helper restituisce istanza corretta</p>';
        } else {
            echo '<p style="color: #d63638;">❌ Funzione helper restituisce tipo errato</p>';
        }
    } catch (Exception $e) {
        echo '<p style="color: #d63638;">❌ Errore nella funzione helper: ' . $e->getMessage() . '</p>';
    }
} else {
    echo '<p style="color: #d63638;">❌ Funzione financial_advisor_menu_manager() non disponibile</p>';
}

echo '</div>';

// 6. Verifica menu WordPress
echo '<h2>🎛️ Menu WordPress</h2>';
echo '<div style="background: white; padding: 15px; border-radius: 5px; margin: 10px 0;">';

global $menu, $submenu;

// Cerca il menu principale
$main_menu_found = false;
$main_menu_slug = '';

if (isset($menu) && is_array($menu)) {
    foreach ($menu as $menu_item) {
        if (isset($menu_item[2]) && (
            strpos($menu_item[2], 'document-viewer-settings') !== false ||
            strpos($menu_item[2], 'financial-advisor') !== false
        )) {
            $main_menu_found = true;
            $main_menu_slug = $menu_item[2];
            echo '<p style="color: #00a32a;">✅ Menu principale trovato: ' . $menu_item[1] . ' (' . $menu_item[2] . ')</p>';
            break;
        }
    }
}

if (!$main_menu_found) {
    echo '<p style="color: #d63638;">❌ Menu principale Financial Advisor non trovato</p>';
}

// Cerca i sottomenu
$submenus_found = array();
if (isset($submenu) && is_array($submenu) && $main_menu_slug) {
    if (isset($submenu[$main_menu_slug])) {
        echo '<h3>📋 Sottomenu trovati:</h3>';
        echo '<ul>';
        foreach ($submenu[$main_menu_slug] as $submenu_item) {
            $submenus_found[] = $submenu_item[2];
            echo '<li>' . $submenu_item[0] . ' (' . $submenu_item[2] . ')</li>';
        }
        echo '</ul>';
        
        // Verifica sottomenu specifici
        $expected_submenus = array(
            'widget-help-admin',
            'test-help-system',
            'repair-help-system'
        );
        
        echo '<h3>🔍 Verifica Sottomenu Help:</h3>';
        foreach ($expected_submenus as $expected) {
            if (in_array($expected, $submenus_found)) {
                echo '<p style="color: #00a32a;">✅ ' . $expected . ' registrato</p>';
            } else {
                echo '<p style="color: #d63638;">❌ ' . $expected . ' non registrato</p>';
            }
        }
    } else {
        echo '<p style="color: #d63638;">❌ Nessun sottomenu trovato per ' . $main_menu_slug . '</p>';
    }
}

echo '</div>';

// 7. Test di inizializzazione
echo '<h2>🚀 Test Inizializzazione</h2>';
echo '<div style="background: white; padding: 15px; border-radius: 5px; margin: 10px 0;">';

// Verifica se la costante è definita
if (defined('FA_MENU_MANAGER_ACTIVE')) {
    echo '<p style="color: #00a32a;">✅ Costante FA_MENU_MANAGER_ACTIVE definita: ' . (FA_MENU_MANAGER_ACTIVE ? 'true' : 'false') . '</p>';
} else {
    echo '<p style="color: #d63638;">❌ Costante FA_MENU_MANAGER_ACTIVE non definita</p>';
}

// Verifica hook WordPress
if (has_action('admin_menu', array('Financial_Advisor_Menu_Manager', 'register_all_menus'))) {
    echo '<p style="color: #00a32a;">✅ Hook admin_menu registrato per register_all_menus</p>';
} else {
    echo '<p style="color: #dba617;">⚠️ Hook admin_menu non trovato (potrebbe essere registrato dinamicamente)</p>';
}

echo '</div>';

// 8. Riepilogo e raccomandazioni
echo '<h2>📋 Riepilogo</h2>';
echo '<div style="background: white; padding: 15px; border-radius: 5px; margin: 10px 0;">';

$issues = array();

if (!class_exists('Financial_Advisor_Menu_Manager')) {
    $issues[] = 'Classe Financial_Advisor_Menu_Manager non caricata';
}

if (!function_exists('financial_advisor_menu_manager')) {
    $issues[] = 'Funzione helper non disponibile';
}

if (!$main_menu_found) {
    $issues[] = 'Menu principale non registrato';
}

if (!in_array('widget-help-admin', $submenus_found)) {
    $issues[] = 'Sottomenu Widget Help non registrato';
}

if (empty($issues)) {
    echo '<p style="color: #00a32a; font-weight: bold;">✅ Menu Manager funziona correttamente!</p>';
    echo '<p>Tutti i test sono passati. Il sistema di menu è completamente operativo.</p>';
} else {
    echo '<p style="color: #d63638; font-weight: bold;">❌ Problemi rilevati nel Menu Manager:</p>';
    echo '<ul>';
    foreach ($issues as $issue) {
        echo '<li style="color: #d63638;">' . $issue . '</li>';
    }
    echo '</ul>';
    
    echo '<h3>💡 Raccomandazioni:</h3>';
    echo '<ol>';
    echo '<li>Verifica che il file class-menu-manager.php sia incluso correttamente</li>';
    echo '<li>Controlla che non ci siano errori di sintassi PHP</li>';
    echo '<li>Assicurati che il plugin sia attivato correttamente</li>';
    echo '<li>Verifica che non ci siano conflitti con altri plugin</li>';
    echo '</ol>';
}

echo '</div>';

echo '</div>';
?>
