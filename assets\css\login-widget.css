/* Login Widget CSS - Allineato con lo stile degli altri widget */

/* <PERSON><PERSON> per l'icona utente */
.user-avatar {
    margin-right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background-color: #f0f2f5;
    border-radius: 50%;
    padding: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.user-avatar-icon {
    width: 100%;
    height: 100%;
    transition: transform 0.3s ease;
}

.user-avatar:hover .user-avatar-icon {
    transform: scale(1.1);
}

/* Login widget container */
.login-widget-container {
    max-width: 100%;
    margin: 20px 0;
    padding: 30px;
    background: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.08);
    display: flex;
    flex-wrap: wrap;
    gap: 25px;
    position: relative;
}

/* Header styling consistency with user-subscription-widget */
.login-widget-container h3 {
    margin-bottom: 20px;
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    letter-spacing: 0.02em;
    text-align: center;
    position: relative;
}

.login-widget-container h3:after {
    content: '';
    display: block;
    width: 60px;
    height: 3px;
    background: #0073aa;
    margin: 8px auto 0;
    border-radius: 2px;
}

/* Tabs navigation */
.login-tabs {
    display: flex;
    margin-bottom: 25px;
    border-bottom: 2px solid #f0f0f0;
    width: 100%;
    background-color: #f9f9f9;
    border-radius: 6px 6px 0 0;
    overflow: hidden;
}

.login-tabs .tab {
    padding: 12px 20px;
    cursor: pointer;
    font-weight: 500;
    color: #666;
    border-bottom: 2px solid transparent;
    margin-bottom: -2px;
    transition: all 0.25s ease;
    text-align: center;
    flex: 1;
    position: relative;
}

.login-tabs .tab:hover {
    color: #0073aa;
    background-color: #f2f2f2;
}

.login-tabs .tab.active {
    color: #0073aa;
    border-bottom-color: #0073aa;
    background-color: #fff;
    font-weight: 600;
}

.login-tabs .tab:after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #0073aa;
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

/* Tab content */
.tab-content {
    display: none;
    width: 100%;
    min-height: 350px;
    overflow-y: hidden; /* Nasconde barre di scorrimento verticali */
}

.tab-content.active {
    display: block;
}

/* Form styling aligned with user-subscription-form */
.login-form, .reset-form {
    width: 100%;
    padding: 20px;
    background-color: #fafafa;
    border-radius: 6px;
    border: 1px solid #f0f0f0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.04);
    min-height: 400px; /* Spazio aumentato per messaggi JSON e controlli di redirect */
    position: relative;
    overflow: visible; /* Previene barre di scorrimento */
}

.form-group {
    margin-bottom: 15px;
    position: relative;
    width: 100%;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="password"] {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.3s ease;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);
    background-color: #fff;
    color: #333;
}

.form-group input:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 115, 170, 0.2), inset 0 1px 3px rgba(0,0,0,0.05);
    background-color: #fafeff;
}

/* Improved focus effect for accessibility */
.form-group input:focus-visible {
    outline: 2px solid #0073aa;
    outline-offset: 1px;
}

/* Improved placeholder styling */
.form-group input::placeholder {
    color: #aaa;
    opacity: 0.7;
}

/* Enhanced error state styling */
.form-group.has-error input {
    border-color: #dc3545;
    background-color: #fff8f8;
}

.form-group.has-error input {
    border-color: #dc3545;
}

.form-group .error-message {
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
    display: block;
    font-style: italic;
    background: transparent;
    border: none;
    padding: 0;
}

.checkbox-group {
    display: flex;
    align-items: center;
    margin-top: 5px;
    margin-bottom: 15px;
}

.checkbox-group input[type="checkbox"] {
    margin-right: 8px;
    width: 16px;
    height: 16px;
    accent-color: #0073aa;
    border: 1px solid #ddd;
    border-radius: 3px;
    cursor: pointer;
}

.checkbox-group label {
    margin-bottom: 0;
    font-weight: normal;
    font-size: 13px;
    color: #666;
    cursor: pointer;
}

.form-submit {
    margin-top: 20px;
    text-align: center;
}

button.submit-button {
    display: inline-block;
    padding: 12px 25px;
    background-color: #0073aa;
    color: white;
    border: none;
    border-radius: 4px;
    font-weight: 500;
    font-size: 15px;
    cursor: pointer;
    transition: all 0.3s;
    width: 100%;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

button.submit-button:hover {
    background-color: #005a87;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

button.submit-button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

/* Messages */
.form-message {
    padding: 12px 15px;
    margin-top: 15px;
    border-radius: 4px;
    font-size: 14px;
    text-align: center;
    transform: translateY(0);
    opacity: 1;
    transition: all 0.3s ease;
    position: relative;
    display: none;
    width: 100%;
    word-wrap: break-word; /* Previene overflow del testo */
    max-height: none; /* Rimuove altezza massima */
    overflow: visible; /* Assicura che non ci siano barre di scorrimento */
}

.form-message.success {
    background-color: rgba(212, 237, 218, 0.7);
    color: #155724;
    border-left: 4px solid #28a745;
    box-shadow: none;
}

.form-message.error {
    background-color: rgba(248, 215, 218, 0.7);
    color: #721c24;
    border-left: 4px solid #dc3545;
    box-shadow: none;
}

.form-message.info {
    background-color: rgba(204, 229, 255, 0.7);
    color: #004085;
    border-left: 4px solid #007bff;
    box-shadow: none;
}

/* JSON response styling */
.form-message pre {
    text-align: left;
    margin: 10px 0;
    padding: 12px;
    background-color: rgba(250, 250, 250, 0.6);
    border-radius: 4px;
    font-family: monospace;
    white-space: pre-wrap; /* Permette wrap del testo */
    word-break: break-word; /* Rompe le parole lunghe */
    font-size: 12px;
    max-height: 200px;
    overflow-y: auto; /* Scorrimento interno solo se necessario */
    color: #333;
}

/* Message icons */
.message-icon {
    display: inline-block;
    width: 18px;
    height: 18px;
    line-height: 18px;
    border-radius: 50%;
    text-align: center;
    font-weight: bold;
    font-size: 12px;
    margin-right: 8px;
}

.success .message-icon {
    background-color: #1a365d;
    color: white;
}

.error .message-icon {
    background-color: #dc3545;
    color: white;
}

/* Message animation */
.form-message.showing {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    animation: messageAppear 0.3s ease-out;
}

@keyframes messageAppear {
    from { 
        opacity: 0;
        transform: translateY(-10px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

.form-message.info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Activation message styling */
.activation-message {
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
    position: relative;
    animation: fadeInDown 0.5s ease-out;
    text-align: center;
    background: transparent;
    border: none;
    overflow: visible;
}

.activation-message.success {
    background-color: rgba(212, 237, 218, 0.7);
    border-left: 4px solid #28a745;
    color: #155724;
    box-shadow: none;
}

.activation-message.error {
    background-color: rgba(248, 215, 218, 0.7);
    border-left: 4px solid #dc3545;
    color: #721c24;
    box-shadow: none;
}

.activation-message p {
    margin: 0 0 10px;
}

.login-link {
    margin-top: 10px;
}

.login-link .start-login-button {
    display: inline-block;
    padding: 8px 16px;
    background-color: #0073aa;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.login-link .start-login-button:hover {
    background-color: #005a87;
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Global messages */
.global-message {
    padding: 12px;
    margin-bottom: 20px;
    border-radius: 4px;
    font-size: 14px;
    text-align: center;
    width: 100%;
}

.global-message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.global-message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.global-message.info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Spinner */
.spinner {
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
    margin-right: 8px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Shake animation for errors */
@keyframes shake {
    0%, 100% {transform: translateX(0);}
    10%, 30%, 50%, 70%, 90% {transform: translateX(-5px);}
    20%, 40%, 60%, 80% {transform: translateX(5px);}
}

.shake {
    animation: shake 0.6s;
}

/* Success login form */
.login-form.login-success {
    opacity: 0.8;
    pointer-events: none;
    transition: opacity 0.3s;
}

/* Logged in user display - aligned with other widget styles */
.logged-in-user-container {
    padding: 25px;
    background-color: #f9fafb;
    border-radius: 8px;
    border: 1px solid #eee;
    box-shadow: 0 2px 6px rgba(0,0,0,0.05);
    width: 100%;
}

.user-info {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.user-avatar {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-left: 15px;
}

.user-name {
    font-weight: 600;
    font-size: 16px;
    color: #2c3e50;
    line-height: 1.4;
    margin: 0;
}

.user-role {
    font-size: 13px;
    color: #fff;
    background-color: #3498db;
    padding: 4px 10px;
    border-radius: 12px;
    display: inline-block;
    max-width: fit-content;
    font-weight: 500;
    text-transform: capitalize;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.user-email {
    font-size: 13px;
    color: #7f8c8d;
    word-break: break-all;
    line-height: 1.4;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 5px;
}

.user-email:before {
    content: '✉️';
    font-size: 12px;
    opacity: 0.8;
}

.user-actions {
    margin-top: 15px;
    display: flex;
    justify-content: flex-end;
}

.logout-button {
    padding: 10px 20px;
    background-color: #f8f9fa;
    color: #333;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-align: center;
}

.logout-button:hover {
    background-color: #e9ecef;
    border-color: #c6c8ca;
    color: #212529;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transform: translateY(-1px);
}

/* Compact layout styling */
.login-widget-compact {
    max-width: 100%;
    margin: 15px 0;
    padding: 20px;
    background: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}

.login-widget-compact h3 {
    margin-bottom: 15px;
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    letter-spacing: 0.02em;
    text-align: center;
    position: relative;
}

.login-widget-compact h3:after {
    content: '';
    display: block;
    width: 50px;
    height: 2px;
    background: #0073aa;
    margin: 6px auto 0;
    border-radius: 2px;
}

.login-widget-compact .login-widget-container {
    margin: 0;
    padding: 0;
    border: none;
    box-shadow: none;
    border-radius: 0;
    background: transparent;
}

.login-widget-compact .login-tabs {
    margin-bottom: 15px;
}

.login-widget-compact .login-form,
.login-widget-compact .reset-form {
    padding: 15px;
}

.login-widget-compact .form-group {
    margin-bottom: 12px;
}

.login-widget-compact .submit-button {
    padding: 10px 20px;
    font-size: 14px;
}

/* Responsive design */
@media (max-width: 768px) {
    .login-widget-container {
        padding: 20px 15px;
    }
    
    .login-tabs .tab {
        padding: 10px 15px;
        font-size: 14px;
    }
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .login-widget-container,
    .login-widget-compact {
        padding: 15px;
    }
    
    .login-tabs .tab {
        padding: 8px 15px;
        font-size: 13px;
    }
    
    .form-group label {
        font-size: 13px;
    }
    
    .submit-button {
        padding: 10px 15px;
        font-size: 14px;
    }
    
    .user-name {
        font-size: 15px;
    }
    
    .user-role, .user-email {
        font-size: 12px;
    }
}

/* Styling per il controllo di reindirizzamento */
.redirect-controls {
    margin-top: 15px;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 4px;
    text-align: center;
}

.redirect-controls p {
    font-size: 13px;
    margin-bottom: 8px;
    color: #555;
}

.manual-redirect {
    display: inline-block;
    padding: 8px 15px;
    border-radius: 4px;
    background-color: #0073aa;
    color: white;
    font-size: 13px;
    cursor: pointer;
    border: none;
    transition: all 0.2s ease;
}

.manual-redirect:hover {
    background-color: #005a87;
    transform: translateY(-1px);
}

.redirect-info, .user-type-info {
    font-size: 13px;
    padding: 5px 10px;
    background-color: rgba(0, 115, 170, 0.08);
    margin: 5px 0;
    border-radius: 3px;
    text-align: left;
    color: #333;
}

.redirect-info {
    background-color: rgba(0, 115, 170, 0.08);
    border-left: 3px solid rgba(0, 115, 170, 0.5);
}

.user-type-info {
    background-color: rgba(40, 167, 69, 0.08);
    border-left: 3px solid rgba(40, 167, 69, 0.5);
}
