<?php
/**
 * Enhanced Payment Gateway Verification Script
 * Advanced testing interface for WordPress payment gateway management
 * 
 * NOTE: This file is no longer used as a standalone admin page.
 * Testing functionality has been integrated into the main Payment Gateway Admin interface.
 * This file is kept for reference and contains reusable testing functions.
 * 
 * This script provides comprehensive testing and monitoring for:
 * - Payment gateway backend functions
 * - AJAX connectivity and response handling
 * - Widget integration and dependency loading
 * - Real-time system status monitoring
 * - Payment transaction simulation
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Ensure this file is loaded only in admin context
if (!is_admin()) {
    return;
}

// Ensure widget dependencies are loaded
add_action('admin_init', 'ensure_payment_gateway_dependencies');

// Register the test page display function if not already defined
if (!function_exists('display_enhanced_payment_gateway_test_page')) {
    add_action('admin_init', function() {
        if (!function_exists('display_enhanced_payment_gateway_test_page')) {
            require_once __DIR__ . '/admin-payment-gateway-test.php';
        }
    });
}

function ensure_payment_gateway_dependencies() {
    // Force reload widget includes if not already loaded
    if (!class_exists('Subscriber_Management_Widget')) {
        $widget_file = dirname(__FILE__) . '/includes/widgets/subscriber-management-widget.php';
        if (file_exists($widget_file)) {
            require_once $widget_file;
        }    }
    
    // Ensure widget is registered (only in WordPress context)
    if (function_exists('register_widget') && class_exists('Subscriber_Management_Widget') && !is_admin()) {
        register_widget('Subscriber_Management_Widget');
    }
}

function display_enhanced_payment_gateway_test_page() {
    // Enqueue necessary scripts and styles for the test page
    wp_enqueue_script('jquery');
    wp_enqueue_style('wp-admin');
    
    // Force enqueue subscriber widget scripts in admin context
    if (class_exists('Subscriber_Management_Widget')) {
        $widget_instance = new Subscriber_Management_Widget();
        $widget_instance->enqueue_scripts();
    }
    ?>
    <div class="wrap">
        <h1>🚀 Enhanced Payment Gateway Management Test</h1>
        <div class="notice notice-info">
            <p><strong>Advanced Diagnostic Tool:</strong> This interface provides comprehensive testing of all payment gateway backend functions with real-time feedback and monitoring.</p>
        </div>
        <h2 class="nav-tab-wrapper">
            <a href="#tab-automatic" class="nav-tab nav-tab-active" id="tab-automatic-link">Test Automatici</a>
            <a href="#tab-manuale" class="nav-tab" id="tab-manuale-link">Test Manuali</a>
            <a href="#tab-monitor" class="nav-tab" id="tab-monitor-link">Monitoraggio</a>
            <a href="#tab-log" class="nav-tab" id="tab-log-link">Log & Errori</a>
        </h2>
        <div id="tab-automatic" class="tab-content active">
            <?php
            // Test automatici
            $test_results = run_enhanced_payment_gateway_tests();
            display_enhanced_test_results($test_results);
            ?>
        </div>
        <div id="tab-manuale" class="tab-content">
            <!-- Test manuali -->
            <div class="postbox" style="margin-top: 30px;">
                <div class="postbox-header">
                    <h2 class="hndle">🧪 Live Payment Gateway Test Interface</h2>
                </div>
                <div class="inside">
                    <div id="gateway-test-container">
                        <?php display_enhanced_test_interface(); ?>
                    </div>
                </div>
            </div>
        </div>
        <div id="tab-monitor" class="tab-content">
            <!-- Monitoraggio -->
            <div class="postbox" style="margin-top: 20px;">
                <div class="postbox-header">
                    <h2 class="hndle">📊 Real-time System Monitor</h2>
                </div>
                <div class="inside">
                    <div id="system-monitor">
                        <div class="monitor-grid">
                            <div class="monitor-item">
                                <strong>AJAX Status:</strong> <span id="ajax-status">Testing...</span>
                            </div>
                            <div class="monitor-item">
                                <strong>Widget Status:</strong> <span id="widget-status">Checking...</span>
                            </div>
                            <div class="monitor-item">
                                <strong>Database Status:</strong> <span id="db-status">Verifying...</span>
                            </div>
                            <div class="monitor-item">
                                <strong>Gateway Connection:</strong> <span id="gateway-status">Testing...</span>
                            </div>
                        </div>
                        <button id="refresh-monitor" class="button">🔄 Refresh Status</button>
                    </div>
                </div>
            </div>
        </div>
        <div id="tab-log" class="tab-content">
            <!-- Log/Errori -->
            <div class="postbox" style="margin-top: 20px;">
                <div class="postbox-header">
                    <h2 class="hndle">📋 Log & Errori</h2>
                </div>
                <div class="inside">
                    <div id="test-feedback-area-log" class="test-feedback-container">
                        <div class="notice notice-info">
                            <p>Qui verranno mostrati i log e gli errori generati durante i test manuali e automatici.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <style>
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        </style>
        <script>
        jQuery(document).ready(function($) {
            $('.nav-tab').click(function(e) {
                e.preventDefault();
                $('.nav-tab').removeClass('nav-tab-active');
                $('.tab-content').removeClass('active');
                $(this).addClass('nav-tab-active');
                var target = $(this).attr('href');
                $(target).addClass('active');
            });
        });
        </script>
        <?php display_enhanced_test_scripts(); ?>
    </div>
    <?php
}

function display_enhanced_test_interface() {
    $current_user_id = function_exists('get_current_user_id') ? get_current_user_id() : 0;
    $current_user = function_exists('wp_get_current_user') ? wp_get_current_user() : null;
    $current_credit = (function_exists('get_user_meta') && $current_user_id) ? get_user_meta($current_user_id, 'credits', true) : 0;
    
    ?>
    <div class="test-interface-wrapper">
        <!-- User Info Panel -->
        <div class="test-panel">
            <h3>👤 Current User Information</h3>
            <table class="form-table">
                <tr>
                    <th>User:</th>
                    <td><?php echo $current_user ? esc_html($current_user->display_name) : 'Unknown User'; ?> (ID: <?php echo $current_user_id; ?>)</td>
                </tr>
                <tr>
                    <th>Email:</th>
                    <td><?php echo esc_html($current_user->user_email); ?></td>
                </tr>
                <tr>
                    <th>Current Credits:</th>
                    <td id="current-credit-display">€<?php echo $current_credit ? number_format((float)$current_credit, 2) : '0.00'; ?></td>
                </tr>
            </table>
        </div>

        <!-- Gateway Test Panel -->
        <div class="test-panel">
            <h3>💳 Payment Gateway Test</h3>
            <table class="form-table">
                <tr>
                    <th><label for="test-amount-select">Amount:</label></th>
                    <td>
                        <select id="test-amount-select" class="regular-text">
                            <option value="">Select Amount</option>
                            <option value="5">€5.00</option>
                            <option value="10">€10.00</option>
                            <option value="20">€20.00</option>
                            <option value="50">€50.00</option>
                            <option value="custom">Custom Amount</option>
                        </select>
                        <div id="custom-amount-input" style="display: none; margin-top: 10px;">
                            <input type="number" id="custom-amount" min="1" max="500" step="0.01" placeholder="Enter amount" />
                            <span class="description">Min: €1.00, Max: €500.00</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <th><label for="test-method-select">Payment Method:</label></th>
                    <td>
                        <select id="test-method-select" class="regular-text">
                            <option value="">Select Method</option>
                            <option value="paypal">PayPal</option>
                            <option value="stripe">Stripe</option>
                            <option value="test">Test Mode</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <th>Actions:</th>
                    <td>
                        <button id="test-recharge-btn" class="button button-primary">🧪 Test Payment Gateway</button>
                        <button id="test-ajax-only-btn" class="button">🔗 Test AJAX Only</button>
                        <button id="simulate-transaction-btn" class="button">⚡ Simulate Transaction</button>
                    </td>
                </tr>
            </table>
        </div>

        <!-- Feedback Area -->
        <div class="test-panel">
            <h3>📋 Test Results & Feedback</h3>
            <div id="test-feedback-area" class="test-feedback-container">
                <div class="notice notice-info">
                    <p>Ready to test payment gateway functions. Select amount and method above.</p>
                </div>
            </div>
        </div>
    </div>
    
    <style>
    .test-interface-wrapper {
        display: grid;
        grid-template-columns: 1fr;
        gap: 20px;
        margin: 20px 0;
    }
    
    .test-panel {
        background: #fff;
        border: 1px solid #c3c4c7;
        border-radius: 4px;
        padding: 20px;
        box-shadow: 0 1px 1px rgba(0,0,0,0.04);
    }
    
    .test-panel h3 {
        margin-top: 0;
        border-bottom: 1px solid #e1e1e1;
        padding-bottom: 10px;
    }
    
    .test-feedback-container {
        min-height: 100px;
        max-height: 400px;
        overflow-y: auto;
        padding: 10px;
        background: #fafafa;
        border: 1px solid #e1e1e1;
        border-radius: 4px;
    }
    
    .monitor-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
    }
    
    .monitor-item {
        padding: 10px;
        background: #f8f9fa;
        border-left: 4px solid #0073aa;
        border-radius: 0 4px 4px 0;
    }
    
    #custom-amount {
        width: 120px;
        margin-right: 10px;
    }
    
    .status-indicator {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 5px;
    }
    
    .status-success { background: #46b450; }
    .status-error { background: #dc3232; }
    .status-warning { background: #ffb900; }
    .status-loading { background: #0073aa; animation: pulse 1.5s infinite; }
    
    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }
    </style>
    <?php
}

function display_enhanced_test_scripts() {
    ?>
    <script>
    jQuery(document).ready(function($) {
        console.log('🚀 Enhanced Payment Gateway Test System Loaded');
        
        // Initialize monitoring
        initializeSystemMonitor();
        
        // Handle custom amount selection
        $('#test-amount-select').change(function() {
            const customInput = $('#custom-amount-input');
            if ($(this).val() === 'custom') {
                customInput.show();
            } else {
                customInput.hide();
            }
        });
        
        // Test recharge button with enhanced feedback
        $('#test-recharge-btn').click(function() {
            performEnhancedRechargeTest();
        });
        
        // AJAX only test
        $('#test-ajax-only-btn').click(function() {
            performAjaxOnlyTest();
        });
        
        // Simulate transaction
        $('#simulate-transaction-btn').click(function() {
            simulatePaymentTransaction();
        });
        
        // Monitor refresh
        $('#refresh-monitor').click(function() {
            refreshSystemMonitor();
        });
        
        function initializeSystemMonitor() {
            updateStatus('ajax-status', 'loading', 'Initializing...');
            updateStatus('widget-status', 'loading', 'Checking...');
            updateStatus('db-status', 'loading', 'Connecting...');
            updateStatus('gateway-status', 'loading', 'Testing...');
            
            // Check each component
            setTimeout(() => {
                checkAjaxStatus();
                checkWidgetStatus();
                checkDatabaseStatus();
                checkGatewayStatus();
            }, 1000);
        }
        
        function checkAjaxStatus() {
            if (typeof subscriberManagementAjax !== 'undefined') {
                updateStatus('ajax-status', 'success', 'Available & Ready');
            } else {
                updateStatus('ajax-status', 'error', 'Object Not Found');
                logError('subscriberManagementAjax object not available');
            }
        }
        
        function checkWidgetStatus() {
            // Test if widget class exists by attempting AJAX call
            $.ajax({
                url: ajaxurl || '<?php echo admin_url("admin-ajax.php"); ?>',
                type: 'POST',
                data: {
                    action: 'get_current_credit',
                    nonce: getAjaxNonce()
                },
                success: function(response) {
                    updateStatus('widget-status', 'success', 'Widget Active');
                },
                error: function(xhr) {
                    if (xhr.status === 400) {
                        updateStatus('widget-status', 'warning', 'Widget Loaded, Auth Required');
                    } else {
                        updateStatus('widget-status', 'error', 'Widget Inactive');
                    }
                }
            });
        }
        
        function checkDatabaseStatus() {
            // Simple database test
            $.ajax({
                url: ajaxurl || '<?php echo admin_url("admin-ajax.php"); ?>',
                type: 'POST',
                data: {
                    action: 'get_current_credit',
                    nonce: getAjaxNonce()
                },
                complete: function(xhr) {
                    if (xhr.status === 200 || xhr.status === 400) {
                        updateStatus('db-status', 'success', 'Connected');
                    } else {
                        updateStatus('db-status', 'error', 'Connection Failed');
                    }
                }
            });
        }
        
        function checkGatewayStatus() {
            // Check if payment gateway admin exists
            const hasPaymentGateway = typeof paymentGatewayAdmin !== 'undefined';
            if (hasPaymentGateway) {
                updateStatus('gateway-status', 'success', 'Gateway Ready');
            } else {
                updateStatus('gateway-status', 'warning', 'Gateway Admin Not Loaded');
            }
        }
        
        function performEnhancedRechargeTest() {
            const amount = getSelectedAmount();
            const method = $('#test-method-select').val();
            
            if (!validateTestInputs(amount, method)) return;
            
            addFeedback('info', '🔄 Starting enhanced payment test...', true);
            
            // Step 1: Test AJAX connectivity
            addFeedback('info', '📡 Testing AJAX connectivity...');
            
            if (typeof subscriberManagementAjax === 'undefined') {
                addFeedback('error', '❌ subscriberManagementAjax not available. Widget may not be loaded correctly.');
                return;
            }
            
            // Step 2: Test credit retrieval
            addFeedback('info', '💰 Testing credit retrieval...');
            
            $.ajax({
                url: subscriberManagementAjax.ajax_url,
                type: 'POST',
                data: {
                    action: 'get_current_credit',
                    nonce: subscriberManagementAjax.nonce
                },
                success: function(response) {
                    addFeedback('success', '✅ Credit retrieval successful: ' + JSON.stringify(response));
                    
                    // Step 3: Test mock recharge (if in test mode)
                    if (method === 'test') {
                        testMockRecharge(amount, method);
                    } else {
                        addFeedback('warning', '⚠️ Live payment test skipped. Use "Test Mode" for full simulation.');
                        addFeedback('success', '🎯 Gateway connection verified. Ready for live payments!');
                    }
                },
                error: function(xhr, status, error) {
                    addFeedback('error', '❌ Credit retrieval failed: ' + error);
                    addFeedback('error', 'Response: ' + (xhr.responseText || 'No response'));
                }
            });
        }
        
        function testMockRecharge(amount, method) {
            addFeedback('info', '🧪 Testing mock recharge...');
            
            // This would be a real recharge call in production
            setTimeout(() => {
                addFeedback('success', `✅ Mock recharge successful! €${amount} via ${method}`);
                addFeedback('success', '🏆 All payment gateway tests passed!');
                
                // Update credit display (mock)
                const currentCredit = parseFloat($('#current-credit-display').text().replace('€', '')) || 0;
                const newCredit = currentCredit + parseFloat(amount);
                $('#current-credit-display').text('€' + newCredit.toFixed(2));
            }, 1500);
        }
        
        function performAjaxOnlyTest() {
            addFeedback('info', '🔗 Testing AJAX connectivity only...', true);
            
            $.ajax({
                url: ajaxurl || '<?php echo admin_url("admin-ajax.php"); ?>',
                type: 'POST',
                data: {
                    action: 'heartbeat',
                    _nonce: '<?php echo wp_create_nonce("heartbeat"); ?>'
                },
                success: function(response) {
                    addFeedback('success', '✅ WordPress AJAX system working');
                    checkSubscriberAjax();
                },
                error: function(xhr, status, error) {
                    addFeedback('error', '❌ WordPress AJAX failed: ' + error);
                }
            });
        }
        
        function checkSubscriberAjax() {
            if (typeof subscriberManagementAjax !== 'undefined') {
                $.ajax({
                    url: subscriberManagementAjax.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'get_current_credit',
                        nonce: subscriberManagementAjax.nonce
                    },
                    success: function(response) {
                        addFeedback('success', '✅ Subscriber widget AJAX working');
                    },
                    error: function(xhr, status, error) {
                        addFeedback('warning', '⚠️ Subscriber AJAX error: ' + error + ' (may require user login)');
                    }
                });
            } else {
                addFeedback('error', '❌ subscriberManagementAjax object not found');
            }
        }
        
        function simulatePaymentTransaction() {
            addFeedback('info', '⚡ Simulating complete payment transaction...', true);
            
            const amount = getSelectedAmount();
            const method = $('#test-method-select').val() || 'test';
            
            if (!amount) {
                addFeedback('error', '❌ Please select an amount');
                return;
            }
            
            // Simulate payment steps
            const steps = [
                { delay: 500, message: '🔐 Initializing secure connection...' },
                { delay: 1000, message: '💳 Processing payment details...' },
                { delay: 1500, message: '🏦 Connecting to payment gateway...' },
                { delay: 2000, message: '✅ Payment confirmed!' },
                { delay: 2500, message: '💰 Updating user credits...' },
                { delay: 3000, message: '📊 Transaction complete!' }
            ];
            
            steps.forEach(step => {
                setTimeout(() => {
                    addFeedback('info', step.message);
                }, step.delay);
            });
            
            setTimeout(() => {
                addFeedback('success', `🎉 Simulation complete! €${amount} transaction via ${method}`);
            }, 3500);
        }
        
        function getSelectedAmount() {
            const select = $('#test-amount-select').val();
            if (select === 'custom') {
                return $('#custom-amount').val();
            }
            return select;
        }
        
        function validateTestInputs(amount, method) {
            if (!amount) {
                addFeedback('error', '❌ Please select an amount');
                return false;
            }
            
            if (!method) {
                addFeedback('error', '❌ Please select a payment method');
                return false;
            }
            
            if (parseFloat(amount) < 1 || parseFloat(amount) > 500) {
                addFeedback('error', '❌ Amount must be between €1.00 and €500.00');
                return false;
            }
            
            return true;
        }
        
        function addFeedback(type, message, clear = false) {
            const container = $('#test-feedback-area');
            
            if (clear) {
                container.empty();
            }
            
            const alertClass = type === 'success' ? 'notice-success' : 
                             type === 'error' ? 'notice-error' : 
                             type === 'warning' ? 'notice-warning' : 'notice-info';
            
            const timestamp = new Date().toLocaleTimeString();
            const feedbackItem = $(`
                <div class="notice ${alertClass} is-dismissible" style="margin: 5px 0; position: relative;">
                    <p><strong>[${timestamp}]</strong> ${message}</p>
                </div>
            `);
            
            container.append(feedbackItem);
            container.scrollTop(container[0].scrollHeight);
        }
        
        function updateStatus(elementId, status, message) {
            const statusMap = {
                'success': { class: 'status-success', text: '● ' + message },
                'error': { class: 'status-error', text: '● ' + message },
                'warning': { class: 'status-warning', text: '● ' + message },
                'loading': { class: 'status-loading', text: '● ' + message }
            };
            
            const statusInfo = statusMap[status] || statusMap['error'];
            $('#' + elementId).html(
                `<span class="status-indicator ${statusInfo.class}"></span>${statusInfo.text}`
            );
        }
        
        function refreshSystemMonitor() {
            addFeedback('info', '🔄 Refreshing system monitor...', true);
            initializeSystemMonitor();
        }
        
        function getAjaxNonce() {
            if (typeof subscriberManagementAjax !== 'undefined') {
                return subscriberManagementAjax.nonce;
            }
            return '<?php echo wp_create_nonce("subscriber_management_nonce"); ?>';
        }
        
        function logError(message) {
            console.error('Gateway Test Error:', message);
            addFeedback('error', '🐛 ' + message);
        }
        
        // Log initial state
        console.log('Available objects:', {
            subscriberManagementAjax: typeof subscriberManagementAjax !== 'undefined' ? subscriberManagementAjax : 'Not available',
            paymentGatewayAdmin: typeof paymentGatewayAdmin !== 'undefined' ? 'Available' : 'Not available',
            jQuery: typeof jQuery !== 'undefined' ? 'Version ' + jQuery.fn.jquery : 'Not available'
        });
    });
    </script>
    <?php
}

function run_enhanced_payment_gateway_tests() {
    $results = array();
    
    // Test 1: Widget class registration
    $widget_exists = class_exists('Subscriber_Management_Widget');
    $results['widget_class'] = array(
        'test' => 'Subscriber Widget Class',
        'status' => $widget_exists,
        'message' => $widget_exists ? 
            'Subscriber_Management_Widget class loaded successfully' : 
            'Subscriber_Management_Widget class NOT found - Check widget file inclusion',
        'details' => $widget_exists ? 'Widget ready for instantiation' : 'Widget file may not be loaded correctly'
    );
    
    // Test 2: Widget instance creation
    if ($widget_exists) {
        try {
            $widget_instance = new Subscriber_Management_Widget();
            $results['widget_instance'] = array(
                'test' => 'Widget Instance Creation',
                'status' => true,
                'message' => 'Widget instance created successfully',
                'details' => 'Widget can be instantiated and used'
            );
        } catch (Exception $e) {
            $results['widget_instance'] = array(
                'test' => 'Widget Instance Creation',
                'status' => false,
                'message' => 'Failed to create widget instance: ' . $e->getMessage(),
                'details' => 'Check widget constructor dependencies'
            );
        }
    }
    
    // Test 3: AJAX handlers registration - Enhanced
    global $wp_filter;
    $ajax_actions = array(
        'wp_ajax_recharge_credits',
        'wp_ajax_nopriv_recharge_credits', 
        'wp_ajax_get_current_credit',
        'wp_ajax_nopriv_get_current_credit',
        'wp_ajax_update_subscriber_data',
        'wp_ajax_nopriv_update_subscriber_data'
    );
    
    $registered_actions = 0;
    $missing_actions = array();
    
    foreach ($ajax_actions as $action) {
        if (isset($wp_filter[$action]) && !empty($wp_filter[$action]->callbacks)) {
            $registered_actions++;
        } else {
            $missing_actions[] = $action;
        }
    }
    
    $results['ajax_handlers'] = array(
        'test' => 'AJAX Handlers Registration',
        'status' => $registered_actions === count($ajax_actions),
        'message' => "$registered_actions of " . count($ajax_actions) . " AJAX handlers registered",
        'details' => empty($missing_actions) ? 'All handlers registered' : 'Missing: ' . implode(', ', $missing_actions)
    );
    
    // Test 4: Database connectivity - Enhanced
    global $wpdb;
    try {
        $db_test = $wpdb->get_var("SELECT 1");
        $wpdb_error = $wpdb->last_error;
        
        $results['database'] = array(
            'test' => 'Database Connection',
            'status' => $db_test == 1 && empty($wpdb_error),
            'message' => $db_test == 1 ? 'Database connection active' : 'Database connection failed',
            'details' => empty($wpdb_error) ? 'No database errors' : 'Error: ' . $wpdb_error
        );
    } catch (Exception $e) {
        $results['database'] = array(
            'test' => 'Database Connection',
            'status' => false,
            'message' => 'Database connection exception: ' . $e->getMessage(),
            'details' => 'Check database configuration'
        );
    }
    
    // Test 5: User tables accessibility
    $user_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->users}");
    $meta_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->usermeta}");
    $db_error = $wpdb->last_error;
    
    $results['user_tables'] = array(
        'test' => 'User Tables Access',
        'status' => $user_count !== null && $meta_count !== null && empty($db_error),
        'message' => "Found $user_count users and $meta_count meta entries",
        'details' => empty($db_error) ? 'Tables accessible' : 'Error: ' . $db_error
    );
      // Test 6: Current user context
    $current_user_id = function_exists('get_current_user_id') ? get_current_user_id() : 0;
    $current_user = function_exists('wp_get_current_user') ? wp_get_current_user() : null;
    $user_credit = (function_exists('get_user_meta') && $current_user_id) ? get_user_meta($current_user_id, 'credits', true) : 0;
    
    $results['user_context'] = array(
        'test' => 'Current User Context',
        'status' => $current_user_id > 0,
        'message' => ($current_user_id > 0 && $current_user) ? 
            "User: {$current_user->display_name} (ID: $current_user_id)" : 
            'No user logged in',
        'details' => "Credit balance: €" . ($user_credit ? number_format((float)$user_credit, 2) : '0.00')
    );
    
    // Test 7: WordPress AJAX system
    $ajax_url = admin_url('admin-ajax.php');
    $results['wp_ajax'] = array(
        'test' => 'WordPress AJAX System',
        'status' => !empty($ajax_url) && function_exists('wp_create_nonce'),
        'message' => !empty($ajax_url) ? 'AJAX system available' : 'AJAX system not available',
        'details' => "AJAX URL: $ajax_url"
    );
    
    // Test 8: Required WordPress functions
    $required_functions = array(
        'wp_send_json_success', 'wp_send_json_error', 'wp_verify_nonce', 
        'wp_create_nonce', 'get_user_meta', 'update_user_meta'
    );
    
    $missing_functions = array();
    foreach ($required_functions as $func) {
        if (!function_exists($func)) {
            $missing_functions[] = $func;
        }
    }
    
    $results['wp_functions'] = array(
        'test' => 'Required WordPress Functions',
        'status' => empty($missing_functions),
        'message' => empty($missing_functions) ? 'All required functions available' : count($missing_functions) . ' functions missing',
        'details' => empty($missing_functions) ? 'WordPress core functions loaded' : 'Missing: ' . implode(', ', $missing_functions)
    );
    
    // Test 9: Payment gateway admin class (if available)
    $gateway_admin_exists = class_exists('Payment_Gateway_Admin');
    $results['gateway_admin'] = array(
        'test' => 'Payment Gateway Admin Class',
        'status' => $gateway_admin_exists,
        'message' => $gateway_admin_exists ? 'Payment Gateway Admin available' : 'Payment Gateway Admin not loaded',
        'details' => $gateway_admin_exists ? 'Full gateway management available' : 'Basic functionality only'
    );
    
    // Test 10: Error logging capability
    $error_log_works = function_exists('error_log');
    $results['error_logging'] = array(
        'test' => 'Error Logging System',
        'status' => $error_log_works,
        'message' => $error_log_works ? 'Error logging available' : 'Error logging not available',
        'details' => $error_log_works ? 'Debug information can be logged' : 'Cannot log debug information'
    );
    
    return $results;
}

function display_enhanced_test_results($results) {
    echo '<div class="test-results-container">';
    echo '<h2>🔍 Enhanced System Diagnostics</h2>';
    
    $passed = 0;
    $total = count($results);
    
    foreach ($results as $key => $result) {
        if ($result['status']) $passed++;
        
        $class = $result['status'] ? 'test-success' : 'test-error';
        $icon = $result['status'] ? '✅' : '❌';
        
        echo '<div class="enhanced-test-result ' . $class . '">';
        echo '<div class="test-header">';
        echo '<strong>' . $icon . ' ' . $result['test'] . '</strong>';
        echo '<span class="test-status">' . ($result['status'] ? 'PASS' : 'FAIL') . '</span>';
        echo '</div>';
        echo '<div class="test-message">' . $result['message'] . '</div>';
        if (isset($result['details'])) {
            echo '<div class="test-details">📋 ' . $result['details'] . '</div>';
        }
        echo '</div>';
    }
    
    // Summary
    $percentage = round(($passed / $total) * 100);
    $summary_class = $percentage >= 80 ? 'summary-good' : ($percentage >= 60 ? 'summary-warning' : 'summary-error');
    
    echo '<div class="test-summary ' . $summary_class . '">';
    echo '<h3>📊 Test Summary</h3>';
    echo '<p><strong>Passed:</strong> ' . $passed . ' / ' . $total . ' (' . $percentage . '%)</p>';
    
    if ($percentage >= 80) {
        echo '<p>🎉 <strong>System Status:</strong> Ready for payment processing!</p>';
    } elseif ($percentage >= 60) {
        echo '<p>⚠️ <strong>System Status:</strong> Functional with warnings</p>';
    } else {
        echo '<p>❌ <strong>System Status:</strong> Requires attention before use</p>';
    }
    echo '</div>';
    
    echo '</div>';
    
    // Enhanced CSS
    echo '<style>
    .test-results-container {
        margin: 20px 0;
    }
    
    .enhanced-test-result {
        margin: 10px 0;
        padding: 15px;
        border-radius: 6px;
        border-left: 4px solid;
    }
    
    .test-success {
        background: #f0f9ff;
        border-left-color: #10b981;
        color: #047857;
    }
    
    .test-error {
        background: #fef2f2;
        border-left-color: #ef4444;
        color: #dc2626;
    }
    
    .test-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
    }
    
    .test-status {
        font-size: 0.85em;
        font-weight: bold;
        padding: 2px 8px;
        border-radius: 12px;
        background: rgba(255,255,255,0.8);
    }
    
    .test-message {
        font-size: 0.95em;
        margin-bottom: 5px;
    }
    
    .test-details {
        font-size: 0.85em;
        opacity: 0.8;
        font-style: italic;
    }
    
    .test-summary {
        margin: 20px 0;
        padding: 20px;
        border-radius: 8px;
        text-align: center;
    }
    
    .summary-good {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
    }
    
    .summary-warning {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        color: white;
    }
    
    .summary-error {
        background: linear-gradient(135deg, #ef4444, #dc2626);
        color: white;
    }
    </style>';
}
?>
