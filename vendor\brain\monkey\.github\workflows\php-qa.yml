name: PHP Quality Assurance
on:
  push:
  # Allow manually triggering the workflow.
  workflow_dispatch:

# Cancels all previous workflow runs for the same branch that have not yet completed.
concurrency:
  # The concurrency group contains the workflow name and the branch name.
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
    qa:
        runs-on: ubuntu-latest
        if: "!contains(github.event.head_commit.message, 'ci skip')"
        strategy:
            fail-fast: true
            matrix:
                php-versions: ['5.6', '7.0', '7.1', '7.2', '7.3', '7.4', '8.0', '8.1', '8.2', '8.3']
                dependency-versions: ['lowest', 'highest']

                include:
                - php-versions: '8.4'
                  dependency-versions: 'highest'

        continue-on-error: ${{ matrix.php-versions == '8.4' }}

        steps:
            - uses: actions/checkout@v4

            - name: Setup PHP
              uses: shivammathur/setup-php@v2
              with:
                php-version: ${{ matrix.php-versions }}
                ini-values: zend.assertions=1, error_reporting=-1, display_errors=On
                coverage: ${{ ( matrix.php-versions == '7.4' && 'xdebug' ) || 'none' }}
                tools: parallel-lint
              env:
                fail-fast: true

            - name: Check syntax error in sources
              if: ${{ matrix.dependency-versions == 'highest' }}
              run: parallel-lint ./src/ ./tests/

            - name: Install dependencies - normal
              if: ${{ matrix.php-versions != '8.4' }}
              uses: "ramsey/composer-install@v3"
              with:
                dependency-versions: ${{ matrix.dependency-versions }}
                # Bust the cache at least once a month - output format: YYYY-MM.
                custom-cache-suffix: $(date -u "+%Y-%m")

            - name: Install dependencies - ignore-platform-reqs
              if: ${{ matrix.php-versions == '8.4' }}
              uses: "ramsey/composer-install@v3"
              with:
                dependency-versions: ${{ matrix.dependency-versions }}
                composer-options: "--ignore-platform-reqs"
                custom-cache-suffix: $(date -u "+%Y-%m")

            - name: Check cross-version PHP compatibility
              if: ${{ matrix.php-versions == '7.4' && matrix.dependency-versions == 'highest' }} # results is same across versions, do it once
              run: composer phpcompat

            - name: Migrate test configuration (>= 7.3)
              if: ${{ matrix.php-versions >= 7.3 && matrix.dependency-versions == 'highest' }}
              run: ./vendor/bin/phpunit --migrate-configuration

            - name: Run unit tests (without code coverage)
              if: ${{ matrix.php-versions != '7.4' || matrix.dependency-versions != 'highest' }}
              run: ./vendor/bin/phpunit

            - name: Run unit tests with code coverage
              if: ${{ matrix.php-versions == '7.4' && matrix.dependency-versions == 'highest' }}
              run: ./vendor/bin/phpunit --coverage-clover=coverage.xml

            - name: Update codecov.io
              uses: codecov/codecov-action@v4
              if: ${{ matrix.php-versions == '7.4' && matrix.dependency-versions == 'highest' }} # upload coverage once is enough
              env:
                CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
              with:
                file: ./coverage.xml
