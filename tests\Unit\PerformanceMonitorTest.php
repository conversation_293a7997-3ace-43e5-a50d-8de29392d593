<?php

namespace Tests\Unit;

use Tests\BaseTestCase;
use Mockery;

/**
 * Unit tests for Office_Addin_Performance_Monitor class
 */
class PerformanceMonitorTest extends BaseTestCase
{
    private $performance_monitor;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Include the performance monitor class
        require_once __DIR__ . '/../../includes/class-office-addin-performance-monitor.php';
        
        $this->performance_monitor = new \Office_Addin_Performance_Monitor();
    }

    public function testStartTimerInitializesTimer(): void
    {
        $operation = 'test_operation';
        
        $result = $this->performance_monitor->start_timer($operation);
        
        $this->assertTrue($result);
        
        // Verify timer is stored
        $timers = $this->getProperty($this->performance_monitor, 'timers');
        $this->assertArrayHasKey($operation, $timers);
        $this->assertIsFloat($timers[$operation]['start_time']);
        $this->assertNull($timers[$operation]['end_time']);
    }

    public function testStopTimerCompletesTimer(): void
    {
        $operation = 'test_operation';
        
        $this->performance_monitor->start_timer($operation);
        usleep(1000); // Sleep 1ms to ensure measurable time
        $result = $this->performance_monitor->stop_timer($operation);
        
        $this->assertIsFloat($result);
        $this->assertGreaterThan(0, $result);
        
        // Verify timer is completed
        $timers = $this->getProperty($this->performance_monitor, 'timers');
        $this->assertIsFloat($timers[$operation]['end_time']);
    }

    public function testStopTimerWithoutStartThrowsException(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Timer not found');
        
        $this->performance_monitor->stop_timer('nonexistent_timer');
    }

    public function testRecordMetricStoresInDatabase(): void
    {
        global $wpdb;
        
        $wpdb->shouldReceive('insert')
            ->once()
            ->with(
                'wp_office_addin_performance',
                \Mockery::subset([
                    'operation' => 'api_call',
                    'duration' => 1.5,
                    'memory_usage' => 1048576
                ])
            )
            ->andReturn(1);
        
        $result = $this->performance_monitor->record_metric(
            'api_call',
            1.5,
            1048576,
            ['endpoint' => '/analyze']
        );
        
        $this->assertTrue($result);
    }

    public function testGetMetricsRetrievesFromDatabase(): void
    {
        global $wpdb;
        
        $mock_metrics = [
            (object)[
                'id' => 1,
                'operation' => 'analyze',
                'duration' => 2.5,
                'memory_usage' => 2097152,
                'created_at' => '2024-01-01 12:00:00'
            ],
            (object)[
                'id' => 2,
                'operation' => 'settings',
                'duration' => 0.5,
                'memory_usage' => 524288,
                'created_at' => '2024-01-01 12:01:00'
            ]
        ];
        
        $wpdb->shouldReceive('get_results')
            ->once()
            ->andReturn($mock_metrics);
        
        $metrics = $this->performance_monitor->get_metrics();
        
        $this->assertIsArray($metrics);
        $this->assertCount(2, $metrics);
        $this->assertEquals('analyze', $metrics[0]->operation);
        $this->assertEquals(2.5, $metrics[0]->duration);
    }

    public function testGetMetricsWithFilters(): void
    {
        global $wpdb;
        
        $wpdb->shouldReceive('prepare')
            ->once()
            ->andReturn('prepared_query');
        
        $wpdb->shouldReceive('get_results')
            ->once()
            ->with('prepared_query')
            ->andReturn([]);
        
        $metrics = $this->performance_monitor->get_metrics([
            'operation' => 'analyze',
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'limit' => 100
        ]);
        
        $this->assertIsArray($metrics);
    }

    public function testGetStatsCalculatesAggregatedData(): void
    {
        global $wpdb;
        
        $mock_stats = [
            (object)[
                'operation' => 'analyze',
                'avg_duration' => 2.5,
                'min_duration' => 1.0,
                'max_duration' => 5.0,
                'total_calls' => 100,
                'avg_memory' => 2097152
            ]
        ];
        
        $wpdb->shouldReceive('get_results')
            ->once()
            ->andReturn($mock_stats);
        
        $stats = $this->performance_monitor->get_stats();
        
        $this->assertIsArray($stats);
        $this->assertArrayHasKey('operations', $stats);
        $this->assertEquals(2.5, $stats['operations']['analyze']['avg_duration']);
        $this->assertEquals(100, $stats['operations']['analyze']['total_calls']);
    }

    public function testGetStatsByOperation(): void
    {
        global $wpdb;
        
        $wpdb->shouldReceive('prepare')
            ->once()
            ->with(
                \Mockery::pattern('/WHERE operation = %s/'),
                'analyze'
            )
            ->andReturn('prepared_query');
        
        $wpdb->shouldReceive('get_row')
            ->once()
            ->with('prepared_query')
            ->andReturn((object)[
                'avg_duration' => 2.3,
                'min_duration' => 0.5,
                'max_duration' => 8.2,
                'total_calls' => 250
            ]);
        
        $stats = $this->performance_monitor->get_operation_stats('analyze');
        
        $this->assertIsObject($stats);
        $this->assertEquals(2.3, $stats->avg_duration);
        $this->assertEquals(250, $stats->total_calls);
    }

    public function testMeasureCallbackExecutionTime(): void
    {
        $test_function = function() {
            usleep(1000); // Sleep 1ms
            return 'test_result';
        };
        
        global $wpdb;
        $wpdb->shouldReceive('insert')->once()->andReturn(1);
        
        $result = $this->performance_monitor->measure('test_callback', $test_function);
        
        $this->assertEquals('test_result', $result);
    }

    public function testGetSlowOperationsIdentifiesBottlenecks(): void
    {
        global $wpdb;
        
        $mock_slow_ops = [
            (object)[
                'operation' => 'heavy_analysis',
                'avg_duration' => 15.2,
                'max_duration' => 30.5,
                'total_calls' => 10
            ],
            (object)[
                'operation' => 'large_document',
                'avg_duration' => 8.7,
                'max_duration' => 12.1,
                'total_calls' => 25
            ]
        ];
        
        $wpdb->shouldReceive('get_results')
            ->once()
            ->andReturn($mock_slow_ops);
        
        $slow_ops = $this->performance_monitor->get_slow_operations(5.0); // Threshold 5 seconds
        
        $this->assertIsArray($slow_ops);
        $this->assertCount(2, $slow_ops);
        $this->assertEquals('heavy_analysis', $slow_ops[0]->operation);
    }

    public function testGetRecommendationsBasedOnMetrics(): void
    {
        // Mock various performance scenarios
        global $wpdb;
        
        $wpdb->shouldReceive('get_results')
            ->andReturn([
                (object)['operation' => 'slow_op', 'avg_duration' => 10.0],
                (object)['operation' => 'memory_heavy', 'avg_memory' => 104857600] // 100MB
            ]);
        
        $recommendations = $this->performance_monitor->get_recommendations();
        
        $this->assertIsArray($recommendations);
        $this->assertNotEmpty($recommendations);
    }

    public function testCleanupOldMetricsRemovesExpiredData(): void
    {
        global $wpdb;
        
        $wpdb->shouldReceive('query')
            ->once()
            ->andReturn(50); // 50 entries removed
        
        $result = $this->performance_monitor->cleanup(7); // Clean older than 7 days
        
        $this->assertEquals(50, $result);
    }

    public function testMemoryUsageTracking(): void
    {
        $initial_memory = memory_get_usage(true);
        
        $memory = $this->callMethod($this->performance_monitor, 'get_memory_usage');
        
        $this->assertIsInt($memory);
        $this->assertGreaterThanOrEqual($initial_memory, $memory);
    }

    public function testPeakMemoryTracking(): void
    {
        $peak_memory = $this->callMethod($this->performance_monitor, 'get_peak_memory_usage');
        
        $this->assertIsInt($peak_memory);
        $this->assertGreaterThan(0, $peak_memory);
    }

    public function testAlertThresholdChecking(): void
    {
        // Mock that an operation exceeds threshold
        global $wpdb;
        $wpdb->shouldReceive('insert')->once()->andReturn(1);
        
        // Mock email notification for slow operation
        \Brain\Monkey\Functions\when('wp_mail')->justReturn(true);
        \Brain\Monkey\Functions\when('get_option')
            ->andReturnUsing(function($option) {
                return $option === 'admin_email' ? '<EMAIL>' : false;
            });
        
        $result = $this->performance_monitor->check_thresholds('slow_operation', 15.0, 104857600);
        
        $this->assertTrue($result);
    }

    public function testExportMetricsToJson(): void
    {
        global $wpdb;
        
        $mock_metrics = [
            (object)[
                'operation' => 'test',
                'duration' => 1.5,
                'memory_usage' => 1048576,
                'created_at' => '2024-01-01 12:00:00'
            ]
        ];
        
        $wpdb->shouldReceive('get_results')
            ->once()
            ->andReturn($mock_metrics);
        
        $json = $this->performance_monitor->export_metrics('json');
        
        $this->assertIsString($json);
        $this->assertJson($json);
        
        $decoded = json_decode($json, true);
        $this->assertIsArray($decoded);
        $this->assertCount(1, $decoded);
    }

    public function testExportMetricsToCsv(): void
    {
        global $wpdb;
        
        $mock_metrics = [
            (object)[
                'operation' => 'test',
                'duration' => 1.5,
                'memory_usage' => 1048576,
                'created_at' => '2024-01-01 12:00:00'
            ]
        ];
        
        $wpdb->shouldReceive('get_results')
            ->once()
            ->andReturn($mock_metrics);
        
        $csv = $this->performance_monitor->export_metrics('csv');
        
        $this->assertIsString($csv);
        $this->assertStringContainsString('operation,duration,memory_usage', $csv);
        $this->assertStringContainsString('test,1.5,1048576', $csv);
    }

    public function testPerformanceProfiler(): void
    {
        $profiler_data = $this->performance_monitor->start_profiler();
        
        // Simulate some work
        usleep(1000);
        $dummy_array = range(1, 1000);
        unset($dummy_array);
        
        $profile = $this->performance_monitor->stop_profiler($profiler_data);
        
        $this->assertIsArray($profile);
        $this->assertArrayHasKey('duration', $profile);
        $this->assertArrayHasKey('memory_start', $profile);
        $this->assertArrayHasKey('memory_end', $profile);
        $this->assertArrayHasKey('memory_peak', $profile);
        
        $this->assertGreaterThan(0, $profile['duration']);
    }

    public function testBenchmarkComparison(): void
    {
        $benchmark_results = [
            'operation_a' => 2.5,
            'operation_b' => 1.2,
            'operation_c' => 3.8
        ];
        
        $comparison = $this->callMethod(
            $this->performance_monitor,
            'compare_with_baseline',
            [$benchmark_results]
        );
        
        $this->assertIsArray($comparison);
    }

    public function testAutoOptimizationSuggestions(): void
    {
        global $wpdb;
        
        // Mock data showing patterns that suggest optimizations
        $wpdb->shouldReceive('get_results')
            ->andReturn([
                (object)['operation' => 'db_query', 'avg_duration' => 5.0, 'total_calls' => 1000],
                (object)['operation' => 'api_call', 'avg_duration' => 3.0, 'total_calls' => 500]
            ]);
        
        $suggestions = $this->performance_monitor->get_optimization_suggestions();
        
        $this->assertIsArray($suggestions);
    }

    public function testRealTimeMonitoring(): void
    {
        $monitor_data = $this->performance_monitor->get_realtime_metrics();
        
        $this->assertIsArray($monitor_data);
        $this->assertArrayHasKey('current_memory', $monitor_data);
        $this->assertArrayHasKey('peak_memory', $monitor_data);
        $this->assertArrayHasKey('active_timers', $monitor_data);
    }
}
