<?php return array(
    'root' => array(
        'name' => 'financial-advisor/document-viewer',
        'pretty_version' => '1.0.0',
        'version' => '1.0.0.0',
        'reference' => null,
        'type' => 'wordpress-plugin',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'antecedent/patchwork' => array(
            'pretty_version' => '2.2.1',
            'version' => '2.2.1.0',
            'reference' => '1bf183a3e1bd094f231a2128b9ecc5363c269245',
            'type' => 'library',
            'install_path' => __DIR__ . '/../antecedent/patchwork',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'brain/monkey' => array(
            'pretty_version' => '2.6.2',
            'version' => '2.6.2.0',
            'reference' => 'd95a9d895352c30f47604ad1b825ab8fa9d1a373',
            'type' => 'library',
            'install_path' => __DIR__ . '/../brain/monkey',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'composer/ca-bundle' => array(
            'pretty_version' => '1.5.6',
            'version' => '1.5.6.0',
            'reference' => 'f65c239c970e7f072f067ab78646e9f0b2935175',
            'type' => 'library',
            'install_path' => __DIR__ . '/./ca-bundle',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'composer/class-map-generator' => array(
            'pretty_version' => '1.6.1',
            'version' => '1.6.1.0',
            'reference' => '134b705ddb0025d397d8318a75825fe3c9d1da34',
            'type' => 'library',
            'install_path' => __DIR__ . '/./class-map-generator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'composer/composer' => array(
            'pretty_version' => '2.8.9',
            'version' => '2.8.9.0',
            'reference' => 'b4e6bff2db7ce756ddb77ecee958a0f41f42bd9d',
            'type' => 'library',
            'install_path' => __DIR__ . '/./composer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'composer/metadata-minifier' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'c549d23829536f0d0e984aaabbf02af91f443207',
            'type' => 'library',
            'install_path' => __DIR__ . '/./metadata-minifier',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'composer/pcre' => array(
            'pretty_version' => '3.3.2',
            'version' => '3.3.2.0',
            'reference' => 'b2bed4734f0cc156ee1fe9c0da2550420d99a21e',
            'type' => 'library',
            'install_path' => __DIR__ . '/./pcre',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'composer/semver' => array(
            'pretty_version' => '3.4.3',
            'version' => '3.4.3.0',
            'reference' => '4313d26ada5e0c4edfbd1dc481a92ff7bff91f12',
            'type' => 'library',
            'install_path' => __DIR__ . '/./semver',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'composer/spdx-licenses' => array(
            'pretty_version' => '1.5.9',
            'version' => '1.5.9.0',
            'reference' => 'edf364cefe8c43501e21e88110aac10b284c3c9f',
            'type' => 'library',
            'install_path' => __DIR__ . '/./spdx-licenses',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'composer/xdebug-handler' => array(
            'pretty_version' => '3.0.5',
            'version' => '3.0.5.0',
            'reference' => '6c1925561632e83d60a44492e0b344cf48ab85ef',
            'type' => 'library',
            'install_path' => __DIR__ . '/./xdebug-handler',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'cordoval/hamcrest-php' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'davedevelopment/hamcrest-php' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'doctrine/instantiator' => array(
            'pretty_version' => '2.0.0',
            'version' => '2.0.0.0',
            'reference' => 'c6222283fa3f4ac679f8b9ced9a4e23f163e80d0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/instantiator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'eftec/bladeone' => array(
            'pretty_version' => '3.52',
            'version' => '3.52.0.0',
            'reference' => 'a19bf66917de0b29836983db87a455a4f6e32148',
            'type' => 'library',
            'install_path' => __DIR__ . '/../eftec/bladeone',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'financial-advisor/document-viewer' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => null,
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'gettext/gettext' => array(
            'pretty_version' => 'v4.8.12',
            'version' => '4.8.12.0',
            'reference' => '11af89ee6c087db3cf09ce2111a150bca5c46e12',
            'type' => 'library',
            'install_path' => __DIR__ . '/../gettext/gettext',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'gettext/languages' => array(
            'pretty_version' => '2.12.1',
            'version' => '2.12.1.0',
            'reference' => '0b0b0851c55168e1dfb14305735c64019732b5f1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../gettext/languages',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'hamcrest/hamcrest-php' => array(
            'pretty_version' => 'v2.1.1',
            'version' => '2.1.1.0',
            'reference' => 'f8b1c0173b22fa6ec77a81fe63e5b01eba7e6487',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hamcrest/hamcrest-php',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'justinrainbow/json-schema' => array(
            'pretty_version' => '6.4.1',
            'version' => '6.4.1.0',
            'reference' => '35d262c94959571e8736db1e5c9bc36ab94ae900',
            'type' => 'library',
            'install_path' => __DIR__ . '/../justinrainbow/json-schema',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'kodova/hamcrest-php' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'marc-mabe/php-enum' => array(
            'pretty_version' => 'v4.7.1',
            'version' => '4.7.1.0',
            'reference' => '7159809e5cfa041dca28e61f7f7ae58063aae8ed',
            'type' => 'library',
            'install_path' => __DIR__ . '/../marc-mabe/php-enum',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'mck89/peast' => array(
            'pretty_version' => 'v1.17.0',
            'version' => '1.17.0.0',
            'reference' => '3a752d39bd7d8dc1e19bcf424f3d5ac1a1ca6ad5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mck89/peast',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'mockery/mockery' => array(
            'pretty_version' => '1.6.12',
            'version' => '1.6.12.0',
            'reference' => '1f4efdd7d3beafe9807b08156dfcb176d18f1699',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mockery/mockery',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'mpdf/mpdf' => array(
            'pretty_version' => 'v8.2.5',
            'version' => '8.2.5.0',
            'reference' => 'e175b05e3e00977b85feb96a8cccb174ac63621f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mpdf/mpdf',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mpdf/psr-http-message-shim' => array(
            'pretty_version' => 'v2.0.1',
            'version' => '2.0.1.0',
            'reference' => 'f25a0153d645e234f9db42e5433b16d9b113920f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mpdf/psr-http-message-shim',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mpdf/psr-log-aware-trait' => array(
            'pretty_version' => 'v3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'a633da6065e946cc491e1c962850344bb0bf3e78',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mpdf/psr-log-aware-trait',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mustache/mustache' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '^2.14.2',
            ),
        ),
        'myclabs/deep-copy' => array(
            'pretty_version' => '1.13.1',
            'version' => '1.13.1.0',
            'reference' => '1720ddd719e16cf0db4eb1c6eca108031636d46c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/deep-copy',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nb/oxymel' => array(
            'pretty_version' => 'v0.1.0',
            'version' => '0.1.0.0',
            'reference' => 'cbe626ef55d5c4cc9b5e6e3904b395861ea76e3c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nb/oxymel',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'nikic/php-parser' => array(
            'pretty_version' => 'v5.4.0',
            'version' => '5.4.0.0',
            'reference' => '447a020a1f875a434d62f2a401f53b82a396e494',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nikic/php-parser',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'paragonie/random_compat' => array(
            'pretty_version' => 'v9.99.100',
            'version' => '9.99.100.0',
            'reference' => '996434e5492cb4c3edcb9168db6fbb1359ef965a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/random_compat',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phar-io/manifest' => array(
            'pretty_version' => '2.0.4',
            'version' => '2.0.4.0',
            'reference' => '54750ef60c58e43759730615a392c31c80e23176',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phar-io/manifest',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phar-io/version' => array(
            'pretty_version' => '3.2.1',
            'version' => '3.2.1.0',
            'reference' => '4f7fd7836c6f332bb2933569e566a0d6c4cbed74',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phar-io/version',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpoffice/math' => array(
            'pretty_version' => '0.2.0',
            'version' => '0.2.0.0',
            'reference' => 'fc2eb6d1a61b058d5dac77197059db30ee3c8329',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoffice/math',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpoffice/phpword' => array(
            'pretty_version' => '1.3.0',
            'version' => '1.3.0.0',
            'reference' => '8392134ce4b5dba65130ba956231a1602b848b7f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoffice/phpword',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpunit/php-code-coverage' => array(
            'pretty_version' => '9.2.32',
            'version' => '9.2.32.0',
            'reference' => '85402a822d1ecf1db1096959413d35e1c37cf1a5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-code-coverage',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-file-iterator' => array(
            'pretty_version' => '3.0.6',
            'version' => '3.0.6.0',
            'reference' => 'cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-file-iterator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-invoker' => array(
            'pretty_version' => '3.1.1',
            'version' => '3.1.1.0',
            'reference' => '5a10147d0aaf65b58940a0b72f71c9ac0423cc67',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-invoker',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-text-template' => array(
            'pretty_version' => '2.0.4',
            'version' => '2.0.4.0',
            'reference' => '5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-text-template',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-timer' => array(
            'pretty_version' => '5.0.3',
            'version' => '5.0.3.0',
            'reference' => '5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-timer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/phpunit' => array(
            'pretty_version' => '9.6.23',
            'version' => '9.6.23.0',
            'reference' => '43d2cb18d0675c38bd44982a5d1d88f6d53d8d95',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/phpunit',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'f16e1d5863e37f8d8c2a01719f5b34baa2b714d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => true,
            'provided' => array(
                0 => '1.0|2.0|3.0',
            ),
        ),
        'react/promise' => array(
            'pretty_version' => 'v3.2.0',
            'version' => '3.2.0.0',
            'reference' => '8a164643313c71354582dc850b42b33fa12a4b63',
            'type' => 'library',
            'install_path' => __DIR__ . '/../react/promise',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/cli-parser' => array(
            'pretty_version' => '1.0.2',
            'version' => '1.0.2.0',
            'reference' => '2b56bea83a09de3ac06bb18b92f068e60cc6f50b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/cli-parser',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/code-unit' => array(
            'pretty_version' => '1.0.8',
            'version' => '1.0.8.0',
            'reference' => '1fc9f64c0927627ef78ba436c9b17d967e68e120',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/code-unit',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/code-unit-reverse-lookup' => array(
            'pretty_version' => '2.0.3',
            'version' => '2.0.3.0',
            'reference' => 'ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/code-unit-reverse-lookup',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/comparator' => array(
            'pretty_version' => '4.0.8',
            'version' => '4.0.8.0',
            'reference' => 'fa0f136dd2334583309d32b62544682ee972b51a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/comparator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/complexity' => array(
            'pretty_version' => '2.0.3',
            'version' => '2.0.3.0',
            'reference' => '25f207c40d62b8b7aa32f5ab026c53561964053a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/complexity',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/diff' => array(
            'pretty_version' => '4.0.6',
            'version' => '4.0.6.0',
            'reference' => 'ba01945089c3a293b01ba9badc29ad55b106b0bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/diff',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/environment' => array(
            'pretty_version' => '5.1.5',
            'version' => '5.1.5.0',
            'reference' => '830c43a844f1f8d5b7a1f6d6076b784454d8b7ed',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/environment',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/exporter' => array(
            'pretty_version' => '4.0.6',
            'version' => '4.0.6.0',
            'reference' => '78c00df8f170e02473b682df15bfcdacc3d32d72',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/exporter',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/global-state' => array(
            'pretty_version' => '5.0.7',
            'version' => '5.0.7.0',
            'reference' => 'bca7df1f32ee6fe93b4d4a9abbf69e13a4ada2c9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/global-state',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/lines-of-code' => array(
            'pretty_version' => '1.0.4',
            'version' => '1.0.4.0',
            'reference' => 'e1e4a170560925c26d424b6a03aed157e7dcc5c5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/lines-of-code',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/object-enumerator' => array(
            'pretty_version' => '4.0.4',
            'version' => '4.0.4.0',
            'reference' => '5c9eeac41b290a3712d88851518825ad78f45c71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/object-enumerator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/object-reflector' => array(
            'pretty_version' => '2.0.4',
            'version' => '2.0.4.0',
            'reference' => 'b4f479ebdbf63ac605d183ece17d8d7fe49c15c7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/object-reflector',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/recursion-context' => array(
            'pretty_version' => '4.0.5',
            'version' => '4.0.5.0',
            'reference' => 'e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/recursion-context',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/resource-operations' => array(
            'pretty_version' => '3.0.4',
            'version' => '3.0.4.0',
            'reference' => '05d5692a7993ecccd56a03e40cd7e5b09b1d404e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/resource-operations',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/type' => array(
            'pretty_version' => '3.2.1',
            'version' => '3.2.1.0',
            'reference' => '75e2c2a32f5e0b3aef905b9ed0b179b953b3d7c7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/type',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/version' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'c6c1022351a901512170118436c764e473f6de8c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/version',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'seld/jsonlint' => array(
            'pretty_version' => '1.11.0',
            'version' => '1.11.0.0',
            'reference' => '1748aaf847fc731cfad7725aec413ee46f0cc3a2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../seld/jsonlint',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'seld/phar-utils' => array(
            'pretty_version' => '1.2.1',
            'version' => '1.2.1.0',
            'reference' => 'ea2f4014f163c1be4c601b9b7bd6af81ba8d701c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../seld/phar-utils',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'seld/signal-handler' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => '04a6112e883ad76c0ada8e4a9f7520bbfdb6bb98',
            'type' => 'library',
            'install_path' => __DIR__ . '/../seld/signal-handler',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'setasign/fpdi' => array(
            'pretty_version' => 'v2.6.3',
            'version' => '2.6.3.0',
            'reference' => '67c31f5e50c93c20579ca9e23035d8c540b51941',
            'type' => 'library',
            'install_path' => __DIR__ . '/../setasign/fpdi',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'smalot/pdfparser' => array(
            'pretty_version' => 'v2.12.0',
            'version' => '2.12.0.0',
            'reference' => '8440edbf58c8596074e78ada38dcb0bd041a5948',
            'type' => 'library',
            'install_path' => __DIR__ . '/../smalot/pdfparser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/console' => array(
            'pretty_version' => 'v6.4.21',
            'version' => '6.4.21.0',
            'reference' => 'a3011c7b7adb58d89f6c0d822abb641d7a5f9719',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/console',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => '74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/filesystem' => array(
            'pretty_version' => 'v7.2.0',
            'version' => '7.2.0.0',
            'reference' => 'b8dce482de9d7c9fe2891155035a7248ab5c7fdb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/filesystem',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/finder' => array(
            'pretty_version' => 'v7.2.2',
            'version' => '7.2.2.0',
            'reference' => '87a71856f2f56e4100373e92529eed3171695cfb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/finder',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => 'a3cc8b044a6ea513310cbd48ef7333b384945638',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-intl-grapheme' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => 'b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-grapheme',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '3833d7255cc303546435cb650316bff708a1c75c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '6d857f4d76bd4b343eac26d6b539585d2bc56493',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php73' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '0f68c03565dcaaf25a890667542e8bd75fe7e5bb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php73',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '0cc9dd0f17f61d8131e7df6b84bd344899fe2608',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/polyfill-php81' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php81',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/process' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '^5.4.47',
            ),
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => 'e53260aabf78fb3d63f8d79d69ece59f80d5eda0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'symfony/string' => array(
            'pretty_version' => 'v7.2.6',
            'version' => '7.2.6.0',
            'reference' => 'a214fe7d62bd4df2a76447c67c6b26e1d5e74931',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/string',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'theseer/tokenizer' => array(
            'pretty_version' => '1.2.3',
            'version' => '1.2.3.0',
            'reference' => '737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../theseer/tokenizer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'thiagoalessio/tesseract_ocr' => array(
            'pretty_version' => '2.13.0',
            'version' => '2.13.0.0',
            'reference' => '232a8cb9d571992f9bd1e263f2f6909cf6c173a1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../thiagoalessio/tesseract_ocr',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'wp-cli/cache-command' => array(
            'pretty_version' => 'v2.2.0',
            'version' => '2.2.0.0',
            'reference' => '14f76b0bc8f9fa0a680e9c70e18fcf627774d055',
            'type' => 'wp-cli-package',
            'install_path' => __DIR__ . '/../wp-cli/cache-command',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/checksum-command' => array(
            'pretty_version' => 'v2.3.1',
            'version' => '2.3.1.0',
            'reference' => '39992dbd66835f8d5c2cc5bfeacf9d2c450cbafe',
            'type' => 'wp-cli-package',
            'install_path' => __DIR__ . '/../wp-cli/checksum-command',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/config-command' => array(
            'pretty_version' => 'v2.3.8',
            'version' => '2.3.8.0',
            'reference' => '994b3dc9e8284fc978366920d5c5ae0dde3a004e',
            'type' => 'wp-cli-package',
            'install_path' => __DIR__ . '/../wp-cli/config-command',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/core-command' => array(
            'pretty_version' => 'v2.1.20',
            'version' => '2.1.20.0',
            'reference' => '83e4692784a815bb7f5df10b72204f237b5224b9',
            'type' => 'wp-cli-package',
            'install_path' => __DIR__ . '/../wp-cli/core-command',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/cron-command' => array(
            'pretty_version' => 'v2.3.2',
            'version' => '2.3.2.0',
            'reference' => '6f450028a75ebd275f12cad62959a0709bf3e7c1',
            'type' => 'wp-cli-package',
            'install_path' => __DIR__ . '/../wp-cli/cron-command',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/db-command' => array(
            'pretty_version' => 'v2.1.3',
            'version' => '2.1.3.0',
            'reference' => 'f857c91454d7092fa672bc388512a51752d9264a',
            'type' => 'wp-cli-package',
            'install_path' => __DIR__ . '/../wp-cli/db-command',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/embed-command' => array(
            'pretty_version' => 'v2.0.18',
            'version' => '2.0.18.0',
            'reference' => '52f59a1dacf1d4a1c68fd685f27909e1f493816b',
            'type' => 'wp-cli-package',
            'install_path' => __DIR__ . '/../wp-cli/embed-command',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/entity-command' => array(
            'pretty_version' => 'v2.8.4',
            'version' => '2.8.4.0',
            'reference' => '213611f8ab619ca137d983e9b987f7fbf1ac21d4',
            'type' => 'wp-cli-package',
            'install_path' => __DIR__ . '/../wp-cli/entity-command',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/eval-command' => array(
            'pretty_version' => 'v2.2.6',
            'version' => '2.2.6.0',
            'reference' => '20ec428a7b9bc604fab0bd33ee8fa20662650455',
            'type' => 'wp-cli-package',
            'install_path' => __DIR__ . '/../wp-cli/eval-command',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/export-command' => array(
            'pretty_version' => 'v2.1.14',
            'version' => '2.1.14.0',
            'reference' => '2af32bf12c1bccd6561a215dbbafc2f272647ee8',
            'type' => 'wp-cli-package',
            'install_path' => __DIR__ . '/../wp-cli/export-command',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/extension-command' => array(
            'pretty_version' => 'v2.1.24',
            'version' => '2.1.24.0',
            'reference' => 'd21a2f504ac43a86b6b08697669b5b0844748133',
            'type' => 'wp-cli-package',
            'install_path' => __DIR__ . '/../wp-cli/extension-command',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/i18n-command' => array(
            'pretty_version' => 'v2.6.5',
            'version' => '2.6.5.0',
            'reference' => '5e73d417398993625331a9f69f6c2ef60f234070',
            'type' => 'wp-cli-package',
            'install_path' => __DIR__ . '/../wp-cli/i18n-command',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/import-command' => array(
            'pretty_version' => 'v2.0.14',
            'version' => '2.0.14.0',
            'reference' => 'b2c48f3e51683e825738df62bf8ccc7004c5f0f9',
            'type' => 'wp-cli-package',
            'install_path' => __DIR__ . '/../wp-cli/import-command',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/language-command' => array(
            'pretty_version' => 'v2.0.23',
            'version' => '2.0.23.0',
            'reference' => '7221cc39d2b14fd39e55aa7884889f26eec2f822',
            'type' => 'wp-cli-package',
            'install_path' => __DIR__ . '/../wp-cli/language-command',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/maintenance-mode-command' => array(
            'pretty_version' => 'v2.1.3',
            'version' => '2.1.3.0',
            'reference' => 'b947e094e00b7b68c6376ec9bd03303515864062',
            'type' => 'wp-cli-package',
            'install_path' => __DIR__ . '/../wp-cli/maintenance-mode-command',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/media-command' => array(
            'pretty_version' => 'v2.2.2',
            'version' => '2.2.2.0',
            'reference' => 'a810ea0e68473fce6a234e67c6c5f19bb820a753',
            'type' => 'wp-cli-package',
            'install_path' => __DIR__ . '/../wp-cli/media-command',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/mustache' => array(
            'pretty_version' => 'v2.14.99',
            'version' => '2.14.99.0',
            'reference' => 'ca23b97ac35fbe01c160549eb634396183d04a59',
            'type' => 'library',
            'install_path' => __DIR__ . '/../wp-cli/mustache',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/mustangostang-spyc' => array(
            'pretty_version' => '0.6.3',
            'version' => '0.6.3.0',
            'reference' => '6aa0b4da69ce9e9a2c8402dab8d43cf32c581cc7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../wp-cli/mustangostang-spyc',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/package-command' => array(
            'pretty_version' => 'v2.6.0',
            'version' => '2.6.0.0',
            'reference' => '682d8c6bb30c782c3b09c015478c7cbe1cc727a9',
            'type' => 'wp-cli-package',
            'install_path' => __DIR__ . '/../wp-cli/package-command',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/php-cli-tools' => array(
            'pretty_version' => 'v0.12.5',
            'version' => '0.12.5.0',
            'reference' => '34b83b4f700df8a4ec3fd17bf7e7e7d8ca5f28da',
            'type' => 'library',
            'install_path' => __DIR__ . '/../wp-cli/php-cli-tools',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/process' => array(
            'pretty_version' => 'v5.9.99',
            'version' => '5.9.99.0',
            'reference' => 'f0aec5ca26a702d3157e3a19982b662521ac2b81',
            'type' => 'library',
            'install_path' => __DIR__ . '/../wp-cli/process',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/rewrite-command' => array(
            'pretty_version' => 'v2.0.15',
            'version' => '2.0.15.0',
            'reference' => '277ec689b7c268680ff429f52558508622c9b34c',
            'type' => 'wp-cli-package',
            'install_path' => __DIR__ . '/../wp-cli/rewrite-command',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/role-command' => array(
            'pretty_version' => 'v2.0.16',
            'version' => '2.0.16.0',
            'reference' => 'ed57fb5436b4d47954b07e56c734d19deb4fc491',
            'type' => 'wp-cli-package',
            'install_path' => __DIR__ . '/../wp-cli/role-command',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/scaffold-command' => array(
            'pretty_version' => 'v2.5.0',
            'version' => '2.5.0.0',
            'reference' => 'b4238ea12e768b3f15d10339a53a8642f82e1d2b',
            'type' => 'wp-cli-package',
            'install_path' => __DIR__ . '/../wp-cli/scaffold-command',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/search-replace-command' => array(
            'pretty_version' => 'v2.1.8',
            'version' => '2.1.8.0',
            'reference' => '65397a7bfdd5ba2cff26f3ab03ef0bcb916c0057',
            'type' => 'wp-cli-package',
            'install_path' => __DIR__ . '/../wp-cli/search-replace-command',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/server-command' => array(
            'pretty_version' => 'v2.0.15',
            'version' => '2.0.15.0',
            'reference' => '80a9243f94e0ac073f9bfdb516d2ac7e1fa01a71',
            'type' => 'wp-cli-package',
            'install_path' => __DIR__ . '/../wp-cli/server-command',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/shell-command' => array(
            'pretty_version' => 'v2.0.16',
            'version' => '2.0.16.0',
            'reference' => '3af53a9f4b240e03e77e815b2ee10f229f1aa591',
            'type' => 'wp-cli-package',
            'install_path' => __DIR__ . '/../wp-cli/shell-command',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/super-admin-command' => array(
            'pretty_version' => 'v2.0.16',
            'version' => '2.0.16.0',
            'reference' => '54ac063c384743ee414806d42cb8c61c6aa1fa8e',
            'type' => 'wp-cli-package',
            'install_path' => __DIR__ . '/../wp-cli/super-admin-command',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/widget-command' => array(
            'pretty_version' => 'v2.1.12',
            'version' => '2.1.12.0',
            'reference' => '73084053f7b32d92583e44d870b81f287beea6a9',
            'type' => 'wp-cli-package',
            'install_path' => __DIR__ . '/../wp-cli/widget-command',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/wp-cli' => array(
            'pretty_version' => 'v2.12.0',
            'version' => '2.12.0.0',
            'reference' => '03d30d4138d12b4bffd8b507b82e56e129e0523f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../wp-cli/wp-cli',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/wp-cli-bundle' => array(
            'pretty_version' => 'v2.12.0',
            'version' => '2.12.0.0',
            'reference' => 'd639a3dab65f4b935b21c61ea3662bf3258a03a5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../wp-cli/wp-cli-bundle',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'wp-cli/wp-config-transformer' => array(
            'pretty_version' => 'v1.4.2',
            'version' => '1.4.2.0',
            'reference' => 'b78cab1159b43eb5ee097e2cfafe5eab573d2a8a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../wp-cli/wp-config-transformer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
    ),
);
