<?php
/**
 * Template per la pagina di amministrazione del Widget Help System
 */

if (!defined('ABSPATH')) {
    exit;
}

$widget_help_manager = Widget_Help_Manager::get_instance();
$registered_widgets = $widget_help_manager->get_registered_widgets();
$widget_helps = get_option('widget_help_content', array());
?>

<div class="wrap">
    <h1><?php _e('Widget Help System', 'document-viewer-plugin'); ?></h1>
    
    <div class="nav-tab-wrapper">
        <?php foreach ($registered_widgets as $widget_id => $config): ?>
            <a href="#tab-<?php echo esc_attr($widget_id); ?>" 
               class="nav-tab <?php echo array_keys($registered_widgets)[0] === $widget_id ? 'nav-tab-active' : ''; ?>"
               data-widget="<?php echo esc_attr($widget_id); ?>">
                <?php echo esc_html($config['name']); ?>
            </a>
        <?php endforeach; ?>
    </div>
    
    <form method="post" action="options.php" id="widget-help-form">
        <?php settings_fields('widget_help_settings'); ?>
        
        <?php foreach ($registered_widgets as $widget_id => $config): ?>
            <div id="tab-<?php echo esc_attr($widget_id); ?>" 
                 class="tab-content <?php echo array_keys($registered_widgets)[0] === $widget_id ? 'active' : ''; ?>">
                
                <div class="widget-help-section">
                    <h2><?php echo esc_html($config['name']); ?></h2>
                    <p class="description"><?php echo esc_html($config['description']); ?></p>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="<?php echo esc_attr($widget_id); ?>_enabled">
                                    <?php _e('Abilita Help', 'document-viewer-plugin'); ?>
                                </label>
                            </th>
                            <td>
                                <input type="checkbox" 
                                       id="<?php echo esc_attr($widget_id); ?>_enabled"
                                       name="widget_help[<?php echo esc_attr($widget_id); ?>][enabled]"
                                       value="1"
                                       <?php checked(!empty($widget_helps[$widget_id]['enabled'])); ?>>
                                <p class="description">
                                    <?php _e('Abilita/disabilita l\'help per questo widget', 'document-viewer-plugin'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="<?php echo esc_attr($widget_id); ?>_position">
                                    <?php _e('Posizione Help', 'document-viewer-plugin'); ?>
                                </label>
                            </th>
                            <td>
                                <?php 
                                $current_position = isset($widget_helps[$widget_id]['position']) 
                                    ? $widget_helps[$widget_id]['position'] 
                                    : $config['position'];
                                ?>
                                <select id="<?php echo esc_attr($widget_id); ?>_position"
                                        name="widget_help[<?php echo esc_attr($widget_id); ?>][position]">
                                    <option value="right" <?php selected($current_position, 'right'); ?>>
                                        <?php _e('Destra', 'document-viewer-plugin'); ?>
                                    </option>
                                    <option value="left" <?php selected($current_position, 'left'); ?>>
                                        <?php _e('Sinistra', 'document-viewer-plugin'); ?>
                                    </option>
                                    <option value="bottom" <?php selected($current_position, 'bottom'); ?>>
                                        <?php _e('Basso', 'document-viewer-plugin'); ?>
                                    </option>
                                    <option value="center" <?php selected($current_position, 'center'); ?>>
                                        <?php _e('Centro (Modal)', 'document-viewer-plugin'); ?>
                                    </option>
                                </select>
                                <p class="description">
                                    <?php _e('Posizione del pannello help nella pagina', 'document-viewer-plugin'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="<?php echo esc_attr($widget_id); ?>_content">
                                    <?php _e('Contenuto Help', 'document-viewer-plugin'); ?>
                                </label>
                            </th>
                            <td>
                                <?php
                                $help_content = isset($widget_helps[$widget_id]['content']) 
                                    ? $widget_helps[$widget_id]['content'] 
                                    : $config['default_content'];
                                
                                $editor_settings = array(
                                    'textarea_name' => "widget_help[{$widget_id}][content]",
                                    'textarea_rows' => 12,
                                    'media_buttons' => false,
                                    'teeny' => true,
                                    'quicktags' => array('buttons' => 'strong,em,link,ul,ol,li,code'),
                                    'tinymce' => array(
                                        'plugins' => 'lists,paste,tabfocus,wplink,wordpress',
                                        'toolbar1' => 'formatselect,bold,italic,bullist,numlist,link,unlink',
                                        'toolbar2' => '',
                                    )
                                );
                                
                                wp_editor($help_content, $widget_id . '_content_editor', $editor_settings);
                                ?>
                                <p class="description">
                                    <?php _e('Contenuto HTML dell\'help mostrato agli utenti per questo widget', 'document-viewer-plugin'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Trigger Selectors', 'document-viewer-plugin'); ?></th>
                            <td>
                                <code><?php echo implode(', ', $config['triggers']); ?></code>
                                <p class="description">
                                    <?php _e('Selettori CSS che attivano questo help (configurati dal codice)', 'document-viewer-plugin'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Anteprima', 'document-viewer-plugin'); ?></th>
                            <td>
                                <div class="help-preview" style="border: 1px solid #ddd; padding: 15px; background: #f9f9f9; max-height: 300px; overflow-y: auto;">
                                    <?php echo wp_kses_post($help_content); ?>
                                </div>
                                <p class="description">
                                    <?php _e('Anteprima del contenuto help come apparirà agli utenti', 'document-viewer-plugin'); ?>
                                </p>
                            </td>
                        </tr>
                    </table>
                    
                    <div class="widget-help-actions">
                        <button type="button" 
                                class="button button-secondary reset-to-default"
                                data-widget="<?php echo esc_attr($widget_id); ?>">
                            <?php _e('Ripristina Default', 'document-viewer-plugin'); ?>
                        </button>
                        
                        <button type="button" 
                                class="button button-primary save-widget-help"
                                data-widget="<?php echo esc_attr($widget_id); ?>">
                            <?php _e('Salva Help', 'document-viewer-plugin'); ?>
                        </button>
                        
                        <button type="button" 
                                class="button button-secondary test-widget-help"
                                data-widget="<?php echo esc_attr($widget_id); ?>">
                            <?php _e('Test Help', 'document-viewer-plugin'); ?>
                        </button>
                    </div>
                    
                    <div class="widget-help-result" style="margin-top: 15px;"></div>
                </div>
            </div>
        <?php endforeach; ?>
        
        <div class="widget-help-global-actions" style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">
            <h3><?php _e('Azioni Globali', 'document-viewer-plugin'); ?></h3>
            
            <div class="global-actions-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                <div class="action-card">
                    <h4><?php _e('Import/Export', 'document-viewer-plugin'); ?></h4>
                    <p><?php _e('Esporta o importa le configurazioni help', 'document-viewer-plugin'); ?></p>
                    <button type="button" class="button button-secondary" id="export-help-config">
                        <?php _e('Esporta Configurazioni', 'document-viewer-plugin'); ?>
                    </button>
                    <input type="file" id="import-help-config" accept=".json" style="display: none;">
                    <button type="button" class="button button-secondary" id="import-help-config-btn">
                        <?php _e('Importa Configurazioni', 'document-viewer-plugin'); ?>
                    </button>
                </div>
                
                <div class="action-card">
                    <h4><?php _e('Statistiche', 'document-viewer-plugin'); ?></h4>
                    <p><?php _e('Visualizza l\'utilizzo dei sistemi help', 'document-viewer-plugin'); ?></p>
                    <button type="button" class="button button-secondary" id="view-help-stats">
                        <?php _e('Visualizza Statistiche', 'document-viewer-plugin'); ?>
                    </button>
                </div>
                
                <div class="action-card">
                    <h4><?php _e('Migration', 'document-viewer-plugin'); ?></h4>
                    <p><?php _e('Migra dal vecchio sistema Help Line', 'document-viewer-plugin'); ?></p>
                    <button type="button" class="button button-secondary" id="migrate-old-help">
                        <?php _e('Migra Help Line', 'document-viewer-plugin'); ?>
                    </button>
                </div>
            </div>
        </div>
        
        <?php submit_button(__('Salva Tutte le Configurazioni', 'document-viewer-plugin')); ?>
    </form>
</div>

<script>
jQuery(document).ready(function($) {
    // Gestione tab
    $('.nav-tab').on('click', function(e) {
        e.preventDefault();
        var target = $(this).attr('href');
        
        $('.nav-tab').removeClass('nav-tab-active');
        $(this).addClass('nav-tab-active');
        
        $('.tab-content').removeClass('active');
        $(target).addClass('active');
    });
    
    // Salvataggio singolo widget
    $('.save-widget-help').on('click', function() {
        var widgetId = $(this).data('widget');
        var $result = $(this).siblings('.widget-help-result');
        
        // Implementa il salvataggio AJAX
        $result.html('<span style="color: green;">Help salvato per ' + widgetId + '</span>');
    });
    
    // Reset al default
    $('.reset-to-default').on('click', function() {
        if (confirm('<?php _e('Sei sicuro di voler ripristinare il contenuto di default?', 'document-viewer-plugin'); ?>')) {
            var widgetId = $(this).data('widget');
            // Implementa il reset
            console.log('Reset widget:', widgetId);
        }
    });
    
    // Test help
    $('.test-widget-help').on('click', function() {
        var widgetId = $(this).data('widget');
        // Apri finestra di test
        console.log('Test widget help:', widgetId);
    });
});
</script>

<style>
.widget-help-section {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.widget-help-actions {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.widget-help-actions .button {
    margin-right: 10px;
}

.action-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
}

.action-card h4 {
    margin-top: 0;
    color: #495057;
}

.action-card .button {
    margin-top: 10px;
    margin-right: 5px;
}

.help-preview {
    border-radius: 4px;
}

.help-preview h4 {
    margin-top: 0;
    color: #0073aa;
}

.help-preview ul, .help-preview ol {
    padding-left: 20px;
}
</style>
