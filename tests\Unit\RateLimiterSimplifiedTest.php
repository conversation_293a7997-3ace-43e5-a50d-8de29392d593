<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;

/**
 * Simplified Rate Limiter Test with minimal dependencies
 * 
 * This test focuses on core Rate Limiter functionality without Brain Monkey conflicts
 */
class RateLimiterSimplifiedTest extends TestCase
{
    private $rate_limiter;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // Reset superglobals
        $_SERVER['REMOTE_ADDR'] = '*************';
        $_SERVER['HTTP_X_FORWARDED_FOR'] = '';
        $_SERVER['HTTP_X_REAL_IP'] = '';
        
        // Require the Rate Limiter class directly
        require_once dirname(dirname(__DIR__)) . '/includes/class-office-addin-rate-limiter.php';
        
        // Create new instance for each test
        $this->rate_limiter = new \Office_Addin_Rate_Limiter();
    }
    
    protected function tearDown(): void
    {
        // Clean up
        $this->rate_limiter = null;
        parent::tearDown();
    }
    
    public function testRateLimiterCanBeInstantiated()
    {
        $this->assertInstanceOf(\Office_Addin_Rate_Limiter::class, $this->rate_limiter);
        $this->assertNotNull($this->rate_limiter);
    }
    
    public function testRateLimiterHasExpectedMethods()
    {
        $this->assertTrue(method_exists($this->rate_limiter, 'is_request_allowed'));
        $this->assertTrue(method_exists($this->rate_limiter, 'get_rate_limit_status'));
        $this->assertTrue(method_exists($this->rate_limiter, 'reset_rate_limit'));
        $this->assertTrue(method_exists($this->rate_limiter, 'update_rate_limits'));
    }
    
    public function testRateLimiterBasicFunctionality()
    {
        // This test just ensures the class loads and basic methods exist
        $reflection = new \ReflectionClass($this->rate_limiter);
        $this->assertEquals('Office_Addin_Rate_Limiter', $reflection->getName());
        
        // Check that key methods are public
        $this->assertTrue($reflection->getMethod('is_request_allowed')->isPublic());
        $this->assertTrue($reflection->getMethod('get_rate_limit_status')->isPublic());
    }
}
