/**
 * Text Enhancer - Migliora la formattazione del testo delle analisi
 * 
 * Questo script applica formattazioni avanzate al testo delle analisi
 * per migliorare leggibilità e impatto visivo.
 */

(function($) {
    'use strict';
    
    // Oggetto globale per le funzioni di enhancer
    window.textEnhancer = {
        /**
         * Applica la formattazione avanzata al contenuto dell'analisi
         * 
         * @param {string} elementId - ID del contenitore dell'analisi
         */
        enhance: function(elementId) {
            console.log('Applicazione formattazione avanzata al testo...', elementId);
            
            const $container = $('#' + elementId);
            if (!$container.length) {
                console.error('Container non trovato:', elementId);
                return;
            }
            
            // Aggiungi la classe base per la formattazione avanzata
            $container.addClass('enhanced-text-formatting');
            
            // Applica le varie formattazioni
            this.convertMarkdownStyleFormatting($container); // Nuova funzione per gestire asterischi e cancelletti
            this.identifyFinancialTerms($container);
            this.convertSpecialBlocks($container);
            this.enhanceTables($container);
            this.addAnimations($container);
            
            console.log('Formattazione avanzata completata');
        },
        
        /**
         * Converte la formattazione stile markdown (asterischi e cancelletti) in testo in grassetto
         */
        convertMarkdownStyleFormatting: function($container) {
            console.log('Conversione formattazione stile markdown in HTML...');
            
            let html = $container.html();
            
            // Converte i doppi asterischi in grassetto (es. **testo** diventa <strong>testo</strong>)
            html = html.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');
            
            // Converte i singoli asterischi in grassetto (es. *testo* diventa <strong>testo</strong>)
            html = html.replace(/\*([^*]+)\*/g, '<strong>$1</strong>');
            
            // Converte i doppi cancelletti in grassetto (es. ##testo## diventa <strong>testo</strong>)
            html = html.replace(/##([^#]+)##/g, '<strong>$1</strong>');
            
            // Converte i singoli cancelletti in grassetto (es. #testo# diventa <strong>testo</strong>)
            // Ma evita di convertire i cancelletti che sono all'inizio di una riga (titoli HTML)
            html = html.replace(/(?<!^|\n\s*)#([^#\n]+)#/g, '<strong>$1</strong>');
            
            // Gestisce anche il caso di cancelletti all'inizio di una riga seguiti da spazio 
            // (questi andrebbero mantenuti come titoli markdown)
            html = html.replace(/(^|\n\s*)#([^#\n])/g, '$1#$2');
            
            $container.html(html);
            
            console.log('Conversione markdown completata');
        },
        
        /**
         * Identifica e formatta termini finanziari nel testo
         */
        identifyFinancialTerms: function($container) {
            const financialTerms = [
                'asset', 'liability', 'equity', 'dividend', 'portfolio',
                'bond', 'stock', 'investment', 'return', 'yield', 'profit',
                'loss', 'expense', 'revenue', 'balance sheet', 'income statement',
                'cash flow', 'depreciation', 'amortization', 'leverage', 'liquidity',
                'solvency', 'capital', 'mortgage', 'loan', 'interest', 'tax', 'hedge',
                'rating', 'patrimoniale', 'finanza', 'finanziario', 'investimento',
                'rendimento', 'mercato', 'bilancio', 'fondi', 'azioni', 'obbligazioni',
                'mutuo', 'prestito', 'tasso', 'imposte', 'fiscale', 'capitale', 
                'interessi', 'budget', 'reddito', 'costo', 'patrimonio', 'rischio',
                'dividendo', 'ammortamento', 'liquidità', 'solvibilità', 'borsa',
                'reddito', 'entrata', 'spesa', 'EBITDA', 'ROI', 'ROE', 'ROCE'
            ];
            
            let html = $container.html();
            
            // Crea una regex per trovare i termini finanziari (rispettando i confini delle parole)
            const termsRegex = new RegExp('\\b(' + financialTerms.join('|') + ')\\b', 'gi');
            
            // Applica la formattazione ma evita di formattare testo già all'interno di tag
            html = html.replace(/(<[^>]*>)|([^<]+)/g, function(match, tag, text) {
                if (tag) return tag; // Mantieni i tag HTML così come sono
                
                // Applica la formattazione solo al testo
                return text.replace(termsRegex, '<span class="financial-term" title="Termine finanziario">$1</span>');
            });
            
            $container.html(html);
        },
        
        /**
         * Converte blocchi di testo speciali (info box, warning box)
         */
        convertSpecialBlocks: function($container) {
            // Cerca pattern come "NOTA:" o "IMPORTANTE:" e convertili in box informativi
            $container.find('p').each(function() {
                const $p = $(this);
                const text = $p.text().trim();
                
                if (text.match(/^(NOTA|INFO|N\.B\.):/i)) {
                    $p.addClass('info-box');
                    $p.html($p.html().replace(/^(NOTA|INFO|N\.B\.):(\s*)/i, ''));
                }
                else if (text.match(/^(IMPORTANTE|ATTENZIONE|AVVERTENZA):/i)) {
                    $p.addClass('warning-box');
                    $p.html($p.html().replace(/^(IMPORTANTE|ATTENZIONE|AVVERTENZA):(\s*)/i, ''));
                }
            });
            
            // Cerca pattern che sembrano definizioni e formattali come concetti chiave
            $container.html($container.html().replace(/"([^"]{2,50})"/g, '<span class="key-concept">$1</span>'));
            
            // Identifica e formatta importi e percentuali
            this.identifyAmountsAndPercentages($container);
        },
        
        /**
         * Identifica e formatta importi e percentuali nel testo
         */
        identifyAmountsAndPercentages: function($container) {
            let html = $container.html();
            
            // Regex per individuare importi monetari (es. €10.000, 10.000€, 10,000.00€)
            const amountRegex = /(€\s*\d{1,3}(?:[.,]\d{3})*(?:[.,]\d{1,2})?|\d{1,3}(?:[.,]\d{3})*(?:[.,]\d{1,2})?\s*€)/g;
            
            // Regex per percentuali (es. 10%, 10.5%, 10,5%)
            const percentageRegex = /(\d{1,3}(?:[.,]\d{1,3})?)\s*%/g;
            
            // Applica la formattazione ma evita di formattare testo già all'interno di tag
            html = html.replace(/(<[^>]*>)|([^<]+)/g, function(match, tag, text) {
                if (tag) return tag; // Mantieni i tag HTML così come sono
                
                // Formatta gli importi
                let formattedText = text.replace(amountRegex, '<span class="amount">$1</span>');
                
                // Formatta le percentuali
                formattedText = formattedText.replace(percentageRegex, '<span class="percentage">$1%</span>');
                
                return formattedText;
            });
            
            $container.html(html);
        },
        
        /**
         * Migliora l'aspetto delle tabelle
         */
        enhanceTables: function($container) {
            $container.find('table').each(function() {
                const $table = $(this);
                
                // Aggiungi header se non presente
                if ($table.find('thead').length === 0 && $table.find('tbody tr').length > 0) {
                    const $firstRow = $table.find('tr:first-child');
                    
                    // Se la prima riga sembra contenere header (tutte celle th o td in grassetto)
                    if ($firstRow.find('th').length > 0 || 
                        $firstRow.find('td strong').length === $firstRow.find('td').length) {
                        
                        $firstRow.wrap('<thead></thead>');
                        $table.find('thead').prependTo($table);
                    }
                }
                
                // Aggiungi hover effect se non già presente
                $table.find('tbody tr').hover(
                    function() { $(this).addClass('hover'); },
                    function() { $(this).removeClass('hover'); }
                );
            });
        },
        
        /**
         * Aggiunge animazioni agli elementi quando diventano visibili
         */
        addAnimations: function($container) {
            // Aggiungi classe per animazione agli elementi principali
            $container.find('h2, h3, .info-box, .warning-box, table').addClass('animate-in');
            
            // Imposta delay incrementale per animazione sequenziale
            let delay = 0;
            $container.find('.animate-in').each(function() {
                $(this).css('animation-delay', delay + 's');
                delay += 0.1; // Incrementa il delay di 0.1 secondi per elemento
            });
            
            // Inizializza IntersectionObserver se disponibile
            if ('IntersectionObserver' in window) {
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.style.visibility = 'visible';
                            observer.unobserve(entry.target);
                        }
                    });
                }, {
                    threshold: 0.1
                });
                
                // Osserva tutti gli elementi con animazione
                $container.find('.animate-in').each(function() {
                    $(this).css('visibility', 'hidden');
                    observer.observe(this);
                });
            }
        }
    };
    
    // Inizializzazione al caricamento del documento
    $(document).ready(function() {
        console.log('Text Enhancer inizializzato');
        
        // Aggiungi evento per processare il testo quando viene inserito nell'analisi
        $(document).on('analysisContentLoaded', function(e, containerId) {
            console.log('Evento analysisContentLoaded rilevato per il container:', containerId);
            window.textEnhancer.enhance(containerId);
        });
    });
    
})(jQuery);