/**
 * OCR Processing Module for Document Advisor
 * Handles image upload, OCR text extraction, and image-to-PDF conversion using Tesseract.js
 */

class DocumentOCR {
    constructor() {
        this.isProcessing = false;
        this.progressCallback = null;
        this.completedCallback = null;
        this.errorCallback = null;
        this.loadTesseract();
    }

    /**
     * Load Tesseract.js library dynamically
     * @returns {Promise} Promise that resolves when Tesseract is loaded
     */
    loadTesseract() {
        return new Promise((resolve, reject) => {
            if (typeof Tesseract !== 'undefined') {
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/tesseract.js@2/dist/tesseract.min.js';
            script.onload = () => resolve();
            script.onerror = () => reject(new Error('Failed to load Tesseract.js'));
            document.head.appendChild(script);
        });
    }

    /**
     * Register callbacks for OCR progress and completion
     * @param {Function} progressCallback Called with progress percentage (0-100)
     * @param {Function} completedCallback Called with extracted text when OCR completes
     * @param {Function} errorCallback Called if an error occurs
     */
    registerCallbacks(progressCallback, completedCallback, errorCallback) {
        this.progressCallback = progressCallback;
        this.completedCallback = completedCallback;
        this.errorCallback = errorCallback;
    }

    /**
     * Process an image file with OCR
     * @param {File} imageFile The image file to process
     * @param {Array} languages The languages to use for OCR (e.g. ['ita', 'eng'])
     * @returns {Promise} Promise that resolves with extracted text
     */
    async processImage(imageFile, languages = ['ita', 'eng']) {
        if (this.isProcessing) {
            throw new Error('OCR processing already in progress');
        }
        this.isProcessing = true;

        try {
            // Log l'inizio del processo OCR
            this.logToServer('Inizializzazione processo OCR per ' + imageFile.name + ' (' + this.getFileSize(imageFile.size) + ')', 'ocr');
            
            // Make sure Tesseract is loaded
            await this.loadTesseract();
            this.logToServer('Tesseract.js caricato con successo', 'ocr');
            
            // Create a URL for the image
            const imageUrl = URL.createObjectURL(imageFile);

            // Report initial progress
            if (this.progressCallback) {
                this.progressCallback(0);
            }

            this.logToServer('Avvio riconoscimento OCR con lingue: ' + languages.join(', '), 'ocr');
            
            // Perform OCR
            const result = await Tesseract.recognize(
                imageUrl,
                languages.join('+'),
                {
                    logger: info => {
                        if (info.status === 'recognizing text' && this.progressCallback) {
                            const progress = Math.round(info.progress * 100);
                            this.progressCallback(progress);
                            
                            // Log ogni 20% di progresso
                            if (progress % 20 === 0) {
                                this.logToServer('Progresso OCR: ' + progress + '%', 'ocr');
                            }
                        } else if (info.status !== 'recognizing text') {
                            // Log delle fasi di processo diverse dal riconoscimento testo
                            this.logToServer('OCR fase: ' + info.status, 'ocr');
                        }
                    }
                }
            );

            // Release the object URL
            URL.revokeObjectURL(imageUrl);
            
            // Clean up the text
            const extractedText = this.cleanExtractedText(result.data.text);

            // Log del completamento con statistiche
            let timingMsg = '';
            if (result.data && result.data.timing && typeof result.data.timing.total !== 'undefined') {
                timingMsg = ' Tempo stimato: ' + this.formatTime(result.data.timing.total) + ' ms';
            }
            this.logToServer(
                'Completato processo OCR. Estratti ' + extractedText.length + ' caratteri.' + timingMsg,
                'ocr'
            );

            // Report completion
            if (this.completedCallback) {
                this.completedCallback(extractedText);
            }

            this.isProcessing = false;
            return extractedText;
        } catch (error) {
            this.isProcessing = false;
            
            // Log dell'errore
            this.logToServer('Errore OCR: ' + error.message, 'ocr');
            
            if (this.errorCallback) {
                this.errorCallback(error);
            }
            throw error;
        }
    }
    
    /**
     * Log messages to server
     * @param {string} message - The message to log
     * @param {string} context - The context of the log (ocr, etc.)
     */
    logToServer(message, context = 'ocr') {
        if (typeof jQuery !== 'undefined') {
            jQuery.ajax({
                url: (window.documentViewerParams && window.documentViewerParams.ajaxUrl) || '/wp-admin/admin-ajax.php',
                type: 'POST',
                data: {
                    action: 'ocr_log',
                    nonce: (window.documentViewerParams && window.documentViewerParams.nonce) || '',
                    message: message,
                    context: context
                }
            });
        } else {
            console.log('[OCR Log]: ' + message);
        }
    }
    
    /**
     * Format file size for display
     * @param {number} bytes - Size in bytes
     * @returns {string} Formatted size string
     */
    getFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    /**
     * Format time in milliseconds to a readable format
     * @param {number} ms - Time in milliseconds
     * @returns {string} Formatted time string
     */
    formatTime(ms) {
        if (ms < 1000) return ms + ' ms';
        return (ms / 1000).toFixed(2) + ' sec';
    }

    /**
     * Convert image to PDF for viewing
     * @param {File} imageFile The image file to convert
     * @returns {Promise<Blob>} Promise that resolves with PDF blob
     */
    async convertImageToPdf(imageFile) {
        return new Promise((resolve, reject) => {
            try {
                const reader = new FileReader();
                reader.onload = () => {
                    const img = new Image();
                    img.onload = () => {
                        // Use jsPDF to convert image to PDF
                        if (typeof jsPDF === 'undefined') {
                            // Dynamically load jsPDF if not already loaded
                            const script = document.createElement('script');
                            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
                            script.onload = () => {
                                this.createPdfFromImage(img, resolve);
                            };
                            script.onerror = () => reject(new Error('Failed to load jsPDF'));
                            document.head.appendChild(script);
                        } else {
                            this.createPdfFromImage(img, resolve);
                        }
                    };
                    img.onerror = () => reject(new Error('Failed to load image'));
                    img.src = reader.result;
                };
                reader.onerror = () => reject(new Error('Failed to read image file'));
                reader.readAsDataURL(imageFile);
            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * Create PDF from image
     * @param {HTMLImageElement} img The image element
     * @param {Function} callback Callback function to call with the PDF blob
     */
    createPdfFromImage(img, callback) {
        // Calculate PDF dimensions based on image aspect ratio
        const imgWidth = img.width;
        const imgHeight = img.height;
        let pdfWidth = 210; // A4 width in mm
        let pdfHeight = 297; // A4 height in mm
        
        // Adjust dimensions to fit image within A4 while maintaining aspect ratio
        if (imgWidth / imgHeight > pdfWidth / pdfHeight) {
            // Image is wider relative to its height than A4
            pdfHeight = pdfWidth * imgHeight / imgWidth;
        } else {
            // Image is taller relative to its width than A4
            pdfWidth = pdfHeight * imgWidth / imgHeight;
        }
        
        // Create PDF
        const pdf = new jspdf.jsPDF({
            orientation: imgWidth > imgHeight ? 'landscape' : 'portrait',
            unit: 'mm',
            format: [pdfWidth, pdfHeight]
        });
        
        // Add image to PDF (centered)
        pdf.addImage(
            img.src, 
            'JPEG', // format 
            0, // x position
            0, // y position
            pdfWidth, // width 
            pdfHeight // height
        );
        
        // Generate the PDF as a blob
        const pdfBlob = pdf.output('blob');
        callback(pdfBlob);
    }

    /**
     * Clean up extracted text for better readability
     * @param {string} text The raw extracted text
     * @returns {string} The cleaned text
     */
    cleanExtractedText(text) {
        if (!text) return '';
        
        // Remove excessive whitespace while preserving paragraph breaks
        let cleanedText = text.replace(/[ \t]+/g, ' ');
        cleanedText = cleanedText.replace(/\n{3,}/g, '\n\n');
        
        // Normalize newlines
        cleanedText = cleanedText.replace(/\r\n|\r/g, '\n');
        
        // Fix common OCR artifacts
        cleanedText = cleanedText.replace(/[�]/g, '');
        
        // Try to detect and fix words broken across lines
        cleanedText = cleanedText.replace(/(\w)-\n(\w)/g, '$1$2');
        
        return cleanedText.trim();
    }
}

// Export for use in document-viewer.js
window.DocumentOCR = DocumentOCR;