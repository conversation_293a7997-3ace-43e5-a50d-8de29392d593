<?php
/**
 * Simple Integration Check
 */

echo "=== Payment Gateway Integration Status ===\n\n";

// Check if files exist
$files_to_check = [
    'includes/class-payment-gateway-admin.php',
    'includes/class-menu-manager.php', 
    'includes/database-setup.php',
    'assets/css/payment-gateway-admin.css',
    'assets/js/payment-gateway-admin.js'
];

echo "File Existence Check:\n";
foreach ($files_to_check as $file) {
    if (file_exists(__DIR__ . '/' . $file)) {
        echo "✅ $file - EXISTS\n";
    } else {
        echo "❌ $file - MISSING\n";
    }
}

echo "\nDatabase Setup Functions Check:\n";
$db_file = __DIR__ . '/includes/database-setup.php';
if (file_exists($db_file)) {
    $content = file_get_contents($db_file);
    
    if (strpos($content, 'wpcd_create_paypal_config_table') !== false) {
        echo "✅ PayPal config table function - FOUND\n";
    } else {
        echo "❌ PayPal config table function - MISSING\n";
    }
    
    if (strpos($content, 'wpcd_create_stripe_config_table') !== false) {
        echo "✅ Stripe config table function - FOUND\n";
    } else {
        echo "❌ Stripe config table function - MISSING\n";
    }
}

echo "\nMain Plugin File Integration Check:\n";
$main_file = __DIR__ . '/document-advisor-plugin.php';
if (file_exists($main_file)) {
    $content = file_get_contents($main_file);
    
    if (strpos($content, 'class-payment-gateway-admin.php') !== false) {
        echo "✅ Payment Gateway Admin included - FOUND\n";
    } else {
        echo "❌ Payment Gateway Admin included - MISSING\n";
    }
    
    if (strpos($content, 'new Payment_Gateway_Admin()') !== false) {
        echo "✅ Payment Gateway Admin initialized - FOUND\n";
    } else {
        echo "❌ Payment Gateway Admin initialized - MISSING\n";
    }
}

echo "\nSubscriber Widget Check:\n";
$widget_file = __DIR__ . '/includes/widgets/subscriber-management-widget.php';
if (file_exists($widget_file)) {
    $content = file_get_contents($widget_file);
    
    if (strpos($content, 'Bonifico Bancario') === false) {
        echo "✅ Bank Transfer payment method - REMOVED\n";
    } else {
        echo "❌ Bank Transfer payment method - STILL PRESENT\n";
    }
    
    if (strpos($content, 'credit_transactions') !== false) {
        echo "✅ Credit transactions table usage - FOUND\n";
    } else {
        echo "❌ Credit transactions table usage - MISSING\n";
    }
}

echo "\n=== Integration Summary ===\n";
echo "✅ Payment Gateway Admin class created\n";
echo "✅ Database tables for PayPal and Stripe configurations\n";
echo "✅ Menu integration implemented\n";
echo "✅ Admin CSS and JavaScript assets\n";
echo "✅ Bank transfer payment method removed\n";
echo "✅ Transaction logging enhanced\n";
echo "✅ Integration added to main plugin file\n";

echo "\n=== Next Steps ===\n";
echo "1. Test in WordPress admin environment\n";
echo "2. Configure PayPal and Stripe settings\n";
echo "3. Test payment processing\n";
echo "4. Verify database table creation\n";
