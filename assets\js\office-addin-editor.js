/**
 * Office Add-in Editor JavaScript
 *
 * Questo file gestisce le funzionalità avanzate dell'editor HTML per l'Office Add-in
 */

(function($) {
    'use strict';

    // Inizializza quando il documento è pronto
    $(document).ready(function() {
        // Inizializza il color picker
        if ($.fn.wpColorPicker) {
            $('.color-picker').wpColorPicker();
        }

        // Gestisci il pulsante per applicare il colore al testo
        $('#apply-text-color').on('click', function() {
            var color = $('#addin-custom-color').val();
            if (color) {
                insertTextWithColor(color);
            }
        });

        // Gestisci il pulsante per applicare il colore allo sfondo
        $('#apply-bg-color').on('click', function() {
            var color = $('#addin-custom-color').val();
            if (color) {
                insertBackgroundColor(color);
            }
        });

        // Gestisci l'inserimento di elementi template
        $('#insert-template-element').on('click', function() {
            var templateType = $('#addin-insert-template').val();
            if (templateType) {
                insertTemplateElement(templateType);
            }
        });
    });

    /**
     * Inserisce il testo selezionato con il colore specificato
     *
     * @param {string} color Il colore da applicare al testo
     */
    function insertTextWithColor(color) {
        // Verifica se l'editor TinyMCE è attivo
        if (typeof tinyMCE !== 'undefined' && tinyMCE.get('office_addin_content_editor') && !tinyMCE.get('office_addin_content_editor').isHidden()) {
            var editor = tinyMCE.get('office_addin_content_editor');
            var selection = editor.selection.getContent();

            if (selection) {
                // Se c'è una selezione, applica il colore solo al testo selezionato
                editor.execCommand('ForeColor', false, color);
            } else {
                // Se non c'è una selezione, inserisci un placeholder con il colore
                editor.execCommand('mceInsertContent', false, '<span style="color: ' + color + '">Testo colorato</span>');
            }
        } else {
            // Fallback per l'editor di testo
            var textarea = $('#office_addin_content');
            var start = textarea[0].selectionStart;
            var end = textarea[0].selectionEnd;
            var text = textarea.val();
            var selectedText = text.substring(start, end);

            if (selectedText) {
                // Se c'è una selezione, applica il colore solo al testo selezionato
                var replacement = '<span style="color: ' + color + '">' + selectedText + '</span>';
                textarea.val(text.substring(0, start) + replacement + text.substring(end));
            } else {
                // Se non c'è una selezione, inserisci un placeholder con il colore
                var placeholder = '<span style="color: ' + color + '">Testo colorato</span>';
                textarea.val(text.substring(0, start) + placeholder + text.substring(start));
            }
        }
    }

    /**
     * Inserisce il testo selezionato con lo sfondo colorato
     *
     * @param {string} color Il colore da applicare allo sfondo
     */
    function insertBackgroundColor(color) {
        // Verifica se l'editor TinyMCE è attivo
        if (typeof tinyMCE !== 'undefined' && tinyMCE.get('office_addin_content_editor') && !tinyMCE.get('office_addin_content_editor').isHidden()) {
            var editor = tinyMCE.get('office_addin_content_editor');
            var selection = editor.selection.getContent();

            if (selection) {
                // Se c'è una selezione, applica il colore di sfondo solo al testo selezionato
                editor.execCommand('HiliteColor', false, color);
            } else {
                // Se non c'è una selezione, inserisci un placeholder con il colore di sfondo
                editor.execCommand('mceInsertContent', false, '<span style="background-color: ' + color + '">Testo con sfondo colorato</span>');
            }
        } else {
            // Fallback per l'editor di testo
            var textarea = $('#office_addin_content');
            var start = textarea[0].selectionStart;
            var end = textarea[0].selectionEnd;
            var text = textarea.val();
            var selectedText = text.substring(start, end);

            if (selectedText) {
                // Se c'è una selezione, applica il colore di sfondo solo al testo selezionato
                var replacement = '<span style="background-color: ' + color + '">' + selectedText + '</span>';
                textarea.val(text.substring(0, start) + replacement + text.substring(end));
            } else {
                // Se non c'è una selezione, inserisci un placeholder con il colore di sfondo
                var placeholder = '<span style="background-color: ' + color + '">Testo con sfondo colorato</span>';
                textarea.val(text.substring(0, start) + placeholder + text.substring(start));
            }
        }
    }

    /**
     * Inserisce un elemento template nell'editor
     *
     * @param {string} templateType Il tipo di elemento template da inserire
     */
    function insertTemplateElement(templateType) {
        var templateHTML = '';

        // Genera l'HTML in base al tipo di elemento
        switch (templateType) {
            case 'button':
                templateHTML = '<button id="custom-button" class="primary-button" title="Custom button">\n    <span class="button-icon">🔍</span> Custom Button\n</button>';
                break;

            case 'section':
                templateHTML = '<div class="section">\n    <h3>Section Title</h3>\n    <p>Section content goes here...</p>\n</div>';
                break;

            case 'form-field':
                templateHTML = '<div class="form-group">\n    <label for="custom-field">Field Label:</label>\n    <input type="text" id="custom-field" class="full-width" placeholder="Enter value..." />\n</div>';
                break;

            case 'result-container':
                templateHTML = '<div class="results-container">\n    <h4>Results:</h4>\n    <div id="custom-results" class="result-content">\n        <p class="placeholder">Results will appear here.</p>\n    </div>\n</div>';
                break;
        }

        // Inserisci l'HTML nell'editor
        if (templateHTML) {
            // Verifica se l'editor TinyMCE è attivo
            if (typeof tinyMCE !== 'undefined' && tinyMCE.get('office_addin_content_editor') && !tinyMCE.get('office_addin_content_editor').isHidden()) {
                tinyMCE.get('office_addin_content_editor').execCommand('mceInsertContent', false, templateHTML);
            } else {
                // Fallback per l'editor di testo
                var textarea = $('#office_addin_content');
                var start = textarea[0].selectionStart;
                var text = textarea.val();
                textarea.val(text.substring(0, start) + templateHTML + text.substring(start));
            }
        }
    }

})(jQuery);
