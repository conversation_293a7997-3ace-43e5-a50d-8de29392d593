{"name": "antecedent/patchwork", "homepage": "https://antecedent.github.io/patchwork/", "description": "Method redefinition (monkey-patching) functionality for PHP.", "keywords": ["testing", "redefinition", "runkit", "monkeypatching", "interception", "aop", "aspect"], "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "minimum-stability": "stable", "require": {"php": ">=7.1.0"}, "require-dev": {"phpunit/phpunit": ">=4"}}