/**
 * User Subscriptions Admin CSS
 * CSS for User Subscriptions management interfaces
 */

/* General styling for admin pages */
.user-subscriptions-admin {
    max-width: 100%;
    padding: 20px 0;
}

.user-subscriptions-admin h1 {
    margin-bottom: 20px;
}

/* Navigation tabs */
.user-subscriptions-admin .nav-tab-wrapper {
    margin-bottom: 20px;
}

/* Table styling */
.user-subscriptions-admin table {
    border-collapse: collapse;
    width: 100%;
    margin-top: 15px;
}

.user-subscriptions-admin th {
    text-align: left;
}

/* Scrollable user table container */
.users-table-container {
    overflow-x: auto;
    margin-bottom: 15px;
}

/* Smaller text for user table */
.users-table th,
.users-table td {
    font-size: 13px;
    padding: 8px 5px;
}

.users-table .column-id,
.subscription-types-table .column-id {
    width: 50px;
}

.users-table .column-username {
    width: 120px;
}

.users-table .column-name,
.users-table .column-surname {
    width: 120px;
}

.users-table .column-phone {
    width: 100px;
}

.users-table .column-email {
    width: 180px;
}

.users-table .column-subscription {
    width: 130px;
}

.users-table .column-credit {
    width: 80px;
}

.users-table .column-analysis-count {
    width: 70px;
}

.users-table .column-tokens {
    width: 80px;
}

.users-table .column-actual-cost,
.users-table .column-total-cost {
    width: 90px;
}

.users-table .column-last-update {
    width: 130px;
}

.users-table .column-actions,
.subscription-types-table .column-actions {
    width: 100px;
}

.subscription-types-table .column-type {
    width: 200px;
}

.subscription-types-table .column-redirect {
    width: auto;
}

.subscription-types-table .column-cost-per-token {
    width: 120px;
}

.subscription-types-table .column-created {
    width: 150px;
}

/* Loading indicators */
.loading-data {
    text-align: center;
    padding: 20px;
}

/* Forms and modals */
#user-modal,
#subscription-type-modal {
    display: none;
    padding: 10px;
}

.form-field {
    margin-bottom: 15px;
}

.form-field label {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
}

.form-field input[type="text"],
.form-field input[type="email"],
.form-field input[type="tel"],
.form-field input[type="password"],
.form-field input[type="number"],
.form-field input[type="url"],
.form-field select {
    width: 100%;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.form-errors {
    color: #d63638;
    margin-top: 10px;
    font-weight: bold;
}

/* Action buttons */
.column-actions {
    text-align: center;
    white-space: nowrap;
    padding: 8px 5px !important;
}

.action-buttons {
    display: flex;
    justify-content: center;
    gap: 5px;
    white-space: nowrap;
}

.action-buttons .button {
    padding: 0 8px;
    height: 28px;
    width: 28px;
    line-height: 26px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.action-buttons .dashicons {
    font-size: 16px;
    line-height: 1;
    width: 16px;
    height: 16px;
    margin: 0;
}

/* Search box */
.search-box {
    padding: 6px 10px;
    width: 200px;
    margin: 0;
}

/* Stats page styling - localizzato al contesto admin */
.stats-container {
    margin-top: 20px;
}

.stats-container .stats-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 30px;
    gap: 20px;
}

.stats-container .stats-card {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    flex: 1;
    min-width: 200px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stats-container .stats-card h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #23282d;
    font-size: 16px;
}

.stats-container .stats-value {
    font-size: 32px;
    font-weight: bold;
    color: #0073aa;
}

/* Enhanced styles for average stats cards */
#avg-cost-per-user,
#avg-analyses-per-user,
#avg-tokens-per-analysis {
    color: #0085ba;
    font-size: 30px;
    position: relative;
    transition: all 0.3s ease;
}

#avg-cost-per-user:after,
#avg-analyses-per-user:after,
#avg-tokens-per-analysis:after {
    content: '';
    display: block;
    width: 40px;
    height: 3px;
    background: #0085ba;
    margin: 5px auto 0;
    border-radius: 2px;
}

.stats-chart-container {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    width: 100%;
    min-height: 300px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stats-chart-container h3 {
    margin-top: 0;
    text-align: center;
    color: #23282d;
    margin-bottom: 15px;
}

.stats-table-container {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    width: 100%;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stats-table-container h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #23282d;
}

/* Table responsive container */
.table-responsive {
    overflow-x: auto;
    margin-bottom: 15px;
}

/* Subscription reports table columns */
.subscription-reports-table {
    table-layout: auto;
    min-width: 100%;
}

.subscription-reports-table .column-username {
    width: 120px;
}

.subscription-reports-table .column-name {
    width: 160px;
}

.subscription-reports-table .column-subscription {
    width: 140px;
}

.subscription-reports-table .column-credit {
    width: 80px;
}

.subscription-reports-table .column-analysis-count {
    width: 70px;
}

.subscription-reports-table .column-tokens {
    width: 80px;
}

.subscription-reports-table .column-actual-cost,
.subscription-reports-table .column-total-cost {
    width: 90px;
}

.subscription-reports-table .column-last-update {
    width: 130px;
}

.subscription-reports-table th {
    font-size: 13px;
}

.subscription-reports-table td {
    font-size: 13px;
}

/* Filter container styles */
.filter-container {
    background-color: #f9f9f9;
    border: 1px solid #e5e5e5;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
    box-shadow: 0 1px 1px rgba(0,0,0,0.04);
}

.filter-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.filter-item {
    margin-bottom: 10px;
    margin-right: 15px;
}

.filter-item label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.filter-item select,
.filter-item input {
    width: 250px;
    max-width: 100%;
    padding: 6px 10px;
    border-radius: 3px;
    border: 1px solid #ddd;
    background-color: #fff;
}

#refresh-stats {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

#refresh-stats .dashicons {
    margin-right: 5px;
    font-size: 16px;
    width: 16px;
    height: 16px;
    line-height: 1;
}

/* Stili per la riga dei grafici in tre colonne */
.stats-charts-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 30px;
    justify-content: space-between;
}

.stats-chart-card {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    flex: 1;
    min-width: 250px;
}

.stats-chart-card h3 {
    margin-top: 0;
    text-align: center;
    color: #23282d;
    margin-bottom: 15px;
    font-size: 15px;
}

.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
    opacity: 1 !important;
    visibility: visible !important;
    min-height: 300px;
    display: block !important;
    z-index: 10;
    box-sizing: border-box;
    background-color: #fff;
}

/* Responsive adjustments */
@media screen and (max-width: 1200px) {
    .subscription-reports-table th,
    .subscription-reports-table td {
        font-size: 12px;
    }
    
    .subscription-reports-table .column-last-update {
        width: 120px;
    }
    
    .stats-charts-row {
        flex-direction: column;
    }
    
    .stats-chart-card {
        width: 100%;
        margin-bottom: 20px;
    }
    
    .chart-container {
        height: 250px;
    }
}

@media screen and (max-width: 782px) {
    .users-table .column-phone,
    .users-table .column-credit {
        display: none;
    }
    
    .stats-container .stats-row {
        flex-direction: column;
    }
    
    .stats-container .stats-card {
        width: 100%;
    }
    
    /* Hide less important columns on small screens */
    .subscription-reports-table .column-analysis-count,
    .subscription-reports-table .column-tokens,
    .subscription-reports-table .column-actual-cost {
        display: none;
    }
    
    .filter-row {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .filter-item {
        width: 100%;
        margin-right: 0;
    }
    
    /* Responsive behavior for stats cards */
    #avg-cost-per-user,
    #avg-analyses-per-user,
    #avg-tokens-per-analysis {
        font-size: 24px;
    }
    
    #avg-cost-per-user:after,
    #avg-analyses-per-user:after,
    #avg-tokens-per-analysis:after {
        width: 30px;
    }
}

/* Responsive styles for action buttons */
@media screen and (max-width: 600px) {
    .action-buttons {
        flex-direction: column;
        gap: 3px;
    }
    
    .users-table .column-actions,
    .subscription-types-table .column-actions {
        width: 40px;
    }
}
