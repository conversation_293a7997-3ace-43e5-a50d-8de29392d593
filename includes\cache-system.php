<?php
/**
 * Classe per la gestione della cache delle analisi per ridurre le chiamate API
 */
class FAIW_Cache_Manager {
    
    private $cache_dir;
    private $cache_expiry = 86400; // 24 ore di default
    
    /**
     * Costruttore della classe
     */
    public function __construct() {
        // Imposta la directory della cache
        $upload_dir = wp_upload_dir();
        $this->cache_dir = $upload_dir['basedir'] . '/financial-analysis-ai/cache';
        
        // Crea la directory della cache se non esiste
        if (!file_exists($this->cache_dir)) {
            wp_mkdir_p($this->cache_dir);
            
            // Crea un file .htaccess per proteggere la directory
            $htaccess = $this->cache_dir . '/.htaccess';
            if (!file_exists($htaccess)) {
                file_put_contents($htaccess, 'Deny from all');
            }
        }
        
        // Imposta il tempo di scadenza della cache dalle opzioni
        $expiry_time = get_option('faiw_cache_expiry', 24);
        $this->cache_expiry = $expiry_time * 3600; // Converti ore in secondi
        
        // Aggiungi hook per la pulizia periodica della cache
        if (!wp_next_scheduled('faiw_clean_cache')) {
            wp_schedule_event(time(), 'daily', 'faiw_clean_cache');
        }
        add_action('faiw_clean_cache', array($this, 'clean_expired_cache'));
    }
    
    /**
     * Genera un ID univoco per la cache in base al contenuto
     */
    public function generate_cache_id($content, $instructions, $model) {
        $data_to_hash = $content . $instructions . $model;
        return md5($data_to_hash);
    }
    
    /**
     * Verifica se esiste una cache valida per l'analisi
     */
    public function has_valid_cache($cache_id) {
        $cache_file = $this->get_cache_file_path($cache_id);
        
        if (!file_exists($cache_file)) {
            return false;
        }
        
        // Verifica la data di creazione del file
        $file_time = filemtime($cache_file);
        $current_time = time();
        
        // Se il file è più vecchio del tempo di scadenza, non è valido
        if (($current_time - $file_time) > $this->cache_expiry) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Ottiene il percorso del file di cache
     */
    private function get_cache_file_path($cache_id) {
        return $this->cache_dir . '/' . $cache_id . '.json';
    }
    
    /**
     * Recupera l'analisi dalla cache
     */
    public function get_cached_analysis($cache_id) {
        $cache_file = $this->get_cache_file_path($cache_id);
        
        if (!$this->has_valid_cache($cache_id)) {
            return null;
        }
        
        $cached_data = file_get_contents($cache_file);
        return json_decode($cached_data, true);
    }
    
    /**
     * Salva l'analisi nella cache
     */
    public function cache_analysis($cache_id, $analysis_data) {
        $cache_file = $this->get_cache_file_path($cache_id);
        
        // Aggiunge timestamp
        $data_to_cache = array(
            'timestamp' => time(),
            'data' => $analysis_data
        );
        
        // Salva i dati come JSON
        if (file_put_contents($cache_file, json_encode($data_to_cache)) === false) {
            error_log('Failed to write cache file: ' . $cache_file);
            return false;
        }
        
        return true;
    }
    
    /**
     * Elimina una singola cache
     */
    public function delete_cache($cache_id) {
        $cache_file = $this->get_cache_file_path($cache_id);
        
        if (file_exists($cache_file)) {
            return unlink($cache_file);
        }
        
        return false;
    }
    
    /**
     * Pulisce tutte le cache scadute
     */
    public function clean_expired_cache() {
        $current_time = time();
        $files = glob($this->cache_dir . '/*.json');
        
        foreach ($files as $file) {
            $file_time = filemtime($file);
            
            // Se il file è più vecchio del tempo di scadenza, eliminalo
            if (($current_time - $file_time) > $this->cache_expiry) {
                unlink($file);
            }
        }
    }
    
    /**
     * Pulisce tutta la cache
     */
    public function clear_all_cache() {
        $files = glob($this->cache_dir . '/*.json');
        
        foreach ($files as $file) {
            unlink($file);
        }
        
        return count($files);
    }
}
?>
