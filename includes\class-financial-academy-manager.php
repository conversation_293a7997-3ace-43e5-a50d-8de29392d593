<?php
/**
 * Financial Academy Manager
 * 
 * Class per gestire le domande finanziarie preconfigurate
 * 
 * @package Financial_Advisor
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class Financial_Academy_Manager {
    /**
     * Table name for academy questions
     * @var string
     */
    private $questions_table;

    /**
     * Constructor
     */
    public function __construct() {
        global $wpdb;
        $this->questions_table = $wpdb->prefix . 'financial_academy_questions';

        // Create table on plugin activation
        register_activation_hook(plugin_dir_path(dirname(__FILE__)) . 'document-advisor-plugin.php', array($this, 'create_questions_table'));

        // AJAX handlers
        add_action('wp_ajax_get_academy_questions', array($this, 'get_academy_questions_ajax'));
        add_action('wp_ajax_nopriv_get_academy_questions', array($this, 'get_academy_questions_ajax'));

        // AJAX handlers for admin
        add_action('wp_ajax_add_academy_question', array($this, 'add_academy_question_ajax'));
        add_action('wp_ajax_update_academy_question', array($this, 'update_academy_question_ajax'));
        add_action('wp_ajax_delete_academy_question', array($this, 'delete_academy_question_ajax'));
        add_action('wp_ajax_import_educational_questions', array($this, 'import_educational_questions_ajax'));
        
        // AJAX handlers for frontend
        add_action('wp_ajax_get_category_stats', array($this, 'get_category_stats_ajax'));
        add_action('wp_ajax_nopriv_get_category_stats', array($this, 'get_category_stats_ajax'));

        // Check if table exists, create if not
        add_action('plugins_loaded', array($this, 'check_table_exists'));
    }

    /**
     * Check if the questions table exists, and create it if not
     */
    public function check_table_exists() {
        global $wpdb;

        if ($wpdb->get_var("SHOW TABLES LIKE '{$this->questions_table}'") != $this->questions_table) {
            $this->create_questions_table();
        }
    }

    /**
     * Create the financial academy questions table
     */
    public function create_questions_table() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE IF NOT EXISTS {$this->questions_table} (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            question_text TEXT NOT NULL,
            category VARCHAR(50) DEFAULT 'general',
            active BOOLEAN NOT NULL DEFAULT 1,
            sort_order INT UNSIGNED DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);

        // Insert default questions if table is empty
        $count = $wpdb->get_var("SELECT COUNT(*) FROM {$this->questions_table}");
        if ($count == 0) {
            $this->insert_default_questions();
        }
    }

    /**
     * Insert default financial academy questions
     */
    private function insert_default_questions() {
        global $wpdb;

        $default_questions = array(
            // Domande generali
            array("text" => "Cos'è un portafoglio d'investimento diversificato?", "category" => "general"),
            array("text" => "Come funzionano i fondi comuni di investimento?", "category" => "general"),
            array("text" => "Quali sono i vantaggi e gli svantaggi dei fondi ETF?", "category" => "investment"),
            array("text" => "Cosa sono i titoli di Stato e come funzionano?", "category" => "investment"),
            array("text" => "Come si calcola il rendimento di un investimento?", "category" => "investment"),
            array("text" => "Quali sono i principali indici di borsa?", "category" => "investment"),
            array("text" => "Cosa significa investire in fondi ESG?", "category" => "investment"),
            array("text" => "Come posso iniziare a investire con un budget limitato?", "category" => "investment"),
            array("text" => "Quali sono le strategie di gestione del rischio finanziario?", "category" => "general"),
            array("text" => "Qual è la differenza tra azioni e obbligazioni?", "category" => "investment"),
            array("text" => "Come valutare la solidità finanziaria di un'azienda?", "category" => "general"),
            array("text" => "Quali sono i vantaggi fiscali degli investimenti a lungo termine?", "category" => "tax"),
            array("text" => "Come funziona il Piano Individuale di Risparmio (PIR)?", "category" => "tax"),
            array("text" => "Quali sono le migliori strategie per la pianificazione pensionistica?", "category" => "retirement"),
            array("text" => "Come si legge un bilancio aziendale?", "category" => "balance_sheet"),
            array("text" => "Quali sono i rischi dell'investimento in criptovalute?", "category" => "investment"),
            array("text" => "Come funziona il mercato immobiliare come investimento?", "category" => "investment"),
            array("text" => "Cos'è il trading algoritmico?", "category" => "investment"),
            array("text" => "Quali sono i principi del value investing?", "category" => "investment"),
            array("text" => "Come proteggere il proprio patrimonio dall'inflazione?", "category" => "general"),
            array("text" => "Quali strumenti finanziari sono più adatti per la pianificazione educativa?", "category" => "general"),
            array("text" => "Come funziona la tassazione dei dividendi?", "category" => "tax"),
            array("text" => "Quali sono le migliori pratiche per la gestione del debito?", "category" => "general"),
            array("text" => "Come si costruisce un fondo di emergenza?", "category" => "general"),
            array("text" => "Quali sono i vantaggi e gli svantaggi dell'investimento immobiliare?", "category" => "investment"),
            array("text" => "Come funzionano le polizze assicurative unit-linked?", "category" => "general"),
            array("text" => "Quali fattori considerare prima di investire in startup?", "category" => "investment"),
            array("text" => "Come diversificare il portafoglio in base al profilo di rischio?", "category" => "investment"),

            // Domande analisi di bilancio
            array("text" => "Analizza la struttura patrimoniale dell'azienda dal bilancio", "category" => "balance_sheet"),
            array("text" => "Valuta la situazione finanziaria aziendale attraverso il bilancio", "category" => "balance_sheet"),
            array("text" => "Esamina i flussi di cassa operativi dell'azienda", "category" => "balance_sheet"),
            array("text" => "Analizza l'evoluzione del fatturato negli ultimi anni", "category" => "balance_sheet"),
            array("text" => "Valuta la gestione dei crediti verso clienti", "category" => "balance_sheet"),
            array("text" => "Esamina la composizione delle rimanenze di magazzino", "category" => "balance_sheet"),
            array("text" => "Analizza la struttura del debito aziendale", "category" => "balance_sheet"),
            array("text" => "Valuta la posizione finanziaria netta dell'azienda", "category" => "balance_sheet"),
            array("text" => "Esamina l'andamento del capitale circolante netto", "category" => "balance_sheet"),
            array("text" => "Analizza la distribuzione degli investimenti aziendali", "category" => "balance_sheet"),

            // Domande indici KPI
            array("text" => "Calcola il ROE (Return on Equity) dell'azienda", "category" => "kpi"),
            array("text" => "Determina il ROI (Return on Investment) aziendale", "category" => "kpi"),
            array("text" => "Calcola il ROA (Return on Assets) dell'azienda", "category" => "kpi"),
            array("text" => "Analizza l'EBITDA margin dell'azienda", "category" => "kpi"),
            array("text" => "Determina il tasso di crescita del fatturato", "category" => "kpi"),
            array("text" => "Calcola l'indice di rotazione del capitale investito", "category" => "kpi"),
            array("text" => "Valuta l'efficienza nella gestione del capitale circolante", "category" => "kpi"),
            array("text" => "Analizza l'indice di produttività del lavoro", "category" => "kpi"),
            array("text" => "Calcola il costo del capitale medio ponderato (WACC)", "category" => "kpi"),
            array("text" => "Determina l'Economic Value Added (EVA) dell'azienda", "category" => "kpi"),

            // Domande ratio finanziari
            array("text" => "Calcola il current ratio dell'azienda", "category" => "ratios"),
            array("text" => "Determina il quick ratio (acid test ratio)", "category" => "ratios"),
            array("text" => "Analizza il debt-to-equity ratio", "category" => "ratios"),
            array("text" => "Calcola il debt service coverage ratio", "category" => "ratios"),
            array("text" => "Determina l'asset turnover ratio", "category" => "ratios"),
            array("text" => "Analizza l'inventory turnover ratio", "category" => "ratios"),
            array("text" => "Calcola il receivables turnover ratio", "category" => "ratios"),
            array("text" => "Determina il gross profit margin", "category" => "ratios"),
            array("text" => "Analizza il net profit margin dell'azienda", "category" => "ratios"),
            array("text" => "Calcola il price-to-earnings ratio (P/E)", "category" => "ratios"),
            array("text" => "Determina il price-to-book ratio (P/B)", "category" => "ratios"),
            array("text" => "Analizza il dividend yield dell'azienda", "category" => "ratios"),
            array("text" => "Calcola l'interest coverage ratio", "category" => "ratios"),
            array("text" => "Determina il times interest earned ratio", "category" => "ratios"),
            array("text" => "Analizza il cash conversion cycle", "category" => "ratios")
        );

        $sort_order = 1;
        foreach ($default_questions as $question) {
            $wpdb->insert(
                $this->questions_table,
                array(
                    'question_text' => $question['text'],
                    'category' => $question['category'],
                    'active' => 1,
                    'sort_order' => $sort_order++
                ),
                array('%s', '%s', '%d', '%d')
            );
        }

        dv_debug_log('Inserted ' . count($default_questions) . ' default financial academy questions', 'academy');
    }

    /**
     * Get all active academy questions
     * 
     * @param string $category Optional category filter
     * @return array List of questions
     */
    public function get_questions($category = '') {
        global $wpdb;

        $where = "WHERE active = 1";
        if (!empty($category) && $category !== 'all') {
            $where .= $wpdb->prepare(" AND category = %s", $category);
        }

        $questions = $wpdb->get_results(
            "SELECT id, question_text, category 
             FROM {$this->questions_table} 
             {$where}
             ORDER BY sort_order ASC, id ASC",
            ARRAY_A
        );

        return $questions;
    }

    /**
     * AJAX handler to get academy questions
     */
    public function get_academy_questions_ajax() {
        // We allow this for all visitors, no nonce check needed
        $category = isset($_REQUEST['category']) ? sanitize_text_field($_REQUEST['category']) : 'all';
        
        $questions = $this->get_questions($category);
        
        if (empty($questions)) {
            wp_send_json_error(array('message' => __('Nessuna domanda finanziaria trovata.', 'document-viewer-plugin')));
            return;
        }
        
        wp_send_json_success(array('questions' => $questions));
    }

    /**
     * Render admin page for managing questions
     */
    public function render_admin_page() {
        if (!current_user_can('manage_options')) {
            wp_die(__('Non hai i permessi sufficienti per accedere a questa pagina.', 'document-viewer-plugin'));
        }
        
    
        // Corretto il riferimento alla pagina nel filtro di categoria
        $current_category = isset($_GET['category']) ? sanitize_text_field($_GET['category']) : 'all';
        
        // Parametri di paginazione
        $per_page = 20;
        $current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        
        $questions = $this->get_all_questions($current_page, $per_page);
        $total_questions = $this->get_questions_count();
        $total_pages = ceil($total_questions / $per_page);
        
        ?>
        <div class="wrap">
            <h1><?php echo esc_html__('Financial Academy Questions', 'document-viewer-plugin'); ?></h1>
            
            <div class="notice notice-info">
                <p><?php _e('Qui puoi gestire le domande finanziarie preconfigurare che appariranno nella chat del consulente finanziario.', 'document-viewer-plugin'); ?></p>
            </div>

            <!-- Import Educational Questions -->
            <div class="card" style="max-width: 800px; margin-top: 20px; padding: 20px; background-color: #f0f8ff; border-left: 4px solid #0073aa;">
                <h3><?php _e('Aggiorna Domande Educative', 'document-viewer-plugin'); ?></h3>
                <p><?php _e('Clicca il pulsante qui sotto per importare nuove domande educative su analisi di bilancio, KPI e ratio finanziari.', 'document-viewer-plugin'); ?></p>
                <p><em><?php _e('Nota: Questa operazione aggiunge solo nuove domande senza rimuovere quelle esistenti.', 'document-viewer-plugin'); ?></em></p>
                <button type="button" id="import-educational-questions" class="button button-secondary">
                    <?php _e('Importa Domande Educative Aggiornate', 'document-viewer-plugin'); ?>
                </button>
                <span class="spinner" id="import-spinner" style="float: none; margin-left: 10px;"></span>
                <div id="import-result" style="margin-top: 10px;"></div>
            </div>
            
            <!-- Add New Question Form -->
            <div class="card" style="max-width: 800px; margin-top: 20px; padding: 20px;">
                <h2><?php _e('Aggiungi Nuova Domanda', 'document-viewer-plugin'); ?></h2>
                <form id="add-academy-question-form">
                    <table class="form-table">
                        <tr>
                            <th scope="row"><label for="new-question-text"><?php _e('Testo Domanda', 'document-viewer-plugin'); ?></label></th>
                            <td>
                                <textarea id="new-question-text" name="question_text" rows="3" class="large-text" required></textarea>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="new-question-category"><?php _e('Categoria', 'document-viewer-plugin'); ?></label></th>
                            <td>
                                <select id="new-question-category" name="category">
                                    <option value="general"><?php _e('Generale', 'document-viewer-plugin'); ?></option>
                                    <option value="investment"><?php _e('Investimenti', 'document-viewer-plugin'); ?></option>
                                    <option value="retirement"><?php _e('Pensione', 'document-viewer-plugin'); ?></option>
                                    <option value="tax"><?php _e('Tasse', 'document-viewer-plugin'); ?></option>
                                    <option value="balance_sheet"><?php _e('Analisi di Bilancio', 'document-viewer-plugin'); ?></option>
                                    <option value="kpi"><?php _e('Indici KPI', 'document-viewer-plugin'); ?></option>
                                    <option value="ratios"><?php _e('Ratio Finanziari', 'document-viewer-plugin'); ?></option>
                                </select>
                            </td>
                        </tr>
                    </table>
                    <p class="submit">
                        <button type="submit" class="button button-primary"><?php _e('Aggiungi Domanda', 'document-viewer-plugin'); ?></button>
                        <span class="spinner" style="float: none; margin-top: 0;"></span>
                    </p>
                </form>
            </div>
            
            <!-- Existing Questions Table -->
            <div style="max-width: 1200px; margin-top: 40px;">
                <h2><?php _e('Domande Esistenti', 'document-viewer-plugin'); ?></h2>
                
                <div class="tablenav top">
                    <div class="alignleft actions">
                        <select id="filter-question-category">
                            <option value="all" <?php selected($current_category, 'all'); ?>><?php _e('Tutte le categorie', 'document-viewer-plugin'); ?></option>
                            <option value="general" <?php selected($current_category, 'general'); ?>><?php _e('Generale', 'document-viewer-plugin'); ?></option>
                            <option value="investment" <?php selected($current_category, 'investment'); ?>><?php _e('Investimenti', 'document-viewer-plugin'); ?></option>
                            <option value="retirement" <?php selected($current_category, 'retirement'); ?>><?php _e('Pensione', 'document-viewer-plugin'); ?></option>
                            <option value="tax" <?php selected($current_category, 'tax'); ?>><?php _e('Tasse', 'document-viewer-plugin'); ?></option>
                            <option value="balance_sheet" <?php selected($current_category, 'balance_sheet'); ?>><?php _e('Analisi di Bilancio', 'document-viewer-plugin'); ?></option>
                            <option value="kpi" <?php selected($current_category, 'kpi'); ?>><?php _e('Indici KPI', 'document-viewer-plugin'); ?></option>
                            <option value="ratios" <?php selected($current_category, 'ratios'); ?>><?php _e('Ratio Finanziari', 'document-viewer-plugin'); ?></option>
                        </select>
                        <button type="button" class="button" id="filter-questions-btn"><?php _e('Filtra', 'document-viewer-plugin'); ?></button>
                    </div>
                    
                    <!-- Paginazione superiore -->
                    <?php if ($total_pages > 1): ?>
                    <div class="tablenav-pages">
                        <span class="displaying-num">
                            <?php printf(
                                _n('%s domanda', '%s domande', $total_questions, 'document-viewer-plugin'),
                                number_format_i18n($total_questions)
                            ); ?>
                        </span>
                        
                        <span class="pagination-links">
                            <?php
                            $base_url = remove_query_arg('paged');
                            if ($current_category !== 'all') {
                                $base_url = add_query_arg('category', $current_category, $base_url);
                            }
                            
                            // Prima pagina
                            if ($current_page > 1): ?>
                                <a class="first-page button" href="<?php echo esc_url($base_url); ?>">
                                    <span class="screen-reader-text"><?php _e('Prima pagina', 'document-viewer-plugin'); ?></span>
                                    <span aria-hidden="true">«</span>
                                </a>
                            <?php else: ?>
                                <span class="tablenav-pages-navspan button disabled" aria-hidden="true">«</span>
                            <?php endif; ?>
                            
                            <!-- Pagina precedente -->
                            <?php if ($current_page > 1): ?>
                                <a class="prev-page button" href="<?php echo esc_url(add_query_arg('paged', $current_page - 1, $base_url)); ?>">
                                    <span class="screen-reader-text"><?php _e('Pagina precedente', 'document-viewer-plugin'); ?></span>
                                    <span aria-hidden="true">‹</span>
                                </a>
                            <?php else: ?>
                                <span class="tablenav-pages-navspan button disabled" aria-hidden="true">‹</span>
                            <?php endif; ?>
                            
                            <!-- Informazioni pagina corrente -->
                            <span class="paging-input">
                                <label for="current-page-selector-top" class="screen-reader-text"><?php _e('Pagina corrente', 'document-viewer-plugin'); ?></label>
                                <input class="current-page" id="current-page-selector-top" type="text" name="paged" value="<?php echo $current_page; ?>" size="2" aria-describedby="table-paging">
                                <span class="tablenav-paging-text"> di <span class="total-pages"><?php echo $total_pages; ?></span></span>
                            </span>
                            
                            <!-- Pagina successiva -->
                            <?php if ($current_page < $total_pages): ?>
                                <a class="next-page button" href="<?php echo esc_url(add_query_arg('paged', $current_page + 1, $base_url)); ?>">
                                    <span class="screen-reader-text"><?php _e('Pagina successiva', 'document-viewer-plugin'); ?></span>
                                    <span aria-hidden="true">›</span>
                                </a>
                            <?php else: ?>
                                <span class="tablenav-pages-navspan button disabled" aria-hidden="true">›</span>
                            <?php endif; ?>
                            
                            <!-- Ultima pagina -->
                            <?php if ($current_page < $total_pages): ?>
                                <a class="last-page button" href="<?php echo esc_url(add_query_arg('paged', $total_pages, $base_url)); ?>">
                                    <span class="screen-reader-text"><?php _e('Ultima pagina', 'document-viewer-plugin'); ?></span>
                                    <span aria-hidden="true">»</span>
                                </a>
                            <?php else: ?>
                                <span class="tablenav-pages-navspan button disabled" aria-hidden="true">»</span>
                            <?php endif; ?>
                        </span>
                    </div>
                    <?php endif; ?>
                </div>
                
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('ID', 'document-viewer-plugin'); ?></th>
                            <th><?php _e('Domanda', 'document-viewer-plugin'); ?></th>
                            <th><?php _e('Categoria', 'document-viewer-plugin'); ?></th>
                            <th><?php _e('Attiva', 'document-viewer-plugin'); ?></th>
                            <th><?php _e('Ordine', 'document-viewer-plugin'); ?></th>
                            <th><?php _e('Azioni', 'document-viewer-plugin'); ?></th>
                        </tr>
                    </thead>
                    <tbody id="academy-questions-list">
                        <?php foreach ($questions as $question): ?>
                            <tr data-id="<?php echo esc_attr($question['id']); ?>">
                                <td><?php echo esc_html($question['id']); ?></td>
                                <td class="question-text"><?php echo esc_html($question['question_text']); ?></td>
                                <td class="question-category"><?php echo esc_html($question['category']); ?></td>
                                <td>
                                    <input type="checkbox" class="question-active" <?php checked($question['active'], 1); ?>>
                                </td>
                                <td>
                                    <input type="number" class="small-text question-order" value="<?php echo esc_attr($question['sort_order']); ?>" min="0" max="999">
                                </td>
                                <td>
                                    <button class="button edit-question" data-id="<?php echo esc_attr($question['id']); ?>"><?php _e('Modifica', 'document-viewer-plugin'); ?></button>
                                    <button class="button button-link-delete delete-question" data-id="<?php echo esc_attr($question['id']); ?>"><?php _e('Elimina', 'document-viewer-plugin'); ?></button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                
                <!-- Paginazione -->
                <?php if ($total_pages > 1): ?>
                <div class="tablenav bottom">
                    <div class="tablenav-pages">
                        <span class="displaying-num">
                            <?php printf(
                                _n('%s domanda', '%s domande', $total_questions, 'document-viewer-plugin'),
                                number_format_i18n($total_questions)
                            ); ?>
                        </span>
                        
                        <?php if ($total_pages > 1): ?>
                            <span class="pagination-links">
                                <?php
                                $base_url = remove_query_arg('paged');
                                if ($current_category !== 'all') {
                                    $base_url = add_query_arg('category', $current_category, $base_url);
                                }
                                
                                // Prima pagina
                                if ($current_page > 1): ?>
                                    <a class="first-page button" href="<?php echo esc_url($base_url); ?>">
                                        <span class="screen-reader-text"><?php _e('Prima pagina', 'document-viewer-plugin'); ?></span>
                                        <span aria-hidden="true">«</span>
                                    </a>
                                <?php else: ?>
                                    <span class="tablenav-pages-navspan button disabled" aria-hidden="true">«</span>
                                <?php endif; ?>
                                
                                <!-- Pagina precedente -->
                                <?php if ($current_page > 1): ?>
                                    <a class="prev-page button" href="<?php echo esc_url(add_query_arg('paged', $current_page - 1, $base_url)); ?>">
                                        <span class="screen-reader-text"><?php _e('Pagina precedente', 'document-viewer-plugin'); ?></span>
                                        <span aria-hidden="true">‹</span>
                                    </a>
                                <?php else: ?>
                                    <span class="tablenav-pages-navspan button disabled" aria-hidden="true">‹</span>
                                <?php endif; ?>
                                
                                <!-- Informazioni pagina corrente -->
                                <span class="paging-input">
                                    <label for="current-page-selector" class="screen-reader-text"><?php _e('Pagina corrente', 'document-viewer-plugin'); ?></label>
                                    <input class="current-page" id="current-page-selector" type="text" name="paged" value="<?php echo $current_page; ?>" size="2" aria-describedby="table-paging">
                                    <span class="tablenav-paging-text"> di <span class="total-pages"><?php echo $total_pages; ?></span></span>
                                </span>
                                
                                <!-- Pagina successiva -->
                                <?php if ($current_page < $total_pages): ?>
                                    <a class="next-page button" href="<?php echo esc_url(add_query_arg('paged', $current_page + 1, $base_url)); ?>">
                                        <span class="screen-reader-text"><?php _e('Pagina successiva', 'document-viewer-plugin'); ?></span>
                                        <span aria-hidden="true">›</span>
                                    </a>
                                <?php else: ?>
                                    <span class="tablenav-pages-navspan button disabled" aria-hidden="true">›</span>
                                <?php endif; ?>
                                
                                <!-- Ultima pagina -->
                                <?php if ($current_page < $total_pages): ?>
                                    <a class="last-page button" href="<?php echo esc_url(add_query_arg('paged', $total_pages, $base_url)); ?>">
                                        <span class="screen-reader-text"><?php _e('Ultima pagina', 'document-viewer-plugin'); ?></span>
                                        <span aria-hidden="true">»</span>
                                    </a>
                                <?php else: ?>
                                    <span class="tablenav-pages-navspan button disabled" aria-hidden="true">»</span>
                                <?php endif; ?>
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Edit Question Modal -->
        <div id="edit-question-modal" style="display: none; position: fixed; z-index: 100001; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.4);">
            <div style="background-color: #fefefe; margin: 10% auto; padding: 20px; border: 1px solid #888; width: 50%; box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);">
                <h2><?php _e('Modifica Domanda', 'document-viewer-plugin'); ?></h2>
                <form id="edit-question-form">
                    <input type="hidden" id="edit-question-id">
                    <table class="form-table">
                        <tr>
                            <th scope="row"><label for="edit-question-text"><?php _e('Testo Domanda', 'document-viewer-plugin'); ?></label></th>
                            <td>
                                <textarea id="edit-question-text" name="question_text" rows="3" class="large-text" required></textarea>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="edit-question-category"><?php _e('Categoria', 'document-viewer-plugin'); ?></label></th>
                            <td>
                                <select id="edit-question-category" name="category">
                                    <option value="general"><?php _e('Generale', 'document-viewer-plugin'); ?></option>
                                    <option value="investment"><?php _e('Investimenti', 'document-viewer-plugin'); ?></option>
                                    <option value="retirement"><?php _e('Pensione', 'document-viewer-plugin'); ?></option>
                                    <option value="tax"><?php _e('Tasse', 'document-viewer-plugin'); ?></option>
                                    <option value="balance_sheet"><?php _e('Analisi di Bilancio', 'document-viewer-plugin'); ?></option>
                                    <option value="kpi"><?php _e('Indici KPI', 'document-viewer-plugin'); ?></option>
                                    <option value="ratios"><?php _e('Ratio Finanziari', 'document-viewer-plugin'); ?></option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="edit-question-active"><?php _e('Attiva', 'document-viewer-plugin'); ?></label></th>
                            <td>
                                <input type="checkbox" id="edit-question-active" name="active" value="1">
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="edit-question-order"><?php _e('Ordine', 'document-viewer-plugin'); ?></label></th>
                            <td>
                                <input type="number" id="edit-question-order" name="sort_order" class="small-text" min="0" max="999">
                            </td>
                        </tr>
                    </table>
                    <div class="submit-container" style="text-align: right; margin-top: 20px;">
                        <button type="button" class="button" id="close-modal"><?php _e('Annulla', 'document-viewer-plugin'); ?></button>
                        <button type="submit" class="button button-primary"><?php _e('Salva Modifiche', 'document-viewer-plugin'); ?></button>
                        <span class="spinner" style="float: none;"></span>
                    </div>
                </form>
            </div>
        </div>

        <script>
            jQuery(document).ready(function($) {
                // Import educational questions
                $('#import-educational-questions').on('click', function() {
                    var $button = $(this);
                    var $spinner = $('#import-spinner');
                    var $result = $('#import-result');
                    
                    $button.prop('disabled', true);
                    $spinner.addClass('is-active');
                    $result.empty();
                    
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'import_educational_questions',
                            nonce: '<?php echo wp_create_nonce('academy_questions_nonce'); ?>'
                        },
                        success: function(response) {
                            if (response.success) {
                                $result.html('<div class="notice notice-success inline"><p>' + response.data.message + '</p></div>');
                                if (response.data.questions_added > 0) {
                                    setTimeout(function() {
                                        location.reload();
                                    }, 2000);
                                }
                            } else {
                                $result.html('<div class="notice notice-error inline"><p>' + (response.data.message || 'Errore durante l\'importazione.') + '</p></div>');
                            }
                        },
                        error: function() {
                            $result.html('<div class="notice notice-error inline"><p>Errore di connessione durante l\'importazione.</p></div>');
                        },
                        complete: function() {
                            $button.prop('disabled', false);
                            $spinner.removeClass('is-active');
                        }
                    });
                });

                // Add new question
                $('#add-academy-question-form').on('submit', function(e) {
                    e.preventDefault();
                    var $form = $(this);
                    var $spinner = $form.find('.spinner');
                    
                    $spinner.addClass('is-active');
                    
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'add_academy_question',
                            nonce: '<?php echo wp_create_nonce('academy_questions_nonce'); ?>',
                            question_text: $('#new-question-text').val(),
                            category: $('#new-question-category').val()
                        },
                        success: function(response) {
                            if (response.success) {
                                location.reload();
                            } else {
                                alert(response.data.message || 'Errore durante il salvataggio della domanda.');
                            }
                        },
                        error: function() {
                            alert('Errore di connessione.');
                        },
                        complete: function() {
                            $spinner.removeClass('is-active');
                        }
                    });
                });
                
                // Edit question - open modal
                $('.edit-question').on('click', function() {
                    var $row = $(this).closest('tr');
                    var id = $row.data('id');
                    var text = $row.find('.question-text').text();
                    var category = $row.find('.question-category').text();
                    var active = $row.find('.question-active').is(':checked');
                    var order = $row.find('.question-order').val();
                    
                    $('#edit-question-id').val(id);
                    $('#edit-question-text').val(text);
                    $('#edit-question-category').val(category);
                    $('#edit-question-active').prop('checked', active);
                    $('#edit-question-order').val(order);
                    
                    $('#edit-question-modal').show();
                });
                
                // Close modal
                $('#close-modal').on('click', function() {
                    $('#edit-question-modal').hide();
                });
                
                $(window).on('click', function(e) {
                    if ($(e.target).is('#edit-question-modal')) {
                        $('#edit-question-modal').hide();
                    }
                });
                
                // Edit question - submit form
                $('#edit-question-form').on('submit', function(e) {
                    e.preventDefault();
                    var $form = $(this);
                    var $spinner = $form.find('.spinner');
                    
                    $spinner.addClass('is-active');
                    
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'update_academy_question',
                            nonce: '<?php echo wp_create_nonce('academy_questions_nonce'); ?>',
                            id: $('#edit-question-id').val(),
                            question_text: $('#edit-question-text').val(),
                            category: $('#edit-question-category').val(),
                            active: $('#edit-question-active').is(':checked') ? 1 : 0,
                            sort_order: $('#edit-question-order').val()
                        },
                        success: function(response) {
                            if (response.success) {
                                $('#edit-question-modal').hide();
                                location.reload();
                            } else {
                                alert(response.data.message || 'Errore durante l\'aggiornamento della domanda.');
                            }
                        },
                        error: function() {
                            alert('Errore di connessione.');
                        },
                        complete: function() {
                            $spinner.removeClass('is-active');
                        }
                    });
                });
                
                // Delete question
                $('.delete-question').on('click', function() {
                    if (!confirm('<?php _e('Sei sicuro di voler eliminare questa domanda?', 'document-viewer-plugin'); ?>')) {
                        return;
                    }
                    
                    var $button = $(this);
                    var id = $button.data('id');
                    
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'delete_academy_question',
                            nonce: '<?php echo wp_create_nonce('academy_questions_nonce'); ?>',
                            id: id
                        },
                        success: function(response) {
                            if (response.success) {
                                $button.closest('tr').fadeOut(400, function() {
                                    $(this).remove();
                                });
                            } else {
                                alert(response.data.message || 'Errore durante l\'eliminazione della domanda.');
                            }
                        },
                        error: function() {
                            alert('Errore di connessione.');
                        }
                    });
                });
                
                // Update active status
                $('.question-active').on('change', function() {
                    var $checkbox = $(this);
                    var id = $checkbox.closest('tr').data('id');
                    var active = $checkbox.is(':checked') ? 1 : 0;
                    
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'update_academy_question',
                            nonce: '<?php echo wp_create_nonce('academy_questions_nonce'); ?>',
                            id: id,
                            active: active,
                            partial_update: true
                        },
                        error: function() {
                            // Revert checkbox state on error
                            $checkbox.prop('checked', !$checkbox.is(':checked'));
                            alert('Errore durante l\'aggiornamento dello stato.');
                        }
                    });
                });
                
                // Update sort order
                $('.question-order').on('change', function() {
                    var $input = $(this);
                    var id = $input.closest('tr').data('id');
                    var order = $input.val();
                    
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'update_academy_question',
                            nonce: '<?php echo wp_create_nonce('academy_questions_nonce'); ?>',
                            id: id,
                            sort_order: order,
                            partial_update: true
                        },
                        error: function() {
                            alert('Errore durante l\'aggiornamento dell\'ordine.');
                        }
                    });
                });
                
                // Filter questions
                $('#filter-questions-btn').on('click', function() {
                    var category = $('#filter-question-category').val();
                    // Utilizziamo lo slug corretto che abbiamo impostato nel menu
                    location.href = 'admin.php?page=financial-academy-manager&category=' + category;
                });
                
                // Handle pagination input
                $('#current-page-selector, #current-page-selector-top').on('keypress', function(e) {
                    if (e.which == 13) { // Enter key
                        var page = parseInt($(this).val());
                        var totalPages = parseInt($('.total-pages').first().text());
                        
                        if (page >= 1 && page <= totalPages) {
                            var currentUrl = new URL(window.location.href);
                            var category = currentUrl.searchParams.get('category');
                            
                            var newUrl = 'admin.php?page=financial-academy-manager&paged=' + page;
                            if (category && category !== 'all') {
                                newUrl += '&category=' + category;
                            }
                            
                            location.href = newUrl;
                        } else {
                            alert('Inserisci un numero di pagina valido (1-' + totalPages + ')');
                            $(this).val(<?php echo $current_page; ?>); // Reset to current page
                        }
                    }
                });
            });
        </script>
        <?php
    }

    /**
     * Get all questions for admin with pagination support
     */
    private function get_all_questions($page = 1, $per_page = 20) {
        global $wpdb;

        // Check if we have a category filter
        $category = isset($_GET['category']) ? sanitize_text_field($_GET['category']) : 'all';
        
        $where = "";
        if (!empty($category) && $category !== 'all') {
            $where = $wpdb->prepare("WHERE category = %s", $category);
        }

        // Calculate offset
        $offset = ($page - 1) * $per_page;

        $questions = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT * FROM {$this->questions_table} 
                 {$where}
                 ORDER BY sort_order ASC, id ASC
                 LIMIT %d OFFSET %d",
                $per_page,
                $offset
            ),
            ARRAY_A
        );

        return $questions;
    }

    /**
     * Get total questions count for pagination
     */
    private function get_questions_count() {
        global $wpdb;

        // Check if we have a category filter
        $category = isset($_GET['category']) ? sanitize_text_field($_GET['category']) : 'all';
        
        $where = "";
        if (!empty($category) && $category !== 'all') {
            $where = $wpdb->prepare("WHERE category = %s", $category);
        }

        $count = $wpdb->get_var(
            "SELECT COUNT(*) FROM {$this->questions_table} {$where}"
        );

        return intval($count);
    }

    /**
     * AJAX handler to add a new academy question
     */
    public function add_academy_question_ajax() {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Permesso negato.', 'document-viewer-plugin')));
            return;
        }
        
        check_ajax_referer('academy_questions_nonce', 'nonce');
        
        $question_text = isset($_POST['question_text']) ? sanitize_textarea_field($_POST['question_text']) : '';
        $category = isset($_POST['category']) ? sanitize_text_field($_POST['category']) : 'general';
        
        if (empty($question_text)) {
            wp_send_json_error(array('message' => __('Il testo della domanda è obbligatorio.', 'document-viewer-plugin')));
            return;
        }
        
        global $wpdb;
        
        // Get the highest sort order value
        $max_order = $wpdb->get_var("SELECT MAX(sort_order) FROM {$this->questions_table}");
        $next_order = ($max_order !== null) ? intval($max_order) + 10 : 10;
        
        $result = $wpdb->insert(
            $this->questions_table,
            array(
                'question_text' => $question_text,
                'category' => $category,
                'active' => 1,
                'sort_order' => $next_order
            ),
            array('%s', '%s', '%d', '%d')
        );
        
        if ($result === false) {
            wp_send_json_error(array('message' => __('Errore durante il salvataggio della domanda.', 'document-viewer-plugin')));
            return;
        }
        
        wp_send_json_success(array(
            'message' => __('Domanda aggiunta con successo.', 'document-viewer-plugin'),
            'id' => $wpdb->insert_id
        ));
    }

    /**
     * AJAX handler to update an academy question
     */
    public function update_academy_question_ajax() {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Permesso negato.', 'document-viewer-plugin')));
            return;
        }
        
        check_ajax_referer('academy_questions_nonce', 'nonce');
        
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        
        if (empty($id)) {
            wp_send_json_error(array('message' => __('ID domanda mancante.', 'document-viewer-plugin')));
            return;
        }
        
        // Build data array based on available fields
        $data = array();
        $formats = array();
        
        // Check if this is a partial update (just active status or order)
        $partial_update = isset($_POST['partial_update']) && $_POST['partial_update'];
        
        if (!$partial_update) {
            // Full update requires question text
            if (!isset($_POST['question_text']) || empty($_POST['question_text'])) {
                wp_send_json_error(array('message' => __('Il testo della domanda è obbligatorio.', 'document-viewer-plugin')));
                return;
            }
            
            $data['question_text'] = sanitize_textarea_field($_POST['question_text']);
            $formats[] = '%s';
            
            if (isset($_POST['category'])) {
                $data['category'] = sanitize_text_field($_POST['category']);
                $formats[] = '%s';
            }
        }
        
        // These fields can be updated in both full and partial updates
        if (isset($_POST['active'])) {
            $data['active'] = intval($_POST['active']);
            $formats[] = '%d';
        }
        
        if (isset($_POST['sort_order'])) {
            $data['sort_order'] = intval($_POST['sort_order']);
            $formats[] = '%d';
        }
        
        global $wpdb;
        
        $result = $wpdb->update(
            $this->questions_table,
            $data,
            array('id' => $id),
            $formats,
            array('%d')
        );
        
        if ($result === false) {
            wp_send_json_error(array('message' => __('Errore durante l\'aggiornamento della domanda.', 'document-viewer-plugin')));
            return;
        }
        
        wp_send_json_success(array(
            'message' => __('Domanda aggiornata con successo.', 'document-viewer-plugin')
        ));
    }

    /**
     * AJAX handler to delete an academy question
     */
    public function delete_academy_question_ajax() {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Permesso negato.', 'document-viewer-plugin')));
            return;
        }
        
        check_ajax_referer('academy_questions_nonce', 'nonce');
        
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        
        if (empty($id)) {
            wp_send_json_error(array('message' => __('ID domanda mancante.', 'document-viewer-plugin')));
            return;
        }
        
        global $wpdb;
        
        $result = $wpdb->delete(
            $this->questions_table,
            array('id' => $id),
            array('%d')
        );
        
        if ($result === false) {
            wp_send_json_error(array('message' => __('Errore durante l\'eliminazione della domanda.', 'document-viewer-plugin')));
            return;
        }
        
        wp_send_json_success(array(
            'message' => __('Domanda eliminata con successo.', 'document-viewer-plugin')
        ));
    }

    /**
     * Add new balance sheet, KPI and ratio questions to existing installations
     */
    public function add_new_financial_questions() {
        global $wpdb;

        // Nuove domande per analisi bilancio, KPI e ratio
        $new_questions = array(
            // Domande analisi di bilancio
            array("text" => "Analizza la struttura patrimoniale dell'azienda dal bilancio", "category" => "balance_sheet"),
            array("text" => "Valuta la situazione finanziaria aziendale attraverso il bilancio", "category" => "balance_sheet"),
            array("text" => "Esamina i flussi di cassa operativi dell'azienda", "category" => "balance_sheet"),
            array("text" => "Analizza l'evoluzione del fatturato negli ultimi anni", "category" => "balance_sheet"),
            array("text" => "Valuta la gestione dei crediti verso clienti", "category" => "balance_sheet"),
            array("text" => "Esamina la composizione delle rimanenze di magazzino", "category" => "balance_sheet"),
            array("text" => "Analizza la struttura del debito aziendale", "category" => "balance_sheet"),
            array("text" => "Valuta la posizione finanziaria netta dell'azienda", "category" => "balance_sheet"),
            array("text" => "Esamina l'andamento del capitale circolante netto", "category" => "balance_sheet"),
            array("text" => "Analizza la distribuzione degli investimenti aziendali", "category" => "balance_sheet"),

            // Domande indici KPI
            array("text" => "Calcola il ROE (Return on Equity) dell'azienda", "category" => "kpi"),
            array("text" => "Determina il ROI (Return on Investment) aziendale", "category" => "kpi"),
            array("text" => "Calcola il ROA (Return on Assets) dell'azienda", "category" => "kpi"),
            array("text" => "Analizza l'EBITDA margin dell'azienda", "category" => "kpi"),
            array("text" => "Determina il tasso di crescita del fatturato", "category" => "kpi"),
            array("text" => "Calcola l'indice di rotazione del capitale investito", "category" => "kpi"),
            array("text" => "Valuta l'efficienza nella gestione del capitale circolante", "category" => "kpi"),
            array("text" => "Analizza l'indice di produttività del lavoro", "category" => "kpi"),
            array("text" => "Calcola il costo del capitale medio ponderato (WACC)", "category" => "kpi"),
            array("text" => "Determina l'Economic Value Added (EVA) dell'azienda", "category" => "kpi"),

            // Domande ratio finanziari
            array("text" => "Calcola il current ratio dell'azienda", "category" => "ratios"),
            array("text" => "Determina il quick ratio (acid test ratio)", "category" => "ratios"),
            array("text" => "Analizza il debt-to-equity ratio", "category" => "ratios"),
            array("text" => "Calcola il debt service coverage ratio", "category" => "ratios"),
            array("text" => "Determina l'asset turnover ratio", "category" => "ratios"),
            array("text" => "Analizza l'inventory turnover ratio", "category" => "ratios"),
            array("text" => "Calcola il receivables turnover ratio", "category" => "ratios"),
            array("text" => "Determina il gross profit margin", "category" => "ratios"),
            array("text" => "Analizza il net profit margin dell'azienda", "category" => "ratios"),
            array("text" => "Calcola il price-to-earnings ratio (P/E)", "category" => "ratios"),
            array("text" => "Determina il price-to-book ratio (P/B)", "category" => "ratios"),
            array("text" => "Analizza il dividend yield dell'azienda", "category" => "ratios"),
            array("text" => "Calcola l'interest coverage ratio", "category" => "ratios"),
            array("text" => "Determina il times interest earned ratio", "category" => "ratios"),
            array("text" => "Analizza il cash conversion cycle", "category" => "ratios")
        );

        $questions_added = 0;
        $max_sort_order = $wpdb->get_var("SELECT MAX(sort_order) FROM {$this->questions_table}");
        $sort_order = $max_sort_order ? $max_sort_order + 1 : 1;

        foreach ($new_questions as $question) {
            // Verifica se la domanda esiste già
            $existing = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM {$this->questions_table} WHERE question_text = %s",
                $question['text']
            ));

            if (!$existing) {
                $result = $wpdb->insert(
                    $this->questions_table,
                    array(
                        'question_text' => $question['text'],
                        'category' => $question['category'],
                        'active' => 1,
                        'sort_order' => $sort_order++
                    ),
                    array('%s', '%s', '%d', '%d')
                );

                if ($result) {
                    $questions_added++;
                }
            }
        }

        dv_debug_log("Added {$questions_added} new financial academy questions", 'academy');
        return $questions_added;
    }

    /**
     * Import updated educational questions for existing installations
     * This function adds new educational questions without removing existing ones
     */
    public function import_updated_educational_questions() {
        global $wpdb;

        // Get current max sort order
        $max_order = $wpdb->get_var("SELECT MAX(sort_order) FROM {$this->questions_table}");
        $current_order = ($max_order !== null) ? intval($max_order) + 1 : 1;

        // New educational questions to add (orientate alla spiegazione)
        $new_educational_questions = array(
            // Analisi di bilancio educative
            array("text" => "Cosa significa struttura patrimoniale nel bilancio aziendale?", "category" => "balance_sheet"),
            array("text" => "Come si interpreta la situazione finanziaria di un'azienda dal bilancio?", "category" => "balance_sheet"),
            array("text" => "Cosa sono i flussi di cassa operativi e perché sono importanti?", "category" => "balance_sheet"),
            array("text" => "Come si legge l'evoluzione del fatturato in un bilancio pluriennale?", "category" => "balance_sheet"),
            array("text" => "Cosa indicano i crediti verso clienti nel bilancio?", "category" => "balance_sheet"),
            array("text" => "Cosa rappresentano le rimanenze di magazzino nel bilancio?", "category" => "balance_sheet"),
            array("text" => "Come si valuta la struttura del debito aziendale?", "category" => "balance_sheet"),
            array("text" => "Cosa significa posizione finanziaria netta e come si calcola?", "category" => "balance_sheet"),
            array("text" => "Cos'è il capitale circolante netto e perché è importante?", "category" => "balance_sheet"),
            array("text" => "Come sono classificati gli investimenti aziendali nel bilancio?", "category" => "balance_sheet"),
            array("text" => "Qual è la differenza tra attivo fisso e attivo circolante?", "category" => "balance_sheet"),
            array("text" => "Cosa rappresenta il patrimonio netto nel bilancio?", "category" => "balance_sheet"),
            array("text" => "Come si legge il conto economico di un'azienda?", "category" => "balance_sheet"),
            array("text" => "Cosa sono gli ammortamenti e come influenzano il bilancio?", "category" => "balance_sheet"),
            array("text" => "Come si interpretano le voci del passivo nel bilancio?", "category" => "balance_sheet"),

            // KPI educativi
            array("text" => "Cos'è il ROE (Return on Equity) e come si interpreta?", "category" => "kpi"),
            array("text" => "Cosa significa ROI (Return on Investment) e quando si usa?", "category" => "kpi"),
            array("text" => "Cos'è il ROA (Return on Assets) e cosa indica?", "category" => "kpi"),
            array("text" => "Cosa rappresenta l'EBITDA margin e perché è importante?", "category" => "kpi"),
            array("text" => "Come si interpreta il tasso di crescita del fatturato?", "category" => "kpi"),
            array("text" => "Cos'è l'indice di rotazione del capitale investito?", "category" => "kpi"),
            array("text" => "Come si misura l'efficienza nella gestione del capitale circolante?", "category" => "kpi"),
            array("text" => "Cos'è l'indice di produttività del lavoro e come si calcola?", "category" => "kpi"),
            array("text" => "Cosa significa WACC (costo del capitale medio ponderato)?", "category" => "kpi"),
            array("text" => "Cos'è l'EVA (Economic Value Added) e come si interpreta?", "category" => "kpi"),
            array("text" => "Cosa sono i KPI operativi e come differiscono da quelli finanziari?", "category" => "kpi"),
            array("text" => "Come si sceglie il giusto KPI per misurare la performance aziendale?", "category" => "kpi"),
            array("text" => "Cos'è il ROIC (Return on Invested Capital)?", "category" => "kpi"),
            array("text" => "Come si interpreta il margine operativo di un'azienda?", "category" => "kpi"),
            array("text" => "Cosa significa efficienza operativa in termini di KPI?", "category" => "kpi"),

            // Ratio finanziari educativi
            array("text" => "Cos'è il current ratio e cosa indica sulla liquidità aziendale?", "category" => "ratios"),
            array("text" => "Qual è la differenza tra current ratio e quick ratio?", "category" => "ratios"),
            array("text" => "Cosa significa debt-to-equity ratio e come si interpreta?", "category" => "ratios"),
            array("text" => "Cos'è il debt service coverage ratio e perché è importante?", "category" => "ratios"),
            array("text" => "Cosa indica l'asset turnover ratio sulla gestione aziendale?", "category" => "ratios"),
            array("text" => "Come si interpreta l'inventory turnover ratio?", "category" => "ratios"),
            array("text" => "Cos'è il receivables turnover ratio e cosa misura?", "category" => "ratios"),
            array("text" => "Qual è la differenza tra gross profit margin e net profit margin?", "category" => "ratios"),
            array("text" => "Cosa rappresenta il net profit margin per un'azienda?", "category" => "ratios"),
            array("text" => "Cos'è il P/E ratio (price-to-earnings) e come si usa?", "category" => "ratios"),
            array("text" => "Cosa indica il P/B ratio (price-to-book) di un'azione?", "category" => "ratios"),
            array("text" => "Cos'è il dividend yield e come si calcola?", "category" => "ratios"),
            array("text" => "Cosa misura l'interest coverage ratio?", "category" => "ratios"),
            array("text" => "Qual è la differenza tra interest coverage e times interest earned?", "category" => "ratios"),
            array("text" => "Cos'è il cash conversion cycle e perché è importante?", "category" => "ratios"),
            array("text" => "Come si interpretano i ratio di liquidità nel loro insieme?", "category" => "ratios"),
            array("text" => "Cosa sono i ratio di leverage e cosa indicano?", "category" => "ratios"),
            array("text" => "Come si valutano i ratio di profittabilità di un'azienda?", "category" => "ratios"),
            array("text" => "Cos'è il working capital ratio e come si interpreta?", "category" => "ratios"),
            array("text" => "Quali sono i principali ratio per valutare la solvibilità aziendale?", "category" => "ratios"),

            // Domande educative generali aggiuntive
            array("text" => "Quali sono le principali differenze tra analisi fondamentale e tecnica?", "category" => "general"),
            array("text" => "Come si valuta il rischio di un investimento finanziario?", "category" => "general"),
            array("text" => "Cosa significa correlazione tra asset finanziari?", "category" => "general"),
            array("text" => "Come funziona il concetto di duration nelle obbligazioni?", "category" => "investment"),
            array("text" => "Cosa sono i derivati finanziari e come funzionano?", "category" => "investment")
        );

        $questions_added = 0;

        foreach ($new_educational_questions as $question) {
            // Check if question already exists
            $exists = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM {$this->questions_table} WHERE question_text = %s",
                $question['text']
            ));

            if ($exists == 0) {
                $result = $wpdb->insert(
                    $this->questions_table,
                    array(
                        'question_text' => $question['text'],
                        'category' => $question['category'],
                        'active' => 1,
                        'sort_order' => $current_order++
                    ),
                    array('%s', '%s', '%d', '%d')
                );

                if ($result !== false) {
                    $questions_added++;
                }
            }
        }

        dv_debug_log("Imported {$questions_added} new educational questions to Financial Academy", 'academy');
        
        return $questions_added;
    }

    /**
     * AJAX handler to import educational questions
     */
    public function import_educational_questions_ajax() {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Permesso negato.', 'document-viewer-plugin')));
            return;
        }
        
        check_ajax_referer('academy_questions_nonce', 'nonce');
        
        $questions_added = $this->import_updated_educational_questions();
        
        if ($questions_added > 0) {
            wp_send_json_success(array(
                'message' => sprintf(
                    __('Importate %d nuove domande educative con successo.', 'document-viewer-plugin'),
                    $questions_added
                ),
                'questions_added' => $questions_added
            ));
        } else {
            wp_send_json_success(array(
                'message' => __('Tutte le domande educative sono già presenti nel database.', 'document-viewer-plugin'),
                'questions_added' => 0
            ));
        }
    }

    /**
     * Get available question categories with localized names
     */
    public function get_categories() {
        return array(
            'all' => __('Tutte le categorie', 'document-viewer-plugin'),
            'general' => __('Generale', 'document-viewer-plugin'),
            'investment' => __('Investimenti', 'document-viewer-plugin'),
            'retirement' => __('Pensione', 'document-viewer-plugin'),
            'tax' => __('Tasse', 'document-viewer-plugin'),
            'balance_sheet' => __('Analisi di Bilancio', 'document-viewer-plugin'),
            'kpi' => __('Indici KPI', 'document-viewer-plugin'),
            'ratios' => __('Ratio Finanziari', 'document-viewer-plugin')
        );
    }

    /**
     * Get category statistics for the mega menu
     */
    public function get_category_stats() {
        global $wpdb;
        
        $stats = $wpdb->get_results(
            "SELECT category, COUNT(*) as count 
             FROM {$this->questions_table} 
             WHERE active = 1 
             GROUP BY category 
             ORDER BY count DESC",
            ARRAY_A
        );
        
        $formatted_stats = array();
        $categories = $this->get_categories();
        
        foreach ($stats as $stat) {
            if (isset($categories[$stat['category']])) {
                $formatted_stats[] = array(
                    'key' => $stat['category'],
                    'name' => $categories[$stat['category']],
                    'count' => intval($stat['count'])
                );
            }
        }
        
        return $formatted_stats;
    }

    /**
     * AJAX handler to get category statistics
     */
    public function get_category_stats_ajax() {
        $stats = $this->get_category_stats();
        wp_send_json_success($stats);
    }

    // ...existing code...
}

// Initialize the Financial Academy Manager
function financial_academy_manager() {
    static $instance;
    
    if (!$instance instanceof Financial_Academy_Manager) {
        $instance = new Financial_Academy_Manager();
    }
    
    return $instance;
}

// Initialize
financial_academy_manager();