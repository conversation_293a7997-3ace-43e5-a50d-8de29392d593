<?php
/**
 * PHPUnit Bootstrap file for Office Add-in testing
 *
 * Sets up WordPress environment mocks and test dependencies
 */

// Composer autoloader
require_once dirname(__DIR__) . '/vendor/autoload.php';

// Initialize Brain Monkey
\Brain\Monkey\setUp();

// Define WordPress constants
if (!defined('ABSPATH')) {
    define('ABSPATH', '/wordpress/');
}

if (!defined('WP_CONTENT_DIR')) {
    define('WP_CONTENT_DIR', ABSPATH . 'wp-content');
}

if (!defined('WP_PLUGIN_DIR')) {
    define('WP_PLUGIN_DIR', WP_CONTENT_DIR . '/plugins');
}

if (!defined('WP_DEBUG')) {
    define('WP_DEBUG', true);
}

if (!defined('HOUR_IN_SECONDS')) {
    define('HOUR_IN_SECONDS', 3600);
}

if (!defined('DAY_IN_SECONDS')) {
    define('DAY_IN_SECONDS', 86400);
}

if (!defined('WEEK_IN_SECONDS')) {
    define('WEEK_IN_SECONDS', 604800);
}

if (!defined('MONTH_IN_SECONDS')) {
    define('MONTH_IN_SECONDS', 2629746);
}

if (!defined('YEAR_IN_SECONDS')) {
    define('YEAR_IN_SECONDS', 31556952);
}

// Mock $_SERVER superglobal
$_SERVER['REMOTE_ADDR'] = '*************';
$_SERVER['HTTP_X_FORWARDED_FOR'] = '';
$_SERVER['HTTP_X_REAL_IP'] = '';
$_SERVER['REQUEST_METHOD'] = 'POST';
$_SERVER['REQUEST_URI'] = '/wp-admin/admin-ajax.php';

// Mock WordPress cache functions with simple array storage
class MockWordPressCache {
    private static $cache = [];
    private static $groups = [];
    
    public static function get($key, $group = 'default') {
        $cache_key = $group . ':' . $key;
        return isset(self::$cache[$cache_key]) ? self::$cache[$cache_key] : false;
    }
    
    public static function set($key, $data, $group = 'default', $expire = 0) {
        $cache_key = $group . ':' . $key;
        self::$cache[$cache_key] = $data;
        return true;
    }
    
    public static function delete($key, $group = 'default') {
        $cache_key = $group . ':' . $key;
        unset(self::$cache[$cache_key]);
        return true;
    }
    
    public static function flush() {
        self::$cache = [];
        return true;
    }
    
    public static function flush_group($group) {
        foreach (self::$cache as $key => $value) {
            if (strpos($key, $group . ':') === 0) {
                unset(self::$cache[$key]);
            }
        }
        return true;
    }
    
    public static function incr($key, $offset = 1, $group = 'default') {
        $cache_key = $group . ':' . $key;
        if (!isset(self::$cache[$cache_key])) {
            self::$cache[$cache_key] = 0;
        }
        self::$cache[$cache_key] += $offset;
        return self::$cache[$cache_key];
    }
    
    public static function add_global_groups($groups) {
        self::$groups = array_merge(self::$groups, (array)$groups);
    }
    
    public static function clear() {
        self::$cache = [];
        self::$groups = [];
    }
}

// Mock WordPress options
class MockWordPressOptions {
    private static $options = [];
    
    public static function get($option, $default = false) {
        return isset(self::$options[$option]) ? self::$options[$option] : $default;
    }
    
    public static function update($option, $value) {
        self::$options[$option] = $value;
        return true;
    }
    
    public static function delete($option) {
        unset(self::$options[$option]);
        return true;
    }
    
    public static function clear() {
        self::$options = [];
    }
}

// Mock WordPress scheduling functions
class MockWordPressScheduler {
    private static $scheduled = [];
    
    public static function isScheduled($hook, $args = []) {
        $key = $hook . serialize($args);
        return isset(self::$scheduled[$key]);
    }
    
    public static function schedule($timestamp, $hook, $args = []) {
        $key = $hook . serialize($args);
        self::$scheduled[$key] = $timestamp;
        return true;
    }
    
    public static function clear($hook, $args = []) {
        $key = $hook . serialize($args);
        unset(self::$scheduled[$key]);
        return true;
    }
    
    public static function clearAll() {
        self::$scheduled = [];
    }
}

// Mock global $wpdb
global $wpdb;
$wpdb = \Mockery::mock('wpdb');
$wpdb->prefix = 'wp_';
$wpdb->shouldReceive('prepare')->andReturnUsing(function($query, ...$args) {
    return vsprintf(str_replace('%s', "'%s'", $query), $args);
});
$wpdb->shouldReceive('get_results')->andReturn([]);
$wpdb->shouldReceive('get_var')->andReturn(null);
$wpdb->shouldReceive('get_row')->andReturn(null);
$wpdb->shouldReceive('query')->andReturn(true);
$wpdb->shouldReceive('insert')->andReturn(true);
$wpdb->shouldReceive('update')->andReturn(true);
$wpdb->shouldReceive('delete')->andReturn(true);
$wpdb->last_error = '';
$wpdb->insert_id = 1;

// WordPress cache functions (actual function definitions)
if (!function_exists('wp_cache_get')) {
    function wp_cache_get($key, $group = '') {
        return MockWordPressCache::get($key, $group);
    }
}

if (!function_exists('wp_cache_set')) {
    function wp_cache_set($key, $data, $group = '', $expire = 0) {
        return MockWordPressCache::set($key, $data, $group, $expire);
    }
}

if (!function_exists('wp_cache_delete')) {
    function wp_cache_delete($key, $group = '') {
        return MockWordPressCache::delete($key, $group);
    }
}

if (!function_exists('wp_cache_flush')) {
    function wp_cache_flush() {
        return MockWordPressCache::flush();
    }
}

if (!function_exists('wp_cache_add_global_groups')) {
    function wp_cache_add_global_groups($groups) {
        return MockWordPressCache::add_global_groups($groups);
    }
}

if (!function_exists('wp_cache_incr')) {
    function wp_cache_incr($key, $offset = 1, $group = '') {
        return MockWordPressCache::incr($key, $offset, $group);
    }
}

if (!function_exists('wp_cache_flush_group')) {
    function wp_cache_flush_group($group) {
        return MockWordPressCache::flush_group($group);
    }
}

// WordPress options functions  
if (!function_exists('get_option')) {
    function get_option($option, $default = false) {
        return MockWordPressOptions::get($option, $default);
    }
}

if (!function_exists('update_option')) {
    function update_option($option, $value) {
        return MockWordPressOptions::update($option, $value);
    }
}

if (!function_exists('delete_option')) {
    function delete_option($option) {
        return MockWordPressOptions::delete($option);
    }
}

// WordPress scheduling functions
if (!function_exists('wp_next_scheduled')) {
    function wp_next_scheduled($hook, $args = []) {
        return MockWordPressScheduler::isScheduled($hook, $args) ? time() + 3600 : false;
    }
}

if (!function_exists('wp_schedule_event')) {
    function wp_schedule_event($timestamp, $recurrence, $hook, $args = []) {
        return MockWordPressScheduler::schedule($timestamp, $hook, $args);
    }
}

if (!function_exists('wp_clear_scheduled_hook')) {
    function wp_clear_scheduled_hook($hook, $args = []) {
        return MockWordPressScheduler::clear($hook, $args);
    }
}

// Basic WordPress functions
if (!function_exists('get_current_user_id')) {
    function get_current_user_id() {
        return 1;
    }
}

if (!function_exists('current_user_can')) {
    function current_user_can($capability) {
        return true;
    }
}

if (!function_exists('is_user_logged_in')) {
    function is_user_logged_in() {
        return true;
    }
}

if (!function_exists('wp_verify_nonce')) {
    function wp_verify_nonce($nonce, $action = -1) {
        return true;
    }
}

if (!function_exists('wp_create_nonce')) {
    function wp_create_nonce($action = -1) {
        return 'test_nonce_12345';
    }
}

if (!function_exists('sanitize_text_field')) {
    function sanitize_text_field($str) {
        return $str;
    }
}

if (!function_exists('sanitize_email')) {
    function sanitize_email($email) {
        return $email;
    }
}

if (!function_exists('sanitize_key')) {
    function sanitize_key($key) {
        return $key;
    }
}

if (!function_exists('esc_html')) {
    function esc_html($text) {
        return $text;
    }
}

if (!function_exists('esc_url')) {
    function esc_url($url) {
        return $url;
    }
}

if (!function_exists('esc_attr')) {
    function esc_attr($text) {
        return $text;
    }
}

if (!function_exists('wp_unslash')) {
    function wp_unslash($value) {
        return $value;
    }
}

if (!function_exists('wp_json_encode')) {
    function wp_json_encode($data, $options = 0, $depth = 512) {
        return json_encode($data, $options, $depth);
    }
}

if (!function_exists('__')) {
    function __($text, $domain = 'default') {
        return $text;
    }
}

if (!function_exists('_e')) {
    function _e($text, $domain = 'default') {
        echo $text;
    }
}

if (!function_exists('esc_html__')) {
    function esc_html__($text, $domain = 'default') {
        return $text;
    }
}

if (!function_exists('esc_attr__')) {
    function esc_attr__($text, $domain = 'default') {
        return $text;
    }
}

if (!function_exists('plugin_dir_path')) {
    function plugin_dir_path($file) {
        return '/path/to/plugin/';
    }
}

if (!function_exists('plugin_dir_url')) {
    function plugin_dir_url($file) {
        return 'http://localhost/wp-content/plugins/plugin/';
    }
}

if (!function_exists('wp_mail')) {
    function wp_mail($to, $subject, $message, $headers = '', $attachments = []) {
        return true;
    }
}

if (!function_exists('wp_safe_remote_get')) {
    function wp_safe_remote_get($url, $args = []) {
        return ['body' => '{"status":"ok"}'];
    }
}

if (!function_exists('wp_remote_retrieve_body')) {
    function wp_remote_retrieve_body($response) {
        return '{"status":"ok"}';
    }
}

if (!function_exists('is_wp_error')) {
    function is_wp_error($thing) {
        return false;
    }
}

if (!function_exists('check_ajax_referer')) {
    function check_ajax_referer($action = -1, $query_arg = false, $die = true) {
        return true;
    }
}

// Mock WordPress functions (Brain Monkey aliases as fallback)
\Brain\Monkey\Functions\when('wp_cache_get')->alias(function($key, $group = 'default') {
    return MockWordPressCache::get($key, $group);
});

\Brain\Monkey\Functions\when('wp_cache_set')->alias(function($key, $data, $group = 'default', $expire = 0) {
    return MockWordPressCache::set($key, $data, $group, $expire);
});

\Brain\Monkey\Functions\when('wp_cache_delete')->alias(function($key, $group = 'default') {
    return MockWordPressCache::delete($key, $group);
});

\Brain\Monkey\Functions\when('wp_cache_flush')->alias(function() {
    return MockWordPressCache::flush();
});

\Brain\Monkey\Functions\when('wp_cache_flush_group')->alias(function($group) {
    return MockWordPressCache::flush_group($group);
});

\Brain\Monkey\Functions\when('wp_cache_incr')->alias(function($key, $offset = 1, $group = 'default') {
    return MockWordPressCache::incr($key, $offset, $group);
});

\Brain\Monkey\Functions\when('wp_cache_add_global_groups')->alias(function($groups) {
    return MockWordPressCache::add_global_groups($groups);
});

\Brain\Monkey\Functions\when('get_option')->alias(function($option, $default = false) {
    return MockWordPressOptions::get($option, $default);
});

\Brain\Monkey\Functions\when('update_option')->alias(function($option, $value) {
    return MockWordPressOptions::update($option, $value);
});

\Brain\Monkey\Functions\when('delete_option')->alias(function($option) {
    return MockWordPressOptions::delete($option);
});

\Brain\Monkey\Functions\when('wp_next_scheduled')->alias(function($hook, $args = []) {
    return MockWordPressScheduler::isScheduled($hook, $args) ? time() + 3600 : false;
});

\Brain\Monkey\Functions\when('wp_schedule_event')->alias(function($timestamp, $recurrence, $hook, $args = []) {
    return MockWordPressScheduler::schedule($timestamp, $hook, $args);
});

\Brain\Monkey\Functions\when('wp_clear_scheduled_hook')->alias(function($hook, $args = []) {
    return MockWordPressScheduler::clear($hook, $args);
});

// Mock basic WordPress functions
\Brain\Monkey\Functions\when('get_current_user_id')->justReturn(1);
\Brain\Monkey\Functions\when('current_user_can')->justReturn(true);
\Brain\Monkey\Functions\when('is_user_logged_in')->justReturn(true);
\Brain\Monkey\Functions\when('wp_verify_nonce')->justReturn(true);
\Brain\Monkey\Functions\when('wp_create_nonce')->justReturn('test_nonce_12345');
\Brain\Monkey\Functions\when('check_ajax_referer')->justReturn(true);
\Brain\Monkey\Functions\when('sanitize_text_field')->returnArg();
\Brain\Monkey\Functions\when('sanitize_email')->returnArg();
\Brain\Monkey\Functions\when('sanitize_key')->returnArg();
\Brain\Monkey\Functions\when('esc_html')->returnArg();
\Brain\Monkey\Functions\when('esc_url')->returnArg();
\Brain\Monkey\Functions\when('esc_attr')->returnArg();
\Brain\Monkey\Functions\when('wp_unslash')->returnArg();
\Brain\Monkey\Functions\when('wp_json_encode')->alias('json_encode');
\Brain\Monkey\Functions\when('__')->returnArg();
\Brain\Monkey\Functions\when('_e')->returnArg();
\Brain\Monkey\Functions\when('esc_html__')->returnArg();
\Brain\Monkey\Functions\when('esc_attr__')->returnArg();
\Brain\Monkey\Functions\when('plugin_dir_path')->justReturn('/path/to/plugin/');
\Brain\Monkey\Functions\when('plugin_dir_url')->justReturn('http://localhost/wp-content/plugins/plugin/');
\Brain\Monkey\Functions\when('wp_mail')->justReturn(true);
\Brain\Monkey\Functions\when('wp_safe_remote_get')->justReturn(['body' => '{"status":"ok"}']);
\Brain\Monkey\Functions\when('wp_remote_retrieve_body')->justReturn('{"status":"ok"}');
\Brain\Monkey\Functions\when('is_wp_error')->justReturn(false);

// Setup PHPUnit teardown to reset mocks
register_shutdown_function(function() {
    MockWordPressCache::clear();
    MockWordPressOptions::clear();
    MockWordPressScheduler::clearAll();
    \Brain\Monkey\tearDown();
    \Mockery::close();
});
