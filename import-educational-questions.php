<?php
/**
 * Import Educational Questions - Standalone Script
 * 
 * Script per importare le nuove domande educative nelle installazioni esistenti
 * 
 * @package Financial_Advisor
 */

// Questo script può essere eseguito direttamente dal browser o tramite WP-CLI
// URL: /wp-content/plugins/financial-advisor-V4/import-educational-questions.php

// Carica WordPress
$wp_load_path = '';
$current_dir = dirname(__FILE__);

// Cerca wp-load.php risalendo la directory
for ($i = 0; $i < 10; $i++) {
    if (file_exists($current_dir . '/wp-load.php')) {
        $wp_load_path = $current_dir . '/wp-load.php';
        break;
    }
    $current_dir = dirname($current_dir);
}

if (empty($wp_load_path)) {
    die('Impossibile trovare wp-load.php. Assicurati che il plugin sia nella directory corretta.');
}

require_once($wp_load_path);

// Verifica che l'utente sia autorizzato (solo admin)
if (!is_admin() && !current_user_can('manage_options')) {
    // Se non siamo in admin e l'utente non ha i permessi, blocca l'accesso
    if (!defined('WP_CLI') || !WP_CLI) {
        die('Accesso negato. Solo gli amministratori possono eseguire questo script.');
    }
}

// Carica la classe Financial Academy Manager
if (!class_exists('Financial_Academy_Manager')) {
    require_once(plugin_dir_path(__FILE__) . 'includes/class-financial-academy-manager.php');
}

// Esegui l'importazione
$academy_manager = financial_academy_manager();
$questions_added = $academy_manager->import_updated_educational_questions();

// Output risultato
if (defined('WP_CLI') && WP_CLI) {
    // Output per WP-CLI
    WP_CLI::success("Importate {$questions_added} nuove domande educative.");
} else {
    // Output per browser
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Importazione Domande Educative - Financial Advisor</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .success { color: #00a32a; background: #f0f8ff; padding: 20px; border-radius: 5px; border-left: 4px solid #00a32a; }
            .info { color: #2271b1; background: #f9f9f9; padding: 20px; border-radius: 5px; border-left: 4px solid #2271b1; }
            .button { display: inline-block; padding: 10px 20px; background: #2271b1; color: white; text-decoration: none; border-radius: 3px; margin-top: 20px; }
            .button:hover { background: #135e96; }
        </style>
    </head>
    <body>
        <h1>Financial Advisor - Importazione Domande Educative</h1>
        
        <?php if ($questions_added > 0): ?>
            <div class="success">
                <h2>✅ Importazione Completata con Successo!</h2>
                <p>Sono state importate <strong><?php echo $questions_added; ?></strong> nuove domande educative nel database.</p>
                <p>Le nuove domande includono:</p>
                <ul>
                    <li><strong>Analisi di Bilancio:</strong> Domande educative su come interpretare bilanci aziendali</li>
                    <li><strong>Indici KPI:</strong> Spiegazioni su ROE, ROI, ROA, EBITDA e altri indicatori</li>
                    <li><strong>Ratio Finanziari:</strong> Domande su current ratio, debt-to-equity, P/E ratio e altri</li>
                </ul>
            </div>
        <?php else: ?>
            <div class="info">
                <h2>ℹ️ Nessuna Nuova Domanda da Importare</h2>
                <p>Tutte le domande educative sono già presenti nel database.</p>
                <p>Il sistema ha verificato che non ci siano duplicati da importare.</p>
            </div>
        <?php endif; ?>
        
        <div style="margin-top: 30px;">
            <h3>Cosa Fare Adesso:</h3>
            <ol>
                <li>Vai alla pagina <strong>Financial Advisor → Financial Academy</strong> nel backend</li>
                <li>Verifica che le nuove domande siano state importate correttamente</li>
                <li>Testa le nuove domande nel widget chat del sito</li>
                <li>Personalizza ulteriormente le domande se necessario</li>
            </ol>
        </div>
        
        <a href="<?php echo admin_url('admin.php?page=financial-academy-manager'); ?>" class="button">
            Vai a Financial Academy
        </a>
        
        <div style="margin-top: 40px; font-size: 12px; color: #666;">
            <p><strong>Note tecniche:</strong></p>
            <ul>
                <li>Le domande esistenti non sono state modificate o eliminate</li>
                <li>Le nuove domande sono orientate alla spiegazione educativa</li>
                <li>Ogni domanda è categorizzata per un facile filtraggio</li>
                <li>L'importazione può essere ripetuta senza creare duplicati</li>
            </ul>
        </div>
    </body>
    </html>
    <?php
}

// Log dell'operazione
if (function_exists('dv_debug_log')) {
    dv_debug_log("Educational questions import completed. Added: {$questions_added} questions", 'academy');
}
?>
