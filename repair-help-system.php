<?php
/**
 * Script di riparazione per il sistema Help
 * Verifica e ripara problemi di configurazione e database
 */

// Prevenire accesso diretto
if (!defined('ABSPATH')) {
    exit('Accesso diretto non consentito');
}

// Verifica permessi admin
if (!current_user_can('manage_options')) {
    wp_die('Non hai i permessi per accedere a questa pagina.');
}

echo '<div style="font-family: Arial, sans-serif; margin: 20px; background: #f9f9f9; padding: 20px; border-radius: 8px;">';
echo '<h1 style="color: #2271b1;">🔧 Riparazione Sistema Help</h1>';

$repair_log = array();
$errors = array();

// 1. Verifica e inizializza istanza globale
echo '<h2>🌐 Verifica Istanza Globale</h2>';
global $widget_help_system;

if (!$widget_help_system) {
    if (class_exists('Widget_Help_System')) {
        try {
            $widget_help_system = new Widget_Help_System();
            $repair_log[] = '✅ Istanza globale inizializzata';
            echo '<p style="color: #00a32a;">✅ Istanza globale inizializzata con successo</p>';
        } catch (Exception $e) {
            $errors[] = 'Errore inizializzazione istanza: ' . $e->getMessage();
            echo '<p style="color: #d63638;">❌ Errore inizializzazione: ' . $e->getMessage() . '</p>';
        }
    } else {
        $errors[] = 'Classe Widget_Help_System non disponibile';
        echo '<p style="color: #d63638;">❌ Classe Widget_Help_System non disponibile</p>';
    }
} else {
    echo '<p style="color: #00a32a;">✅ Istanza globale già disponibile</p>';
}

// 2. Verifica e popola database
echo '<h2>💾 Verifica Database</h2>';
$content_option = get_option('widget_help_content', array());
$settings_option = get_option('widget_help_widget_settings', array());

if (empty($content_option) || empty($settings_option)) {
    echo '<p style="color: #dba617;">⚠️ Database vuoto o incompleto - Inizializzazione...</p>';
    
    // Contenuti predefiniti completi
    $default_content = array(
        'report_viewer' => array(
            'main' => '<div class="widget-help-content">
                <h3>Report Viewer - Guida Rapida</h3>
                <div class="help-sections">
                    <div class="help-section">
                        <h4>📊 Visualizzazione Report</h4>
                        <ul>
                            <li><strong>Filtri:</strong> Usa i filtri per trovare rapidamente i report</li>
                            <li><strong>Ordinamento:</strong> Ordina per data, nome o tipo</li>
                            <li><strong>Anteprima:</strong> Visualizza anteprima prima di aprire</li>
                        </ul>
                    </div>
                    <div class="help-section">
                        <h4>📤 Export e Condivisione</h4>
                        <ul>
                            <li><strong>PDF:</strong> Esporta in formato PDF</li>
                            <li><strong>Excel:</strong> Esporta dati in Excel</li>
                            <li><strong>Link:</strong> Condividi tramite link sicuro</li>
                        </ul>
                    </div>
                </div>
            </div>',
            'advanced' => '<h4>Funzioni Avanzate</h4><p>Strumenti per analisi approfondite e personalizzazioni avanzate.</p>'
        ),
        'document_analyzer' => array(
            'main' => '<div class="widget-help-content">
                <h3>Document Analyzer - Guida Rapida</h3>
                <div class="help-sections">
                    <div class="help-section">
                        <h4>📁 Caricamento Documenti</h4>
                        <ul>
                            <li><strong>Formati:</strong> PDF, DOC, DOCX, XLS, XLSX</li>
                            <li><strong>Dimensione:</strong> Max 10MB per file</li>
                            <li><strong>Drag & Drop:</strong> Trascina i file nell\'area</li>
                        </ul>
                    </div>
                    <div class="help-section">
                        <h4>🔍 Analisi AI</h4>
                        <ul>
                            <li><strong>OCR:</strong> Riconoscimento testo automatico</li>
                            <li><strong>Estrazione:</strong> Estrazione dati chiave</li>
                            <li><strong>Classificazione:</strong> Categorizzazione automatica</li>
                        </ul>
                    </div>
                </div>
            </div>',
            'setup' => '<h4>Configurazione</h4><p>Impostazioni per personalizzare l\'analizzatore.</p>'
        ),
        'financial_academy' => array(
            'main' => '<div class="widget-help-content">
                <h3>Financial Academy - Guida Rapida</h3>
                <div class="help-sections">
                    <div class="help-section">
                        <h4>❓ Gestione Domande</h4>
                        <ul>
                            <li><strong>Creazione:</strong> Aggiungi nuove domande educative</li>
                            <li><strong>Categorie:</strong> Organizza per argomenti</li>
                            <li><strong>Difficoltà:</strong> Imposta livelli di complessità</li>
                        </ul>
                    </div>
                </div>
            </div>'
        ),
        'payment_gateway' => array(
            'main' => '<div class="widget-help-content">
                <h3>Payment Gateway - Guida Rapida</h3>
                <div class="help-sections">
                    <div class="help-section">
                        <h4>💳 Gestione Pagamenti</h4>
                        <ul>
                            <li><strong>Configurazione:</strong> Setup provider di pagamento</li>
                            <li><strong>Abbonamenti:</strong> Gestione piani ricorrenti</li>
                            <li><strong>Monitoraggio:</strong> Tracciamento transazioni</li>
                        </ul>
                    </div>
                </div>
            </div>'
        )
    );
    
    $default_settings = array(
        'report_viewer' => array('enabled' => true, 'position' => 'right'),
        'document_analyzer' => array('enabled' => true, 'position' => 'right'),
        'financial_academy' => array('enabled' => true, 'position' => 'left'),
        'payment_gateway' => array('enabled' => true, 'position' => 'center')
    );
    
    // Salva nel database
    update_option('widget_help_content', $default_content);
    update_option('widget_help_widget_settings', $default_settings);
    
    // Salva anche le opzioni generali
    $general_options = array(
        'global_enabled' => true,
        'default_position' => 'right',
        'auto_show' => false,
        'show_on_hover' => true
    );
    update_option('widget_help_general_settings', $general_options);
    
    $repair_log[] = '✅ Database inizializzato con ' . count($default_content) . ' widget';
    echo '<p style="color: #00a32a;">✅ Database inizializzato con ' . count($default_content) . ' widget</p>';
    
    // Aggiorna il sistema se disponibile
    if ($widget_help_system && method_exists($widget_help_system, 'load_saved_content')) {
        $widget_help_system->load_saved_content();
        $repair_log[] = '✅ Sistema aggiornato con nuovi contenuti';
        echo '<p style="color: #00a32a;">✅ Sistema aggiornato con nuovi contenuti</p>';
    }
} else {
    echo '<p style="color: #00a32a;">✅ Database già popolato</p>';
}

// 3. Verifica widget registrati
echo '<h2>📋 Verifica Widget Registrati</h2>';
if ($widget_help_system && method_exists($widget_help_system, 'get_all_widgets')) {
    $widgets = $widget_help_system->get_all_widgets();
    if (empty($widgets)) {
        echo '<p style="color: #dba617;">⚠️ Nessun widget registrato - Forzando registrazione...</p>';
        
        // Forza la registrazione dei widget predefiniti
        if (method_exists($widget_help_system, 'register_default_widgets')) {
            // Usa reflection per accedere al metodo privato
            $reflection = new ReflectionClass($widget_help_system);
            $method = $reflection->getMethod('register_default_widgets');
            $method->setAccessible(true);
            $method->invoke($widget_help_system);
            
            $repair_log[] = '✅ Widget predefiniti registrati forzatamente';
            echo '<p style="color: #00a32a;">✅ Widget predefiniti registrati</p>';
        }
    } else {
        echo '<p style="color: #00a32a;">✅ ' . count($widgets) . ' widget registrati</p>';
        echo '<ul>';
        foreach ($widgets as $id => $config) {
            $status = $config['enabled'] ? '🟢' : '🔴';
            echo "<li>$status $id: {$config['name']}</li>";
        }
        echo '</ul>';
    }
} else {
    $errors[] = 'Impossibile verificare widget registrati';
    echo '<p style="color: #d63638;">❌ Impossibile verificare widget registrati</p>';
}

// 4. Test AJAX endpoints
echo '<h2>🔄 Verifica AJAX Endpoints</h2>';
if (has_action('wp_ajax_get_widget_help')) {
    echo '<p style="color: #00a32a;">✅ Handler AJAX wp_ajax_get_widget_help registrato</p>';
} else {
    echo '<p style="color: #d63638;">❌ Handler AJAX wp_ajax_get_widget_help non registrato</p>';
    $errors[] = 'Handler AJAX mancante';
}

// 5. Riepilogo riparazione
echo '<h2>📋 Riepilogo Riparazione</h2>';

if (empty($errors)) {
    echo '<div style="background: #d1edff; border: 1px solid #72aee6; padding: 15px; border-radius: 5px;">';
    echo '<h3 style="color: #00a32a;">✅ Sistema Help Riparato con Successo!</h3>';
    echo '<p>Tutte le verifiche sono state completate e i problemi risolti.</p>';
    
    if (!empty($repair_log)) {
        echo '<h4>Azioni eseguite:</h4>';
        echo '<ul>';
        foreach ($repair_log as $action) {
            echo '<li>' . $action . '</li>';
        }
        echo '</ul>';
    }
    
    echo '<p><strong>Il sistema Help è ora completamente funzionale!</strong></p>';
    echo '</div>';
} else {
    echo '<div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;">';
    echo '<h3 style="color: #d63638;">⚠️ Alcuni problemi persistono</h3>';
    echo '<ul>';
    foreach ($errors as $error) {
        echo '<li style="color: #d63638;">' . $error . '</li>';
    }
    echo '</ul>';
    echo '</div>';
}

// 6. Link di test
echo '<h2>🔗 Link Utili</h2>';
echo '<p>';
echo '<a href="' . admin_url('admin.php?page=widget-help-admin') . '" class="button button-primary">Vai alla Configurazione Help</a> ';
echo '<a href="' . admin_url('admin.php?page=test-help-system') . '" class="button button-secondary">Test Sistema Help</a>';
echo '</p>';

echo '</div>';
?>
