<?php

/**
 * @link       http://patchwork2.org/
 * <AUTHOR> <<EMAIL>>
 * @copyright  2010-2018 I<PERSON><PERSON>
 * @license    http://www.opensource.org/licenses/mit-license.html
 */
namespace Patchwork\CallRerouting;

class Handle
{
    private $references = [];
    private $expirationHandlers = [];
    private $silenced = false;
    private $tags = [];

    public function __destruct()
    {
        $this->expire();
    }

    public function tag($tag)
    {
        $this->tags[] = $tag;
    }

    public function hasTag($tag)
    {
        return in_array($tag, $this->tags);
    }

    public function addReference(&$reference)
    {
        $this->references[] = &$reference;
    }

    public function expire()
    {
        foreach ($this->references as &$reference) {
            $reference = null;
        }
        if (!$this->silenced) {
            foreach ($this->expirationHandlers as $expirationHandler) {
                $expirationHandler();
            }
        }
        $this->expirationHandlers = [];
    }

    public function addExpirationHandler(callable $expirationHandler)
    {
        $this->expirationH<PERSON><PERSON>[] = $expirationHandler;
    }

    public function silence()
    {
        $this->silenced = true;
    }

    public function unsilence()
    {
        $this->silenced = false;
    }
}
