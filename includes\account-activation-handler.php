<?php
/**
 * Account Activation Handler
 * 
 * Gestisce l'attivazione degli account utente dopo la registrazione
 * 
 * @package Financial_Advisor
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class Account_Activation_Handler {
    /**
     * Constructor
     */
    public function __construct() {
        // Intercetta le richieste di attivazione degli account
        add_action('init', array($this, 'process_activation_request'));
        
        // Aggiungi shortcode per mostrare un messaggio dopo l'attivazione
        add_shortcode('activation_message', array($this, 'activation_message_shortcode'));
        
        // Aggiungi un filtro per i redirect dopo l'attivazione
        add_filter('login_redirect', array($this, 'maybe_show_activation_message'), 10, 3);
    }
    
    /**
     * Processa la richiesta di attivazione dell'account
     */
    public function process_activation_request() {
        // Verifica se è una richiesta di attivazione
        if (!isset($_GET['action']) || $_GET['action'] !== 'activate' || 
            !isset($_GET['user_id']) || !isset($_GET['token'])) {
            return;
        }
        
        $user_id = absint($_GET['user_id']);
        $token = sanitize_text_field($_GET['token']);
        
        // Attiva l'account e ottieni il risultato
        $result = $this->activate_account($user_id, $token);
        
        // Salva il risultato in una transient per poterlo mostrare dopo il redirect
        set_transient('account_activation_result_' . $user_id, $result, 300); // 5 minuti
        
        // Redirect alla pagina di login con un parametro per mostrare il messaggio
        $redirect_url = add_query_arg(
            array(
                'activation' => $result['success'] ? 'success' : 'failed',
                'uid' => $user_id
            ),
            home_url()
        );
        
        wp_redirect($redirect_url);
        exit;
    }
    
    /**
     * Attiva l'account dell'utente
     * 
     * @param int $user_id ID dell'utente
     * @param string $token Token di attivazione
     * @return array Risultato dell'attivazione con status e messaggio
     */
    public function activate_account($user_id, $token) {
        // Verifica che l'utente esista
        $user = get_user_by('ID', $user_id);
        
        if (!$user) {
            return array(
                'success' => false,
                'message' => __('Utente non trovato. Il link di attivazione non è valido.', 'document-viewer-plugin')
            );
        }
        
        // Verifica se l'account è già attivato
        $is_activated = get_user_meta($user_id, 'account_activated', true);
        
        if ($is_activated === '1') {
            return array(
                'success' => true,
                'message' => __('Il tuo account è già attivo. Puoi accedere normalmente.', 'document-viewer-plugin')
            );
        }
        
        // Verifica il token di attivazione
        $stored_token = get_user_meta($user_id, 'activation_token', true);
        
        if (empty($stored_token) || $stored_token !== $token) {
            return array(
                'success' => false,
                'message' => __('Token di attivazione non valido. Il link potrebbe essere scaduto o errato.', 'document-viewer-plugin')
            );
        }
        
        // Verifica che il token non sia scaduto
        $token_expiry = intval(get_user_meta($user_id, 'activation_token_expiry', true));
        
        if ($token_expiry && time() > $token_expiry) {
            return array(
                'success' => false,
                'message' => __('Il link di attivazione è scaduto. Richiedi una nuova email di attivazione.', 'document-viewer-plugin')
            );
        }
          // Attiva l'account
        update_user_meta($user_id, 'account_activated', '1');
        
        // Rimuovi i meta dell'attivazione
        delete_user_meta($user_id, 'activation_token');
        delete_user_meta($user_id, 'activation_token_expiry');
        
        // Registra la data e l'ora di attivazione
        update_user_meta($user_id, 'account_activation_date', current_time('mysql'));
        
        // Inizializza i campi statistici nella tabella user_subscription
        $this->initialize_subscription_stats($user_id);
        
        // Invia email di benvenuto se configurato
        $this->maybe_send_welcome_email($user_id);
        
        return array(
            'success' => true,
            'message' => __('Il tuo account è stato attivato con successo! Ora puoi accedere con email e password.', 'document-viewer-plugin')
        );
    }
      /**
     * Inizializza i campi statistici nella tabella user_subscription
     * 
     * @param int $user_id ID dell'utente
     */
    private function initialize_subscription_stats($user_id) {
        global $wpdb;
        
        $table_name = 'wpcd_user_subscription';
        
        // Verifica se l'utente ha già un record nella tabella user_subscription
        $subscription_exists = $wpdb->get_var(
            $wpdb->prepare("SELECT COUNT(*) FROM {$table_name} WHERE id = %d", $user_id)
        );
        
        if ($subscription_exists) {
            // Aggiorna i campi statistici se il record già esiste
            $wpdb->update(
                $table_name,
                array(
                    'analysis_count' => 0,
                    'tokens_used' => 0,
                    'actual_cost' => 0.00,
                    'tot_cost' => 0.00,
                    'last_update' => current_time('mysql')
                ),
                array('id' => $user_id),
                array('%d', '%d', '%f', '%f', '%s'),
                array('%d')
            );
            
            dv_debug_log("Campi statistici inizializzati per utente esistente ID: {$user_id}");
        }
    }
    
    /**
     * Invia email di benvenuto all'utente attivato
     * 
     * @param int $user_id ID dell'utente
     * @return bool True se l'email è stata inviata
     */
    private function maybe_send_welcome_email($user_id) {
        // Controlla se le email di benvenuto sono abilitate
        $send_welcome = get_option('send_welcome_email', true);
        
        if (!$send_welcome) {
            return false;
        }
        
        $user = get_user_by('ID', $user_id);
        
        if (!$user) {
            return false;
        }
        
        $subject = sprintf(__('Benvenuto su %s', 'document-viewer-plugin'), get_bloginfo('name'));
        
        $message = '<p>' . sprintf(__('Ciao %s,', 'document-viewer-plugin'), $user->display_name) . '</p>';
        $message .= '<p>' . sprintf(__('Il tuo account su %s è stato attivato con successo!', 'document-viewer-plugin'), get_bloginfo('name')) . '</p>';
        $message .= '<p>' . __('Ora puoi accedere al sito per utilizzare tutte le funzionalità.', 'document-viewer-plugin') . '</p>';
        $message .= '<p style="text-align: center; margin: 20px 0;">';
        $message .= '<a href="' . wp_login_url() . '" style="display: inline-block; padding: 10px 20px; background-color: #1a4b78; color: #ffffff; text-decoration: none; border-radius: 4px; font-weight: bold;">' . __('Accedi ora', 'document-viewer-plugin') . '</a>';
        $message .= '</p>';
        $message .= '<p>' . __('Cordiali saluti,', 'document-viewer-plugin') . '<br>' . get_bloginfo('name') . '</p>';
        
        $headers = array('Content-Type: text/html; charset=UTF-8');
        
        return wp_mail($user->user_email, $subject, $message, $headers);
    }
    
    /**
     * Shortcode per mostrare il messaggio di attivazione
     * 
     * @param array $atts Attributi dello shortcode
     * @return string HTML del messaggio
     */
    public function activation_message_shortcode($atts) {
        $atts = shortcode_atts(array(
            'class' => 'activation-message'
        ), $atts, 'activation_message');
        
        // Controlla se siamo in una pagina di attivazione
        if (!isset($_GET['activation']) || !isset($_GET['uid'])) {
            return '';
        }
        
        $user_id = absint($_GET['uid']);
        $activation_status = sanitize_text_field($_GET['activation']);
        
        // Ottieni il risultato dell'attivazione dalla transient
        $result = get_transient('account_activation_result_' . $user_id);
        
        // Se non c'è un risultato, mostra un messaggio generico
        if (!$result) {
            if ($activation_status === 'success') {
                $result = array(
                    'success' => true,
                    'message' => __('Il tuo account è stato attivato con successo! Ora puoi accedere.', 'document-viewer-plugin')
                );
            } else {
                $result = array(
                    'success' => false,
                    'message' => __('Si è verificato un errore durante l\'attivazione del tuo account. Riprova o contatta l\'amministratore.', 'document-viewer-plugin')
                );
            }
        } else {
            // Elimina la transient dopo averla letta
            delete_transient('account_activation_result_' . $user_id);
        }
        
        $class = esc_attr($atts['class']);
        $result_class = $result['success'] ? 'success' : 'error';
        
        ob_start();
        ?>
        <div class="<?php echo $class; ?> <?php echo $result_class; ?>">
            <p><?php echo esc_html($result['message']); ?></p>
            
            <?php if ($result['success']): ?>
            <p class="login-link">
                <a href="<?php echo esc_url(wp_login_url()); ?>" class="button">
                    <?php _e('Accedi ora', 'document-viewer-plugin'); ?>
                </a>
            </p>
            <?php else: ?>
            <p class="resend-link">
                <a href="#" class="resend-activation-link" data-email="">
                    <?php _e('Richiedi una nuova email di attivazione', 'document-viewer-plugin'); ?>
                </a>
            </p>
            <?php endif; ?>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Mostra un messaggio di attivazione nella pagina di login
     * 
     * @param string $redirect_to URL di redirect
     * @param string $requested_redirect_to URL di redirect richiesto
     * @param WP_User|WP_Error $user Utente o errore
     * @return string URL di redirect modificato
     */
    public function maybe_show_activation_message($redirect_to, $requested_redirect_to, $user) {
        if (isset($_GET['activation']) && isset($_GET['uid'])) {
            // Aggiungi parametri per mostrare il messaggio dopo il login
            $redirect_to = add_query_arg(
                array(
                    'activation' => sanitize_text_field($_GET['activation']),
                    'uid' => absint($_GET['uid'])
                ),
                $redirect_to
            );
        }
        
        return $redirect_to;
    }
}

// Inizializza il gestore
new Account_Activation_Handler();