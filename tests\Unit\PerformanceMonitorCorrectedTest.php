<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;

/**
 * Unit tests for Office_Addin_Performance_Monitor class
 * Converted to use simplified testing infrastructure without Brain Monkey
 */
class PerformanceMonitorCorrectedTest extends TestCase
{
    private $performance_monitor;    protected function setUp(): void
    {
        parent::setUp();
        
        // Clear test data before each test
        \TestOptions::clear();
        \TestCache::clear();
        
        // Include the performance monitor class
        require_once __DIR__ . '/../../includes/class-office-addin-performance-monitor.php';
        
        $this->performance_monitor = new \Office_Addin_Performance_Monitor();
    }

    protected function tearDown(): void
    {
        // Clear test data after each test
        \TestOptions::clear();
        \TestCache::clear();
        
        parent::tearDown();
    }

    /**
     * Helper method to access private properties via reflection
     */
    private function getProperty($object, $property)
    {
        $reflection = new \ReflectionClass($object);
        $prop = $reflection->getProperty($property);
        $prop->setAccessible(true);
        return $prop->getValue($object);
    }

    /**
     * Helper method to set private properties via reflection
     */
    private function setProperty($object, $property, $value)
    {
        $reflection = new \ReflectionClass($object);
        $prop = $reflection->getProperty($property);
        $prop->setAccessible(true);
        $prop->setValue($object, $value);
    }

    public function testStartTimerInitializesTimer(): void
    {
        $operation = 'test_operation';
        
        $result = $this->performance_monitor->start_timer($operation);
        
        $this->assertTrue($result);
        
        // Verify timer is stored
        $timers = $this->getProperty($this->performance_monitor, 'timers');
        $this->assertArrayHasKey($operation, $timers);
        $this->assertIsFloat($timers[$operation]['start_time']);
        $this->assertNull($timers[$operation]['end_time']);
    }

    public function testStopTimerCompletesTimer(): void
    {
        $operation = 'test_operation';
        
        $this->performance_monitor->start_timer($operation);
        usleep(1000); // Sleep 1ms to ensure measurable time
        $result = $this->performance_monitor->stop_timer($operation);
        
        $this->assertIsFloat($result);
        $this->assertGreaterThan(0, $result);
        
        // Verify timer is completed
        $timers = $this->getProperty($this->performance_monitor, 'timers');
        $this->assertIsFloat($timers[$operation]['end_time']);
        $this->assertGreaterThan($timers[$operation]['start_time'], $timers[$operation]['end_time']);
    }

    public function testStopTimerWithoutStartReturnsNull(): void
    {
        $operation = 'nonexistent_operation';
        
        $result = $this->performance_monitor->stop_timer($operation);
        
        $this->assertNull($result);
    }

    public function testGetTimerDuration(): void
    {
        $operation = 'duration_test';
        
        $this->performance_monitor->start_timer($operation);
        usleep(2000); // Sleep 2ms
        $this->performance_monitor->stop_timer($operation);
        
        $duration = $this->performance_monitor->get_timer_duration($operation);
        
        $this->assertIsFloat($duration);
        $this->assertGreaterThan(0.001, $duration); // Should be > 1ms
        $this->assertLessThan(0.1, $duration);      // Should be < 100ms
    }

    public function testGetTimerDurationForRunningTimer(): void
    {
        $operation = 'running_timer';
        
        $this->performance_monitor->start_timer($operation);
        usleep(1000); // Sleep 1ms
        
        $duration = $this->performance_monitor->get_timer_duration($operation);
        
        $this->assertIsFloat($duration);
        $this->assertGreaterThan(0, $duration);
    }

    public function testGetTimerDurationForNonexistentTimer(): void
    {
        $operation = 'nonexistent_timer';
        
        $duration = $this->performance_monitor->get_timer_duration($operation);
        
        $this->assertNull($duration);
    }

    public function testRecordMemoryUsage(): void
    {
        $operation = 'memory_test';
        $memory_before = memory_get_usage();
        
        $result = $this->performance_monitor->record_memory_usage($operation);
        
        $this->assertTrue($result);
        
        // Verify memory usage was recorded
        $memory_data = $this->getProperty($this->performance_monitor, 'memory_usage');
        $this->assertArrayHasKey($operation, $memory_data);
        $this->assertIsInt($memory_data[$operation]);
        $this->assertGreaterThanOrEqual($memory_before, $memory_data[$operation]);
    }

    public function testGetMemoryUsage(): void
    {
        $operation = 'memory_get_test';
        
        $this->performance_monitor->record_memory_usage($operation);
        $memory_usage = $this->performance_monitor->get_memory_usage($operation);
        
        $this->assertIsInt($memory_usage);
        $this->assertGreaterThan(0, $memory_usage);
    }

    public function testGetMemoryUsageForNonexistentOperation(): void
    {
        $operation = 'nonexistent_memory';
        
        $memory_usage = $this->performance_monitor->get_memory_usage($operation);
        
        $this->assertNull($memory_usage);
    }

    public function testLogSlowQuery(): void
    {
        $query = 'SELECT * FROM large_table WHERE complex_condition = 1';
        $duration = 2.5; // 2.5 seconds
        
        $result = $this->performance_monitor->log_slow_query($query, $duration);
        
        $this->assertTrue($result);
        
        // Verify slow query was logged
        $slow_queries = $this->getProperty($this->performance_monitor, 'slow_queries');
        $this->assertCount(1, $slow_queries);
        $this->assertEquals($query, $slow_queries[0]['query']);
        $this->assertEquals($duration, $slow_queries[0]['duration']);
        $this->assertIsInt($slow_queries[0]['timestamp']);
    }

    public function testGetSlowQueries(): void
    {
        // Log multiple slow queries
        $this->performance_monitor->log_slow_query('SELECT * FROM table1', 1.5);
        $this->performance_monitor->log_slow_query('SELECT * FROM table2', 3.2);
        $this->performance_monitor->log_slow_query('SELECT * FROM table3', 0.8);
        
        $slow_queries = $this->performance_monitor->get_slow_queries();
        
        $this->assertIsArray($slow_queries);
        $this->assertCount(3, $slow_queries);
        
        // Verify structure
        foreach ($slow_queries as $query_data) {
            $this->assertArrayHasKey('query', $query_data);
            $this->assertArrayHasKey('duration', $query_data);
            $this->assertArrayHasKey('timestamp', $query_data);
        }
    }

    public function testGetSlowQueriesWithThreshold(): void
    {
        // Log queries with different durations
        $this->performance_monitor->log_slow_query('Fast query', 0.5);
        $this->performance_monitor->log_slow_query('Slow query 1', 2.0);
        $this->performance_monitor->log_slow_query('Slow query 2', 3.5);
        
        $threshold = 1.0; // 1 second threshold
        $slow_queries = $this->performance_monitor->get_slow_queries($threshold);
        
        $this->assertIsArray($slow_queries);
        $this->assertCount(2, $slow_queries); // Only queries > 1 second
        
        foreach ($slow_queries as $query_data) {
            $this->assertGreaterThan($threshold, $query_data['duration']);
        }
    }

    public function testClearSlowQueries(): void
    {
        // Log some slow queries
        $this->performance_monitor->log_slow_query('Query 1', 1.5);
        $this->performance_monitor->log_slow_query('Query 2', 2.0);
        
        $result = $this->performance_monitor->clear_slow_queries();
        
        $this->assertTrue($result);
        
        // Verify queries were cleared
        $slow_queries = $this->performance_monitor->get_slow_queries();
        $this->assertIsArray($slow_queries);
        $this->assertCount(0, $slow_queries);
    }

    public function testGetPerformanceStats(): void
    {
        // Start and stop some timers
        $this->performance_monitor->start_timer('operation1');
        usleep(1000);
        $this->performance_monitor->stop_timer('operation1');
        
        $this->performance_monitor->start_timer('operation2');
        usleep(2000);
        $this->performance_monitor->stop_timer('operation2');
        
        // Record memory usage
        $this->performance_monitor->record_memory_usage('memory_op');
        
        // Log slow queries
        $this->performance_monitor->log_slow_query('Slow query', 1.8);
        
        $stats = $this->performance_monitor->get_performance_stats();
        
        $this->assertIsArray($stats);
        $this->assertArrayHasKey('timers', $stats);
        $this->assertArrayHasKey('memory_usage', $stats);
        $this->assertArrayHasKey('slow_queries', $stats);
        
        $this->assertCount(2, $stats['timers']);
        $this->assertCount(1, $stats['memory_usage']);
        $this->assertCount(1, $stats['slow_queries']);
    }

    public function testResetPerformanceData(): void
    {
        // Populate with test data
        $this->performance_monitor->start_timer('test_op');
        $this->performance_monitor->stop_timer('test_op');
        $this->performance_monitor->record_memory_usage('test_memory');
        $this->performance_monitor->log_slow_query('Test query', 1.5);
        
        $result = $this->performance_monitor->reset_performance_data();
        
        $this->assertTrue($result);
        
        // Verify all data was cleared
        $stats = $this->performance_monitor->get_performance_stats();
        $this->assertCount(0, $stats['timers']);
        $this->assertCount(0, $stats['memory_usage']);
        $this->assertCount(0, $stats['slow_queries']);
    }    public function testIsMonitoringEnabled(): void
    {
        // Test when monitoring is enabled
        \TestOptions::update('office_addin_performance_monitoring_enabled', true);
        $result = $this->performance_monitor->is_monitoring_enabled();
        $this->assertTrue($result);
        
        // Test when monitoring is disabled
        \TestOptions::update('office_addin_performance_monitoring_enabled', false);
        $result = $this->performance_monitor->is_monitoring_enabled();
        $this->assertFalse($result);
        
        // Test default behavior
        \TestOptions::delete('office_addin_performance_monitoring_enabled');
        $result = $this->performance_monitor->is_monitoring_enabled();
        // Should default to enabled
        $this->assertTrue($result);
    }    public function testEnableMonitoring(): void
    {
        $result = $this->performance_monitor->enable_monitoring();
        $this->assertTrue($result);
        
        // Verify option was set
        $enabled = \TestOptions::get('office_addin_performance_monitoring_enabled');
        $this->assertTrue($enabled);
    }    public function testDisableMonitoring(): void
    {
        $result = $this->performance_monitor->disable_monitoring();
        $this->assertTrue($result);
        
        // Verify option was set
        $enabled = \TestOptions::get('office_addin_performance_monitoring_enabled');
        $this->assertFalse($enabled);
    }

    public function testTimerOperationsWhenMonitoringDisabled(): void
    {
        // Disable monitoring
        $this->performance_monitor->disable_monitoring();
        
        $operation = 'disabled_test';
        $start_result = $this->performance_monitor->start_timer($operation);
        $stop_result = $this->performance_monitor->stop_timer($operation);
        
        // Behavior when disabled depends on implementation
        // Either it returns false or still works but doesn't persist data
        if ($start_result === false) {
            $this->assertFalse($start_result);
            $this->assertFalse($stop_result);
        } else {
            // Still allow operations but maybe don't persist
            $this->assertTrue($start_result);
        }
    }

    public function testMultipleTimersSimultaneously(): void
    {
        $operations = ['op1', 'op2', 'op3'];
        
        // Start all timers
        foreach ($operations as $op) {
            $result = $this->performance_monitor->start_timer($op);
            $this->assertTrue($result);
            usleep(500); // Small delay between starts
        }
        
        // Stop timers in reverse order
        foreach (array_reverse($operations) as $op) {
            $duration = $this->performance_monitor->stop_timer($op);
            $this->assertIsFloat($duration);
            $this->assertGreaterThan(0, $duration);
        }
        
        // Verify all timers have durations
        foreach ($operations as $op) {
            $duration = $this->performance_monitor->get_timer_duration($op);
            $this->assertIsFloat($duration);
            $this->assertGreaterThan(0, $duration);
        }
    }

    public function testTimerAccuracy(): void
    {
        $operation = 'accuracy_test';
        
        $this->performance_monitor->start_timer($operation);
        
        $sleep_time = 0.01; // 10ms
        usleep($sleep_time * 1000000);
        
        $duration = $this->performance_monitor->stop_timer($operation);
        
        // Duration should be approximately equal to sleep time (within 5ms tolerance)
        $this->assertGreaterThan($sleep_time - 0.005, $duration);
        $this->assertLessThan($sleep_time + 0.005, $duration);
    }

    public function testMemoryUsageTracking(): void
    {
        $operation = 'memory_tracking';
        
        $memory_before = memory_get_usage();
        
        // Allocate some memory
        $large_array = array_fill(0, 10000, 'test_data');
        
        $this->performance_monitor->record_memory_usage($operation);
        $recorded_memory = $this->performance_monitor->get_memory_usage($operation);
        
        $memory_after = memory_get_usage();
        
        // Recorded memory should be between before and after values
        $this->assertGreaterThanOrEqual($memory_before, $recorded_memory);
        $this->assertLessThanOrEqual($memory_after, $recorded_memory);
        
        // Clean up
        unset($large_array);
    }

    public function testSlowQueryThresholdConfiguration(): void
    {
        // Test if threshold can be configured
        $reflection = new \ReflectionClass($this->performance_monitor);
        
        if ($reflection->hasMethod('set_slow_query_threshold')) {
            $method = $reflection->getMethod('set_slow_query_threshold');
            $method->setAccessible(true);
            
            $new_threshold = 0.5; // 500ms
            $result = $method->invoke($this->performance_monitor, $new_threshold);
            $this->assertTrue($result);
            
            // Test with a query that would be slow with new threshold
            $this->performance_monitor->log_slow_query('Medium query', 0.8);
            $slow_queries = $this->performance_monitor->get_slow_queries($new_threshold);
            $this->assertCount(1, $slow_queries);
        } else {            // If method doesn't exist, test through options
            \TestOptions::update('office_addin_slow_query_threshold', 0.5);
            
            // Test if option affects behavior
            $this->performance_monitor->log_slow_query('Test query', 0.8);
            $slow_queries = $this->performance_monitor->get_slow_queries(0.5);
            $this->assertGreaterThanOrEqual(1, count($slow_queries));
        }
    }

    public function testPerformanceDataPersistence(): void
    {
        // Test if performance data persists across instances
        $operation = 'persistence_test';
        
        $this->performance_monitor->start_timer($operation);
        usleep(1000);
        $this->performance_monitor->stop_timer($operation);
        
        // Create new instance
        $new_monitor = new \Office_Addin_Performance_Monitor();
        
        // Check if data persists (depends on implementation)
        $duration = $new_monitor->get_timer_duration($operation);
        
        // This might be null if data doesn't persist, which is valid
        if ($duration !== null) {
            $this->assertIsFloat($duration);
            $this->assertGreaterThan(0, $duration);
        } else {
            // Data doesn't persist across instances - also valid
            $this->assertNull($duration);
        }
    }

    public function testPerformanceMonitoringOverhead(): void
    {
        // Test that monitoring itself doesn't add significant overhead
        $start_time = microtime(true);
        
        // Perform many monitoring operations
        for ($i = 0; $i < 100; $i++) {
            $op = "perf_test_$i";
            $this->performance_monitor->start_timer($op);
            $this->performance_monitor->stop_timer($op);
            $this->performance_monitor->record_memory_usage($op);
        }
        
        $end_time = microtime(true);
        $total_duration = $end_time - $start_time;
        
        // 100 operations should complete quickly (less than 100ms)
        $this->assertLessThan(0.1, $total_duration, 'Performance monitoring overhead too high');
    }
}
