// Payment Gateway Admin JavaScript
(function($) {
    'use strict';

    $(document).ready(function() {
        console.log('🚀 Payment Gateway Admin JS Loaded');
        initPaymentGatewayAdmin();
    });    function initPaymentGatewayAdmin() {
        console.log('🔧 Initializing Payment Gateway Admin');

        // Tab navigation - Updated to use data-tab instead of href
        $('.nav-tab').on('click', function(e) {
            e.preventDefault();
            console.log('Tab clicked:', $(this).data('tab'));
            const tabId = $(this).data('tab');
            if (tabId) {
                switchTab(tabId);
            }
        });
        
        // Initialize payment gateway testing functionality
        initPaymentGatewayTesting();

        // Keyboard navigation for tabs
        $('.nav-tab').on('keydown', function(e) {
            const $tabs = $('.nav-tab');
            const currentIndex = $tabs.index(this);
            let targetIndex = currentIndex;

            switch(e.key) {
                case 'ArrowLeft':
                    e.preventDefault();
                    targetIndex = currentIndex > 0 ? currentIndex - 1 : $tabs.length - 1;
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    targetIndex = currentIndex < $tabs.length - 1 ? currentIndex + 1 : 0;
                    break;
                case 'Home':
                    e.preventDefault();
                    targetIndex = 0;
                    break;
                case 'End':
                    e.preventDefault();
                    targetIndex = $tabs.length - 1;
                    break;
                case 'Enter':
                case ' ':
                    e.preventDefault();
                    $(this).click();
                    return;
            }

            if (targetIndex !== currentIndex) {
                const $targetTab = $tabs.eq(targetIndex);
                $targetTab.focus();
                const tabId = $targetTab.data('tab');
                if (tabId) {
                    switchTab(tabId);
                }
            }
        });

        // PayPal form submission
        $('#paypal-config-form').on('submit', function(e) {
            e.preventDefault();
            savePayPalConfig();
        });

        // Stripe form submission
        $('#stripe-config-form').on('submit', function(e) {
            e.preventDefault();
            saveStripeConfig();
        });

        // Test connections
        $('#test-paypal-config').on('click', function() {
            testPayPalConfig();
        });

        $('#test-stripe-config').on('click', function() {
            testStripeConfig();
        });

        // Password field visibility toggle
        $('input[type="password"]').each(function() {
            addPasswordToggle($(this));
        });

        // Testing & Debug tab event handlers
        $('#test-all-gateways').on('click', function() {
            testAllGateways();
        });

        $('#test-webhook-endpoints').on('click', function() {
            testWebhookEndpoints();
        });

        $('#simulate-timeout').on('click', function() {
            simulateError('timeout');
        });

        $('#simulate-api-error').on('click', function() {
            simulateError('api_error');
        });

        $('#simulate-network-error').on('click', function() {
            simulateError('network_error');
        });

        $('#run-performance-test').on('click', function() {
            runPerformanceTest();
        });

        $('#stress-test-gateways').on('click', function() {
            stressTestGateways();
        });

        $('#open-debug-console').on('click', function() {
            openDebugConsole();
        });

        $('#export-debug-report').on('click', function() {
            exportDebugReport();
        });

        // System Checklist tab event handlers
        $('#run-full-checklist').on('click', function() {
            runFullChecklist();
        });

        $('#run-quick-check').on('click', function() {
            runQuickCheck();
        });

        $('#export-checklist-report').on('click', function() {
            exportChecklistReport();
        });

        $('#validate-settings').on('click', function() {
            validateGatewaySettings();
        });

        // Error Monitoring tab event handlers
        $('#refresh-monitoring').on('click', function() {
            refreshMonitoringStats();
        });

        $('#clear-error-logs').on('click', function() {
            clearErrorLogs();
        });

        $('#enable-auto-fix').on('click', function() {
            toggleAutoFix();
        });

        $('#start-monitoring').on('click', function() {
            startErrorMonitoring();
        });

        $('#auto-fix-errors').on('click', function() {
            autoFixErrors();
        });

        // Debug panel handlers
        $('#close-debug-panel').on('click', function() {
            closeDebugPanel();
        });

        // Checklist category toggle
        $(document).on('click', '.category-header', function() {
            const $items = $(this).next('.category-items');
            $items.toggleClass('expanded');
        });

        // Environment change handlers
        $('#paypal_environment').on('change', function() {
            const environment = $(this).val();
            updateEnvironmentBadge('paypal', environment);
        });

        $('#stripe_environment').on('change', function() {
            const environment = $(this).val();
            updateEnvironmentBadge('stripe', environment);
        });

        // Trigger environment badges on load
        $('#paypal_environment, #stripe_environment').trigger('change');

        // Auto-refresh monitoring stats every 30 seconds
        setInterval(function() {
            if ($('#monitoring-tab').hasClass('active')) {
                refreshMonitoringStats(true); // Silent refresh
            }
        }, 30000);

        // Initialize monitoring on load
        if ($('#monitoring-tab').hasClass('active')) {
            refreshMonitoringStats(true);
        }

        // Initialize ARIA attributes for accessibility
        initializeTabAccessibility();

        // Initialize testing sub-tabs
        $('.testing-sub-tabs .nav-tab').on('click', function() {
            const testingTabId = $(this).data('testing-tab');
            console.log('Testing sub-tab clicked:', testingTabId);
            
            if (testingTabId) {
                switchTestingSubTab(testingTabId);
            }
        });

        // Manual test buttons for individual gateway tests
        $('.gateway-testing-controls button[data-gateway]').on('click', function() {
            const gateway = $(this).data('gateway');
            const testType = $(this).data('test');
            runGatewayTest(gateway, testType);
        });

        // Automatic test refresh button
        $('#refresh-automatic-tests').on('click', function() {
            refreshAutomaticTests();
        });

        // Export test results
        $('#export-test-results').on('click', function() {
            exportTestResults();
        });

        // Initialize logs management
        initLogsManagement();

        // Initialize Payment Gateway Testing Functionality
        initPaymentGatewayTesting();
    }

    function initializeTabAccessibility() {
        // Set initial ARIA attributes for tab content
        $('.tab-content').each(function() {
            const isActive = $(this).hasClass('active');
            $(this).attr('aria-hidden', !isActive);
        });

        // Ensure proper tab order and ARIA attributes
        $('.nav-tab').each(function() {
            const isActive = $(this).hasClass('nav-tab-active');
            $(this).attr('aria-selected', isActive)
                   .attr('tabindex', isActive ? '0' : '-1');
        });
    }

    function switchTab(tabId) {
        console.log('🔄 Switching to tab:', tabId);

        // Update nav tabs - Updated to use data-tab instead of href
        $('.nav-tab').removeClass('nav-tab-active')
                    .attr('aria-selected', 'false')
                    .attr('tabindex', '-1');

        const $activeTab = $('[data-tab="' + tabId + '"]');
        $activeTab.addClass('nav-tab-active')
                  .attr('aria-selected', 'true')
                  .attr('tabindex', '0');

        // Update content
        $('.tab-content').removeClass('active').attr('aria-hidden', 'true');
        $('#' + tabId).addClass('active').attr('aria-hidden', 'false');

        // Focus management for accessibility
        if (document.activeElement && $(document.activeElement).hasClass('nav-tab')) {
            $activeTab.focus();
        }

        // Trigger custom event for tab change
        $(document).trigger('payment-gateway-tab-changed', [tabId]);

        console.log('✅ Tab switched successfully to:', tabId);
    }

    function savePayPalConfig() {
        const $form = $('#paypal-config-form');
        const $submitBtn = $('#save-paypal-config');

        // Collect form data
        const formData = {
            action: 'save_paypal_config',
            nonce: paymentGatewayAjax.nonce,
            client_id: $('#paypal_client_id').val(),
            client_secret: $('#paypal_client_secret').val(),
            environment: $('#paypal_environment').val(),
            webhook_id: $('#paypal_webhook_id').val(),
            is_active: $('#paypal_is_active').is(':checked') ? 1 : 0
        };

        // Validate required fields
        if (!formData.client_id || !formData.client_secret) {
            showFeedbackMessage('Please fill in all required fields.', 'error');
            return;
        }

        // Show loading state
        $submitBtn.addClass('loading').prop('disabled', true);

        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    showFeedbackMessage(response.data.message, 'success');
                } else {
                    showFeedbackMessage(response.data.message, 'error');
                }
            },
            error: function() {
                showFeedbackMessage(paymentGatewayAjax.messages.save_error, 'error');
            },
            complete: function() {
                $submitBtn.removeClass('loading').prop('disabled', false);
            }
        });
    }

    function saveStripeConfig() {
        const $form = $('#stripe-config-form');
        const $submitBtn = $('#save-stripe-config');

        // Collect form data
        const formData = {
            action: 'save_stripe_config',
            nonce: paymentGatewayAjax.nonce,
            public_key: $('#stripe_public_key').val(),
            secret_key: $('#stripe_secret_key').val(),
            environment: $('#stripe_environment').val(),
            webhook_secret: $('#stripe_webhook_secret').val(),
            is_active: $('#stripe_is_active').is(':checked') ? 1 : 0
        };

        // Validate required fields
        if (!formData.public_key || !formData.secret_key) {
            showFeedbackMessage('Please fill in all required fields.', 'error');
            return;
        }

        // Show loading state
        $submitBtn.addClass('loading').prop('disabled', true);

        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    showFeedbackMessage(response.data.message, 'success');
                } else {
                    showFeedbackMessage(response.data.message, 'error');
                }
            },
            error: function() {
                showFeedbackMessage(paymentGatewayAjax.messages.save_error, 'error');
            },
            complete: function() {
                $submitBtn.removeClass('loading').prop('disabled', false);
            }
        });
    }

    function testPayPalConfig() {
        const $testBtn = $('#test-paypal-config');

        // Collect form data for testing
        const testData = {
            action: 'test_paypal_config',
            nonce: paymentGatewayAjax.nonce,
            client_id: $('#paypal_client_id').val(),
            client_secret: $('#paypal_client_secret').val(),
            environment: $('#paypal_environment').val()
        };

        // Validate required fields
        if (!testData.client_id || !testData.client_secret) {
            showFeedbackMessage('Please fill in Client ID and Client Secret before testing.', 'warning');
            return;
        }

        // Show loading state
        $testBtn.addClass('loading').prop('disabled', true);

        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: testData,
            success: function(response) {
                if (response.success) {
                    showFeedbackMessage(response.data.message, 'success');
                } else {
                    showFeedbackMessage(response.data.message, 'error');
                }
            },
            error: function() {
                showFeedbackMessage(paymentGatewayAjax.messages.test_error, 'error');
            },
            complete: function() {
                $testBtn.removeClass('loading').prop('disabled', false);
            }
        });
    }

    function testStripeConfig() {
        const $testBtn = $('#test-stripe-config');

        // Collect form data for testing
        const testData = {
            action: 'test_stripe_config',
            nonce: paymentGatewayAjax.nonce,
            secret_key: $('#stripe_secret_key').val()
        };

        // Validate required fields
        if (!testData.secret_key) {
            showFeedbackMessage('Please fill in Secret Key before testing.', 'warning');
            return;
        }

        // Show loading state
        $testBtn.addClass('loading').prop('disabled', true);

        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: testData,
            success: function(response) {
                if (response.success) {
                    showFeedbackMessage(response.data.message, 'success');
                } else {
                    showFeedbackMessage(response.data.message, 'error');
                }
            },
            error: function() {
                showFeedbackMessage(paymentGatewayAjax.messages.test_error, 'error');
            },
            complete: function() {
                $testBtn.removeClass('loading').prop('disabled', false);
            }
        });
    }

    // Testing & Debug Functions
    function testAllGateways() {
        showTestingResults();
        updateTestingOutput('🔄 Running comprehensive gateway tests...\n');

        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'test_gateway_connection',
                gateway: 'all',
                test_type: 'comprehensive',
                nonce: paymentGatewayAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    updateTestingOutput('✅ Gateway tests completed successfully!\n\n');
                    updateTestingOutput(formatTestResults(response.data.results));
                } else {
                    updateTestingOutput('❌ Gateway tests failed: ' + response.data.message + '\n');
                }
            },
            error: function() {
                updateTestingOutput('❌ Connection error occurred during testing\n');
            }
        });
    }

    function testWebhookEndpoints() {
        showTestingResults();
        updateTestingOutput('🔄 Testing webhook endpoints...\n');

        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'test_webhook_endpoints',
                nonce: paymentGatewayAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    const results = response.data.results;
                    Object.entries(results).forEach(([gateway, result]) => {
                        const status = result.success ? '✅' : '❌';
                        updateTestingOutput(`${status} ${gateway.toUpperCase()} webhook: ${result.message}\n`);
                    });
                    updateTestingOutput('📊 Webhook endpoint testing completed\n');
                } else {
                    updateTestingOutput('❌ Webhook testing failed: ' + response.data.message + '\n');
                }
            },
            error: function() {
                updateTestingOutput('❌ Connection error during webhook testing\n');
            }
        });
    }

    function simulateError(errorType) {
        showTestingResults();
        updateTestingOutput('🧪 Simulating ' + errorType + ' error...\n');

        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'simulate_payment_error',
                error_type: errorType,
                nonce: paymentGatewayAjax.nonce
            },
            success: function(response) {
                updateTestingOutput('✅ Error simulation completed\n');
                updateTestingOutput('📝 Error details: ' + JSON.stringify(response, null, 2) + '\n');
            },
            error: function(xhr) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    updateTestingOutput('🎯 Simulated error triggered successfully!\n');
                    updateTestingOutput('📝 Error type: ' + errorType + '\n');
                    updateTestingOutput('📝 Error response: ' + JSON.stringify(response.data, null, 2) + '\n');
                } catch (e) {
                    updateTestingOutput('🎯 Error simulation completed with network error\n');
                }
            }
        });
    }

    function runPerformanceTest() {
        showTestingResults();
        updateTestingOutput('⚡ Running performance tests...\n');

        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'run_performance_test',
                nonce: paymentGatewayAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    const results = response.data;
                    updateTestingOutput(`\n📊 Performance Test Results:\n`);
                    updateTestingOutput(`Total execution time: ${results.total_time}ms\n`);
                    updateTestingOutput(`Average response time: ${results.average_time}ms\n`);
                    updateTestingOutput(`Memory usage: ${results.memory_usage}\n`);
                    updateTestingOutput(`Tests completed: ${results.test_count}\n`);

                    if (results.gateway_results) {
                        Object.entries(results.gateway_results).forEach(([gateway, result]) => {
                            updateTestingOutput(`${gateway.toUpperCase()}: ${result.response_time}ms - ${result.status}\n`);
                        });
                    }
                } else {
                    updateTestingOutput('❌ Performance test failed: ' + response.data.message + '\n');
                }
            },
            error: function() {
                updateTestingOutput('❌ Connection error during performance testing\n');
            }
        });
    }

    function stressTestGateways() {
        showTestingResults();
        updateTestingOutput('💪 Running stress test...\n');

        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'stress_test_gateways',
                nonce: paymentGatewayAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    const results = response.data;
                    updateTestingOutput(`\n🏆 Stress Test Results:\n`);
                    updateTestingOutput(`Total requests: ${results.total_requests}\n`);
                    updateTestingOutput(`Successful requests: ${results.successful}\n`);
                    updateTestingOutput(`Failed requests: ${results.failed}\n`);
                    updateTestingOutput(`Success rate: ${results.success_rate}%\n`);
                    updateTestingOutput(`Average response time: ${results.avg_response_time}ms\n`);
                    updateTestingOutput(`Peak memory usage: ${results.peak_memory}\n`);
                } else {
                    updateTestingOutput('❌ Stress test failed: ' + response.data.message + '\n');
                }
            },
            error: function() {
                updateTestingOutput('❌ Connection error during stress testing\n');
            }
        });
    }

    function openDebugConsole() {
        $('#debug-panel').show().addClass('fade-in');
        updateDebugConsole('🔧 Debug Console Initialized\n');
        updateDebugConsole('📊 System Status: Online\n');
        updateDebugConsole('⚙️ Available Commands:\n');
        updateDebugConsole('  - PaymentGatewayChecker.runChecklist()\n');
        updateDebugConsole('  - AdvancedErrorMonitor.showStats()\n');
        updateDebugConsole('  - Use Ctrl+Shift+M for quick access\n\n');
    }

    function exportDebugReport() {
        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'export_debug_report',
                nonce: paymentGatewayAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    downloadReport(response.data.report, response.data.filename);
                    showFeedbackMessage('Debug report exported successfully!', 'success');
                } else {
                    showFeedbackMessage('Failed to export debug report: ' + response.data.message, 'error');
                }
            },
            error: function() {
                showFeedbackMessage('Error occurred while exporting debug report', 'error');
            }
        });
    }

    // System Checklist Functions
    function runFullChecklist() {
        $('#checklist-progress').show();
        $('#checklist-results').hide();
        updateChecklistProgress(0, 'Initializing comprehensive checklist...');

        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'run_gateway_checklist',
                type: 'full',
                nonce: paymentGatewayAjax.nonce
            },
            success: function(response) {
                updateChecklistProgress(100, 'Checklist completed!');

                setTimeout(function() {
                    $('#checklist-progress').hide();
                    if (response.success) {
                        displayChecklistResults(response.data.results);
                        showFeedbackMessage('Checklist completed successfully!', 'success');
                    } else {
                        showFeedbackMessage('Checklist error: ' + response.data.message, 'error');
                    }
                }, 1000);
            },
            error: function() {
                $('#checklist-progress').hide();
                showFeedbackMessage('Error occurred while running checklist', 'error');
            }
        });

        // Simulate progress updates
        animateProgress();
    }

    function runQuickCheck() {
        $('#checklist-progress').show();
        $('#checklist-results').hide();
        updateChecklistProgress(0, 'Running quick health check...');

        // Simulate progress
        animateProgress(3000);

        setTimeout(function() {
            updateChecklistProgress(100, 'Quick check completed!');
            setTimeout(function() {
                $('#checklist-progress').hide();
                displayQuickCheckResults();
            }, 1000);
        }, 3000);
    }

    function exportChecklistReport() {
        const results = getCurrentChecklistResults();
        if (results) {
            downloadReport(results, 'checklist-report-' + new Date().toISOString().slice(0,19) + '.json');
            showFeedbackMessage('Checklist report exported successfully!', 'success');
        } else {
            showFeedbackMessage('No checklist results to export. Please run a checklist first.', 'warning');
        }
    }

    function validateGatewaySettings() {
        showFeedbackMessage('Validating gateway settings...', 'info');

        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'validate_gateway_settings',
                nonce: paymentGatewayAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    showFeedbackMessage('Gateway settings validated successfully!', 'success');
                    displayValidationResults(response.data);
                } else {
                    showFeedbackMessage('Validation failed: ' + response.data.message, 'error');
                }
            },
            error: function() {
                showFeedbackMessage('Error occurred during validation', 'error');
            }
        });
    }

    // Error Monitoring Functions
    function refreshMonitoringStats(silent = false) {
        if (!silent) {
            $('#refresh-monitoring').prop('disabled', true).prepend('<span class="loading-spinner">⟳</span>');
        }

        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'get_system_status',
                nonce: paymentGatewayAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    updateMonitoringStats(response.data.error_stats || {});
                    updateErrorLogs(response.data.recent_logs || []);
                    updateSystemStatusDisplay(response.data);
                    if (!silent) {
                        showFeedbackMessage('Monitoring stats refreshed', 'success');
                    }
                } else {
                    if (!silent) {
                        showFeedbackMessage('Failed to refresh stats: ' + response.data.message, 'error');
                    }
                }
            },
            error: function() {
                if (!silent) {
                    showFeedbackMessage('Error occurred while refreshing stats', 'error');
                }
            },
            complete: function() {
                if (!silent) {
                    $('#refresh-monitoring').prop('disabled', false).find('.loading-spinner').remove();
                }
            }
        });
    }

    function clearErrorLogs() {
        if (!confirm('Are you sure you want to clear all error logs?')) {
            return;
        }

        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'clear_error_logs',
                nonce: paymentGatewayAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    $('#error-logs-list').html('<p class="no-errors">No errors recorded. System running smoothly! ✅</p>');
                    updateMonitoringStats({total: 0, recent: 0});
                    showFeedbackMessage('Error logs cleared successfully', 'success');
                } else {
                    showFeedbackMessage('Failed to clear logs: ' + response.data.message, 'error');
                }
            },
            error: function() {
                showFeedbackMessage('Error occurred while clearing logs', 'error');
            }
        });
    }

    function toggleAutoFix() {
        const $btn = $('#enable-auto-fix');
        const isEnabled = $btn.hasClass('auto-fix-enabled');

        if (isEnabled) {
            $btn.removeClass('auto-fix-enabled').text('🔧 Enable Auto-Fix');
            showFeedbackMessage('Auto-fix disabled', 'info');
        } else {
            $btn.addClass('auto-fix-enabled').text('🛑 Disable Auto-Fix');
            showFeedbackMessage('Auto-fix enabled', 'success');
        }
    }

    function startErrorMonitoring() {
        const $btn = $('#start-monitoring');
        const isMonitoring = $btn.hasClass('monitoring-active');

        if (isMonitoring) {
            $btn.removeClass('monitoring-active').text('▶️ Start Monitoring');
            $('#monitoring-live-feed').hide();
            showFeedbackMessage('Error monitoring stopped', 'info');
        } else {
            $btn.addClass('monitoring-active').text('⏹️ Stop Monitoring');
            $('#monitoring-live-feed').show();
            startLiveFeed();
            showFeedbackMessage('Error monitoring started', 'success');
        }
    }

    function autoFixErrors() {
        if (!confirm('Are you sure you want to run auto-fix? This will attempt to automatically resolve detected issues.')) {
            return;
        }

        showFeedbackMessage('Running auto-fix procedures...', 'info');

        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'auto_fix_errors',
                nonce: paymentGatewayAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    const results = response.data;
                    let message = `Auto-fix completed! Fixed ${results.fixed_count} issues.`;
                    if (results.remaining_count > 0) {
                        message += ` ${results.remaining_count} issues require manual attention.`;
                    }
                    showFeedbackMessage(message, 'success');

                    // Refresh monitoring stats
                    refreshMonitoringStats(true);
                } else {
                    showFeedbackMessage('Auto-fix failed: ' + response.data.message, 'error');
                }
            },
            error: function() {
                showFeedbackMessage('Error occurred during auto-fix', 'error');
            }
        });
    }

    // Payment Gateway Testing Functions
    /**
     * Initialize Payment Gateway Testing Functionality
     */
    function initPaymentGatewayTesting() {
        // Run test button click handler
        $('#run-gateway-tests').on('click', function() {
            runPaymentGatewayTests();
        });
        
        // Run diagnostic button click handler
        $('#run-gateway-diagnostic').on('click', function() {
            const gateway = $(this).data('gateway') || '';
            runPaymentGatewayDiagnostic(gateway);
        });
        
        // Log filters
        $('#log-gateway-filter, #log-level-filter, #log-date-filter').on('change', function() {
            loadPaymentGatewayLogs();
        });
        
        // Clear logs button
        $('#clear-gateway-logs').on('click', function() {
            clearPaymentGatewayLogs();
        });
        
        // Refresh logs button
        $('#refresh-gateway-logs').on('click', function() {
            loadPaymentGatewayLogs();
        });
        
        // Expandable log entries
        $(document).on('click', '.log-entry-expandable', function() {
            $(this).toggleClass('expanded');
        });
        
        // Diagnostic tab navigation
        $(document).on('click', '.diagnostic-tab', function() {
            const target = $(this).data('target');
            $('.diagnostic-tab').removeClass('active');
            $(this).addClass('active');
            $('.diagnostic-content').removeClass('active');
            $('#' + target).addClass('active');
        });
        
        // Export diagnostic report
        $(document).on('click', '.export-diagnostic-report', function() {
            exportDiagnosticReport();
        });
        
        // Run tests again
        $(document).on('click', '.run-diagnostic-tests', function() {
            const gateway = $(this).closest('.diagnostic-content').attr('id').replace('-diagnostic', '');
            runPaymentGatewayDiagnostic(gateway);
        });
        
        // Load logs on page load
        loadPaymentGatewayLogs();
    }

    /**
     * Run payment gateway tests
     */
    function runPaymentGatewayTests() {
        const $resultsContainer = $('#test-results-container');
        
        // Show loading indicator
        $resultsContainer.html('<div class="loading-indicator"><span class="spinner is-active"></span> ' + paymentGatewayTestingData.messages.test_running + '</div>');
        
        // Make AJAX request
        $.ajax({
            url: paymentGatewayTestingData.ajax_url,
            type: 'POST',
            data: {
                action: 'run_payment_gateway_tests',
                nonce: paymentGatewayTestingData.nonce
            },
            success: function(response) {
                if (response.success) {
                    displayTestResults(response.data);
                } else {
                    displayTestError(response.data.message || paymentGatewayTestingData.messages.test_failed);
                }
            },
            error: function() {
                displayTestError(paymentGatewayTestingData.messages.test_failed);
            }
        });
    }

    /**
     * Run payment gateway diagnostic
     * 
     * @param {string} gateway Optional gateway identifier
     */
    function runPaymentGatewayDiagnostic(gateway) {
        const $diagnosticContainer = $('#diagnostic-results-container');
        
        // Show loading indicator
        $diagnosticContainer.html('<div class="loading-indicator"><span class="spinner is-active"></span> ' + paymentGatewayTestingData.messages.test_running + '</div>');
        
        // Make AJAX request
        $.ajax({
            url: paymentGatewayTestingData.ajax_url,
            type: 'POST',
            data: {
                action: 'run_payment_gateway_diagnostic',
                gateway: gateway,
                nonce: paymentGatewayTestingData.nonce
            },
            success: function(response) {
                if (response.success) {
                    $diagnosticContainer.html(response.data.html);
                } else {
                    $diagnosticContainer.html('<div class="notice notice-error"><p>' + (response.data.message || paymentGatewayTestingData.messages.test_failed) + '</p></div>');
                }
            },
            error: function() {
                $diagnosticContainer.html('<div class="notice notice-error"><p>' + paymentGatewayTestingData.messages.test_failed + '</p></div>');
            }
        });
    }

    /**
     * Display test results
     * 
     * @param {object} results Test results data
     */
    function displayTestResults(results) {
        const $resultsContainer = $('#test-results-container');
        let html = '';
        
        // Iterate through test categories
        for (const category in results) {
            if (results.hasOwnProperty(category)) {
                const categoryData = results[category];
                
                html += '<div class="test-results-container">';
                html += '<h3 class="test-results-title">' + categoryData.title + '</h3>';
                html += '<ul class="test-results-list">';
                
                // Iterate through tests in this category
                for (const testId in categoryData.tests) {
                    if (categoryData.tests.hasOwnProperty(testId)) {
                        const test = categoryData.tests[testId];
                        const statusClass = test.result ? 'test-success' : (test.critical ? 'test-error' : 'test-warning');
                        const statusText = test.result ? 'Pass' : 'Fail';
                        const statusStyle = test.result ? 'status-pass' : 'status-fail';
                        
                        html += '<li class="test-item ' + statusClass + '">';
                        html += '<div class="test-item-header">';
                        html += '<span class="test-item-name">' + test.name + '</span>';
                        html += '<span class="test-item-status ' + statusStyle + '">' + statusText + '</span>';
                        html += '</div>';
                        html += '<p class="test-item-message">' + test.message + '</p>';
                        html += '</li>';
                    }
                }
                
                html += '</ul></div>';
            }
        }
        
        $resultsContainer.html(html);
    }

    /**
     * Display test error
     * 
     * @param {string} message Error message
     */
    function displayTestError(message) {
        const $resultsContainer = $('#test-results-container');
        $resultsContainer.html('<div class="notice notice-error"><p>' + message + '</p></div>');
    }

    /**
     * Load payment gateway logs
     */
    function loadPaymentGatewayLogs() {
        const $logsContainer = $('#logs-container');
        const gateway = $('#log-gateway-filter').val();
        const level = $('#log-level-filter').val();
        const date = $('#log-date-filter').val();
        
        // Show loading indicator
        $logsContainer.html('<div class="loading-indicator"><span class="spinner is-active"></span> Loading logs...</div>');
        
        // Make AJAX request
        $.ajax({
            url: paymentGatewayTestingData.ajax_url,
            type: 'POST',
            data: {
                action: 'get_payment_logs',
                gateway: gateway,
                level: level,
                date: date,
                nonce: paymentGatewayTestingData.nonce
            },
            success: function(response) {
                if (response.success) {
                    displayLogs(response.data);
                } else {
                    $logsContainer.html('<div class="notice notice-error"><p>' + (response.data.message || 'Failed to load logs') + '</p></div>');
                }
            },
            error: function() {
                $logsContainer.html('<div class="notice notice-error"><p>Failed to load logs</p></div>');
            }
        });
    }

    /**
     * Display logs
     * 
     * @param {object} data Log data
     */
    function displayLogs(data) {
        const $logsContainer = $('#logs-container');
        
        if (!data.logs || data.logs.length === 0) {
            $logsContainer.html('<div class="no-logs-message">No logs found matching your criteria</div>');
            return;
        }
        
        let html = '';
        
        // Iterate through logs
        data.logs.forEach(function(log) {
            const gatewayClass = 'log-gateway-' + log.gateway;
            const levelClass = 'log-level-' + log.level;
            
            html += '<div class="log-entry log-entry-expandable">';
            html += '<div class="log-time">' + log.time + '</div>';
            html += '<div class="log-gateway ' + gatewayClass + '">' + log.gateway + '</div>';
            html += '<div class="log-message">' + log.message + '</div>';
            html += '<div class="log-level ' + levelClass + '">' + log.level + '</div>';
            html += '</div>';
            
            if (log.details) {
                html += '<div class="log-entry-details">';
                html += '<div class="log-details-content">' + log.details + '</div>';
                html += '</div>';
            }
        });
        
        $logsContainer.html(html);
    }

    /**
     * Clear payment gateway logs
     */
    function clearPaymentGatewayLogs() {
        if (!confirm('Are you sure you want to clear all logs? This action cannot be undone.')) {
            return;
        }
        
        const $logsContainer = $('#logs-container');
        
        // Show loading indicator
        $logsContainer.html('<div class="loading-indicator"><span class="spinner is-active"></span> Clearing logs...</div>');
        
        // Make AJAX request
        $.ajax({
            url: paymentGatewayTestingData.ajax_url,
            type: 'POST',
            data: {
                action: 'clear_payment_logs',
                nonce: paymentGatewayTestingData.nonce
            },
            success: function(response) {
                if (response.success) {
                    $logsContainer.html('<div class="notice notice-success"><p>' + response.data.message + '</p></div>');
                    setTimeout(loadPaymentGatewayLogs, 2000);
                } else {
                    $logsContainer.html('<div class="notice notice-error"><p>' + (response.data.message || 'Failed to clear logs') + '</p></div>');
                }
            },
            error: function() {
                $logsContainer.html('<div class="notice notice-error"><p>Failed to clear logs</p></div>');
            }
        });
    }

    /**
     * Export diagnostic report
     */    function exportDiagnosticReport() {
        const $activeReport = $('.diagnostic-content.active');
        const gateway = $activeReport.attr('id').replace('-diagnostic', '');
        
        // Show loading indicator
        const $exportBtn = $('.export-diagnostic-report');
        const originalBtnText = $exportBtn.text();
        $exportBtn.text('Exporting...').prop('disabled', true);
        
        // Make AJAX request to export report as JSON
        $.ajax({
            url: paymentGatewayTestingData.ajax_url,
            type: 'POST',
            data: {
                action: 'export_payment_gateway_diagnostic',
                gateway: gateway,
                nonce: paymentGatewayTestingData.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Create a JSON blob and initiate download
                    const jsonData = JSON.stringify(response.data.data, null, 2);
                    const blob = new Blob([jsonData], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    
                    a.href = url;
                    a.download = response.data.filename;
                    document.body.appendChild(a);
                    a.click();
                    
                    // Cleanup
                    setTimeout(function() {
                        document.body.removeChild(a);
                        URL.revokeObjectURL(url);
                        $exportBtn.text(originalBtnText).prop('disabled', false);
                    }, 100);
                } else {
                    alert('Error exporting report: ' + (response.data.message || 'Unknown error'));
                    $exportBtn.text(originalBtnText).prop('disabled', false);
                }
            },
            error: function() {
                alert('Error exporting report: Server error');
                $exportBtn.text(originalBtnText).prop('disabled', false);
            }
        });
        
        // Also create a plain text version for immediate download
        const reportTitle = $activeReport.find('.diagnostic-report-header h2').text();
        const timestamp = $activeReport.find('.diagnostic-timestamp').text();
        
        // Prepare report content
        let reportContent = reportTitle + '\n' + timestamp + '\n\n';
        
        // Add sections
        $activeReport.find('.diagnostic-section').each(function() {
            const sectionTitle = $(this).find('h3').text();
            reportContent += sectionTitle + '\n' + '='.repeat(sectionTitle.length) + '\n\n';
            
            // Add test results
            $(this).find('tbody tr').each(function() {
                if (!$(this).hasClass('test-details-row')) {
                    const testName = $(this).find('td:eq(0)').text();
                    const testStatus = $(this).find('td:eq(1)').text().trim();
                    const testMessage = $(this).find('td:eq(2)').text();
                    
                    reportContent += '- ' + testName + ': ' + testStatus + ' - ' + testMessage + '\n';
                    
                    // Add details if any
                    const detailsRow = $(this).next('.test-details-row');
                    if (detailsRow.length) {
                        const details = detailsRow.find('pre').text();
                        reportContent += '  Details: ' + details + '\n';
                    }
                }
            });
            
            reportContent += '\n';
        });
        
        // Create text download link
        const txtBlob = new Blob([reportContent], { type: 'text/plain' });
        const txtUrl = URL.createObjectURL(txtBlob);
        const txtA = document.createElement('a');
        
        // Generate filename
        const date = new Date().toISOString().slice(0, 10);
        const txtFilename = gateway + '-diagnostic-' + date + '.txt';
        
        txtA.href = txtUrl;
        txtA.download = txtFilename;
        document.body.appendChild(txtA);
        txtA.click();
        
        // Cleanup
        setTimeout(function() {
            document.body.removeChild(txtA);
            URL.revokeObjectURL(txtUrl);
        }, 100);
    }
    
    // Helper Functions
    function addPasswordToggle(passwordField) {
        const $field = passwordField;
        const $wrapper = $('<div class="password-field-wrapper" style="position: relative; display: inline-block; width: 100%;"></div>');
        const $toggleBtn = $('<button type="button" class="password-toggle" style="position: absolute; right: 8px; top: 50%; transform: translateY(-50%); background: none; border: none; cursor: pointer; color: #666;">👁️</button>');

        $field.wrap($wrapper);
        $field.after($toggleBtn);

        $toggleBtn.on('click', function() {
            const type = $field.attr('type') === 'password' ? 'text' : 'password';
            $field.attr('type', type);
            $toggleBtn.text(type === 'password' ? '👁️' : '🙈');
        });
    }

    function showFeedbackMessage(message, type) {
        const $feedback = $('#gateway-feedback-message');

        $feedback
            .removeClass('notice-success notice-error notice-warning notice-info')
            .addClass('notice-' + type)
            .text(message)
            .show();

        // Auto hide after 5 seconds for success messages
        if (type === 'success') {
            setTimeout(function() {
                $feedback.fadeOut();
            }, 5000);
        }

        // Scroll to message
        $('html, body').animate({
            scrollTop: $feedback.offset().top - 50
        }, 500);
    }

    function updateEnvironmentBadge(gateway, environment) {
        const $badge = $('.' + gateway + '-environment-badge');

        if ($badge.length === 0) {
            $('#' + gateway + '_environment').after('<span class="environment-badge ' + gateway + '-environment-badge"></span>');
        }

        $('.' + gateway + '-environment-badge')
            .removeClass('sandbox live test')
            .addClass(environment)
            .text(environment.toUpperCase());
    }

    function showTestingResults() {
        $('#testing-results').show().addClass('fade-in');
        $('#testing-output').html('');
    }

    function updateTestingOutput(message) {
        const $output = $('#testing-output');
        $output.append(message);
        $output.scrollTop($output[0].scrollHeight);
    }

    function formatTestResults(results) {
        let output = '<div class="test-results-summary">';
        
        if (!results || $.isEmptyObject(results)) {
            return '<div class="notice notice-error"><p>No test results received.</p></div>';
        }
        
        for (const gateway in results) {
            if (results.hasOwnProperty(gateway)) {
                const result = results[gateway];
                const statusClass = result.success ? 'success' : 'error';
                const icon = result.success ? '✅' : '❌';
                
                output += '<div class="test-result-item ' + statusClass + '">';
                output += '<span class="test-result-icon">' + icon + '</span>';
                output += '<span class="test-result-name">' + capitalizeFirstLetter(gateway) + ':</span> ';
                output += '<span class="test-result-message">' + result.message + '</span>';
                output += '</div>';
            }
        }
        
        output += '</div>';
        return output;
    }

    function showTestingOutput(content, type) {
        const noticeClass = type === 'error' ? 'notice-error' : 
                           (type === 'success' ? 'notice-success' : 'notice-info');
        
        $('#test-result-output').html('<div class="notice ' + noticeClass + '"><p>' + content + '</p></div>');
        $('#testing-results').show();
    }

    function switchTestingSubTab(tabId) {
        $('.testing-sub-tabs .nav-tab').removeClass('nav-tab-active');
        $('[data-testing_tab="' + tabId + '"]').addClass('nav-tab-active');
        
        $('.testing-sub-tab-content').removeClass('active').hide();
        $('#' + tabId).addClass('active').show();
    }

    function runGatewayTest(gateway, testType) {
        showTestingOutput('Running ' + testType + ' test for ' + gateway + '...', 'info');
        
        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'test_gateway_connection',
                nonce: paymentGatewayAjax.nonce,
                gateway: gateway,
                test_type: testType
            },
            beforeSend: function() {
                $('#test-result-output').html('<div class="spinner is-active" style="float:none;"></div>');
            },
            success: function(response) {
                if (response.success) {
                    console.log('Test successful:', response.data);
                    showTestingOutput(formatTestResults(response.data.results), 'success');
                } else {
                    console.error('Test failed:', response.data);
                    showTestingOutput('Test failed: ' + response.data.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', error);
                showTestingOutput('Error running test: ' + error, 'error');
            }
        });
    }

    /**
     * Run gateway checklist for system health
     */
    function runGatewayChecklist(mode = 'full') {
        console.log('Running gateway checklist:', mode);
        
        $('#checklist-results').hide();
        $('#checklist-progress').show();
        
        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'run_gateway_checklist',
                nonce: paymentGatewayAjax.nonce,
                mode: mode
            },
            beforeSend: function() {
                $('.progress-text').text(paymentGatewayAjax.messages.checklist_running);
                animateProgressBar(0);
            },
            success: function(response) {
                if (response.success) {
                    console.log('Checklist completed:', response.data);
                    displayChecklistResults(response.data.results);
                    showFeedback(paymentGatewayAjax.messages.checklist_success, 'success');
                } else {
                    console.error('Checklist failed:', response.data);
                    showFeedback(paymentGatewayAjax.messages.checklist_error, 'error');
                }
                
                $('#checklist-progress').hide();
                $('#checklist-results').show();
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', error);
                showFeedback('Error running checklist: ' + error, 'error');
                $('#checklist-progress').hide();
            }
        });
    }

    /**
     * Animation for progress bar in checklist
     */
    function animateProgressBar(progress) {
        const $progressFill = $('.progress-fill');
        const $progressText = $('.progress-text');
        
        $progressFill.css('width', progress + '%');
        
        if (progress < 100) {
            setTimeout(function() {
                animateProgressBar(progress + 10);
            }, 300);
        } else {
            $progressText.text('Checklist completed!');
        }
    }
    
    /**
     * Display checklist results
     */
    function displayChecklistResults(results) {
        let passedCount = 0;
        let failedCount = 0;
        let warningCount = 0;
        let detailsHtml = '';
        
        // Process results
        for (const section in results) {
            if (results.hasOwnProperty(section)) {
                const sectionData = results[section];
                detailsHtml += '<div class="checklist-section">';
                detailsHtml += '<h3>' + sectionData.title + '</h3>';
                detailsHtml += '<table class="wp-list-table widefat fixed striped">';
                detailsHtml += '<thead><tr><th>Test</th><th>Status</th><th>Details</th></tr></thead>';
                detailsHtml += '<tbody>';
                
                sectionData.items.forEach(function(item) {
                    const statusClass = item.status === 'passed' ? 'success' : 
                                      (item.status === 'failed' ? 'error' : 'warning');
                    const icon = item.status === 'passed' ? '✅' : 
                                (item.status === 'failed' ? '❌' : '⚠️');
                    
                    if (item.status === 'passed') {
                        passedCount++;
                    } else if (item.status === 'failed') {
                        failedCount++;
                    } else {
                        warningCount++;
                    }
                    
                    detailsHtml += '<tr class="' + statusClass + '">';
                    detailsHtml += '<td>' + item.name + '</td>';
                    detailsHtml += '<td><span class="test-icon">' + icon + '</span></td>';
                    detailsHtml += '<td>' + item.message + '</td>';
                    detailsHtml += '</tr>';
                });
                
                detailsHtml += '</tbody></table>';
                detailsHtml += '</div>';
            }
        }
        
        // Update summary counters
        $('.summary-passed .summary-count').text(passedCount);
        $('.summary-failed .summary-count').text(failedCount);
        $('.summary-warnings .summary-count').text(warningCount);
        
        // Add details to the container
        $('#checklist-details').html(detailsHtml);
    }
    
    /**
     * Show feedback message
     */
    function showFeedback(message, type) {
        const $feedback = $('#gateway-feedback-message');
        
        $feedback.removeClass('notice-info notice-success notice-warning notice-error')
                .addClass('notice-' + (type || 'info'))
                .html('<p>' + message + '</p>')
                .show();
        
        // Hide after 5 seconds
        setTimeout(function() {
            $feedback.fadeOut();
        }, 5000);
    }
    
    /**
     * Initialize and attach event handlers for testing sub-tabs
     */
    function initTestingSubTabs() {
        console.log('🔄 Initializing testing sub-tabs');
        
        $('.testing-sub-tabs .nav-tab').on('click', function() {
            console.log('Testing sub-tab clicked:', $(this).data('testing-tab'));
            const tabId = $(this).data('testing-tab');
            if (tabId) {
                switchTestingSubTab(tabId);
            }
        });

        // Set first tab as active
        const firstTab = $('.testing-sub-tabs .nav-tab').first();
        if (firstTab.length) {
            firstTab.addClass('nav-tab-active');
            $('#' + firstTab.data('testing-tab')).addClass('active').show();
        }
    }

    /**
     * Switch main tab function
     */
    function switchTab(tabId) {
        console.log('Switching to tab:', tabId);
        
        // Update tab selection
        $('.nav-tab').removeClass('nav-tab-active')
                      .attr('aria-selected', 'false')
                      .attr('tabindex', '-1');
                      
        $('[data-tab="' + tabId + '"]').addClass('nav-tab-active')
                                       .attr('aria-selected', 'true')
                                       .attr('tabindex', '0');
                                       
        // Update content visibility
        $('.tab-content').removeClass('active').hide();
        $('#' + tabId).addClass('active').show();
        
        // Initialize sub-tabs if this is the testing tab
        if (tabId === 'testing-tab') {
            initTestingSubTabs();
        }
    }

    /**
     * Initialize logs management
     */
    function initLogsManagement() {
        console.log('🔧 Initializing Logs Management');

        // Event handlers for log controls
        $('#refresh-logs').on('click', fetchPaymentLogs);
        $('#clear-logs').on('click', clearPaymentLogs);
        $('#export-logs').on('click', exportPaymentLogs);
        
        // Initialize with log data
        fetchPaymentLogs();
        console.log('✅ Logs Management Initialized');
    }
    
    /**
     * Fetch payment gateway logs
     */
    function fetchPaymentLogs() {
        console.log('📋 Fetching payment logs');
        
        $('#error-log-display').html(
            '<div class="loading-indicator">' +
            '<div class="spinner is-active" style="float:none; margin:0 auto; display:block;"></div>' +
            '<p>Loading logs...</p>' +
            '</div>'
        );
        
        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'get_payment_logs',
                nonce: paymentGatewayAjax.nonce,
                limit: 100 // Get last 100 logs
            },
            success: function(response) {
                if (response.success) {
                    displayLogs(response.data.logs);
                } else {
                    $('#error-log-display').html(
                        '<div class="notice notice-error">' +
                        '<p>' + (response.data.message || 'Failed to fetch logs') + '</p>' +
                        '</div>'
                    );
                }
            },
            error: function(xhr, status, error) {
                console.error('Error fetching logs:', error);
                $('#error-log-display').html(
                    '<div class="notice notice-error">' +
                    '<p>Error fetching logs: ' + error + '</p>' +
                    '</div>'
                );
            }
        });
    }
    
    /**
     * Clear payment gateway logs
     */
    function clearPaymentLogs() {
        if (!confirm('Are you sure you want to clear all logs? This action cannot be undone.')) {
            return;
        }
        
        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'clear_payment_logs',
                nonce: paymentGatewayAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    $('#error-log-display').html(
                        '<div class="notice notice-success">' +
                        '<p>' + response.data.message + '</p>' +
                        '</div>'
                    );
                    // Re-fetch logs (should be empty now)
                    fetchPaymentLogs();
                } else {
                    $('#error-log-display').html(
                        '<div class="notice notice-error">' +
                        '<p>' + (response.data.message || 'Failed to clear logs') + '</p>' +
                        '</div>'
                    );
                }
            },
            error: function(xhr, status, error) {
                console.error('Error clearing logs:', error);
                $('#error-log-display').html(
                    '<div class="notice notice-error">' +
                    '<p>Error clearing logs: ' + error + '</p>' +
                    '</div>'
                );
            }
        });
    }
    
    /**
     * Export payment gateway logs
     */
    function exportPaymentLogs() {
        $.ajax({
            url: paymentGatewayAjax.ajax_url,
            type: 'POST',
            data: {
                action: 'get_payment_logs',
                nonce: paymentGatewayAjax.nonce,
                limit: 1000 // Get up to 1000 logs for export
            },
            success: function(response) {
                if (response.success) {
                    // Format logs as CSV
                    const logs = response.data.logs;
                    let csvContent = 'ID,Gateway,Type,Message,Level,Timestamp\n';
                    
                    logs.forEach(function(log) {
                        // Escape any commas in the message
                        const message = log.error_message.replace(/"/g, '""');
                        
                        csvContent += `${log.id},"${log.gateway}","${log.error_type}","${message}",${log.log_level},${log.timestamp}\n`;
                    });
                    
                    // Create and download file
                    const blob = new Blob([csvContent], { type: 'text/csv' });
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    const today = new Date().toISOString().split('T')[0];
                    a.download = `payment-gateway-logs-${today}.csv`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                } else {
                    alert('Failed to export logs: ' + (response.data.message || 'Unknown error'));
                }
            },
            error: function(xhr, status, error) {
                console.error('Error exporting logs:', error);
                alert('Error exporting logs: ' + error);
            }
        });
    }
    
    /**
     * Display logs in the log display area
     */
    function displayLogs(logs) {
        if (!logs || logs.length === 0) {
            $('#error-log-display').html(
                '<div class="notice notice-info">' +
                '<p>No logs found.</p>' +
                '</div>'
            );
            return;
        }
        
        let html = '<table class="widefat logs-table">';
        html += '<thead><tr>' +
                '<th>ID</th>' +
                '<th>Gateway</th>' +
                '<th>Type</th>' +
                '<th>Message</th>' +
                '<th>Level</th>' +
                '<th>Timestamp</th>' +
                '</tr></thead><tbody>';
                
        logs.forEach(function(log) {
            const levelClass = getLevelClass(log.log_level);
            
            html += '<tr class="log-entry ' + levelClass + '">' +
                    '<td>' + log.id + '</td>' +
                    '<td>' + log.gateway + '</td>' +
                    '<td>' + log.error_type + '</td>' +
                    '<td>' + log.error_message + '</td>' +
                    '<td><span class="log-level ' + levelClass + '">' + log.log_level + '</span></td>' +
                    '<td>' + log.timestamp + '</td>' +
                    '</tr>';
        });
        
        html += '</tbody></table>';
        $('#error-log-display').html(html);
    }
    
    /**
     * Get CSS class for log level
     */
    function getLevelClass(level) {
        switch (level.toLowerCase()) {
            case 'error':
                return 'log-level-error';
            case 'warning':
                return 'log-level-warning';
            case 'info':
                return 'log-level-info';
            case 'success':
                return 'log-level-success';
            default:
                return '';
        }
    }
})(jQuery);
