/**
 * Widget Help System CSS
 * 
 * Stili per il sistema di help context-aware per ogni widget.
 * Sostituisce e migliora il sistema Help Line globale.
 * 
 * @version 1.0.0
 */

/* ===========================
   CONTAINER PRINCIPALE
   =========================== */

.widget-help-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
}

.widget-help-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

.widget-help-container {
    position: absolute;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    width: 400px;
    max-width: 90vw;
    max-height: 80vh;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    overflow: hidden;
}

.widget-help-container.position-left {
    left: 20px;
    right: auto;
}

.widget-help-container.position-center {
    left: 50%;
    right: auto;
    transform: translate(-50%, -50%);
}

/* ===========================
   PANNELLO HELP
   =========================== */

.widget-help-panel {
    position: relative;
    display: flex;
    transition: transform 0.3s ease-in-out;
    pointer-events: auto;
}

/* Posizionamenti specifici */
.widget-help-container.position-right {
    right: 0;
    top: 40%;
    transform: translateY(-50%);
}

.widget-help-container.position-left {
    left: 0;
    top: 40%;
    transform: translateY(-50%);
}

.widget-help-container.position-bottom {
    bottom: 20px;
    right: 20px;
}

.widget-help-container.position-center {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* ===========================
   TOGGLE BUTTON
   =========================== */

.widget-help-toggle {
    background: linear-gradient(135deg, #0073aa, #005177);
    color: white;
    padding: 12px 15px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    user-select: none;
    border: none;
    outline: none;
}

/* Toggle per posizione destra */
.position-right .widget-help-toggle {
    border-radius: 8px 0 0 8px;
    align-self: center;
}

/* Toggle per posizione sinistra */
.position-left .widget-help-toggle {
    border-radius: 0 8px 8px 0;
    align-self: center;
}

/* Toggle per posizione bottom */
.position-bottom .widget-help-toggle {
    border-radius: 8px 8px 0 0;
    width: 100%;
    justify-content: center;
}

/* Toggle per posizione center */
.position-center .widget-help-toggle {
    border-radius: 8px 8px 0 0;
    width: 100%;
    justify-content: center;
}

.widget-help-toggle:hover {
    background: linear-gradient(135deg, #005177, #003d5c);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    transform: translateY(-2px);
}

.help-icon {
    font-size: 18px;
    font-weight: bold;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
}

.help-label {
    font-size: 13px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    white-space: nowrap;
}

/* ===========================
   CONTENUTO HELP
   =========================== */

.widget-help-content {
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    transform: translateX(100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    max-height: 80vh;
    min-width: 320px;
    max-width: 400px;
    opacity: 0;
    pointer-events: none;
}

/* Contenuto per posizione sinistra */
.position-left .widget-help-content {
    transform: translateX(-100%);
    border-radius: 8px;
}

/* Contenuto per posizione bottom */
.position-bottom .widget-help-content {
    transform: translateY(100%);
    border-radius: 8px 8px 0 0;
    max-width: 350px;
}

/* Contenuto per posizione center */
.position-center .widget-help-content {
    transform: scale(0.8);
    border-radius: 8px;
    max-width: 500px;
    max-height: 70vh;
}

/* Stato aperto */
.widget-help-panel.open .widget-help-content {
    transform: translateX(0);
    opacity: 1;
    pointer-events: auto;
}

.position-left .widget-help-panel.open .widget-help-content {
    transform: translateX(0);
}

.position-bottom .widget-help-panel.open .widget-help-content {
    transform: translateY(0);
}

.position-center .widget-help-panel.open .widget-help-content {
    transform: scale(1);
}

/* ===========================
   HEADER HELP
   =========================== */

.widget-help-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 20px 15px;
    border-bottom: 1px solid #e8ecef;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.widget-help-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
}

.widget-help-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.widget-help-close:hover {
    background: #dee2e6;
    color: #dc3545;
}

/* ===========================
   BODY HELP
   =========================== */

.widget-help-body {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    font-size: 14px;
    line-height: 1.6;
    color: #495057;
}

.widget-help-body::-webkit-scrollbar {
    width: 6px;
}

.widget-help-body::-webkit-scrollbar-track {
    background: #f8f9fa;
}

.widget-help-body::-webkit-scrollbar-thumb {
    background: #dee2e6;
    border-radius: 3px;
}

.widget-help-body::-webkit-scrollbar-thumb:hover {
    background: #adb5bd;
}

/* Stili contenuto */
.widget-help-body h4 {
    margin: 0 0 15px 0;
    font-size: 15px;
    font-weight: 600;
    color: #0073aa;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 8px;
}

.widget-help-body h5 {
    margin: 20px 0 10px 0;
    font-size: 14px;
    font-weight: 600;
    color: #495057;
}

.widget-help-body ol,
.widget-help-body ul {
    margin: 0 0 15px 0;
    padding-left: 20px;
}

.widget-help-body li {
    margin-bottom: 8px;
}

.widget-help-body strong {
    color: #0073aa;
    font-weight: 600;
}

.widget-help-body code {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 3px;
    padding: 2px 6px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #d63384;
}

.widget-help-body a {
    color: #0073aa;
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: border-color 0.2s ease;
}

.widget-help-body a:hover {
    border-bottom-color: #0073aa;
}

/* ===========================
   FOOTER HELP
   =========================== */

.widget-help-footer {
    padding: 12px 20px;
    border-top: 1px solid #e8ecef;
    background: #f8f9fa;
    text-align: center;
}

.widget-help-footer small {
    color: #6c757d;
    font-size: 11px;
}

/* ===========================
   RESPONSIVE DESIGN
   =========================== */

@media (max-width: 768px) {
    .widget-help-container {
        width: 95vw;
        max-width: none;
        left: 2.5vw;
        right: 2.5vw;
        transform: translateY(-50%);
    }
    
    .widget-help-container.position-left,
    .widget-help-container.position-center {
        left: 2.5vw;
        right: 2.5vw;
        transform: translateY(-50%);
    }
    
    .widget-help-panel.mobile .widget-help-toggle {
        border-radius: 50%;
        padding: 15px;
        position: relative;
    }
    
    .widget-help-panel.mobile .help-label {
        display: none;
    }
    
    .widget-help-panel.mobile .widget-help-content {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        max-width: none;
        max-height: 70vh;
        border-radius: 12px 12px 0 0;
        transform: translateY(100%);
    }
    
    .widget-help-panel.mobile.open .widget-help-content {
        transform: translateY(0);
    }
    
    .position-center .widget-help-panel.mobile .widget-help-content {
        position: fixed;
        top: 5%;
        left: 5%;
        right: 5%;
        bottom: 5%;
        max-height: 90vh;
        border-radius: 8px;
        transform: scale(0.8);
    }
    
    .position-center .widget-help-panel.mobile.open .widget-help-content {
        transform: scale(1);
    }
}

@media (max-width: 480px) {
    .widget-help-content {
        min-width: 280px;
    }
    
    .widget-help-body {
        padding: 15px;
        font-size: 13px;
    }
    
    .widget-help-header {
        padding: 15px;
    }
    
    .widget-help-header h3 {
        font-size: 15px;
    }
}

/* ===========================
   ANIMAZIONI CUSTOM
   =========================== */

@keyframes helpSlideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes helpSlideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes helpFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* ===========================
   STATI SPECIALI
   =========================== */

/* Stato di caricamento */
.widget-help-loading .widget-help-body {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100px;
}

.widget-help-loading .widget-help-body::before {
    content: '';
    width: 30px;
    height: 30px;
    border: 3px solid #e8ecef;
    border-top-color: #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Stato di errore */
.widget-help-error .widget-help-body {
    color: #dc3545;
    text-align: center;
    padding: 30px 20px;
}

.widget-help-error .widget-help-body::before {
    content: '⚠';
    display: block;
    font-size: 24px;
    margin-bottom: 10px;
}

/* ===========================
   UTILITY CLASSES
   =========================== */

.widget-help-hidden {
    display: none !important;
}

.widget-help-disabled {
    opacity: 0.5;
    pointer-events: none;
}

.widget-help-highlight {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 10px;
    margin: 10px 0;
}

.widget-help-highlight.info {
    background: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.widget-help-highlight.warning {
    background: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.widget-help-highlight.success {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

/* ===========================
   ACCESSIBILITÀ
   =========================== */

.widget-help-toggle:focus,
.widget-help-close:focus {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

/* Supporto modalità ad alto contrasto */
@media (prefers-contrast: high) {
    .widget-help-content {
        border: 2px solid #000000;
    }
    
    .widget-help-toggle {
        border: 2px solid #ffffff;
    }
    
    .widget-help-body {
        color: #000000;
    }
}

/* Supporto modalità scura */
@media (prefers-color-scheme: dark) {
    .widget-help-container {
        background: #2c3e50;
        color: #ecf0f1;
    }
    
    .widget-help-header,
    .widget-help-footer {
        background: #34495e;
        border-color: #4a5f7a;
    }
    
    .widget-help-title {
        color: #ecf0f1;
    }
    
    .help-section {
        border-color: #4a5f7a;
    }
    
    .help-section:hover {
        background-color: #34495e;
    }
}

/* Print styles */
@media print {
    .widget-help-container {
        display: none !important;
    }
}
}
