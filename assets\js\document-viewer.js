jQuery(document).ready(function ($) {
    let zoomLevel = 1;
    
    // Variables to store document data
    let currentDocumentFile = null;
    let currentPdfFile = null; // Store PDF version for export
    let currentDocumentTitle = '';
    let currentAnalysisResults = '';
    let extractedDocumentContent = ''; // Variable to store the extracted text content
    let customLogo = null; // Variable to store the custom logo
    let analysisDisplayed = false; // Flag to prevent auto-refresh of analysis
    let preventReloadOnSuccess = false; // Flag to prevent further reloads
    let documentDescription = ''; // Store the document description/query
    let ocrProcessor = null; // OCR processor instance
    
    // Funzione per calcolare e visualizzare la spesa stimata in base ai token
    function updateEstimatedCost(tokenCount) {
        if (!tokenCount) return;
        
        let formattedCost = '€0,00';
        
        // Utilizzo prioritario del calcolo client-side
        if (window.documentStats && typeof window.documentStats.calculateCost === 'function') {
            // Utilizziamo il calcolo centralizzato da document-stats.js
            const costValue = window.documentStats.calculateCost(tokenCount);
            formattedCost = '€' + costValue;
            console.log('Spesa stimata calcolata da documentStats:', formattedCost, 'per', tokenCount, 'token');
        } else {
            // Fallback al calcolo locale se documentStats non è disponibile
            const costRate = 0.01; // €0.01 per token
            const estimatedCost = tokenCount * costRate;
            formattedCost = '€' + estimatedCost.toFixed(2).replace('.', ',');
            console.log('Spesa stimata calcolata localmente:', formattedCost, 'per', tokenCount, 'token');
        }
        
        // Aggiorna il costo stimato nell'interfaccia utente
        if ($('#cost-estimate').length) {
            $('#cost-estimate').text(formattedCost);
            
            // Evidenzia il cambiamento con una classe speciale
            $('#cost-estimate').addClass('cost-updated');
            setTimeout(() => {
                $('#cost-estimate').removeClass('cost-updated');
            }, 3000);
        }
        
        return formattedCost;
    }
    
    // OCR completion callback: salva SEMPRE il testo estratto in extractedDocumentContent
    function ocrCompletionHandler(extractedText) {
        // Salva il testo estratto in modo affidabile
        extractedDocumentContent = extractedText ? extractedText.trim() : '';
        // Log di debug per verifica
        console.log('[OCR] Fase: Estrazione testo da immagine completata.');
        console.log('[OCR] Testo estratto, lunghezza:', extractedDocumentContent.length);
        // Logga anche un estratto del testo per verifica completezza
        if (extractedDocumentContent.length > 0) {
            const preview = extractedDocumentContent.substring(0, 500);
            console.log('[OCR] Estratto (primi 500 caratteri):', preview);
            // Logga tutto il testo estratto (attenzione: può essere molto lungo)
            console.log('[OCR] Testo estratto COMPLETO:', extractedDocumentContent);
            // INVIA il testo estratto al server per il log viewer
            $.ajax({
                url: documentViewerParams.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'ocr_log',
                    nonce: documentViewerParams.nonce,
                    message: '[OCR] Testo estratto da immagine, lunghezza: ' + extractedDocumentContent.length,
                    context: 'ocr',
                    extracted_text: extractedDocumentContent
                }
            });
        } else {
            console.log('[OCR] Nessun testo estratto.');
        }
        
        // Update the inline document info with character count
        $('#document-info-inline').show();
        $('#document-chars-inline').html('<strong>Chars:</strong> ' + formatCharCount(extractedDocumentContent.length));
        
        const tokenCount = estimateTokenCount(extractedDocumentContent);
        $('#document-tokens-inline').html('<strong>Tokens:</strong> ' + formatCharCount(tokenCount));
        
        // Calcola e visualizza la spesa stimata
        updateEstimatedCost(tokenCount);
        
        // Applica effetto blink sui campi token e menu domande preset
        if (window.documentStats && typeof window.documentStats.applyBlinkEffectOnLoad === 'function') {
            console.log('Applicazione effetto blink dopo estrazione documento');
            window.documentStats.applyBlinkEffectOnLoad();
        }
        
        // Show success notification
        showDocumentNotification('<strong>✓ ' + documentViewerParams.i18n.ocrComplete + '</strong> ' + 
            formatCharCount(extractedDocumentContent.length) + ' caratteri estratti', 'success');
        
        // Show successful extraction message
        $('#analysis-results').html(
            '<div class="extraction-success">' +
            '<p><strong>✓ Estrazione OCR completata!</strong></p>' +
            '<p>Sono stati estratti ' + formatCharCount(extractedDocumentContent.length) + ' caratteri.</p>' +
            '<p>Puoi ora inserire una descrizione o domanda e cliccare su "Analizza" per procedere.</p>' +
            '</div>'
        );
        
        // Scroll to document view after a short delay
        setTimeout(function() {
            $('.extraction-success').fadeOut(1000, function() {
                $('html, body').animate({
                    scrollTop: $('#document-display').offset().top - 50
                }, 500);
            });
        }, 4000);
    }

    // Inizializza OCR processor con il nuovo callback
    function initOcrProcessor() {
        if (!ocrProcessor) {
            ocrProcessor = new DocumentOCR();
            ocrProcessor.registerCallbacks(
                // Progress callback
                (progress) => {
                    showDocumentNotification('<div class="spinner"></div> ' + 
                        documentViewerParams.i18n.ocrProcessing + ' ' + progress + '%', 'processing');
                },
                // Completion callback
                ocrCompletionHandler,
                // Error callback
                (error) => {
                    hideDocumentNotification();
                    showDocumentNotification(documentViewerParams.i18n.ocrError + ': ' + error.message, 'error');
                    console.error('OCR error:', error);
                }
            );
        }
        return ocrProcessor;
    }

    // Gestione click sui tab di analisi - Utilizziamo un solo handler a livello documento
    $(document).on('click', '.analysis-tab', function() {
        const tabId = $(this).data('tab');
        
        // Rimuovi la classe active da tutti i tab e contenuti
        $('.analysis-tab').removeClass('active');
        $('.tab-content').removeClass('active');
        
        // Aggiungi la classe active al tab cliccato e al contenuto corrispondente
        $(this).addClass('active');
        $('#tab-' + tabId).addClass('active');
    });
    
    // Costanti per le dimensioni del formato A4 (mm)
    const A4_WIDTH_MM = 210;
    const A4_HEIGHT_MM = 297;
    const A4_RATIO = A4_WIDTH_MM / A4_HEIGHT_MM; // ~0.707
    
    // Function to show notification in the document notification area
    function showDocumentNotification(message, type = 'success', autoDismiss = true) {
        // Get the notification area
        const $notificationArea = $('#document-notification-area');
        const $notificationContent = $notificationArea.find('.notification-content');
        
        // Set the message and add appropriate styling
        $notificationContent.html(message);
        
        // Remove any existing classes and add the new type class
        $notificationArea.removeClass('error processing');
        if (type === 'error') {
            $notificationArea.addClass('error');
            autoDismiss = false; // Non nascondere automaticamente gli errori
        } else if (type === 'processing') {
            $notificationArea.addClass('processing');
            
            // Sostituisci il semplice spinner con un indicatore visivo migliore
            if (message.includes('<div class="spinner"></div>')) {
                const enhancedMessage = message.replace('<div class="spinner"></div>', 
                    '<div class="processing-notification">' +
                    '<div class="processing-spinner"></div>' +
                    '<div class="processing-message">' + message.replace('<div class="spinner"></div>', '') + '</div>' +
                    '</div>'
                );
                $notificationContent.html(enhancedMessage);
            }
            
            autoDismiss = false; // Non nascondere automaticamente i messaggi di elaborazione
        }
        
        // Show the notification area with animation
        $notificationArea.fadeIn(300);
        
        // Auto dismiss after 4 seconds for success messages
        if (autoDismiss) {
            clearTimeout($notificationArea.data('dismiss-timer'));
            const timer = setTimeout(function() {
                hideDocumentNotification();
            }, 4000);
            $notificationArea.data('dismiss-timer', timer);
        }
        
        // Return the notification area for chaining
        return $notificationArea;
    }
    
    // Function to hide the document notification
    function hideDocumentNotification() {
        $('#document-notification-area').fadeOut(300);
    }

    // Prevent accidental form submissions that might reload the page
    $(document).on('submit', '#document-viewer-form', function(e) {
        e.preventDefault();
        return false;
    });

    // Hide zoom buttons and document container initially
    $('#zoom-in, #zoom-out, #document-display').hide();

    // Custom logo upload handling
    $('#custom-logo-upload').on('change', function() {
        console.log('Logo upload change event triggered');
        const file = this.files[0];
        if (file) {
            console.log('Logo file selected:', file.name, 'Type:', file.type, 'Size:', file.size);
            
            // Check if file is an image
            if (!file.type.match('image.*')) {
                alert('Per favore seleziona un file immagine (JPG, PNG, GIF, etc.)');
                $('#custom-logo-upload').val('');
                $('#logo-preview').attr('src', '').hide();
                return;
            }
            
            customLogo = file;
            
            // Display logo preview with standard document dimensions
            const reader = new FileReader();
            reader.onload = function(e) {
                console.log('Logo file read successfully');
                
                // Create a new image to check dimensions
                const img = new Image();
                img.onload = function() {
                    const imgWidth = this.width;
                    const imgHeight = this.height;
                    console.log('Logo dimensions:', imgWidth, 'x', imgHeight);
                    
                    // Calcola le dimensioni proporzionali con altezza massima di 50px
                    let displayHeight = imgHeight;
                    let displayWidth = imgWidth;
                    
                    if (imgHeight > 50) {
                        // Mantieni il rapporto di proporzione
                        const ratio = imgWidth / imgHeight;
                        displayHeight = 50;
                        displayWidth = Math.round(displayHeight * ratio);
                    }
                    
                    // Set the logo preview with proper dimensions
                    $('#logo-preview')
                        .attr('src', e.target.result)
                        .css({
                            'height': displayHeight + 'px',
                            'width': displayWidth + 'px',
                            'object-fit': 'contain',
                            'max-height': '50px'
                        })
                        .show();
                    
                    // Aggiorna info dimensioni
                    if (imgHeight > 50) {
                        $('#logo-dimensions-info').html(`L'immagine originale (${imgWidth}x${imgHeight}px) verrà ridimensionata a ${displayWidth}x${displayHeight}px per adattarsi al documento`).show();
                    } else {
                        $('#logo-dimensions-info').html(`Dimensioni logo: ${imgWidth}x${imgHeight}px`).show();
                    }
                    
                    console.log('Logo preview updated and displayed');
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        } else {
            // Clear preview if no file selected
            $('#logo-preview').attr('src', '').hide();
            $('#logo-dimensions-info').hide();
            customLogo = null;
        }
    });

    // Analysis title field handling
    $('#analysis-title').on('change keyup', function() {
        // Update the title for export
        currentDocumentTitle = $(this).val();
    });

    // Function to estimate token count from text
    function estimateTokenCount(text) {
        if (!text) return 0;
        // Rough estimation: tokens are ~4 characters on average for English text
        // This is a simple approximation - actual token count depends on tokenization algorithm
        return Math.round(text.length / 4);
    }

    // Funzione per formattare le dimensioni del file
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Funzione per formattare conteggio caratteri/token
    function formatCharCount(count) {
        if (count < 1000) {
            return count.toString();
        } else {
            return (count / 1000).toFixed(1) + 'k';
        }
    }

    // Image upload handling for OCR
    $('#image-upload').on('change', function() {
        const file = this.files[0];
        if (!file) return;
        
        console.log('Image file selected:', file.name, 'Type:', file.type, 'Size:', file.size);
        
        // Reset form state
        $('#document-upload').val('');
        analysisDisplayed = false;
        
        // Reset spesa effettiva quando viene caricata una nuova immagine
        if (window.documentStats && typeof window.documentStats.clearActualCost === 'function') {
            window.documentStats.clearActualCost();
        }
        
        // Verify that the file is an image
        if (!file.type.match('image.*')) {
            alert('Solo file immagine sono supportati (JPG, PNG, GIF, etc.)');
            $('#image-upload').val('');
            return;
        }
        
        currentDocumentFile = file;
        
        // Update the inline document info area with file details
        $('#document-info-inline').show();
        $('#document-name-inline').html('<strong>File:</strong> ' + file.name);
        $('#document-size-inline').html('<strong>Dim:</strong> ' + formatFileSize(file.size));
        $('#document-type-inline').html('<strong>Tipo:</strong> ' + file.type.split('/')[1].toUpperCase());
        
        // Show loading message and prepare the image for display
        showDocumentNotification('<div class="spinner"></div> Preparazione dell\'immagine...', 'processing');
        
        // Create object URL for image display
        const fileURL = URL.createObjectURL(file);
        
        // Show the image in the document viewer
        $('#document-frame').css({
            'height': 'auto',
            'width': '100%',
            'border': 'none'
        }).attr('src', fileURL).show();
        $('#document-display').show();
        
        // Initialize OCR processor if needed
        initOcrProcessor();
        
        // First convert image to PDF for display consistency
        ocrProcessor.convertImageToPdf(file).then(pdfBlob => {
            // Create a URL for the PDF
            const pdfUrl = URL.createObjectURL(pdfBlob);
            
            // Aggiungi una struttura di visualizzazione A4 per il PDF convertito da immagine
            $('#document-display').html('<div class="a4-container"><iframe id="document-frame"></iframe></div>');
            
            // Update the document frame with the PDF
            $('#document-frame').attr('src', pdfUrl).show();
            
            // Show zoom buttons
            $('#zoom-in, #zoom-out').show();
            
            // Perform OCR extraction
            processImageWithOCR(file);
        }).catch(error => {
            // If PDF conversion fails, continue with just the image display
            console.error('PDF conversion error:', error);
            showDocumentNotification('Errore nella conversione dell\'immagine in PDF. Proseguo con estrazione OCR...', 'error', true);
            
            // Proceed with OCR on the original image
            processImageWithOCR(file);
        });
    });

    // Process image with OCR using our OCR processor
    function processImageWithOCR(imageFile) {
        // Make sure OCR processor is initialized
        const processor = initOcrProcessor();
        
        // Log inizio del processo OCR
        console.log('Avvio processo OCR per l\'immagine:', imageFile.name);
        
        // Invia log al server
        $.ajax({
            url: documentViewerParams.ajaxUrl,
            type: 'POST',
            data: {
                action: 'ocr_log',
                nonce: documentViewerParams.nonce,
                message: 'Avvio processo OCR per l\'immagine: ' + imageFile.name + ' (' + formatFileSize(imageFile.size) + ')',
                context: 'ocr'
            }
        });
        
        // Process the image
        processor.processImage(imageFile, ['ita', 'eng'])
            .then(extractedText => {
                // Log completamento OCR
                console.log('OCR completato con successo:', {
                    fileName: imageFile.name,
                    fileSize: formatFileSize(imageFile.size),
                    charsExtracted: extractedText.length
                });
                
                // Invia log al server per il completamento con successo
                $.ajax({
                    url: documentViewerParams.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'ocr_log',
                        nonce: documentViewerParams.nonce,
                        message: 'OCR completato con successo per ' + imageFile.name + '. Estratti ' + formatCharCount(extractedText.length) + ' caratteri.',
                        context: 'ocr'
                    }
                });
            })
            .catch(error => {
                console.error('OCR processing error:', error);
                showDocumentNotification('Errore durante l\'OCR: ' + error.message, 'error');
                
                // Invia log al server per errore
                $.ajax({
                    url: documentViewerParams.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'ocr_log',
                        nonce: documentViewerParams.nonce,
                        message: 'Errore durante il processo OCR: ' + error.message,
                        context: 'ocr'
                    }
                });
            });
    }

    // Document upload handling
    $('#document-upload').on('change', function () {
        const file = this.files[0];
        if (!file) return;
        
        console.log('File selected:', file.name, 'Type:', file.type, 'Size:', file.size);
        
        // Reset the image upload field
        $('#image-upload').val('');
        
        // Reset the auto-reload prevention when a new document is uploaded
        analysisDisplayed = false;
        
        // Reset spesa effettiva quando viene caricato un nuovo documento
        if (window.documentStats && typeof window.documentStats.clearActualCost === 'function') {
            window.documentStats.clearActualCost();
        }
        
        currentDocumentFile = file;
        const fileType = file.type;

        // Update the inline document info area with file details
        $('#document-info-inline').show();
        $('#document-name-inline').html('<strong>File:</strong> ' + file.name);
        $('#document-size-inline').html('<strong>Dim:</strong> ' + formatFileSize(file.size));
        $('#document-type-inline').html('<strong>Tipo:</strong> ' + file.type.split('/')[1].toUpperCase());

        // Check if file is PDF or Word document
        if (fileType === 'application/pdf' || 
            fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' || 
            fileType === 'application/msword') {
            
            // Show loading message in notification area
            showDocumentNotification('<div class="spinner"></div> Preparazione del documento...', 'processing');
            
            if (fileType === 'application/pdf') {
                // Handle PDF directly in the browser
                const fileURL = URL.createObjectURL(file);
                console.log('Created URL for PDF:', fileURL);
                
                // Aggiungi una struttura di visualizzazione A4 per il PDF
                $('#document-display').html('<div class="a4-container"><iframe id="document-frame"></iframe></div>');
                
                // Show the document frame and container
                $('#document-frame').attr('src', fileURL).show();
                $('#document-display').show();
                
                // Show zoom buttons only after document is loaded
                $('#zoom-in, #zoom-out').show();
                
                // Auto-resize container to fit content when the document is loaded
                $('#document-frame').on('load', function() {
                    console.log('PDF loaded in iframe');
                });
                
                // Extract the document content
                extractDocumentContent(file);
                
                // Hide the loading notification
                hideDocumentNotification();
                
                // Show success notification
                showDocumentNotification('<strong>✓ Documento PDF caricato con successo!</strong>', 'success');
            } else {
                // For Word documents, send to server for PDF conversion
                console.log('Word document detected, starting conversion process');
                convertWordToPdf(file);
            }
        } else {
            console.error('Unsupported file type:', fileType);
            alert('Solo PDF, Word e immagini sono supportati.');
            $('#document-upload').val('');
            $('#document-frame').attr('src', '').hide();
            $('#document-display').hide();
            $('#zoom-in, #zoom-out').hide();
        }
    });

    // Funzione per convertire i documenti Word in PDF
    function convertWordToPdf(file) {
        const formData = new FormData();
        formData.append('action', 'convert_word_to_pdf');
        formData.append('nonce', documentViewerParams.nonce);
        formData.append('word_file', file);
        
        // Mostra un messaggio di caricamento
        showDocumentNotification('<div class="spinner"></div> Conversione del documento Word in PDF...', 'processing');
        
        $.ajax({
            url: documentViewerParams.ajaxUrl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                // Hide the loading notification
                hideDocumentNotification();
                
                if (response.success) {
                    // Salva il contenuto del documento estratto (importante!)
                    if (response.data.pdf_content) {
                        extractedDocumentContent = response.data.pdf_content;
                        console.log('Contenuto estratto dal documento Word: ' + extractedDocumentContent.length + ' caratteri');
                        
                        // Update the inline document info with character count
                        $('#document-info-inline').show();
                        $('#document-chars-inline').html('<strong>Chars:</strong> ' + formatCharCount(extractedDocumentContent.length));
                        
                        // Calcola il numero di token
                        const tokenCount = estimateTokenCount(extractedDocumentContent);
                        $('#document-tokens-inline').html('<strong>Tokens:</strong> ' + formatCharCount(tokenCount));
                        
                        // Calcola e visualizza la spesa stimata
                        updateEstimatedCost(tokenCount);
                        
                        // Applica effetto blink sui campi token e menu domande preset
                        if (window.documentStats && typeof window.documentStats.applyBlinkEffectOnLoad === 'function') {
                            console.log('Applicazione effetto blink dopo estrazione documento');
                            window.documentStats.applyBlinkEffectOnLoad();
                        }
                    }
                    
                    // Show success notification
                    showDocumentNotification('<strong>✓ Documento Word convertito in PDF!</strong> ' + 
                        (extractedDocumentContent ? 'Estratti ' + formatCharCount(extractedDocumentContent.length) + ' caratteri.' : ''), 'success');
                    
                    // Visualizza il PDF convertito
                    $('#document-frame').attr('src', response.data.pdf_url).show();
                    $('#document-display').show();
                    $('#zoom-in, #zoom-out').show();
                    
                    // Se non è stato possibile estrarre il contenuto dal server, prova a farlo dal PDF
                    if (!extractedDocumentContent && response.data.pdf_url) {
                        // Scarica il PDF convertito e prova a estrarre il testo
                        $.ajax({
                            url: response.data.pdf_url,
                            type: 'GET',
                            xhrFields: {
                                responseType: 'blob'
                            },
                            success: function(pdfBlob) {
                                const pdfFile = new File([pdfBlob], file.name.replace(/\.(doc|docx)$/i, '.pdf'), { type: 'application/pdf' });
                                // Estrai il contenuto del PDF
                                extractDocumentContent(pdfFile);
                            },
                            error: function() {
                                showDocumentNotification('Errore nel recupero del PDF convertito', 'error');
                            }
                        });
                    } else {
                        // Aggiorna l'interfaccia per mostrare che il contenuto è stato estratto
                        if ($('#analysis-results').find('.analysis-container').length === 0) {
                            // Messaggio di conferma migliorato con stile
                            $('#analysis-results').html(
                                '<div class="extraction-success">' +
                                '<p><strong>✓ Estrazione del testo completata!</strong></p>' +
                                '<p>Sono stati estratti ' + formatCharCount(extractedDocumentContent.length) + ' caratteri.</p>' +
                                '<p>Puoi ora inserire una descrizione o domanda e cliccare su "Analizza" per procedere.</p>' +
                                '</div>'
                            );
                            
                            // Automatically fade out the extraction success message after 4 seconds
                            setTimeout(function() {
                                $('.extraction-success').fadeOut(1000, function() {
                                    // Sposta l'attenzione sul preview del documento
                                    $('html, body').animate({
                                        scrollTop: $('#document-display').offset().top - 50
                                    }, 500);
                                });
                            }, 4000);
                        }
                    }
                } else {
                    showDocumentNotification('Errore nella conversione: ' + (response.data && response.data.message ? response.data.message : 'Errore sconosciuto'), 'error');
                }
            },
            error: function(xhr, status, error) {
                // Hide the loading notification
                hideDocumentNotification();
                
                // Show error notification
                showDocumentNotification('Errore nella conversione: ' + error, 'error');
            }
        });
    }

    // Gestione del campo titolo
    $('#document-title').on('change keyup', function() {
        currentDocumentTitle = $(this).val();
    });

    // Handle the analyze button click
    $('#analyze-description').on('click', function() {
        const description = $('#document-description').val();

        // Debug: mostra la lunghezza e un estratto del testo che verrà inviato
        console.log('[ANALYZE] Lunghezza testo estratto:', extractedDocumentContent.length);
        console.log('[ANALYZE] Primo pezzo testo estratto:', extractedDocumentContent.substring(0, 200));

        if (!extractedDocumentContent) {
            alert('Carica prima un documento o un\'immagine.');
            return;
        }
        if (!description) {
            alert('Inserisci una descrizione o domanda sul documento.');
            return;
        }
        
        // Set flag to prevent auto-reload once analysis is displayed
        preventReloadOnSuccess = true;
        
        // Store the document description/query
        documentDescription = description;
        
        // Now analyze the document with the description
        analyzeDocument(extractedDocumentContent, description);
    });

    // Function to analyze document with extracted content and description
    function analyzeDocument(content, description) {
        // Verifico se è già presente una notifica di processing
        if (!$('#document-notification-area').is(':visible') || !$('#document-notification-area').hasClass('processing')) {
            console.log('Messaggio di processing già visibile, evito di mostrare un altro messaggio');
        }
        
        // Flag per tracciare se questa è una nuova richiesta di analisi
        const isNewAnalysisRequest = true;
        
        // Aggiunge un indicatore di caricamento animato nell'area dei risultati dell'analisi
        $('#analysis-results').html(`
            <div class="analysis-loading-indicator">
                <div class="analysis-spinner"></div>
                <p>Fase 2: Analisi in corso...</p>
                <p class="analysis-wait-message">L'elaborazione di documenti complessi può richiedere fino a un minuto</p>
            </div>
        `);
        
        $.ajax({
            url: documentViewerParams.ajaxUrl,
            type: 'POST',
            data: {
                action: 'analyze_document',
                nonce: documentViewerParams.nonce,
                document_data: content,
                description: description
            },
            beforeSend: function() {
                // Aggiornamento alla Fase 2 dopo l'invio dei dati
                hideDocumentNotification();
                showDocumentNotification('<div class="spinner"></div> Fase 2: Analisi AI in corso...', 'processing');
            },
            success: function(response) {
                // Nascondi sempre la notifica al completamento
                hideDocumentNotification();
                
                if (response.success) {
                    // Mostra il messaggio di Fase 3 - elaborazione completata
                    showDocumentNotification('<strong>✓ Fase 3: Elaborazione AI completata con successo!</strong>', 'success');
                    
                    // Set flag to indicate analysis is displayed
                    analysisDisplayed = true;
                    
                    // Update the UI with the analysis results
                    if (response.data && response.data.results) {
                        // Debug logging per verificare cosa contiene effettivamente la risposta
                        console.log('Risposta analisi ricevuta:', response.data.results);
                        
                        // Estrai il contenuto dell'analisi - aggiungiamo controlli aggiuntivi
                        let analysisContent = '';
                        
                        // Rimuovi eventuali riferimenti a commit GitHub prima di qualsiasi elaborazione
                        let cleanedResults = response.data.results;
                        if (typeof cleanedResults === 'string') {
                            cleanedResults = cleanedResults.replace(/\[([0-9a-f]+)\]\(https:\/\/github\.com\/.*?\/commit\/[0-9a-f]+\)/g, '');
                        }
                        
                        // Verifica se response.data.results contiene un elemento con id="analysis-content"
                        if ($(cleanedResults).find('#analysis-content').length > 0) {
                            analysisContent = $(cleanedResults).find('#analysis-content').html();
                        } else if ($(cleanedResults).filter('#analysis-content').length > 0) {
                            // Se non lo trova come elemento figlio, prova come elemento diretto
                            analysisContent = $(cleanedResults).filter('#analysis-content').html();
                        } else {
                            // Se ancora non lo trova, usa direttamente il risultato (potrebbe già essere HTML)
                            analysisContent = cleanedResults;
                        }
                        
                        console.log('Contenuto analisi estratto:', analysisContent);
                        
                        // Genera punti chiave automaticamente
                        const keyPointsContent = extractKeyPoints(analysisContent || '');
                        
                        // Crea la struttura a tab per l'analisi - versione più robusta
                        const tabsHTML = `
                        <div class="analysis-container">
                            <div class="analysis-header">
                                <h3 class="analysis-title">Risultati dell'Analisi</h3>
                                <div class="analysis-controls">
                                    <button type="button" class="toggle-view-btn" data-state="expanded" title="Espandi/Comprimi">
                                        <span class="compress-icon">&#8722;</span>
                                        <span class="expand-icon" style="display:none;">&#43;</span>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="analysis-content">
                                <div class="analysis-tabs">
                                    <div class="analysis-tab active" data-tab="analysis">Analisi Completa</div>
                                    <div class="analysis-tab" data-tab="keypoints">Punti Chiave</div>
                                </div>
                                
                                <div id="tab-analysis" class="tab-content active">
                                    <div id="analysis-content" class="typing-effect"></div>
                                </div>
                                
                                <div id="tab-keypoints" class="tab-content">
                                    ${keyPointsContent}
                                </div>
                            </div>
                        </div>`;
                        
                        console.log('HTML dei tab generato:', tabsHTML);
                        
                        // Aggiorna l'interfaccia con la nuova struttura a tab
                        $('#analysis-results').html(tabsHTML);
                        currentAnalysisResults = tabsHTML;

                        // Applica l'effetto di digitazione al contenuto dell'analisi
                        typeContentWithEffect(analysisContent);
                        
                        // Debugging - verifica che i tab siano stati aggiunti
                        console.log('Numero di tab trovati:', $('.analysis-tab').length);
                        console.log('Contenuto dell\'elemento #analysis-results:', $('#analysis-results').html());
                        
                        // Assicurati che il contenuto sia visibile
                        $('#analysis-results').show();
                        
                        // Gestisci il click sul pulsante espandi/comprimi
                        $('.toggle-view-btn').on('click', function() {
                            const $btn = $(this);
                            const currentState = $btn.data('state');
                            
                            if (currentState === 'expanded') {
                                // Comprimi la visualizzazione
                                $('.analysis-content').slideUp(300);
                                $btn.data('state', 'collapsed');
                                $('.compress-icon').hide();
                                $('.expand-icon').show();
                            } else {
                                // Espandi la visualizzazione  
                                $('.analysis-content').slideDown(300);
                                $btn.data('state', 'expanded');
                                $('.compress-icon').show();
                                $('.expand-icon').hide();
                            }
                        });
                        
                        // Aggiungi il bottone per il mega tooltip dopo che l'analisi è stata caricata
                        addMegaTooltipTrigger();
                        
                        // Aggiorna automaticamente la spesa effettiva e i contatori dopo l'analisi completata
                        const tokenCount = estimateTokenCount(extractedDocumentContent);
                        if (window.documentStats && typeof window.documentStats.updateActualCostAfterAnalysis === 'function') {
                            console.log('Aggiornamento automatico della spesa effettiva e contatori dopo analisi completata');
                            // Ottieni il titolo dell'analisi, usa quello inserito dall'utente o un valore predefinito
                            const analysisTitle = $('#analysis-title').val() || 'Analisi documento';
                            window.documentStats.updateActualCostAfterAnalysis(tokenCount, analysisTitle);
                            
                            // Aggiorna anche la sezione delle analisi recenti
                            if (window.documentStats && typeof window.documentStats.updateRecentAnalysisSection === 'function') {
                                console.log('Aggiornamento automatico della sezione analisi recenti');
                                setTimeout(function() {
                                    window.documentStats.updateRecentAnalysisSection();
                                }, 1000); // Leggero ritardo per assicurarsi che i dati siano stati salvati nel database
                            }
                        } else {
                            console.warn('Funzione updateActualCostAfterAnalysis non disponibile');
                        }
                    } else {
                        $('#analysis-results').html('<p>L\'analisi non ha prodotto risultati.</p>');
                    }
                } else {
                    // Show error notification con il messaggio di elaborazione fallita
                    showDocumentNotification('<strong>❌ Elaborazione fallita, provare a ripetere.</strong><br>' + 
                        (response.data && response.data.message ? response.data.message : 'Errore sconosciuto'), 'error');
                        
                    // Display error in results area
                    $('#analysis-results').html('<div class="error-message">Elaborazione fallita, provare a ripetere: ' + 
                        (response.data && response.data.message ? response.data.message : 'Errore sconosciuto') + '</div>');
                }
            },
            error: function(xhr, status, error) {
                hideDocumentNotification();
                
                // Handle API errors con il messaggio di elaborazione fallita
                let errorMessage = 'Elaborazione fallita, provare a ripetere: ' + error;
                if (xhr.responseJSON && xhr.responseJSON.data && xhr.responseJSON.data.message) {
                    errorMessage = xhr.responseJSON.data.message;
                }
                
                console.error('Errore AJAX:', status, error, xhr.responseText);
                
                showDocumentNotification('<strong>❌ Elaborazione fallita, provare a ripetere.</strong><br>' + errorMessage, 'error');
                $('#analysis-results').html('<div class="error-message">Elaborazione fallita, provare a ripetere: ' + errorMessage + '</div>');
            }
        });
    }

    // Clear document button
    $('#clear-document').on('click', function () {
        // Reset flags when clearing the document
        analysisDisplayed = false;
        preventReloadOnSuccess = false;
        
        // Clear file input fields
        $('#document-upload').val('');        
        $('#image-upload').val('');
        
        // Reset document viewer
        $('#document-frame').attr('src', '').hide();
        $('#document-display').hide();
        $('#zoom-in, #zoom-out').hide();
        
        // Reset analysis and other fields
        $('#analysis-results').html('<p>Carica un documento per visualizzare l\'analisi</p>');
        $('#analysis-title').val('');
        $('#custom-logo-upload').val('');
        $('#logo-preview').attr('src', '').hide();
        $('#logo-dimensions-info').hide();
        $('#document-info-inline').hide();
        
        // Reset stored data
        currentDocumentFile = null;
        currentDocumentTitle = '';
        currentAnalysisResults = '';
        extractedDocumentContent = '';
        customLogo = null;
        documentDescription = '';
        
        // Notify server
        $.post(documentViewerParams.ajaxUrl, {
            action: 'clear_document',
            nonce: documentViewerParams.nonce
        });
    });

    $('#zoom-in').on('click', function () {
        zoomLevel += 0.1;
        $('#document-frame').css('transform', `scale(${zoomLevel})`);
    });

    $('#zoom-out').on('click', function () {
        zoomLevel = Math.max(0.1, zoomLevel - 0.1);
        $('#document-frame').css('transform', `scale(${zoomLevel})`);
    });

    // Gestione del pulsante Export PDF
    $('#export-pdf').on('click', function() {
        if (!currentDocumentFile) {
            alert('Carica prima un documento per esportarlo in PDF.');
            return;
        }
        
        exportToPdf();
    });

    // Save Analysis button click handler
    $('#save-analysis').on('click', function() {
        console.log('Save Analysis button clicked');
        
        if (!extractedDocumentContent || !currentAnalysisResults) {
            console.error('Missing required data for saving: extractedDocumentContent=' + 
                (extractedDocumentContent ? 'yes' : 'no') + ', currentAnalysisResults=' + 
                (currentAnalysisResults ? 'yes' : 'no'));
            alert('Please analyze a document first before saving.');
            return;
        }
        
        // Get the data to be saved
        const title = $('#analysis-title').val() || currentDocumentTitle || 'Untitled Analysis';
        const query = documentDescription;
        const analysis_results = $('#analysis-content').html();
        
        // Debug info
        console.log('Preparing to save analysis with data:', {
            'title': title,
            'query_length': query ? query.length : 0,
            'analysis_results_length': analysis_results ? analysis_results.length : 0,
            'has_document': currentDocumentFile ? true : false,
            'has_logo': customLogo ? true : false
        });
        
        // Show a notification that saving is in progress
        showDocumentNotification('<div class="spinner"></div> Saving analysis to database...', 'processing');
        
        // Create FormData object for the AJAX request
        const formData = new FormData();
        formData.append('action', 'save_document_analysis');
        formData.append('nonce', documentViewerParams.nonce);
        formData.append('title', title);
        formData.append('query', query);
        formData.append('analysis_results', analysis_results);
        
        // Add the logo if it exists
        if (customLogo) {
            console.log('Adding logo to FormData, type:', customLogo.type, 'size:', customLogo.size);
            formData.append('logo', customLogo);
        }
        
        // Add the document if it exists
        if (currentDocumentFile) {
            console.log('Adding document to FormData, type:', currentDocumentFile.type, 'size:', currentDocumentFile.size);
            formData.append('document', currentDocumentFile);
        }
        
        console.log('AJAX request details:', {
            'url': documentViewerParams.ajaxUrl,
            'action': 'save_document_analysis',
            'has_nonce': documentViewerParams.nonce ? true : false
        });
        
        // Send the AJAX request to save the data
        $.ajax({
            url: documentViewerParams.ajaxUrl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            beforeSend: function(xhr) {
                console.log('Starting save analysis request to server');
            },
            success: function(response) {
                console.log('Received server response:', response);
                
                if (response.success) {
                    // Show success notification
                    showDocumentNotification('<strong>✓ Analysis saved successfully!</strong>', 'success');
                    
                    // Mostra il messaggio nel nuovo elemento save-result-message
                    $('#save-result-message')
                        .removeClass('error')
                        .addClass('success')
                        .html(documentViewerParams.i18n.saved)
                        .fadeIn(300);
                    
                    // Auto-hide the notification after 5 seconds
                    setTimeout(function() {
                        hideDocumentNotification();
                        $('#save-result-message').fadeOut(300);
                    }, 5000);
                } else {
                    // Show error notification
                    console.error('Server reported error:', response);
                    showDocumentNotification('Error saving analysis: ' + (response.data ? response.data.message : 'Unknown error'), 'error');
                    
                    // Mostra il messaggio di errore nel nuovo elemento save-result-message
                    $('#save-result-message')
                        .removeClass('success')
                        .addClass('error')
                        .html(documentViewerParams.i18n.saveError + ': ' + (response.data ? response.data.message : 'Unknown error'))
                        .fadeIn(300);
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error details:', {
                    'status': status,
                    'error': error,
                    'response': xhr.responseText,
                    'status_code': xhr.status
                });
                
                let errorDetail = '';
                
                if (xhr.status === 0) {
                    errorDetail = 'Connection to server interrupted. Check your internet connection.';
                } else if (xhr.status === 500) {
                    errorDetail = 'Internal server error. Check server logs.';
                } else if (xhr.responseText) {
                    try {
                        const jsonResponse = JSON.parse(xhr.responseText);
                        if (jsonResponse.data && jsonResponse.data.message) {
                            errorDetail = jsonResponse.data.message;
                        }
                    } catch (e) {
                        errorDetail = xhr.responseText.substring(0, 100) + (xhr.responseText.length > 100 ? '...' : '');
                    }
                }
                
                // Show error notification
                showDocumentNotification('Failed to save analysis: ' + error + (errorDetail ? '<br>Details: ' + errorDetail : ''), 'error');
            }
        });
    });

    // Document drag functionality
    let isDragging = false;
    let startX, startY, scrollLeft, scrollTop;

    const display = document.getElementById('document-display');
    if (display) {
        display.addEventListener('mousedown', (e) => {
            isDragging = true;
            display.classList.add('grabbing');
            startX = e.pageX - display.offsetLeft;
            startY = e.pageY - display.offsetTop;
            scrollLeft = display.scrollLeft;
            scrollTop = display.scrollTop;
        });

        display.addEventListener('mouseleave', () => {
            isDragging = false;
            display.classList.remove('grabbing');
        });

        display.addEventListener('mouseup', () => {
            isDragging = false;
            display.classList.remove('grabbing');
        });

        display.addEventListener('mousemove', (e) => {
            if (!isDragging) return;
            e.preventDefault();
            const x = e.pageX - display.offsetLeft;
            const y = e.pageY - display.offsetTop;
            const walkX = (x - startX) * 1;
            const walkY = (y - startY) * 1;
            display.scrollLeft = scrollLeft - walkX;
            display.scrollTop = scrollTop - walkY;
        });
    }

    // Function to get the HTML content for PDF export
    function getAnalysisHtml() {
        if ($('#analysis-content').length) {
            return $('#analysis-content').html();
        }
        return '';
    }

    // Function to get the Key Points content for PDF export
    function getKeyPointsHtml() {
        if ($('#tab-keypoints').length) {
            return $('#tab-keypoints').html();
        }
        return '';
    }
    
    // Function to add the mega tooltip trigger button to the analysis section
    function addMegaTooltipTrigger() {
        // Verifica se esiste già un trigger
        if ($('.analysis-container .mega-tooltip-trigger').length === 0) {
            // Crea il bottone trigger per il mega tooltip con icona a forma di lente e tooltip informativo
            const $trigger = $('<button>', {
                'class': 'mega-tooltip-trigger',
                'title': 'Apri la visualizzazione estesa dell\'analisi in una finestra più grande',
                'aria-label': 'Visualizzazione estesa',
                'data-tooltip': 'Visualizza l\'analisi completa in una finestra più grande',
                'html': '<i class="dashicons dashicons-search"></i>'
            });
            
            // Aggiungi il trigger all'inizio dell'header dell'analisi
            $('.analysis-container .analysis-header').prepend($trigger);
            
            console.log('Mega tooltip trigger aggiunto alla finestra di analisi');
        }
    }

    // Function to extract document content without analysis
    function extractDocumentContent(file) {
        if (!file) return;
        
        // Show loading message in notification area
        showDocumentNotification('<div class="spinner"></div> Estrazione del testo in corso...', 'processing');
        
        // Update the inline document info area
        $('#document-info-inline').show();
        $('#document-name-inline').html('<strong>File:</strong> ' + file.name);
        $('#document-size-inline').html('<strong>Dim:</strong> ' + formatFileSize(file.size));
        $('#document-type-inline').html('<strong>Tipo:</strong> ' + file.type.split('/')[1].toUpperCase());
        
        // Se il contenuto è già stato estratto dai file Word convertiti, usa quello
        if (file.type === 'application/pdf' && file.name.match(/\.docx?\.pdf$/i) && extractedDocumentContent) {
            console.log('Using already extracted content from Word conversion');
            
            // Success notification in notification area
            hideDocumentNotification();
            showDocumentNotification('<strong>✓ Documento caricato con successo!</strong> Testo estratto dalla conversione Word precedente.', 'success');
            
            // Update the inline document info with character count
            $('#document-chars-inline').html('<strong>Chars:</strong> ' + formatCharCount(extractedDocumentContent.length));
            
            const tokenCount = estimateTokenCount(extractedDocumentContent);
            $('#document-tokens-inline').html('<strong>Tokens:</strong> ' + formatCharCount(tokenCount));
            
            // Calcola e visualizza la spesa stimata
            updateEstimatedCost(tokenCount);
            
            // Applica effetto blink sui campi token e menu domande preset
            if (window.documentStats && typeof window.documentStats.applyBlinkEffectOnLoad === 'function') {
                console.log('Applicazione effetto blink dopo estrazione documento');
                window.documentStats.applyBlinkEffectOnLoad();
            }
            
            return;
        }
        
        var formData = new FormData();
        formData.append('action', 'analyze_document');
        formData.append('nonce', documentViewerParams.nonce);
        formData.append('document_file', file);
        formData.append('save_content', 'true');

        console.log('FormData created for extraction:', {
            action: 'analyze_document',
            has_file: formData.has('document_file'),
            file_name: file.name,
            file_type: file.type,
            file_size: file.size,
            nonce: documentViewerParams.nonce ? 'presente' : 'mancante'
        });

        $.ajax({
            url: documentViewerParams.ajaxUrl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            timeout: 120000, // Aumentato a 2 minuti per file più grandi
            beforeSend: function(xhr) {
                console.log('Starting text extraction request to ' + documentViewerParams.ajaxUrl);
            },
            success: function(response) {
                console.log('Text extraction response received:', response);
                
                // Se nel frattempo è stata completata un'analisi, non aggiorniamo l'interfaccia
                if (preventReloadOnSuccess && $('#analysis-results').find('.analysis-container').length > 0) {
                    console.log('Analysis results exist, not updating UI after extraction');
                    if (response.success && response.data && response.data.document_content) {
                        extractedDocumentContent = response.data.document_content;
                        hideDocumentNotification();
                    }
                    return;
                }
                
                // Verifica che la risposta sia un oggetto valido
                if (typeof response !== 'object') {
                    try {
                        response = JSON.parse(response);
                        console.log('Response parsed from string to object');
                    } catch (e) {
                        console.error('Failed to parse response:', e);
                        
                        // Show error in notification area
                        showDocumentNotification('Errore nel formato della risposta dal server. Controlla la console per i dettagli.', 'error');
                        
                        // Also show in the analysis results area
                        if ($('#analysis-results').find('.analysis-container').length === 0) {
                            $('#analysis-results').html('<p class="error">Errore nel formato della risposta dal server. Controlla la console per i dettagli.</p>');
                        }
                        return;
                    }
                }
                
                if (response.success) {
                    if (response.data && response.data.document_content) {
                        extractedDocumentContent = response.data.document_content;
                        
                        // Success notification in notification area
                        showDocumentNotification('<strong>✓ Estrazione del testo completata!</strong> Sono stati estratti ' + 
                            formatCharCount(extractedDocumentContent.length) + ' caratteri.', 'success', true);
                        
                        // Update the inline document info with character count
                        $('#document-chars-inline').html('<strong>Chars:</strong> ' + formatCharCount(extractedDocumentContent.length));
                        
                        const tokenCount = estimateTokenCount(extractedDocumentContent);
                        $('#document-tokens-inline').html('<strong>Tokens:</strong> ' + formatCharCount(tokenCount));
                        
                        // Calcola e visualizza la spesa stimata
                        updateEstimatedCost(tokenCount);
                        
                        // Applica effetto blink sui campi token e menu domande preset
                        if (window.documentStats && typeof window.documentStats.applyBlinkEffectOnLoad === 'function') {
                            console.log('Applicazione effetto blink dopo estrazione documento');
                            window.documentStats.applyBlinkEffectOnLoad();
                        }
                        
                        // Only update the analysis results if no analysis container exists
                        if ($('#analysis-results').find('.analysis-container').length === 0) {
                            // Messaggio di conferma migliorato con stile
                            $('#analysis-results').html(
                                '<div class="extraction-success">' +
                                '<p><strong>✓ Estrazione del testo completata!</strong></p>' +
                                '<p>Sono stati estratti ' + formatCharCount(extractedDocumentContent.length) + ' caratteri.</p>' +
                                '<p>Puoi ora inserire una descrizione o domanda e cliccare su "Analizza" per procedere.</p>' +
                                '</div>'
                            );
                            
                            // Automatically fade out the extraction success message after 4 seconds
                            setTimeout(function() {
                                $('.extraction-success').fadeOut(1000, function() {
                                    $('html, body').animate({
                                        scrollTop: $('#document-display').offset().top - 50
                                    }, 500);
                                });
                            }, 4000);
                        }
                        
                        console.log('Document content saved to variable, length:', extractedDocumentContent.length);
                    } else {
                        console.error('No document content in response:', response);
                        
                        // Show error in notification area
                        showDocumentNotification('Estrazione del testo completata, ma nessun contenuto disponibile. Il documento potrebbe essere protetto o non contenere testo estraibile.', 'error');
                        
                        // Only update if no analysis results exist
                        if ($('#analysis-results').find('.analysis-container').length === 0) {
                            $('#analysis-results').html('<p class="error">Estrazione del testo completata, ma nessun contenuto disponibile. Il documento potrebbe essere protetto o non contenere testo estraibile.</p>');
                        }
                    }
                    
                    // Se il titolo non è impostato, utilizza il nome del file
                    if (!currentDocumentTitle) {
                        currentDocumentTitle = file.name.replace(/\.[^/.]+$/, ""); // Rimuove l'estensione
                        $('#analysis-title').val(currentDocumentTitle);
                    }
                } else {
                    let errorMessage = 'Errore durante l\'estrazione del testo';
                    
                    if (response.data && response.data.message) {
                        errorMessage += ': ' + response.data.message;
                    }
                    
                    console.error('Text extraction failed:', errorMessage);
                    
                    // Show error in notification area
                    showDocumentNotification(errorMessage, 'error');
                    
                    // Only update if no analysis results exist
                    if ($('#analysis-results').find('.analysis-container').length === 0) {
                        $('#analysis-results').html('<p class="error">' + errorMessage + '</p>');
                    }
                }
            },
            error: function(xhr, status, error) {
                // Se nel frattempo è stata completata un'analisi, non aggiorniamo l'interfaccia
                if (preventReloadOnSuccess && $('#analysis-results').find('.analysis-container').length > 0) {
                    console.error('Extraction failed but not updating UI because analysis results exist');
                    hideDocumentNotification();
                    return;
                }
                
                // Special handling for nonce verification errors (common for non-WordPress users)
                if (xhr.status === 403 || (xhr.responseText && xhr.responseText.indexOf('nonce') !== -1)) {
                    console.log('Possible nonce verification issue - attempting to continue with extraction');
                    // Try to extract content from the response if possible
                    try {
                        const jsonResponse = JSON.parse(xhr.responseText);
                        if (jsonResponse.data && jsonResponse.data.document_content) {
                            extractedDocumentContent = jsonResponse.data.document_content;
                            
                            // Success notification in notification area
                            showDocumentNotification('<strong>✓ Estrazione del testo completata!</strong> Sono stati estratti ' + 
                                formatCharCount(extractedDocumentContent.length) + ' caratteri.', 'success', true);
                            
                            // Update the inline document info with character count
                            $('#document-chars-inline').html('<strong>Chars:</strong> ' + formatCharCount(extractedDocumentContent.length));
                            
                            const tokenCount = estimateTokenCount(extractedDocumentContent);
                            $('#document-tokens-inline').html('<strong>Tokens:</strong> ' + formatCharCount(tokenCount));
                            
                            // Calcola e visualizza la spesa stimata
                            updateEstimatedCost(tokenCount);
                            return;
                        }
                    } catch (e) {
                        console.error('Failed to parse response during nonce error handling:', e);
                    }
                }
                
                let errorDetail = '';
                
                if (status === 'timeout') {
                    errorDetail = 'La richiesta è scaduta. Il documento potrebbe essere troppo grande o complesso.';
                } else if (xhr.status === 0) {
                    errorDetail = 'Connessione al server interrotta. Controlla la tua connessione internet.';
                } else if (xhr.status === 500) {
                    errorDetail = 'Errore interno del server. Controlla i log del server.';
                } else if (xhr.responseText) {
                    try {
                        const jsonResponse = JSON.parse(xhr.responseText);
                        if (jsonResponse.data && jsonResponse.data.message) {
                            errorDetail = jsonResponse.data.message;
                        }
                    } catch (e) {
                        errorDetail = xhr.responseText.substring(0, 100) + (xhr.responseText.length > 100 ? '...' : '');
                    }
                }
                
                console.error('AJAX error details:', {
                    status: status,
                    statusCode: xhr.status,
                    error: error,
                    response: xhr.responseText
                });
                
                // Show error in notification area
                showDocumentNotification('Estrazione fallita: ' + error + (errorDetail ? '<br>Dettagli: ' + errorDetail : ''), 'error');
                
                // Only update if no analysis results exist
                if ($('#analysis-results').find('.analysis-container').length === 0) {
                    $('#analysis-results').html('<p class="error">Estrazione fallita: ' + error + (errorDetail ? '<br>Dettagli: ' + errorDetail : '') + '</p>');
                }
            }
        });
    }

    // Funzione per esportare in PDF
    function exportToPdf() {
        if (!currentDocumentFile) {
            alert('Carica un documento e attendi che l\'analisi sia completata prima di esportare in PDF.');
            return;
        }
        
        // Show export options dialog
        showExportOptionsDialog();
    }
    
    // Function to show export options dialog
    function showExportOptionsDialog() {
        // Create a modal dialog for export options
        const dialogHtml = `
            <div id="export-options-modal" class="export-options-modal">
                <div class="export-options-content">
                    <h3>Opzioni di Esportazione PDF</h3>
                    <p>Seleziona le sezioni da includere nel documento PDF:</p>
                    
                    <div class="export-option">
                        <input type="checkbox" id="include-query" checked>
                        <label for="include-query">Richiesta di Analisi</label>
                    </div>
                    
                    <div class="export-option">
                        <input type="checkbox" id="include-full-analysis" checked>
                        <label for="include-full-analysis">Analisi Completa</label>
                    </div>
                    
                    <div class="export-option">
                        <input type="checkbox" id="include-key-points" checked>
                        <label for="include-key-points">Punti Chiave</label>
                    </div>
                    
                    <div class="export-option">
                        <input type="checkbox" id="include-original-doc" checked>
                        <label for="include-original-doc">Documento Originale</label>
                    </div>
                    
                    <div class="export-buttons">
                        <button type="button" id="cancel-export" class="btn-secondary">Annulla</button>
                        <button type="button" id="confirm-export" class="btn-primary">Esporta PDF</button>
                    </div>
                </div>
            </div>
        `;
        
        // Add modal to body
        $('body').append(dialogHtml);
        
        // Add styles for the modal
        $('head').append(`
            <style>
                .export-options-modal {
                    display: block;
                    position: fixed;
                    z-index: 1000;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0,0,0,0.5);
                }
                
                .export-options-content {
                    background-color: #fff;
                    margin: 10% auto;
                    padding: 25px;
                    border-radius: 8px;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                    width: 450px;
                    max-width: 90%;
                }
                
                .export-options-content h3 {
                    margin-top: 0;
                    color: #1a4b78;
                    border-bottom: 2px solid #eee;
                    padding-bottom: 10px;
                }
                
                .export-option {
                    margin: 15px 0;
                    display: flex;
                    align-items: center;
                }
                
                .export-option input[type="checkbox"] {
                    margin-right: 10px;
                    width: 18px;
                    height: 18px;
                }
                
                .export-option label {
                    font-size: 16px;
                    color: #333;
                }
                
                .export-buttons {
                    margin-top: 25px;
                    display: flex;
                    justify-content: flex-end;
                    gap: 10px;
                }
                
                .btn-primary {
                    background-color: #1a4b78;
                    color: white;
                    border: none;
                    padding: 10px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-weight: 600;
                }
                
                .btn-secondary {
                    background-color: #f1f1f1;
                    color: #333;
                    border: 1px solid #ddd;
                    padding: 10px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                }
                
                .btn-primary:hover {
                    background-color: #0d3a67;
                }
                
                .btn-secondary:hover {
                    background-color: #e5e5e5;
                }
            </style>
        `);
        
        // Handle close button
        $('#cancel-export').on('click', function() {
            $('#export-options-modal').remove();
        });
        
        // Handle confirm button
        $('#confirm-export').on('click', function() {
            // Get options
            const includeQuery = $('#include-query').is(':checked');
            const includeFullAnalysis = $('#include-full-analysis').is(':checked');
            const includeKeyPoints = $('#include-key-points').is(':checked');
            const includeOriginalDoc = $('#include-original-doc').is(':checked');
            
            // Validate at least one section is selected
            if (!includeFullAnalysis && !includeKeyPoints) {
                alert('Seleziona almeno una sezione di analisi da includere (Analisi Completa o Punti Chiave).');
                return;
            }
            
            // Remove modal
            $('#export-options-modal').remove();
            
            // Proceed with PDF export
            proceedWithPdfExport(includeQuery, includeFullAnalysis, includeKeyPoints, includeOriginalDoc);
        });
    }
    
    // Function to proceed with PDF export after options are selected
    function proceedWithPdfExport(includeQuery, includeFullAnalysis, includeKeyPoints, includeOriginalDoc) {
        // Ottieni il contenuto dell'analisi (solo se selezionato)
        let analysisHtml = includeFullAnalysis ? getAnalysisHtml() : '';
        let keyPointsHtml = includeKeyPoints ? getKeyPointsHtml() : '';
        
        if (!includeFullAnalysis && !includeKeyPoints) {
            alert('Non ci sono risultati di analisi da esportare.');
            return;
        }
        
        const title = $('#analysis-title').val() || currentDocumentTitle || 'Documento senza titolo';
        const annotations = $('#document-annotations').val();
        const description = includeQuery ? $('#document-description').val() : '';
        
        // Debug dei dati esportati
        console.log('Dati per esportazione PDF:', {
            title: title,
            include_query: includeQuery,
            include_full_analysis: includeFullAnalysis,
            include_key_points: includeKeyPoints,
            include_original_doc: includeOriginalDoc,
            analysis_html_length: analysisHtml ? analysisHtml.length : 0,
            key_points_html_length: keyPointsHtml ? keyPointsHtml.length : 0,
            description_length: description ? description.length : 0,
            annotations_length: annotations ? annotations.length : 0,
            hasCustomLogo: !!customLogo
        });
        
        // Verifico se c'è già un messaggio di processing visualizzato
        const isProcessingVisible = $('#document-notification-area').is(':visible') && 
                                   $('#document-notification-area').hasClass('processing');
        
        // Mostro il messaggio solo se non c'è già un messaggio di processing
        if (!isProcessingVisible) {
            showDocumentNotification('<div class="spinner"></div> Esportazione del PDF in corso...', 'processing');
        } else {
            console.log('Messaggio di processing già visibile, evito di mostrare un altro messaggio per l\'esportazione PDF');
        }
        
        // Usa FormData per gestire sia i dati testuali che i file
        const formData = new FormData();
        formData.append('action', 'export_analysis_pdf');
        formData.append('nonce', documentViewerParams.nonce);
        formData.append('title', title);
        formData.append('analysis_html', analysisHtml);
        formData.append('key_points_html', keyPointsHtml);
        formData.append('description', description);
        formData.append('annotations', annotations);
        
        // Add flags for sections to include
        formData.append('include_query', includeQuery ? '1' : '0');
        formData.append('include_full_analysis', includeFullAnalysis ? '1' : '0');
        formData.append('include_key_points', includeKeyPoints ? '1' : '0');
        formData.append('include_original_doc', includeOriginalDoc ? '1' : '0');
        
        // Aggiungi il documento PDF originale se disponibile e selezionato
        if (currentDocumentFile && includeOriginalDoc) {
            formData.append('uploaded_pdf', currentDocumentFile);
        }
        
        // Aggiungi il logo personalizzato se disponibile
        if (customLogo) {
            formData.append('custom_logo', customLogo);
        }
        
        // Esegui la richiesta AJAX per l'esportazione
        $.ajax({
            url: documentViewerParams.ajaxUrl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            xhr: function() {
                var xhr = new XMLHttpRequest();
                xhr.responseType = 'blob';
                return xhr;
            },
            success: function(response, status, xhr) {
                // Hide the processing notification
                hideDocumentNotification();
                
                // Show success notification
                showDocumentNotification('<strong>✓ PDF generato con successo!</strong>', 'success');
                
                // Crea un link per scaricare il PDF
                var blob = new Blob([response], { type: 'application/pdf' });
                var link = document.createElement('a');
                link.href = window.URL.createObjectURL(blob);
                link.download = title + '-analysis.pdf';
                link.click();
            },
            error: function(xhr, status, error) {
                // Hide the processing notification
                hideDocumentNotification();
                
                console.error('PDF export error:', error);
                showDocumentNotification('Errore durante l\'esportazione del PDF: ' + error, 'error');
            }
        });
    }

    // Funzione per estrarre i punti chiave dall'analisi completa
    function extractKeyPoints(analysisHtml) {
        // Rimuovi tutti i tag HTML per lavorare con testo puro
        let plainText = analysisHtml.replace(/<[^>]*>/g, ' ');
        
        // Rimuovi i simboli &nbsp; sostituendoli con spazi normali
        plainText = plainText.replace(/&nbsp;/g, ' ');
        
        // Elimina asterischi e cancelletti dal testo
        plainText = plainText.replace(/\*/g, '');
        plainText = plainText.replace(/#/g, '');
        
        // Dividi il testo in paragrafi
        let paragraphs = [];
        
        // Strategia 1: Se il contenuto contiene tag HTML, estrai i paragrafi
        if (analysisHtml.includes('<p>')) {
            // Estrai tutti i tag <p> con il loro contenuto
            const regex = /<p[^>]*>(.*?)<\/p>/gs;
            let match;
            while ((match = regex.exec(analysisHtml)) !== null) {
                paragraphs.push(match[1].trim());
            }
        } 
        // Strategia 2: Se non ci sono tag HTML, dividi per nuove linee
        else {
            paragraphs = plainText.split(/\n\s*\n|\r\n\s*\r\n|\r\s*\r/);
            paragraphs = paragraphs.filter(p => p.trim().length > 0);
        }
        
        // Se non abbiamo paragrafi, dividi il contenuto in frasi
        if (paragraphs.length === 0) {
            const sentences = plainText.split(/(?<=[.!?;])\s+/);
            paragraphs = sentences.filter(s => s.trim().length > 0);
        }
        
        // Se ancora non abbiamo contenuto, usa il testo così com'è
        if (paragraphs.length === 0) {
            paragraphs = [plainText];
        }
        
        // Estrai i punti chiave dai paragrafi - massimo 5 punti
        const keyPoints = [];
        const maxPoints = 5;
        
        // Scorri i paragrafi per estrarre i punti più significativi
        for (let i = 0; i < paragraphs.length && keyPoints.length < maxPoints; i++) {
            const para = paragraphs[i];
            
            // Usa solo paragrafi abbastanza lunghi e significativi
            if (para.length > 40 && !para.includes("In conclusione") && !para.includes("In sintesi")) {
                // Assicurati di rimuovere eventuali asterischi e cancelletti residui
                const cleanedPara = para.replace(/[\*\#]/g, '');
                keyPoints.push(cleanedPara);
            }
        }
        
        // Se non abbiamo abbastanza punti chiave, aggiungi più paragrafi
        if (keyPoints.length < 3 && paragraphs.length > 0) {
            for (let i = 0; i < paragraphs.length && keyPoints.length < 3; i++) {
                if (!keyPoints.includes(paragraphs[i])) {
                    // Assicurati di rimuovere eventuali asterischi e cancelletti residui
                    const cleanedPara = paragraphs[i].replace(/[\*\#]/g, '');
                    keyPoints.push(cleanedPara);
                }
            }
        }
        
        // Formatta i punti chiave in HTML
        let keyPointsHtml = '';
        if (keyPoints.length > 0) {
            keyPointsHtml += '<ul class="key-points-list">';
            keyPoints.forEach(point => {
                // Sostituisci eventuali trattini con simboli di spunta e rimuovi asterischi/cancelletti residui
                const processedPoint = point
                    .replace(/^\s*-\s*/g, '✓ ')
                    .replace(/-\s+/g, '✓ ')
                    .replace(/[\*\#]/g, '');
                keyPointsHtml += '<li>' + processedPoint + '</li>';
            });
            keyPointsHtml += '</ul>';
        } else {
            keyPointsHtml = '<p>Non è stato possibile generare punti chiave da questa analisi.</p>';
        }
        
        return keyPointsHtml;
    }

    // Gestione delle richieste predefinite
    $('#preset-queries').on('change', function() {
        const selectedQuery = $(this).val();
        
        if (selectedQuery) {
            // Inserisci la richiesta predefinita nel campo di testo
            $('#document-description').val(selectedQuery);
            
            // Aggiungi la classe per l'animazione di evidenziazione
            $('#document-description').addClass('textarea-highlight');
            
            // Rimuovi la classe dopo la fine dell'animazione
            setTimeout(function() {
                $('#document-description').removeClass('textarea-highlight');
            }, 1500);
            
            // Sposta il focus sul textarea per permettere modifiche immediate
            $('#document-description').focus();
        }
    });
    
    // Gestione del pannello di amministrazione per le query predefinite
    $('#add-financial-queries-set').on('click', function() {
        if (!confirm('Sei sicuro di voler aggiungere il set di query finanziarie predefinite? Questo aggiungerà circa 15 nuove query predefinite.')) {
            return;
        }
        
        // Show loading state
        $(this).prop('disabled', true).text('Aggiunta in corso...');
        const $button = $(this);
        
        $.ajax({
            url: documentViewerParams.ajaxUrl,
            type: 'POST',
            data: {
                action: 'add_financial_query_set',
                nonce: documentViewerParams.nonce
            },
            success: function(response) {
                // Reset button state
                $button.prop('disabled', false).text('Aggiungi Set di Query Finanziarie');
                
                if (response.success) {
                    alert(response.data.message);
                    // Reload the page to show the new queries
                    location.reload();
                } else {
                    alert('Errore: ' + (response.data ? response.data.message : 'Errore sconosciuto'));
                }
            },
            error: function(xhr, status, error) {
                // Reset button state
                $button.prop('disabled', false).text('Aggiungi Set di Query Finanziarie');
                alert('Errore durante l\'aggiunta delle query: ' + error);
            }
        });
    });
    
    // Gestione del modulo di aggiunta query predefinite
    $('#add-preset-query-form').on('submit', function(e) {
        e.preventDefault();
        
        const title = $('#preset-query-title').val();
        const queryText = $('#preset-query-text').val();
        
        if (!title || !queryText) {
            alert('Per favore, compila tutti i campi.');
            return;
        }
        
        $.ajax({
            url: documentViewerParams.ajaxUrl,
            type: 'POST',
            data: {
                action: 'add_preset_query',
                nonce: documentViewerParams.nonce,
                title: title,
                query_text: queryText
            },
            success: function(response) {
                if (response.success) {
                    $('#preset-query-title').val('');
                    $('#preset-query-text').val('');
                    alert(response.data.message);
                    // Reload the page to show the new query
                    location.reload();
                } else {
                    alert('Errore: ' + (response.data ? response.data.message : 'Errore sconosciuto'));
                }
            },
            error: function(xhr, status, error) {
                alert('Errore durante l\'aggiunta della query: ' + error);
            }
        });
    });
    
    // Gestione del pulsante di modifica query predefinita
    $('.edit-preset-query').on('click', function() {
        const queryId = $(this).data('query-id');
        const title = $(this).data('title');
        const queryText = $(this).data('query-text');
        
        // Compila il modulo di modifica
        $('#edit-query-id').val(queryId);
        $('#edit-query-title').val(title);
        $('#edit-query-text').val(queryText);
        
        // Mostra il modulo di modifica
        $('#edit-preset-query-modal').show();
    });
    
    // Gestione del modulo di modifica query predefinite
    $('#edit-preset-query-form').on('submit', function(e) {
        e.preventDefault();
        
        const queryId = $('#edit-query-id').val();
        const title = $('#edit-query-title').val();
        const queryText = $('#edit-query-text').val();
        
        if (!queryId || !title || !queryText) {
            alert('Per favore, compila tutti i campi.');
            return;
        }
        
        $.ajax({
            url: documentViewerParams.ajaxUrl,
            type: 'POST',
            data: {
                action: 'update_preset_query',
                nonce: documentViewerParams.nonce,
                query_id: queryId,
                title: title,
                query_text: queryText
            },
            success: function(response) {
                if (response.success) {
                    // Nascondi il modulo di modifica
                    $('#edit-preset-query-modal').hide();
                    alert(response.data.message);
                    // Reload the page to show the updated query
                    location.reload();
                } else {
                    alert('Errore: ' + (response.data ? response.data.message : 'Errore sconosciuto'));
                }
            },
            error: function(xhr, status, error) {
                alert('Errore durante l\'aggiornamento della query: ' + error);
            }
        });
    });
    
    // Gestione del pulsante di chiusura modale
    $('.close-modal').on('click', function() {
        $(this).closest('.modal').hide();
    });
    
    // Gestione del pulsante di eliminazione query predefinita
    $('.delete-preset-query').on('click', function() {
        if (!confirm('Sei sicuro di voler eliminare questa query predefinita?')) {
            return;
        }
        
        const queryId = $(this).data('query-id');
        
        $.ajax({
            url: documentViewerParams.ajaxUrl,
            type: 'POST',
            data: {
                action: 'delete_preset_query',
                nonce: documentViewerParams.nonce,
                query_id: queryId
            },
            success: function(response) {
                if (response.success) {
                    alert(response.data.message);
                    // Reload the page to update the list
                    location.reload();
                } else {
                    alert('Errore: ' + (response.data ? response.data.message : 'Errore sconosciuto'));
                }
            },
            error: function(xhr, status, error) {
                alert('Errore durante l\'eliminazione della query: ' + error);
            }
        });
    });

    // Chat functionality
    function showTyping() {
        $('#chat-typing, #settings-chat-typing').show().html('Model is typing<span class="dots">...</span>');
    }
    
    function hideTyping() {
        $('#chat-typing, #settings-chat-typing').hide();
    }
    
    // ... altre funzionalità chat esistenti
    
    // Funzione per creare l'effetto di digitazione del testo
    function typeContentWithEffect(content) {
        // Verifica che ci sia un contenuto valido
        if (!content) {
            $('#analysis-content').html('<p>Nessun contenuto disponibile per l\'analisi.</p>');
            return;
        }
        
        // Rimuovi i simboli &nbsp; sostituendoli con spazi normali
        content = content.replace(/&nbsp;/g, ' ');
        
        // Rimuovi il carattere \ prima degli apostrofi
        content = content.replace(/\\'/g, "'");
        
        // Correggi altri caratteri speciali che potrebbero apparire nel testo
        content = content.replace(/\\"/g, '"');
        content = content.replace(/\\\\/g, '\\');
        
        // Rimuovi riferimenti a commit GitHub
        content = content.replace(/\[([0-9a-f]+)\]\(https:\/\/github\.com\/.*?\/commit\/[0-9a-f]+\)/g, '');
        
        // Elimina asterischi e cancelletti dal contenuto dell'analisi
        content = content.replace(/\*/g, '');
        content = content.replace(/#/g, '');
        
        // Aggiungi CSS necessario per l'effetto di digitazione
        const typingCSS = `
            <style>
                .typing-effect {
                    min-height: 200px;
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    justify-content: flex-end;
                    overflow-y: auto;
                }
                
                .typing-effect .cursor {
                    display: inline-block;
                    width: 2px;
                    height: 18px;
                    background-color: #333;
                    animation: blink 1s infinite;
                    margin-left: 2px;
                    vertical-align: middle;
                }
                
                @keyframes blink {
                    0%, 100% { opacity: 1; }
                    50% { opacity: 0; }
                }
                
                .paragraph-break {
                    display: block;
                    height: 1em;
                }
            </style>
        `;
        
        // Aggiungi lo stile se non esiste già
        if (!$('style:contains(.typing-effect)').length) {
            $('head').append(typingCSS);
        }
        
        // Prepara il contenitore per l'effetto di digitazione
        const $container = $('#analysis-content');
        $container.empty().html('<span class="cursor"></span>');
        
        // Processa il contenuto HTML per preservare formattazione
        let paragraphs = [];
        
        // Strategia 1: Se il contenuto contiene tag HTML, estrai i paragrafi
        if (content.includes('<p>')) {
            // Estrai tutti i tag <p> con il loro contenuto
            const regex = /<p[^>]*>(.*?)<\/p>/gs;
            let match;
            while ((match = regex.exec(content)) !== null) {
                paragraphs.push(match[1].trim());
            }
        } 
        // Strategia 2: Se non ci sono tag HTML, dividi per nuove linee
        else {
            paragraphs = content.split(/\n\s*\n|\r\n\s*\r\n|\r\s*\r/);
            paragraphs = paragraphs.filter(p => p.trim().length > 0);
        }
        
        // Se non abbiamo paragrafi, dividi il contenuto in frasi
        if (paragraphs.length === 0) {
            const sentences = content.split(/(?<=[.!?;])\s+/);
            paragraphs = sentences.filter(s => s.trim().length > 0);
        }
        
        // Se ancora non abbiamo contenuto, usa il testo così com'è
        if (paragraphs.length === 0) {
            paragraphs = [content];
        }
        
        // Variabili per l'effetto di typing
        let paragraphIndex = 0;
        let charIndex = 0;
        let currentText = '';
        const $cursor = $container.find('.cursor');
        
        // Imposta la velocità di digitazione (caratteri al secondo) - RALLENTATA
        const typingSpeed = 30; // Rallentato a 30 caratteri al secondo
        
        // Intervallo per l'effetto di digitazione
        const interval = setInterval(() => {
            // Se abbiamo finito tutti i paragrafi, ferma l'animazione
            if (paragraphIndex >= paragraphs.length) {
                clearInterval(interval);
                $cursor.remove(); // Rimuovi il cursore alla fine
                return;
            }
            
            // Paragrafo corrente
            const currentParagraph = paragraphs[paragraphIndex];
            
            // Se abbiamo finito il paragrafo corrente, passa al successivo
            if (charIndex >= currentParagraph.length) {
                paragraphIndex++;
                charIndex = 0;
                
                // Aggiungi una fine paragrafo e continua con il prossimo
                $cursor.before('<br><br>');
                
              
                
                // Se abbiamo finito tutti i paragrafi, ferma l'animazione
                if (paragraphIndex >= paragraphs.length) {
                    clearInterval(interval);
                    $cursor.remove(); // Rimuovi il cursore alla fine
                    return;
                }
                
                return; // Aspetta il prossimo ciclo periniziare il nuovo paragrafo
            }
            
            // Prendi un gruppo di caratteri più piccolo per una digitazione più lenta e naturale
            const charsToType = Math.floor(Math.random() * 3) + 1; // Ridotto a 1-3 caratteri per volta
            const nextChars = currentParagraph.substr(charIndex, charsToType);
            charIndex += nextChars.length;
            
            // Inserisci i nuovi caratteri prima del cursore
            $cursor.before(nextChars);
            
            // Scorri sempre in basso per mantenere visibile il testo nuovo (allineamento in basso)
            const container = $container[0];
            container.scrollTop = container.scrollHeight;
            
        }, 1000 / typingSpeed);
    }

    // Gestione delle richieste predefinite
    $('#preset-queries').on('change', function() {
        const selectedQuery = $(this).val();
        
        if (selectedQuery) {
            // Inserisci la richiesta predefinita nel campo di testo
            $('#document-description').val(selectedQuery);
            
            // Aggiungi la classe per l'animazione di evidenziazione
            $('#document-description').addClass('textarea-highlight');
            
            // Rimuovi la classe dopo la fine dell'animazione
            setTimeout(function() {
                $('#document-description').removeClass('textarea-highlight');
            }, 1500);
            
            // Sposta il focus sul textarea per permettere modifiche immediate
            $('#document-description').focus();
        }
    });
    
    // Gestione del pannello di amministrazione per le query predefinite
    $('#add-financial-queries-set').on('click', function() {
        if (!confirm('Sei sicuro di voler aggiungere il set di query finanziarie predefinite? Questo aggiungerà circa 15 nuove query predefinite.')) {
            return;
        }
        
        // Show loading state
        $(this).prop('disabled', true).text('Aggiunta in corso...');
        const $button = $(this);
        
        $.ajax({
            url: documentViewerParams.ajaxUrl,
            type: 'POST',
            data: {
                action: 'add_financial_query_set',
                nonce: documentViewerParams.nonce
            },
            success: function(response) {
                // Reset button state
                $button.prop('disabled', false).text('Aggiungi Set di Query Finanziarie');
                
                if (response.success) {
                    alert(response.data.message);
                    // Reload the page to show the new queries
                    location.reload();
                } else {
                    alert('Errore: ' + (response.data ? response.data.message : 'Errore sconosciuto'));
                }
            },
            error: function(xhr, status, error) {
                // Reset button state
                $button.prop('disabled', false).text('Aggiungi Set di Query Finanziarie');
                alert('Errore durante l\'aggiunta delle query: ' + error);
            }
        });
    });
    
    // Gestione del modulo di aggiunta query predefinite
    $('#add-preset-query-form').on('submit', function(e) {
        e.preventDefault();
        
        const title = $('#preset-query-title').val();
        const queryText = $('#preset-query-text').val();
        
        if (!title || !queryText) {
            alert('Per favore, compila tutti i campi.');
            return;
        }
        
        $.ajax({
            url: documentViewerParams.ajaxUrl,
            type: 'POST',
            data: {
                action: 'add_preset_query',
                nonce: documentViewerParams.nonce,
                title: title,
                query_text: queryText
            },
            success: function(response) {
                if (response.success) {
                    $('#preset-query-title').val('');
                    $('#preset-query-text').val('');
                    alert(response.data.message);
                    // Reload the page to show the new query
                    location.reload();
                } else {
                    alert('Errore: ' + (response.data ? response.data.message : 'Errore sconosciuto'));
                }
            },
            error: function(xhr, status, error) {
                alert('Errore durante l\'aggiunta della query: ' + error);
            }
        });
    });
    
    // Gestione del pulsante di modifica query predefinita
    $('.edit-preset-query').on('click', function() {
        const queryId = $(this).data('query-id');
        const title = $(this).data('title');
        const queryText = $(this).data('query-text');
        
        // Compila il modulo di modifica
        $('#edit-query-id').val(queryId);
        $('#edit-query-title').val(title);
        $('#edit-query-text').val(queryText);
        
        // Mostra il modulo di modifica
        $('#edit-preset-query-modal').show();
    });
    
    // Gestione del modulo di modifica query predefinite
    $('#edit-preset-query-form').on('submit', function(e) {
        e.preventDefault();
        
        const queryId = $('#edit-query-id').val();
        const title = $('#edit-query-title').val();
        const queryText = $('#edit-query-text').val();
        
        if (!queryId || !title || !queryText) {
            alert('Per favore, compila tutti i campi.');
            return;
        }
        
        $.ajax({
            url: documentViewerParams.ajaxUrl,
            type: 'POST',
            data: {
                action: 'update_preset_query',
                nonce: documentViewerParams.nonce,
                query_id: queryId,
                title: title,
                query_text: queryText
            },
            success: function(response) {
                if (response.success) {
                    // Nascondi il modulo di modifica
                    $('#edit-preset-query-modal').hide();
                    alert(response.data.message);
                    // Reload the page to show the updated query
                    location.reload();
                } else {
                    alert('Errore: ' + (response.data ? response.data.message : 'Errore sconosciuto'));
                }
            },
            error: function(xhr, status, error) {
                alert('Errore durante l\'aggiornamento della query: ' + error);
            }
        });
    });
    
    // Gestione del pulsante di chiusura modale
    $('.close-modal').on('click', function() {
        $(this).closest('.modal').hide();
    });
    
    // Gestione del pulsante di eliminazione query predefinita
    $('.delete-preset-query').on('click', function() {
        if (!confirm('Sei sicuro di voler eliminare questa query predefinita?')) {
            return;
        }
        
        const queryId = $(this).data('query-id');
        
        $.ajax({
            url: documentViewerParams.ajaxUrl,
            type: 'POST',
            data: {
                action: 'delete_preset_query',
                nonce: documentViewerParams.nonce,
                query_id: queryId
            },
            success: function(response) {
                if (response.success) {
                    alert(response.data.message);
                    // Reload the page to update the list
                    location.reload();
                } else {
                    alert('Errore: ' + (response.data ? response.data.message : 'Errore sconosciuto'));
                }
            },
            error: function(xhr, status, error) {
                alert('Errore durante l\'eliminazione della query: ' + error);
            }
        });
    });

    // Chat functionality
    function showTyping() {
        $('#chat-typing, #settings-chat-typing').show().html('Model is typing<span class="dots">...</span>');
    }
    
    function hideTyping() {
        $('#chat-typing, #settings-chat-typing').hide();
    }
    
    // ... altre funzionalità chat esistenti
});
