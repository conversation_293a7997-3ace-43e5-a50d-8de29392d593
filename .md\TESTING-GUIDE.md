# Payment Gateway WordPress Testing Guide

## Testing Environment Setup

### Prerequisites
1. WordPress installation with admin access
2. Financial Advisor plugin activated
3. Database backup recommended before testing

## Step-by-Step Testing Process

### Phase 1: Database Tables Verification

1. **Check Plugin Activation**
   ```sql
   -- Check if tables were created
   SHOW TABLES LIKE '%paypal_config%';
   SHOW TABLES LIKE '%stripe_config%';
   
   -- Verify table structure
   DESCRIBE wp_paypal_config;
   DESCRIBE wp_stripe_config;
   ```

2. **Expected Table Structure**
   - `wp_paypal_config`: id, client_id, client_secret, environment, webhook_id, is_active, created_at, updated_at
   - `wp_stripe_config`: id, public_key, secret_key, environment, webhook_endpoint_secret, is_active, created_at, updated_at

### Phase 2: Admin Interface Testing

1. **Access Payment Gateway Configuration**
   - Navigate to WordPress Admin → Financial Advisor → Payment Gateways
   - Verify the page loads without errors
   - Check that both PayPal and Stripe tabs are visible

2. **PayPal Configuration Testing**
   - Click on PayPal tab
   - Fill in test credentials:
     - Client ID: `test_client_id_123`
     - Client Secret: `test_client_secret_456`
     - Environment: Select "Sandbox"
   - Click "Save PayPal Settings"
   - Verify success message appears
   - Check "Test Connection" functionality

3. **Stripe Configuration Testing**
   - Click on Stripe tab
   - Fill in test credentials:
     - Public Key: `pk_test_123456789`
     - Secret Key: `sk_test_987654321`
     - Environment: Select "Test"
   - Click "Save Stripe Settings"
   - Verify success message appears
   - Check "Test Connection" functionality

4. **UI/UX Testing**
   - Test password field show/hide toggles
   - Verify responsive design on different screen sizes
   - Check loading states during save/test operations
   - Verify error handling with invalid data

### Phase 3: Subscriber Widget Testing

1. **Access Subscriber Widget**
   - Navigate to page with subscriber management widget
   - Verify only PayPal and Stripe payment options are shown
   - Confirm Bank Transfer option is NOT visible

2. **Payment Method Selection**
   - Test selecting PayPal option
   - Test selecting Stripe option
   - Verify no "Bank Transfer" or "Bonifico Bancario" option exists

3. **Transaction Logging**
   - Perform a test credit recharge (if payment processing is implemented)
   - Check `wp_credit_transactions` table for new entries:
   ```sql
   SELECT * FROM wp_credit_transactions ORDER BY created_at DESC LIMIT 5;
   ```
   - Verify transaction includes proper payment_method, notes, and transaction details

### Phase 4: Menu Integration Testing

1. **Menu Access Control**
   - Test access with admin user (should work)
   - Test access with non-admin user (should be restricted)
   - Verify proper error messages for unauthorized access

2. **Menu Navigation**
   - Check Financial Advisor main menu exists
   - Verify "Payment Gateways" submenu appears
   - Test navigation between different admin pages

### Phase 5: Error Handling Testing

1. **Invalid API Credentials**
   - Enter invalid PayPal credentials and test connection
   - Enter invalid Stripe credentials and test connection
   - Verify appropriate error messages

2. **Database Connectivity**
   - Test saving settings with database connection issues
   - Verify graceful error handling

3. **Permission Testing**
   - Test access with insufficient permissions
   - Verify proper WordPress capability checks

## Expected Test Results

### ✅ Success Criteria

1. **Database Tables**
   - PayPal and Stripe config tables created successfully
   - Proper indexes and constraints in place
   - Tables accessible via WordPress $wpdb

2. **Admin Interface**
   - Payment gateway page loads without PHP errors
   - Both tabs (PayPal/Stripe) function correctly
   - Settings save successfully to database
   - Connection testing works (returns proper responses)
   - UI elements respond correctly (toggles, loading states)

3. **Subscriber Widget**
   - Only PayPal and Stripe options visible
   - Bank transfer completely removed
   - Payment method selection works
   - Transaction logging functions properly

4. **Integration**
   - Menu appears in correct location
   - Access controls work properly
   - No conflicts with existing functionality
   - WordPress hooks and filters work correctly

### ❌ Common Issues and Solutions

1. **Fatal Error: Class not found**
   - **Issue**: Menu_Manager class instantiation error
   - **Solution**: Use `Financial_Advisor_Menu_Manager::get_instance()` instead of `new Menu_Manager()`
   - **Fixed**: ✅ Implemented proper singleton pattern

2. **Database Tables Not Created**
   - **Issue**: Tables missing after plugin activation
   - **Solution**: Manually run `wpcd_initialize_database_tables()` or reactivate plugin
   - **Check**: Verify activation hooks are properly registered

3. **Payment Gateway Page Not Found**
   - **Issue**: 404 error when accessing payment gateway page
   - **Solution**: Verify Menu_Manager is properly initialized
   - **Check**: Confirm `render_payment_gateway_page()` method exists

4. **Bank Transfer Still Visible**
   - **Issue**: Old payment method not removed
   - **Solution**: Clear any caches, check widget template files
   - **Verify**: Search codebase for "Bonifico Bancario" or "Bank Transfer"

## Performance Testing

1. **Page Load Times**
   - Admin page should load within 2-3 seconds
   - Widget should render quickly on frontend
   - Database queries should be optimized

2. **AJAX Response Times**
   - Save operations should complete within 5 seconds
   - Connection tests should timeout appropriately
   - User feedback should be immediate

## Security Testing

1. **Nonce Verification**
   - All AJAX requests include valid nonces
   - Form submissions are properly protected
   - Cross-site request forgery prevention

2. **Capability Checks**
   - Only users with proper permissions can access admin
   - Subscriber actions are properly authenticated
   - Data sanitization works correctly

3. **API Key Protection**
   - Sensitive data stored securely
   - Password fields properly masked
   - No sensitive data in logs or output

## Debugging Tips

1. **Enable WordPress Debug Mode**
   ```php
   define('WP_DEBUG', true);
   define('WP_DEBUG_LOG', true);
   define('WP_DEBUG_DISPLAY', false);
   ```

2. **Check Error Logs**
   - WordPress debug.log
   - PHP error logs
   - Browser console for JavaScript errors

3. **Database Debugging**
   ```php
   // Add to wp-config.php for SQL debugging
   define('SAVEQUERIES', true);
   ```

4. **Plugin Debugging**
   ```php
   // Use built-in logging function
   dv_debug_log('Payment gateway test message', 'payment_gateway');
   ```

## Final Checklist

- [ ] Database tables created and populated
- [ ] Admin interface accessible and functional
- [ ] PayPal configuration saves and tests properly
- [ ] Stripe configuration saves and tests properly
- [ ] Bank transfer payment method completely removed
- [ ] Subscriber widget shows only PayPal and Stripe
- [ ] Transaction logging works to credit_transactions table
- [ ] Menu integration functions correctly
- [ ] Access controls work properly
- [ ] No PHP errors or warnings
- [ ] UI/UX works across different devices
- [ ] Security measures are in place
- [ ] Performance is acceptable

## Next Phase: Payment Processing Implementation

After successful testing of the configuration interface, the next phase would involve:

1. **PayPal API Integration**
   - Implement PayPal JavaScript SDK
   - Create payment processing endpoints
   - Handle payment confirmations and webhooks

2. **Stripe API Integration**
   - Implement Stripe Elements
   - Create secure payment processing
   - Handle payment intents and webhooks

3. **Frontend Payment Forms**
   - Create interactive payment interfaces
   - Implement client-side validation
   - Handle success/failure states

This testing guide ensures that the foundation (configuration and admin interface) is solid before implementing the actual payment processing functionality.
