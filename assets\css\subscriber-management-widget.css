/**
 * Subscriber Management Widget CSS
 * Stile allineato con gli altri widget del plugin
 */

/* Container principale */
.subscriber-management-widget-container {
    max-width: 100%;
    margin: 20px 0;
    padding: 0;
    background: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.08);
    display: flex;
    min-height: 500px;
    position: relative;
    overflow: hidden;
}

/* Menu sinistro - MIGLIORATO */
.subscriber-management-widget-container .subscriber-menu-column {
    flex: 0 0 280px; /* Aumentata larghezza da 250px a 280px */
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-right: 2px solid #dee2e6;
    padding: 0;
    position: relative;
    overflow: hidden;
}

.subscriber-management-widget-container .subscriber-menu-column::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(135deg, #0073aa 0%, #005177 100%);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.subscriber-management-widget-container .subscriber-menu {
    list-style: none;
    margin: 0;
    padding: 70px 0 20px 0; /* Spazio per l'header */
    position: relative;
    z-index: 2;
}

/* Header del menu */
.subscriber-management-widget-container .subscriber-menu-column::after {
    content: '📊 Dashboard';
    position: absolute;
    top: 15px;
    left: 20px;
    right: 20px;
    color: white;
    font-weight: 600;
    font-size: 16px;
    text-align: center;
    z-index: 3;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.subscriber-management-widget-container .menu-item {
    display: flex;
    align-items: center;
    padding: 18px 25px;
    cursor: pointer;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    color: #495057;
    font-weight: 500;
    position: relative;
    margin: 0 10px;
    border-radius: 8px;
    margin-bottom: 8px;
    background: transparent;
}

.subscriber-management-widget-container .menu-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: transparent;
    border-radius: 0 4px 4px 0;
    transition: all 0.3s ease;
}

.subscriber-management-widget-container .menu-item:hover {
    background: rgba(0, 115, 170, 0.1);
    color: #0073aa;
    transform: translateX(5px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.subscriber-management-widget-container .menu-item:hover::before {
    background: #0073aa;
    width: 6px;
}

.subscriber-management-widget-container .menu-item.active {
    background: linear-gradient(135deg, #0073aa 0%, #005177 100%);
    color: white;
    transform: translateX(8px);
    box-shadow: 0 4px 12px rgba(0, 115, 170, 0.3);
    font-weight: 600;
}

.subscriber-management-widget-container .menu-item.active::before {
    background: #28a745;
    width: 6px;
}

.subscriber-management-widget-container .menu-item i {
    margin-right: 15px;
    font-size: 18px;
    width: 24px;
    text-align: center;
    transition: transform 0.3s ease;
}

.subscriber-management-widget-container .menu-item:hover i,
.subscriber-management-widget-container .menu-item.active i {
    transform: scale(1.1);
}

.subscriber-management-widget-container .menu-item span {
    font-size: 15px;
    font-weight: inherit;
    letter-spacing: 0.3px;
}

/* Contenuto principale */
.subscriber-management-widget-container .subscriber-content-column {
    flex: 1;
    padding: 30px;
    background: #fff;
    position: relative;
    /* Sfondo con pattern SVG per il content column */
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%230073aa' fill-opacity='0.02' fill-rule='evenodd'/%3E%3C/svg%3E") !important;
    background-size: 120px !important;
    background-color: #fff !important;
}

.subscriber-management-widget-container .content-section {
    display: none;
}

.subscriber-management-widget-container .content-section.active {
    display: block;
}

/* Header delle sezioni */
.subscriber-management-widget-container .section-header {
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
    position: relative;
}

.subscriber-management-widget-container .section-header::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 80px;
    height: 2px;
    background: linear-gradient(90deg, #0073aa 0%, rgba(0,115,170,0.2) 100%);
}

.subscriber-management-widget-container .section-header h3 {
    margin: 0 0 10px 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
}

#consumption-section .section-header h3::before {
    content: '\f080';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    margin-right: 10px;
    color: #0073aa;
    background: rgba(0,115,170,0.1);
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.subscriber-management-widget-container .section-header p {
    margin: 0;
    color: #666;
    font-size: 1rem;
    line-height: 1.4;
    max-width: 80%;
}

/* Form styling */
.subscriber-management-widget-container .subscriber-form {
    max-width: 600px;
}

.subscriber-management-widget-container .form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.subscriber-management-widget-container .form-group {
    flex: 1;
}

.subscriber-management-widget-container .form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 0.95rem;
}

.subscriber-management-widget-container .form-group input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.95rem;
    color: #444;
    background-color: #fff;
    transition: border-color 0.3s, box-shadow 0.3s;
}

.subscriber-management-widget-container .form-group input:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 2px rgba(0, 115, 170, 0.2);
    outline: none;
}

.subscriber-management-widget-container .form-group input[readonly] {
    background-color: #f8f9fa;
    color: #6c757d;
}

.subscriber-management-widget-container .form-actions {
    margin-top: 30px;
}

.subscriber-management-widget-container .btn-primary {
    padding: 12px 24px;
    background-color: #0073aa;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.subscriber-management-widget-container .btn-primary:hover {
    background-color: #005177;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.subscriber-management-widget-container .btn-primary:disabled {
    background-color: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Stats grid - ISOLATO al subscriber management widget con specificity aumentata */
.subscriber-management-widget-container .stats-grid,
.subscriber-management-widget-container .subscriber-stats-wrapper,
.subscriber-management-widget-container #consumption-section .stats-grid,
.subscriber-management-widget-container #consumption-section .subscriber-stats-wrapper,
.subscriber-management-widget-container #consumption-section div.subscriber-stats-wrapper,
.subscriber-management-widget-container #consumption-section .subscriber-stats-wrapper.stats-grid,
#consumption-section .subscriber-stats-wrapper,
#consumption-section .stats-grid,
#subscriber-stats-grid.subscriber-stats-wrapper,
#subscriber-stats-grid.stats-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    gap: 20px !important;
    margin-bottom: 30px !important;
    /* Isola completamente dal document viewer flexbox layout */
    flex-direction: initial !important;
    flex-wrap: initial !important;
    flex: initial !important;
    flex-grow: initial !important;
    flex-shrink: initial !important;
    flex-basis: initial !important;
    /* Ulteriore isolamento da document-stats.css */
    align-items: stretch !important;
    justify-content: initial !important;
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
}

.subscriber-management-widget-container .stats-card,
.subscriber-management-widget-container #consumption-section .stats-card,
#consumption-section .stats-card,
#subscriber-stats-grid .stats-card {
    background: #fff !important;
    border: 1px solid #e1e1e1 !important;
    border-radius: 12px !important;
    padding: 22px !important;
    display: flex !important;
    align-items: center !important;
    gap: 18px !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.03) !important;
    transition: all 0.3s ease !important;
    /* Assicura che le cards mantengano il layout previsto */
    flex-direction: row !important;
    justify-content: flex-start !important;
    position: relative !important;
    overflow: hidden !important;
    /* Reset proprietà che potrebbero interferire */
    width: auto !important;
    min-width: initial !important;
    max-width: initial !important;
    flex: initial !important;
}

.subscriber-management-widget-container .stats-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.08);
    border-color: #d1d1d1;
}

.subscriber-management-widget-container .stats-card.credit-card {
    border-color: #28a745;
    background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
    position: relative;
}

.subscriber-management-widget-container .stats-card.credit-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%2328a745' fill-opacity='0.03' fill-rule='evenodd'/%3E%3C/svg%3E") repeat;
    border-radius: 12px;
    opacity: 0.5;
    z-index: 0;
}

.subscriber-management-widget-container .stats-icon {
    flex: 0 0 56px;
    height: 56px;
    background: linear-gradient(135deg, #0073aa 0%, #005a87 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 22px;
    box-shadow: 0 4px 10px rgba(0, 115, 170, 0.15);
    transition: all 0.3s ease;
}

.subscriber-management-widget-container .stats-card:hover .stats-icon {
    transform: scale(1.05);
    box-shadow: 0 6px 12px rgba(0, 115, 170, 0.2);
}

.subscriber-management-widget-container .credit-card .stats-icon {
    background: linear-gradient(135deg, #28a745 0%, #218838 100%);
    box-shadow: 0 4px 10px rgba(40, 167, 69, 0.15);
}

.subscriber-management-widget-container .credit-card:hover .stats-icon {
    box-shadow: 0 6px 12px rgba(40, 167, 69, 0.2);
}

.subscriber-management-widget-container .stats-content {
    flex: 1;
    position: relative;
    z-index: 1;
}

.subscriber-management-widget-container .stats-value {
    font-size: 2rem !important;
    font-weight: 700 !important;
    color: #333 !important;
    margin-bottom: 6px !important;
    /* Previene interferenze con document viewer stats */
    text-align: left !important;
    line-height: 1.2 !important;
    transition: all 0.3s ease;
    position: relative;
}

.subscriber-management-widget-container .stats-card:hover .stats-value {
    color: #0073aa !important;
}

.subscriber-management-widget-container .credit-card:hover .stats-value {
    color: #28a745 !important;
}

.subscriber-management-widget-container .stats-label {
    font-size: 0.8rem !important;
    color: #777 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.8px !important;
    /* Isola lo stile dal document viewer */
    text-align: left !important;
    margin-bottom: 0 !important;
    font-weight: 500 !important;
    position: relative;
}

/* Consumption details */
.subscriber-management-widget-container .consumption-details {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
    border-radius: 16px !important;
    padding: 28px !important;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.04), 0 4px 12px rgba(0, 0, 0, 0.03) !important;
    margin-top: 30px !important;
    position: relative !important;
    overflow: hidden !important;
}

.subscriber-management-widget-container .consumption-details::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    right: 0 !important;
    width: 250px !important;
    height: 250px !important;
    background: radial-gradient(circle, rgba(0,115,170,0.03) 0%, rgba(0,115,170,0) 70%) !important;
    z-index: 0 !important;
}

.subscriber-management-widget-container .consumption-details h4 {
    margin: 0 0 22px 0 !important;
    font-size: 1.2rem !important;
    color: #333 !important;
    font-weight: 600 !important;
    padding-bottom: 14px !important;
    border-bottom: 2px solid #f0f0f0 !important;
    position: relative !important;
    display: flex !important;
    align-items: center !important;
}

.subscriber-management-widget-container .consumption-details h4::before {
    content: '\f080' !important;
    font-family: 'Font Awesome 6 Free' !important;
    font-weight: 900 !important;
    margin-right: 10px !important;
    font-size: 16px !important;
    color: #0073aa !important;
    opacity: 0.8 !important;
}

.subscriber-management-widget-container .consumption-details h4::after {
    content: '' !important;
    position: absolute !important;
    bottom: -2px !important;
    left: 0 !important;
    width: 80px !important;
    height: 2px !important;
    background: linear-gradient(90deg, #0091d4 0%, rgba(0,115,170,0.2) 100%) !important;
}

.subscriber-management-widget-container .detail-row {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 14px 0 !important;
    border-bottom: 1px solid #f0f0f0 !important;
    transition: all 0.25s ease-out !important;
    position: relative !important;
    z-index: 1 !important;
}

.subscriber-management-widget-container .detail-row:hover {
    background-color: rgba(240, 244, 248, 0.7) !important;
    padding-left: 12px !important;
    padding-right: 12px !important;
    margin-left: -12px !important;
    margin-right: -12px !important;
    border-radius: 8px !important;
}

.subscriber-management-widget-container .detail-row:last-child {
    border-bottom: none !important;
}

.subscriber-management-widget-container .detail-label {
    font-weight: 500 !important;
    color: #555 !important;
    display: flex !important;
    align-items: center !important;
}

/* Indicatori colorati per le varie metriche */
.subscriber-management-widget-container .detail-row:nth-child(1) .detail-label::before {
    background-color: #3498db !important;
}

.subscriber-management-widget-container .detail-row:nth-child(2) .detail-label::before {
    background-color: #9b59b6 !important;
}

.subscriber-management-widget-container .detail-row:nth-child(3) .detail-label::before {
    background-color: #f1c40f !important;
}

.subscriber-management-widget-container .detail-row:nth-child(4) .detail-label::before {
    background-color: #e74c3c !important;
}

.subscriber-management-widget-container .detail-row:nth-child(5) .detail-label::before {
    background-color: #1abc9c !important;
}

.subscriber-management-widget-container .detail-row:nth-child(6) .detail-label::before {
    background-color: #f39c12 !important;
}

.subscriber-management-widget-container .detail-label::before {
    content: '' !important;
    display: inline-block !important;
    width: 8px !important;
    height: 8px !important;
    background-color: #0073aa !important;
    border-radius: 50% !important;
    margin-right: 10px !important;
    opacity: 1 !important;
    box-shadow: 0 0 0 2px rgba(0, 115, 170, 0.2) !important;
}

.subscriber-management-widget-container .detail-value {
    font-weight: 600 !important;
    color: #333 !important;
    background: rgba(0,0,0,0.03) !important;
    padding: 6px 12px !important;
    border-radius: 6px !important;
    box-shadow: inset 0 1px 2px rgba(0,0,0,0.03) !important;
    transition: all 0.25s ease !important;
}

.subscriber-management-widget-container .detail-row:hover .detail-value {
    background: rgba(0,115,170,0.07) !important;
}

/* Stile speciale per il tipo di sottoscrizione */
.subscriber-management-widget-container .subscription-type {
    background: linear-gradient(135deg, rgba(0,115,170,0.1) 0%, rgba(0,115,170,0.05) 100%) !important;
    color: #0073aa !important;
    font-weight: 700 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    font-size: 0.9em !important;
}

/* Credit recharge section */
.subscriber-management-widget-container .current-credit-display {
    background: linear-gradient(135deg, #e8f5e8 0%, #f8fff9 100%);
    border: 1px solid #28a745;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
    text-align: center;
}

.subscriber-management-widget-container .credit-amount {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.subscriber-management-widget-container .credit-label {
    font-size: 1.1rem;
    color: #555;
}

.subscriber-management-widget-container .credit-value {
    font-size: 2rem !important;
    font-weight: 700 !important;
    color: #28a745 !important;
    transition: all 0.3s ease !important;
}

/* Animazione specifica per il credit updating nel subscriber widget */
.subscriber-management-widget-container .credit-updating {
    animation: subscriber-credit-update 0.5s ease-in-out !important;
    transform: scale(1.05) !important;
}

@keyframes subscriber-credit-update {
    0% {
        transform: scale(1) !important;
        background-color: rgba(40, 167, 69, 0.1) !important;
    }
    50% {
        transform: scale(1.05) !important;
        background-color: rgba(40, 167, 69, 0.2) !important;
    }
    100% {
        transform: scale(1) !important;
        background-color: transparent !important;
    }
}

/* Recharge options */
.subscriber-management-widget-container .recharge-options {
    margin-bottom: 30px;
}

.subscriber-management-widget-container .recharge-options h4 {
    margin: 0 0 15px 0;
    font-size: 1.1rem;
    color: #333;
}

.subscriber-management-widget-container .amount-buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.subscriber-management-widget-container .amount-btn {
    padding: 10px 20px;
    border: 2px solid #0073aa;
    background: white;
    color: #0073aa;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.subscriber-management-widget-container .amount-btn:hover,
.subscriber-management-widget-container .amount-btn.selected {
    background: #0073aa;
    color: white;
}

.subscriber-management-widget-container .custom-amount {
    margin-top: 20px;
}

.subscriber-management-widget-container .custom-amount label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.subscriber-management-widget-container .amount-input-group {
    display: flex;
    align-items: center;
    max-width: 200px;
}

.subscriber-management-widget-container .currency-symbol {
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-right: none;
    padding: 10px 12px;
    border-radius: 4px 0 0 4px;
    font-weight: 600;
    color: #555;
}

.subscriber-management-widget-container .amount-input-group input {
    border-radius: 0 4px 4px 0;
    border-left: none;
}

/* Payment methods */
.subscriber-management-widget-container .payment-methods {
    margin-bottom: 30px;
}

.subscriber-management-widget-container .payment-methods h4 {
    margin: 0 0 15px 0;
    font-size: 1.1rem;
    color: #333;
}

.subscriber-management-widget-container .payment-options {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.subscriber-management-widget-container .payment-method {
    flex: 1;
    min-width: 150px;
    padding: 15px;
    border: 2px solid #e1e1e1;
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.subscriber-management-widget-container .payment-method:hover,
.subscriber-management-widget-container .payment-method.selected {
    border-color: #0073aa;
    background: #f0f8ff;
}

.subscriber-management-widget-container .payment-method i {
    font-size: 24px;
    margin-bottom: 8px;
    display: block;
    color: #0073aa;
}

.subscriber-management-widget-container .payment-method span {
    font-weight: 600;
    color: #333;
}

/* Feedback messages */
.subscriber-management-widget-container .feedback-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 4px;
    font-weight: 500;
    z-index: 9999;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.subscriber-management-widget-container .feedback-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.subscriber-management-widget-container .feedback-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Login required message */
.subscriber-management-widget-container .login-required-message {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.subscriber-management-widget-container .login-required-message h3 {
    margin-bottom: 15px;
    color: #333;
}

/* Credit updating animation */
.subscriber-management-widget-container .credit-updating {
    position: relative;
    transform: scale(1.05);
    transition: all 0.3s ease;
    background-color: #fff3cd;
    border-radius: 4px;
    padding: 2px 4px;
}

.subscriber-management-widget-container .credit-updating::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #28a745, #20c997, #28a745);
    border-radius: 4px;
    opacity: 0.7;
    z-index: -1;
    animation: credit-pulse 0.6s ease-in-out;
}

@keyframes credit-pulse {
    0%, 100% { opacity: 0.7; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.02); }
}

/* Button processing state */
.subscriber-management-widget-container .btn-primary.processing {
    position: relative;
    pointer-events: none;
}

.subscriber-management-widget-container .btn-primary.processing::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid rgba(255,255,255,0.3);
    border-top: 2px solid #fff;
    border-radius: 50%;
    animation: btn-spin 1s linear infinite;
}

@keyframes btn-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Fast confirmation modal for better performance */
.fast-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.fast-modal-content {
    background: white;
    border-radius: 8px;
    padding: 20px;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    transform: scale(0.9);
    transition: transform 0.2s ease;
}

.fast-modal-overlay:not([style*="display: none"]) .fast-modal-content {
    transform: scale(1);
}

.fast-modal-message {
    margin-bottom: 20px;
    font-size: 16px;
    line-height: 1.5;
    color: #333;
}

.fast-modal-buttons {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.fast-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    min-width: 80px;
}

.fast-btn-confirm {
    background: #007cba;
    color: white;
}

.fast-btn-confirm:hover {
    background: #005a87;
}

.fast-btn-cancel {
    background: #ddd;
    color: #333;
}

.fast-btn-cancel:hover {
    background: #ccc;
}

/* ===================================================================
 * ANIMAZIONI E MIGLIORAMENTI INTERATTIVI PER LA SEZIONE CONSUMI
 * ===================================================================
 */

/* Animazione di ingresso migliorata per le stats card */
@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    70% {
        opacity: 1;
        transform: translateY(-5px) scale(1.02);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.subscriber-management-widget-container #consumption-section .stats-card {
    animation: fadeInUp 0.7s cubic-bezier(0.18, 0.89, 0.32, 1.28) forwards !important;
    opacity: 0 !important;
    transform-origin: center bottom !important;
}

.subscriber-management-widget-container #consumption-section .stats-card:nth-child(1) {
    animation-delay: 0.1s !important;
}

.subscriber-management-widget-container #consumption-section .stats-card:nth-child(2) {
    animation-delay: 0.25s !important;
}

.subscriber-management-widget-container #consumption-section .stats-card:nth-child(3) {
    animation-delay: 0.4s !important;
}

.subscriber-management-widget-container #consumption-section .stats-card:nth-child(4) {
    animation-delay: 0.55s !important;
}

/* Animazioni per l'hover delle stats card */
.subscriber-management-widget-container .stats-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%);
    opacity: 0;
    transition: all 0.5s ease;
    z-index: 0;
    border-radius: 12px;
    pointer-events: none;
}

.subscriber-management-widget-container .stats-card:hover {
    transform: translateY(-8px) !important;
    box-shadow: 0 15px 25px rgba(0, 115, 170, 0.15), 0 6px 15px rgba(0, 0, 0, 0.07) !important;
}

.subscriber-management-widget-container .stats-card:hover::after {
    opacity: 0.5;
    transform: scale(1.5);
}

/* Indicatore pulsante per i dettagli */
.subscriber-management-widget-container .detail-row {
    position: relative;
}

.subscriber-management-widget-container .detail-row::after {
    content: '\f105';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    right: -5px;
    color: #bbb;
    opacity: 0;
    transition: all 0.3s ease;
    transform: translateX(-5px);
}

.subscriber-management-widget-container .detail-row:hover::after {
    opacity: 1;
    transform: translateX(0);
    color: #0073aa;
}

/* Miglioramento visivo della sezione attiva */
.subscriber-management-widget-container .menu-item.active[data-section="consumption"] {
    background: linear-gradient(90deg, #0073aa 0%, #0091d4 100%);
    box-shadow: 0 4px 10px rgba(0, 115, 170, 0.2);
}

/* Badge per l'intestazione */
.subscriber-management-widget-container .header-badge {
    display: inline-block !important;
    background: linear-gradient(135deg, #0091d4 0%, #0073aa 100%) !important;
    color: white !important;
    font-size: 0.6em !important;
    padding: 4px 8px !important;
    border-radius: 12px !important;
    margin-left: 10px !important;
    vertical-align: middle !important;
    font-weight: 500 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    box-shadow: 0 2px 5px rgba(0, 115, 170, 0.2) !important;
    position: relative !important;
    top: -2px !important;
}

/* ===================================================================
 * HEADER CREDITO SEMPRE VISIBILE - RIMOSSO
 * ===================================================================
 */
/*
.subscriber-management-widget-container .subscriber-credit-header {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f9ff 100%);
    border: 1px solid rgba(40, 167, 69, 0.2);
    border-radius: 12px;
    padding: 15px 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.subscriber-management-widget-container .header-credit-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
}

.subscriber-management-widget-container .header-credit-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    color: #2d5a27;
}

.subscriber-management-widget-container .header-credit-label i {
    color: #28a745;
    font-size: 1.2rem;
}

.subscriber-management-widget-container .header-credit-value {
    font-size: 1.8rem !important;
    font-weight: 700 !important;
    color: #28a745 !important;
    background: rgba(255, 255, 255, 0.9);
    padding: 8px 16px;
    border-radius: 8px;
    border: 1px solid rgba(40, 167, 69, 0.3);
    transition: all 0.3s ease;
    min-width: 120px;
    text-align: center;
}

.subscriber-management-widget-container .credit-refresh-btn {
    background: #28a745;
    color: white;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.subscriber-management-widget-container .credit-refresh-btn:hover {
    background: #218838;
    transform: rotate(90deg) scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.subscriber-management-widget-container .credit-refresh-btn:active {
    transform: rotate(180deg) scale(0.95);
}

.subscriber-management-widget-container .credit-refresh-btn.refreshing {
    animation: spin 1s linear infinite;
    background: #6c757d;
    cursor: not-allowed;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.subscriber-management-widget-container .header-credit-value.credit-updating {
    animation: header-credit-pulse 0.6s ease-in-out;
    background: rgba(40, 167, 69, 0.1);
    border-color: #28a745;
}

@keyframes header-credit-pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
}
*/

/* Responsive per header credito - RIMOSSO */
/*
@media (max-width: 768px) {
    .subscriber-management-widget-container .header-credit-display {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .subscriber-management-widget-container .header-credit-label {
        justify-content: center;
    }
    
    .subscriber-management-widget-container .header-credit-value {
        font-size: 1.5rem !important;
        min-width: auto;
    }
}
*/

/* ===================================================================
 * PREVENZIONE CONFLITTI CSS CON DOCUMENT VIEWER
 * ===================================================================
 * Questa sezione garantisce l'isolamento completo degli stili
 * del subscriber management widget dal document viewer widget
 */

/* Stile specifico per la credit card con pattern SVG */
.subscriber-management-widget-container .credit-card {
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%230073aa' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E") !important;
    background-size: 170px !important;
    border: 1px solid rgba(0, 115, 170, 0.2) !important;
}

.subscriber-management-widget-container .credit-card .stats-icon {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%) !important;
    box-shadow: 0 5px 15px rgba(46, 204, 113, 0.2) !important;
}

.subscriber-management-widget-container .credit-card .stats-value {
    color: #27ae60 !important;
    font-weight: 700 !important;
}

/* Previene che le regole del document viewer influenzino il subscriber widget */
/* PRIORITY FIX: Risolve la sovrascrittura dal file principale */
body .subscriber-management-widget-container .stats-grid,
html body .subscriber-management-widget-container .stats-grid,
.subscriber-management-widget-container .stats-grid,
.subscriber-management-widget-container .stats-grid[class] {
    /* Reset di qualsiasi stile flexbox che potrebbe essere applicato */
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    flex-direction: initial !important;
    flex-wrap: initial !important;
    align-items: initial !important;
    justify-content: initial !important;
}

/* Isola completamente le stats cards del subscriber widget */
.subscriber-management-widget-container .stats-card {
    /* Previene interferenze con gli stats-item del document viewer */
    flex: initial !important;
    width: auto !important;
    min-width: initial !important;
    max-width: initial !important;
    position: relative;
    border-radius: 16px !important;
    padding: 22px !important;
    background: linear-gradient(135deg, #ffffff 0%, #f9f9f9 100%) !important;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06), 0 4px 12px rgba(0, 0, 0, 0.04) !important;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
    overflow: visible !important;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
}

/* Miglioramento stile icone stats */
.subscriber-management-widget-container .stats-icon {
    width: 56px !important;
    height: 56px !important;
    background: linear-gradient(135deg, #0091d4 0%, #0073aa 100%) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin-bottom: 15px !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    box-shadow: 0 5px 15px rgba(0, 115, 170, 0.2) !important;
}

.subscriber-management-widget-container .stats-icon i {
    font-size: 26px !important;
    color: white !important;
    position: relative !important;
    z-index: 2 !important;
    text-shadow: 0 2px 3px rgba(0, 0, 0, 0.1) !important;
}

.subscriber-management-widget-container .stats-card:hover .stats-icon {
    transform: scale(1.1) rotate(5deg) !important;
}

/* Garantisce che le animazioni del credit sync non interferiscano */
.subscriber-management-widget-container .stats-value,
.subscriber-management-widget-container .credit-value {
    /* Reset di stili che potrebbero essere applicati dal document viewer */
    text-align: left !important;
    font-family: inherit !important;
    line-height: 1.2 !important;
    font-size: 24px !important;
    font-weight: 700 !important;
    margin-bottom: 5px !important;
    color: #333 !important;
    position: relative !important;
}

/* Prevenzione conflitti con tooltip del document viewer e miglioramento stile */
.subscriber-management-widget-container .stats-label {
    position: relative !important;
    overflow: visible !important;
    font-size: 0.8rem !important;
    color: #666 !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
    font-weight: 500 !important;
    margin-top: 6px !important;
    padding-top: 6px !important;
    border-top: 1px solid rgba(0, 0, 0, 0.05) !important;
}

/* Assicura che il layout responsive rimanga isolato */
@media (max-width: 768px) {
    .subscriber-management-widget-container .stats-grid {
        display: grid !important;
        grid-template-columns: 1fr !important;
    }
}

/* Stili specifici per i wrapper di isolamento - POTENZIATO con massima specificità */
html body .subscriber-management-widget-container .subscriber-stats-wrapper,
.subscriber-management-widget-container .subscriber-stats-wrapper,
.subscriber-management-widget-container .subscriber-stats-wrapper[class],
.subscriber-management-widget-container div.subscriber-stats-wrapper {
    /* Assicura che il wrapper mantenga lo stile grid indipendentemente da altre regole */
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    gap: 20px !important;
    margin-bottom: 30px !important;
    /* Isola completamente dal document viewer flexbox layout */
    flex-direction: initial !important;
    flex-wrap: initial !important;
    /* Ulteriore isolamento da document-stats.css */
    align-items: stretch !important;
    justify-content: initial !important;
    width: 100% !important;
    max-width: 100% !important;
}

.subscriber-management-widget-container .subscriber-recharge-wrapper {
    /* Stili specifici per il wrapper di ricarica */
    background: linear-gradient(135deg, #e8f5e8 0%, #f8fff9 100%) !important;
    border: 1px solid #28a745 !important;
}

/* ===================================================================
 * GRIGLIA DATI UTILIZZO - STILI UNICI SUBSCRIBER MANAGEMENT
 * Classi rinominate per evitare conflitti con stats-grid del document viewer
 * ===================================================================
 */

/* Container principale griglia dati utilizzo */
.subscriber-management-widget-container .consumption-data-grid,
.subscriber-management-widget-container .subscriber-usage-wrapper,
#subscriber-usage-data-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
    width: 100%;
    box-sizing: border-box;
}

/* Cards singole per dati utilizzo */
.subscriber-management-widget-container .usage-data-card {
    background: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    position: relative;
    overflow: hidden;
}

.subscriber-management-widget-container .usage-data-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-color: #0073aa;
}

/* Icone per le cards dati utilizzo */
.subscriber-management-widget-container .usage-data-icon {
    flex-shrink: 0;
    margin-right: 15px;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #0073aa, #005177);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

/* Contenuto delle cards dati utilizzo */
.subscriber-management-widget-container .usage-data-content {
    flex-grow: 1;
}

.subscriber-management-widget-container .usage-data-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: #333;
    line-height: 1.2;
    margin-bottom: 5px;
}

.subscriber-management-widget-container .usage-data-label {
    font-size: 0.8rem;
    color: #666;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Card speciale per crediti - POTENZIATA */
.subscriber-management-widget-container .usage-data-card.credit-card {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
    color: white !important;
    border: 2px solid #28a745 !important;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3) !important;
    position: relative !important;
    overflow: visible !important;
}

/* Animazione per aggiornamento credito nella sezione consumi */
.subscriber-management-widget-container .usage-data-card.credit-card.credit-updating {
    animation: consumptionCreditPulse 0.8s ease-in-out;
    box-shadow: 0 0 20px rgba(40, 167, 69, 0.6) !important;
}

@keyframes consumptionCreditPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 8px 24px rgba(40, 167, 69, 0.6);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    }
}

/* Stile per il pulsante di aggiornamento consumi */
.subscriber-management-widget-container .consumption-refresh-btn {
    background: #0073aa !important;
    color: white !important;
    border: none !important;
    border-radius: 6px !important;
    padding: 8px 16px !important;
    font-size: 12px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

.subscriber-management-widget-container .consumption-refresh-btn:hover {
    background: #005a87 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
}

.subscriber-management-widget-container .consumption-refresh-btn:disabled {
    background: #6c757d !important;
    cursor: not-allowed !important;
    transform: none !important;
}

/* Inizializzazione sezione consumi */
.subscriber-management-widget-container #consumption-section.consumption-initialized {
    opacity: 1 !important;
    visibility: visible !important;
}

.subscriber-management-widget-container #consumption-section.consumption-initialized .usage-data-card {
    opacity: 1 !important;
    transform: translateY(0) !important;
    transition: all 0.3s ease !important;
}

/* Debug per card vuote */
.subscriber-management-widget-container .usage-data-card.empty-card {
    border: 2px dashed #ff6b6b !important;
    background: rgba(255, 107, 107, 0.1) !important;
}

.subscriber-management-widget-container .usage-data-card.empty-card::after {
    content: '⚠️ Card Vuota - Controllare i Dati';
    display: block;
    padding: 20px;
    text-align: center;
    color: #ff6b6b;
    font-weight: bold;
    font-size: 14px;
}

/* Assicurati che tutte le card abbiano la stessa altezza */
.subscriber-management-widget-container .usage-data-card {
    min-height: 120px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important;
}

/* Fallback per grid layout */
.subscriber-management-widget-container .consumption-data-grid.force-grid-layout,
.subscriber-management-widget-container .subscriber-usage-wrapper.force-grid-layout,
.subscriber-management-widget-container #subscriber-usage-data-grid.force-grid-layout {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
    gap: 20px !important;
}

/* Responsive migliorato per la sezione consumi */
@media (max-width: 768px) {
    .subscriber-management-widget-container .consumption-data-grid,
    .subscriber-management-widget-container .subscriber-usage-wrapper,
    .subscriber-management-widget-container #subscriber-usage-data-grid {
        grid-template-columns: 1fr !important;
        gap: 15px !important;
    }
    
    .subscriber-management-widget-container .consumption-refresh-btn {
        position: static !important;
        margin-top: 10px !important;
        width: 100% !important;
    }
}

/* Miglioramenti responsivi per il menu di navigazione */
@media (max-width: 768px) {
    .subscriber-management-widget-container {
        flex-direction: column;
    }
    
    .subscriber-management-widget-container .subscriber-menu-column {
        flex: none;
        width: 100%;
        min-height: auto;
        border-right: none;
        border-bottom: 2px solid #dee2e6;
    }
    
    .subscriber-management-widget-container .subscriber-menu-column::before {
        height: 50px;
    }
    
    .subscriber-management-widget-container .subscriber-menu-column::after {
        content: '📊 Menu Navigazione';
        top: 12px;
        font-size: 14px;
    }
    
    .subscriber-management-widget-container .subscriber-menu {
        padding: 60px 0 10px 0;
        display: flex;
        overflow-x: auto;
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
    }
    
    .subscriber-management-widget-container .menu-item {
        flex: 0 0 auto;
        margin: 0 5px;
        padding: 12px 20px;
        white-space: nowrap;
        border-radius: 20px;
        text-align: center;
        min-width: 140px;
    }
    
    .subscriber-management-widget-container .menu-item span {
        display: block;
        font-size: 12px;
        margin-top: 4px;
    }
    
    .subscriber-management-widget-container .menu-item i {
        margin-right: 0;
        margin-bottom: 4px;
        display: block;
        font-size: 16px;
    }
    
    .subscriber-management-widget-container .menu-item:hover,
    .subscriber-management-widget-container .menu-item.active {
        transform: none;
    }
}

@media (max-width: 480px) {
    .subscriber-management-widget-container .menu-item {
        min-width: 120px;
        padding: 10px 15px;
    }
    
    .subscriber-management-widget-container .menu-item span {
        font-size: 11px;
    }
    
    .subscriber-management-widget-container .menu-item i {
        font-size: 14px;
    }
}

/* Animazioni per il cambio di sezione */
.subscriber-management-widget-container .content-section {
    opacity: 0;
    transform: translateX(20px);
    transition: all 0.4s ease;
    pointer-events: none;
}

.subscriber-management-widget-container .content-section.active {
    opacity: 1;
    transform: translateX(0);
    pointer-events: auto;
}

/* Indicatore di caricamento per le transizioni */
.subscriber-management-widget-container .content-loading {
    position: relative;
}

.subscriber-management-widget-container .content-loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    z-index: 100;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

.subscriber-management-widget-container .content-loading.loading::before {
    opacity: 1;
}

/* Miglioramento visivo breadcrumb navigation */
.subscriber-management-widget-container .section-breadcrumb {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    font-size: 14px;
    color: #6c757d;
}

.subscriber-management-widget-container .section-breadcrumb::before {
    content: '🏠';
    margin-right: 8px;
}

.subscriber-management-widget-container .section-breadcrumb .separator {
    margin: 0 8px;
    color: #adb5bd;
}

.subscriber-management-widget-container .section-breadcrumb .current {
    color: #0073aa;
    font-weight: 500;
}

/* Tooltip personalizzato per mobile */
.custom-tooltip {
    position: fixed;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    z-index: 9999;
    pointer-events: none;
    backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.custom-tooltip::before {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 6px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
}

/* Miglioramenti per l'accessibility */
.subscriber-management-widget-container .menu-item:focus {
    outline: 3px solid #0073aa;
    outline-offset: 2px;
    border-radius: 8px;
}

.subscriber-management-widget-container .menu-item[aria-selected="true"] {
    background: linear-gradient(135deg, #0073aa 0%, #005177 100%);
    color: white;
}

/* Skip link per accessibility */
.skip-to-content {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #0073aa;
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 10000;
    transition: top 0.3s;
}

.skip-to-content:focus {
    top: 6px;
}

/* Indicatore di caricamento migliorato */
.subscriber-management-widget-container .loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    z-index: 200;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #666;
    font-size: 14px;
    font-weight: 500;
}

/* Miglioramenti per High Contrast Mode */
@media (prefers-contrast: high) {
    .subscriber-management-widget-container .menu-item {
        border: 2px solid;
    }
    
    .subscriber-management-widget-container .menu-item.active {
        border-color: currentColor;
        background: HighlightText;
        color: Highlight;
    }
}

/* Miglioramenti per Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .subscriber-management-widget-container .menu-item,
    .subscriber-management-widget-container .content-section,
    .subscriber-management-widget-container .stats-card {
        transition: none !important;
        animation: none !important;
        transform: none !important;
    }
    
    .loading-spinner {
        animation: none !important;
    }
}
