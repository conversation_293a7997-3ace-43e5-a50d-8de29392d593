<?php
/**
 * Test completo del sistema database Widget Help
 * 
 * Questo script verifica tutti gli aspetti del database:
 * - Salvataggio e recupero contenuti
 * - Consistenza delle chiavi di opzione  
 * - Migrazione dati legacy
 * - Integrità della struttura dati
 */

if (!defined('ABSPATH')) {
    exit;
}

// Include delle classi necessarie
require_once('includes/class-widget-help-system.php');
require_once('includes/class-widget-help-admin.php'); 
require_once('includes/class-widget-help-migration.php');

echo "<div style='font-family: Arial, sans-serif; margin: 20px;'>";
echo "<h1>🔍 Test Completo Database Widget Help System</h1>";

/**
 * Test dello stato iniziale del database
 */
function test_initial_database_state() {
    echo "<h2>📊 Stato Iniziale Database</h2>";
    
    $content_option = get_option('widget_help_content', array());
    $settings_option = get_option('widget_help_widget_settings', array());
    $legacy_option = get_option('widget_help_contents', array());
    
    echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>📄 Opzioni WordPress correnti:</h3>";
    echo "<ul>";
    echo "<li><strong>widget_help_content:</strong> " . count($content_option) . " widget</li>";
    echo "<li><strong>widget_help_widget_settings:</strong> " . count($settings_option) . " impostazioni</li>";
    echo "<li><strong>widget_help_contents (legacy):</strong> " . count($legacy_option) . " widget legacy</li>";
    echo "</ul>";
    echo "</div>";
    
    // Dettagli dei contenuti
    if (!empty($content_option)) {
        echo "<h4>Contenuti attuali:</h4>";
        echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 3px;'>";
        foreach ($content_option as $widget_id => $sections) {
            echo "- $widget_id:\n";
            if (is_array($sections)) {
                foreach ($sections as $section => $content) {
                    $preview = substr(strip_tags($content), 0, 80) . "...";
                    echo "  - $section: $preview\n";
                }
            } else {
                echo "  - (formato non valido)\n";
            }
        }
        echo "</pre>";
    }
    
    return array($content_option, $settings_option, $legacy_option);
}

/**
 * Test della migrazione delle chiavi
 */
function test_option_key_migration() {
    echo "<h2>🔄 Test Migrazione Chiavi Opzione</h2>";
    
    // Verifica consistenza
    $consistency = Widget_Help_Migration::verify_option_consistency();
    
    echo "<div style='background: " . ($consistency['consistent'] ? '#e8f5e8' : '#fff2e8') . "; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>" . ($consistency['consistent'] ? '✅' : '⚠️') . " Stato Consistenza</h3>";
    
    if ($consistency['consistent']) {
        echo "<p>✅ Le opzioni del database sono consistenti</p>";
    } else {
        echo "<p>⚠️ Rilevate le seguenti inconsistenze:</p>";
        echo "<ul>";
        foreach ($consistency['issues'] as $issue) {
            echo "<li>$issue</li>";
        }
        echo "</ul>";
    }
    
    echo "<h4>📋 Riepilogo widget:</h4>";
    echo "<ul>";
    echo "<li>Widget con contenuti: " . count($consistency['content_widgets']) . "</li>";
    echo "<li>Widget con impostazioni: " . count($consistency['settings_widgets']) . "</li>";
    echo "<li>Widget legacy: " . count($consistency['legacy_widgets']) . "</li>";
    echo "</ul>";
    echo "</div>";
    
    // Esegui migrazione se necessario
    if (!$consistency['consistent'] && !empty($consistency['legacy_widgets'])) {
        echo "<h4>🔧 Esecuzione migrazione automatica...</h4>";
        $migrated = Widget_Help_Migration::migrate_option_keys();
        echo "<p>✅ Migrati " . count($migrated) . " widget dalla chiave legacy</p>";
    }
    
    return $consistency;
}

/**
 * Test di salvataggio contenuti
 */
function test_content_saving() {
    echo "<h2>💾 Test Salvataggio Contenuti</h2>";
    
    global $widget_help_system;
    if (!$widget_help_system) {
        $widget_help_system = new Widget_Help_System();
    }
    
    $test_data = array(
        'report_viewer' => array(
            'main' => '<h3>Report Viewer Test</h3><p>Contenuto principale di test aggiornato il ' . date('Y-m-d H:i:s') . '</p>',
            'advanced' => '<h4>Funzioni Avanzate</h4><p>Test delle funzioni avanzate per il report viewer.</p>'
        ),
        'document_analyzer' => array(
            'main' => '<h3>Document Analyzer Test</h3><p>Guida principale per l\'analizzatore documenti.</p>',
            'setup' => '<h4>Configurazione</h4><p>Impostazioni di configurazione per l\'analizzatore.</p>'
        )
    );
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>📝 Salvataggio test data...</h3>";
    
    $saved_count = 0;
    foreach ($test_data as $widget_id => $sections) {
        foreach ($sections as $section => $content) {
            // Usa il metodo del Widget Help System
            $widget_help_system->set_help_content($widget_id, $content, $section);
            
            // Salva nel database
            $saved_content = get_option('widget_help_content', array());
            if (!isset($saved_content[$widget_id])) {
                $saved_content[$widget_id] = array();
            }
            $saved_content[$widget_id][$section] = $content;
            update_option('widget_help_content', $saved_content);
            
            echo "<p>✅ Salvato: $widget_id/$section</p>";
            $saved_count++;
        }
    }
    
    echo "<p><strong>Total: $saved_count sezioni salvate</strong></p>";
    echo "</div>";
    
    return $test_data;
}

/**
 * Test di recupero contenuti
 */
function test_content_retrieval() {
    echo "<h2>📖 Test Recupero Contenuti</h2>";
    
    global $widget_help_system;
    if (!$widget_help_system) {
        $widget_help_system = new Widget_Help_System();
    }
    
    // Test recupero tramite Widget Help System
    echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🔍 Test recupero tramite Widget Help System:</h3>";
    
    $test_cases = array(
        array('widget_id' => 'report_viewer', 'section' => 'main'),
        array('widget_id' => 'report_viewer', 'section' => 'advanced'),
        array('widget_id' => 'document_analyzer', 'section' => 'main'),
        array('widget_id' => 'document_analyzer', 'section' => 'setup'),
        array('widget_id' => 'nonexistent', 'section' => 'main') // Test fallback
    );
    
    foreach ($test_cases as $test) {
        $widget_id = $test['widget_id'];
        $section = $test['section'];
        
        $content = $widget_help_system->get_help_content($widget_id, $section);
        
        if (!empty($content)) {
            $preview = substr(strip_tags($content), 0, 60) . "...";
            echo "<p>✅ $widget_id/$section: $preview</p>";
        } else {
            echo "<p>❌ $widget_id/$section: Contenuto non trovato</p>";
        }
    }
    echo "</div>";
    
    // Test recupero diretto dal database
    echo "<div style='background: #fff8f0; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🗄️ Test recupero diretto dal database:</h3>";
    
    $saved_content = get_option('widget_help_content', array());
    echo "<p>Widget nel database: " . count($saved_content) . "</p>";
    
    foreach ($saved_content as $widget_id => $sections) {
        echo "<p><strong>$widget_id:</strong></p>";
        echo "<ul>";
        foreach ($sections as $section => $content) {
            $word_count = str_word_count(strip_tags($content));
            echo "<li>$section: $word_count parole</li>";
        }
        echo "</ul>";
    }
    echo "</div>";
}

/**
 * Test della struttura dati e integrità
 */
function test_data_integrity() {
    echo "<h2>🔒 Test Integrità Dati</h2>";
    
    $content_data = get_option('widget_help_content', array());
    $settings_data = get_option('widget_help_widget_settings', array());
    
    echo "<div style='background: #f0fff0; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🔍 Verifica struttura dati:</h3>";
    
    $integrity_issues = array();
    
    // Verifica struttura contenuti
    foreach ($content_data as $widget_id => $sections) {
        if (!is_array($sections)) {
            $integrity_issues[] = "Widget $widget_id: struttura sezioni non valida";
            continue;
        }
        
        foreach ($sections as $section => $content) {
            if (!is_string($content)) {
                $integrity_issues[] = "Widget $widget_id/$section: contenuto non stringa";
            }
            
            if (empty(trim(strip_tags($content)))) {
                $integrity_issues[] = "Widget $widget_id/$section: contenuto vuoto";
            }
        }
    }
    
    // Verifica struttura settings
    foreach ($settings_data as $widget_id => $settings) {
        if (!is_array($settings)) {
            $integrity_issues[] = "Widget $widget_id: impostazioni non valide";
        }
    }
    
    if (empty($integrity_issues)) {
        echo "<p>✅ Struttura dati integra - nessun problema rilevato</p>";
    } else {
        echo "<p>⚠️ Rilevati " . count($integrity_issues) . " problemi di integrità:</p>";
        echo "<ul>";
        foreach ($integrity_issues as $issue) {
            echo "<li>$issue</li>";
        }
        echo "</ul>";
    }
    
    // Statistiche
    $total_sections = 0;
    $total_content_size = 0;
    foreach ($content_data as $sections) {
        $total_sections += count($sections);
        foreach ($sections as $content) {
            $total_content_size += strlen($content);
        }
    }
    
    echo "<h4>📊 Statistiche:</h4>";
    echo "<ul>";
    echo "<li>Widget totali: " . count($content_data) . "</li>";
    echo "<li>Sezioni totali: $total_sections</li>";
    echo "<li>Dimensione contenuti: " . number_format($total_content_size / 1024, 2) . " KB</li>";
    echo "<li>Widget con impostazioni: " . count($settings_data) . "</li>";
    echo "</ul>";
    echo "</div>";
    
    return $integrity_issues;
}

/**
 * Test di performance del database
 */
function test_database_performance() {
    echo "<h2>⚡ Test Performance Database</h2>";
    
    echo "<div style='background: #fffef0; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>⏱️ Benchmark operazioni database:</h3>";
    
    // Test velocità lettura
    $start_time = microtime(true);
    for ($i = 0; $i < 100; $i++) {
        $content = get_option('widget_help_content', array());
    }
    $read_time = (microtime(true) - $start_time) * 1000;
    
    // Test velocità scrittura
    $start_time = microtime(true);
    $test_data = array('test_widget' => array('test_section' => 'test content ' . time()));
    for ($i = 0; $i < 10; $i++) {
        update_option('widget_help_content_test', $test_data);
    }
    $write_time = (microtime(true) - $start_time) * 1000;
    
    // Pulizia
    delete_option('widget_help_content_test');
    
    echo "<ul>";
    echo "<li>100 letture: " . number_format($read_time, 2) . " ms</li>";
    echo "<li>10 scritture: " . number_format($write_time, 2) . " ms</li>";
    echo "<li>Media lettura: " . number_format($read_time / 100, 2) . " ms</li>";
    echo "<li>Media scrittura: " . number_format($write_time / 10, 2) . " ms</li>";
    echo "</ul>";
    
    // Valutazione performance
    if ($read_time / 100 < 1.0 && $write_time / 10 < 10.0) {
        echo "<p>✅ Performance database ottimali</p>";
    } elseif ($read_time / 100 < 5.0 && $write_time / 10 < 50.0) {
        echo "<p>⚠️ Performance database accettabili</p>";
    } else {
        echo "<p>❌ Performance database subottimali</p>";
    }
    echo "</div>";
}

// Esecuzione di tutti i test
echo "<div style='border: 2px solid #007cba; border-radius: 10px; padding: 20px; margin: 20px 0;'>";
echo "<h2>🚀 Avvio Test Completo</h2>";
echo "<p>Inizializzazione del sistema di test per Widget Help Database...</p>";

$initial_state = test_initial_database_state();
$migration_result = test_option_key_migration();
$save_result = test_content_saving();
test_content_retrieval();
$integrity_issues = test_data_integrity();
test_database_performance();

// Riepilogo finale
echo "<div style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>📋 Riepilogo Test Database Widget Help</h2>";
echo "<ul>";
echo "<li>✅ Test stato iniziale: Completato</li>";
echo "<li>" . ($migration_result['consistent'] ? '✅' : '⚠️') . " Test migrazione: " . ($migration_result['consistent'] ? 'Consistente' : 'Richiede attenzione') . "</li>";
echo "<li>✅ Test salvataggio: Completato</li>";
echo "<li>✅ Test recupero: Completato</li>";
echo "<li>" . (empty($integrity_issues) ? '✅' : '⚠️') . " Test integrità: " . (empty($integrity_issues) ? 'Integro' : count($integrity_issues) . ' problemi') . "</li>";
echo "<li>✅ Test performance: Completato</li>";
echo "</ul>";

if ($migration_result['consistent'] && empty($integrity_issues)) {
    echo "<p><strong>🎉 SUCCESSO: Il database del Widget Help System è completamente funzionale!</strong></p>";
} else {
    echo "<p><strong>⚠️ ATTENZIONE: Rilevati alcuni problemi che richiedono correzione.</strong></p>";
}
echo "</div>";

echo "</div>";
echo "</div>";
?>
