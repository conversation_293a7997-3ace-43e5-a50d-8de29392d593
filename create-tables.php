<?php
/**
 * Table Creation Script
 * 
 * This script explicitly creates the required database tables
 * for the Financial Advisor plugin.
 */

// Initialize for standalone operation
// Define required globals for database operations
global $wpdb;

// Mock WordPress database class for standalone operation
if (!class_exists('WPDB')) {
    class WPDB {
        public $prefix = 'wp_';
        
        public function get_charset_collate() {
            return "DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
        }
        
        public function get_var($query) {
            return null; // Mock method - would actually check if table exists
        }
    }
    
    $wpdb = new WPDB();
}

// Load database setup functions if available
if (file_exists('includes/database-setup.php')) {
    require_once('includes/database-setup.php');
}

echo "<h1>Creating Database Tables</h1>";

// Create the subscription stats table
wpcd_create_user_subscription_stats_table();
echo "<p>Table <code>wpcd_user_subscription_stats</code> creation attempted.</p>";

// Create payment gateway tables
create_paypal_config_table();
echo "<p>Table <code>" . $wpdb->prefix . "paypal_config</code> creation attempted.</p>";

create_stripe_config_table();
echo "<p>Table <code>" . $wpdb->prefix . "stripe_config</code> creation attempted.</p>";

create_payment_gateway_logs_table();
echo "<p>Table <code>" . $wpdb->prefix . "payment_gateway_logs</code> creation attempted.</p>";

// Verify table exists
global $wpdb;
$table_name = $wpdb->prefix . 'user_subscription_stats';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;

if ($table_exists) {
    echo "<p style='color: green;'>✓ Table <strong>{$table_name}</strong> exists.</p>";
    
    // Show table structure
    $table_structure = $wpdb->get_results("DESCRIBE {$table_name}");
    echo "<h2>Table Structure:</h2>";
    echo "<pre>";
    print_r($table_structure);
    echo "</pre>";
} else {
    echo "<p style='color: red;'>✗ Table <strong>{$table_name}</strong> does NOT exist!</p>";
    
    // Show error info
    echo "<h2>Error Information:</h2>";
    echo "<pre>";
    print_r($wpdb->last_error);
    echo "</pre>";
}

echo "<p>Script completed.</p>";


/**
 * Create PayPal configuration table
 */
function create_paypal_config_table() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'paypal_config';
    
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE IF NOT EXISTS $table_name (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        client_id varchar(255) NOT NULL,
        client_secret varchar(255) NOT NULL,
        environment varchar(50) NOT NULL DEFAULT 'sandbox',
        webhook_id varchar(255),
        is_active tinyint(1) NOT NULL DEFAULT 0,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY  (id),
        KEY environment (environment),
        KEY is_active (is_active)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
    
    // Verify table exists
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
    
    if ($table_exists) {
        echo "<p style='color: green;'>✓ Table <strong>{$table_name}</strong> exists.</p>";
    } else {
        echo "<p style='color: red;'>✗ Failed to create table <strong>{$table_name}</strong>.</p>";
    }
}

/**
 * Create Stripe configuration table
 */
function create_stripe_config_table() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'stripe_config';
    
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE IF NOT EXISTS $table_name (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        public_key varchar(255) NOT NULL,
        secret_key varchar(255) NOT NULL,
        environment varchar(50) NOT NULL DEFAULT 'test',
        webhook_endpoint_secret varchar(255),
        is_active tinyint(1) NOT NULL DEFAULT 0,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY  (id),
        KEY environment (environment),
        KEY is_active (is_active)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
    
    // Verify table exists
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
    
    if ($table_exists) {
        echo "<p style='color: green;'>✓ Table <strong>{$table_name}</strong> exists.</p>";
    } else {
        echo "<p style='color: red;'>✗ Failed to create table <strong>{$table_name}</strong>.</p>";
    }
}

/**
 * Create payment gateway logs table
 */
function create_payment_gateway_logs_table() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'payment_gateway_logs';
    
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE IF NOT EXISTS $table_name (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        gateway varchar(20) NOT NULL,
        error_type varchar(50) NOT NULL,
        error_message text NOT NULL,
        error_data longtext,
        log_level varchar(20) NOT NULL DEFAULT 'error',
        user_id bigint(20),
        ip_address varchar(45),
        user_agent text,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY  (id),
        KEY gateway (gateway),
        KEY error_type (error_type),
        KEY log_level (log_level),
        KEY created_at (created_at)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
    
    // Verify table exists
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
    
    if ($table_exists) {
        echo "<p style='color: green;'>✓ Table <strong>{$table_name}</strong> exists.</p>";
    } else {
        echo "<p style='color: red;'>✗ Failed to create table <strong>{$table_name}</strong>.</p>";
    }
}
