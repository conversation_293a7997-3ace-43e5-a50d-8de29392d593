<?php
/**
 * Widget Help Migration Script
 * 
 * Migra dal sistema Help Line globale al nuovo sistema Widget Help modulare.
 * Mantiene la compatibilità e permette rollback.
 * 
 * @package Financial_Advisor
 * @version 1.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class Widget_Help_Migration {
    
    /**
     * Versione del sistema di migrazione
     */
    const MIGRATION_VERSION = '1.0.0';
    
    /**
     * Opzione per tracking della migrazione
     */
    const MIGRATION_OPTION = 'widget_help_migration_status';
    
    /**
     * Backup dell'help line originale
     */
    const BACKUP_OPTION = 'widget_help_backup_original';
    
    /**
     * Esegue la migrazione completa
     */
    public static function migrate() {
        $migration_status = get_option(self::MIGRATION_OPTION, array());
        
        // Verifica se la migrazione è già stata eseguita
        if (!empty($migration_status['completed'])) {
            return array(
                'success' => false,
                'message' => __('Migrazione già completata', 'document-viewer-plugin'),
                'data' => $migration_status
            );
        }
        
        try {
            // Fase 1: Backup del sistema esistente
            self::backup_existing_system();
            
            // Fase 2: Migrazione contenuti
            $migrated_data = self::migrate_help_content();
            
            // Fase 3: Configurazione nuovo sistema
            self::configure_new_system($migrated_data);
            
            // Fase 4: Verifica migrazione
            $verification = self::verify_migration();
            
            if ($verification['success']) {
                // Salva stato migrazione
                update_option(self::MIGRATION_OPTION, array(
                    'completed' => true,
                    'version' => self::MIGRATION_VERSION,
                    'date' => current_time('mysql'),
                    'migrated_widgets' => array_keys($migrated_data),
                    'backup_created' => true
                ));
                
                return array(
                    'success' => true,
                    'message' => __('Migrazione completata con successo', 'document-viewer-plugin'),
                    'data' => array(
                        'migrated_widgets' => count($migrated_data),
                        'verification' => $verification
                    )
                );
            } else {
                throw new Exception($verification['message']);
            }
            
        } catch (Exception $e) {
            // Rollback in caso di errore
            self::rollback_migration();
            
            return array(
                'success' => false,
                'message' => sprintf(__('Errore durante la migrazione: %s', 'document-viewer-plugin'), $e->getMessage()),
                'data' => null
            );
        }
    }
    
    /**
     * Backup del sistema esistente
     */
    private static function backup_existing_system() {
        $backup_data = array(
            'help_line_content' => get_option('help_line_content'),
            'help_line_enabled' => get_option('help_line_enabled', true),
            'backup_date' => current_time('mysql'),
            'backup_version' => self::MIGRATION_VERSION
        );
        
        update_option(self::BACKUP_OPTION, $backup_data);
    }
    
    /**
     * Migra il contenuto help esistente
     */
    private static function migrate_help_content() {
        $original_content = get_option('help_line_content');
        
        if (empty($original_content)) {
            // Se non c'è contenuto, usa il default della classe Help_Line
            if (class_exists('Help_Line')) {
                $help_line = new Help_Line();
                $original_content = $help_line->get_default_content();
            } else {
                $original_content = self::get_fallback_default_content();
            }
        }
        
        // Crea versioni personalizzate per ogni widget
        $migrated_data = array();
        
        // Document Viewer - personalizza per l'analisi documenti
        $migrated_data['document_viewer'] = array(
            'content' => self::adapt_content_for_widget($original_content, 'document_viewer'),
            'enabled' => true,
            'position' => 'right',
            'migrated_from' => 'help_line'
        );
        
        // Subscriber Management - personalizza per gestione abbonamenti
        $migrated_data['subscriber_management'] = array(
            'content' => self::adapt_content_for_widget($original_content, 'subscriber_management'),
            'enabled' => true,
            'position' => 'right',
            'migrated_from' => 'help_line'
        );
        
        // Chat Model - personalizza per chat AI
        $migrated_data['chat_model'] = array(
            'content' => self::adapt_content_for_widget($original_content, 'chat_model'),
            'enabled' => true,
            'position' => 'bottom',
            'migrated_from' => 'help_line'
        );
        
        // User Subscription - personalizza per sottoscrizioni
        $migrated_data['user_subscription'] = array(
            'content' => self::adapt_content_for_widget($original_content, 'user_subscription'),
            'enabled' => true,
            'position' => 'right',
            'migrated_from' => 'help_line'
        );
        
        // Login - personalizza per login
        $migrated_data['login'] = array(
            'content' => self::adapt_content_for_widget($original_content, 'login'),
            'enabled' => true,
            'position' => 'center',
            'migrated_from' => 'help_line'
        );
        
        return $migrated_data;
    }
    
    /**
     * Adatta il contenuto per un widget specifico
     */
    private static function adapt_content_for_widget($original_content, $widget_type) {
        // Aggiungi header specifico per widget
        $widget_headers = array(
            'document_viewer' => '<h4>Document Viewer - Guida Migrata</h4>',
            'subscriber_management' => '<h4>Gestione Abbonamenti - Guida Migrata</h4>',
            'chat_model' => '<h4>Chat AI - Guida Migrata</h4>',
            'user_subscription' => '<h4>Sottoscrizioni - Guida Migrata</h4>',
            'login' => '<h4>Login - Guida Migrata</h4>'
        );
        
        $adapted_content = $widget_headers[$widget_type] ?? '<h4>Guida Migrata</h4>';
        
        // Aggiungi nota di migrazione
        $adapted_content .= '<div class="widget-help-highlight info">';
        $adapted_content .= '<p><strong>Nota:</strong> Questo contenuto è stato migrato dal sistema Help Line globale. ';
        $adapted_content .= 'Puoi personalizzarlo specificamente per questo widget dalle impostazioni.</p>';
        $adapted_content .= '</div>';
        
        // Aggiungi contenuto originale
        $adapted_content .= $original_content;
        
        // Aggiungi sezione widget-specifica
        $widget_specific = self::get_widget_specific_content($widget_type);
        if (!empty($widget_specific)) {
            $adapted_content .= '<hr><h4>Informazioni Specifiche</h4>';
            $adapted_content .= $widget_specific;
        }
        
        return $adapted_content;
    }
    
    /**
     * Ottiene contenuto specifico per widget
     */
    private static function get_widget_specific_content($widget_type) {
        $specific_content = array(
            'document_viewer' => '<ul>
                <li>Carica documenti PDF o Word</li>
                <li>Inserisci domande specifiche per l\'analisi</li>
                <li>Usa lo zoom per vedere i dettagli</li>
                <li>Esporta i risultati in PDF</li>
            </ul>',
            
            'subscriber_management' => '<ul>
                <li>Gestisci il tuo piano di abbonamento</li>
                <li>Visualizza la cronologia dei pagamenti</li>
                <li>Effettua upgrade/downgrade del piano</li>
                <li>Gestisci i metodi di pagamento</li>
            </ul>',
            
            'chat_model' => '<ul>
                <li>Fai domande in linguaggio naturale</li>
                <li>Riferisciti ai documenti caricati</li>
                <li>Visualizza la cronologia delle conversazioni</li>
                <li>Chiedi spiegazioni dettagliate</li>
            </ul>',
            
            'user_subscription' => '<ul>
                <li>Registra il tuo account</li>
                <li>Conferma l\'indirizzo email</li>
                <li>Scegli il piano più adatto</li>
                <li>Gestisci il profilo utente</li>
            </ul>',
            
            'login' => '<ul>
                <li>Accedi con email e password</li>
                <li>Usa il reset password se necessario</li>
                <li>Registrati se non hai un account</li>
                <li>Abilita l\'autenticazione a due fattori</li>
            </ul>'
        );
        
        return $specific_content[$widget_type] ?? '';
    }
    
    /**
     * Configura il nuovo sistema
     */
    private static function configure_new_system($migrated_data) {
        // Salva i dati migrati
        update_option('widget_help_contents', $migrated_data);
        
        // Configura il Widget Help Manager
        if (class_exists('Widget_Help_Manager')) {
            $manager = Widget_Help_Manager::get_instance();
            
            // Verifica che tutti i widget siano registrati
            foreach (array_keys($migrated_data) as $widget_id) {
                $config = $manager->get_widget_config($widget_id);
                if (!$config) {
                    error_log("Widget Help Migration: Widget '{$widget_id}' non registrato nel manager");
                }
            }
        }
        
        // Disabilita temporaneamente il vecchio sistema
        update_option('help_line_migration_disabled', true);
    }
    
    /**
     * Verifica che la migrazione sia avvenuta correttamente
     */
    private static function verify_migration() {
        $widget_helps = get_option('widget_help_contents', array());
        
        if (empty($widget_helps)) {
            return array(
                'success' => false,
                'message' => __('Nessun dato migrato trovato', 'document-viewer-plugin')
            );
        }
        
        $expected_widgets = array('document_viewer', 'subscriber_management', 'chat_model', 'user_subscription', 'login');
        $migrated_widgets = array_keys($widget_helps);
        
        $missing_widgets = array_diff($expected_widgets, $migrated_widgets);
        
        if (!empty($missing_widgets)) {
            return array(
                'success' => false,
                'message' => sprintf(__('Widget mancanti: %s', 'document-viewer-plugin'), implode(', ', $missing_widgets))
            );
        }
        
        // Verifica che ogni widget abbia contenuto
        foreach ($widget_helps as $widget_id => $help_data) {
            if (empty($help_data['content'])) {
                return array(
                    'success' => false,
                    'message' => sprintf(__('Contenuto mancante per widget: %s', 'document-viewer-plugin'), $widget_id)
                );
            }
        }
        
        return array(
            'success' => true,
            'message' => __('Verifica migrazione completata con successo', 'document-viewer-plugin'),
            'migrated_widgets' => count($widget_helps)
        );
    }
    
    /**
     * Rollback della migrazione
     */
    public static function rollback_migration() {
        $backup_data = get_option(self::BACKUP_OPTION);
        
        if (empty($backup_data)) {
            return array(
                'success' => false,
                'message' => __('Nessun backup trovato per il rollback', 'document-viewer-plugin')
            );
        }
        
        try {
            // Ripristina il sistema originale
            if (isset($backup_data['help_line_content'])) {
                update_option('help_line_content', $backup_data['help_line_content']);
            }
            
            // Riabilita il vecchio sistema
            delete_option('help_line_migration_disabled');
            
            // Pulisci i dati migrati
            delete_option('widget_help_contents');
            
            // Reset stato migrazione
            delete_option(self::MIGRATION_OPTION);
            
            return array(
                'success' => true,
                'message' => __('Rollback completato con successo', 'document-viewer-plugin'),
                'restored_date' => $backup_data['backup_date'] ?? null
            );
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => sprintf(__('Errore durante il rollback: %s', 'document-viewer-plugin'), $e->getMessage())
            );
        }
    }
    
    /**
     * Ottiene lo stato della migrazione
     */
    public static function get_migration_status() {
        return get_option(self::MIGRATION_OPTION, array());
    }
    
    /**
     * Verifica se la migrazione è stata completata
     */
    public static function is_migration_completed() {
        $status = self::get_migration_status();
        return !empty($status['completed']);
    }
    
    /**
     * Contenuto di fallback di default
     */
    private static function get_fallback_default_content() {
        return '<h4>Come utilizzare il sistema:</h4>
        <ol>
            <li>Naviga tra le diverse sezioni del plugin</li>
            <li>Usa i widget per le funzionalità specifiche</li>
            <li>Consulta l\'help contextual per ogni widget</li>
            <li>Contatta il supporto per assistenza</li>
        </ol>
        <h4>Suggerimenti generali:</h4>
        <ul>
            <li>Mantieni sempre aggiornato il plugin</li>
            <li>Verifica la connessione internet per le funzionalità online</li>
            <li>Consulta la documentazione per funzionalità avanzate</li>
        </ul>';
    }
    
    /**
     * Utility per export/import configurazioni
     */
    public static function export_configuration() {
        $widget_helps = get_option('widget_help_contents', array());
        $migration_status = get_option(self::MIGRATION_OPTION, array());
        
        return array(
            'version' => self::MIGRATION_VERSION,
            'export_date' => current_time('mysql'),
            'widget_helps' => $widget_helps,
            'migration_status' => $migration_status
        );
    }
    
    /**
     * Import configurazioni
     */
    public static function import_configuration($config_data) {
        if (empty($config_data['widget_helps'])) {
            return array(
                'success' => false,
                'message' => __('Dati di configurazione non validi', 'document-viewer-plugin')
            );
        }
        
        try {
            update_option('widget_help_contents', $config_data['widget_helps']);
            
            if (!empty($config_data['migration_status'])) {
                update_option(self::MIGRATION_OPTION, $config_data['migration_status']);
            }
            
            return array(
                'success' => true,
                'message' => __('Configurazione importata con successo', 'document-viewer-plugin'),
                'imported_widgets' => count($config_data['widget_helps'])
            );
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => sprintf(__('Errore durante l\'import: %s', 'document-viewer-plugin'), $e->getMessage())
            );
        }
    }
    
    /**
     * Migra dalla chiave legacy 'widget_help_contents' alla nuova 'widget_help_content'
     * e sincronizza la struttura dati
     */
    public static function migrate_option_keys() {
        // Verifica se esiste la vecchia chiave
        $legacy_content = get_option('widget_help_contents', array());
        $current_content = get_option('widget_help_content', array());
        
        // Se esistono dati legacy e non nella nuova struttura, migra
        if (!empty($legacy_content) && empty($current_content)) {
            error_log('Widget Help Migration: Migrating from legacy option key');
            
            $migrated_data = array();
            
            foreach ($legacy_content as $widget_id => $widget_data) {
                if (is_string($widget_data)) {
                    // Vecchio formato: solo contenuto stringa
                    $migrated_data[$widget_id] = array(
                        'main' => $widget_data
                    );
                } elseif (is_array($widget_data)) {
                    if (isset($widget_data['content'])) {
                        // Formato intermedio: oggetto con 'content'
                        $migrated_data[$widget_id] = array(
                            'main' => $widget_data['content']
                        );
                    } else {
                        // Già nel formato corretto
                        $migrated_data[$widget_id] = $widget_data;
                    }
                }
            }
            
            // Salva nella nuova chiave
            update_option('widget_help_content', $migrated_data);
            
            // Aggiorna il log di migrazione
            $migration_log = get_option('widget_help_migration_log', array());
            $migration_log[] = array(
                'timestamp' => current_time('mysql'),
                'action' => 'migrate_option_keys',
                'widgets_migrated' => count($migrated_data),
                'source' => 'widget_help_contents',
                'target' => 'widget_help_content'
            );
            update_option('widget_help_migration_log', $migration_log);
            
            return $migrated_data;
        }
        
        return $current_content;
    }
    
    /**
     * Verifica la consistenza delle opzioni e corregge eventuali problemi
     */
    public static function verify_option_consistency() {
        $content_data = get_option('widget_help_content', array());
        $settings_data = get_option('widget_help_widget_settings', array());
        $legacy_data = get_option('widget_help_contents', array());
        
        $issues = array();
        
        // Verifica 1: Dati legacy senza migrazione
        if (!empty($legacy_data) && empty($content_data)) {
            $issues[] = 'Legacy data found without migration';
        }
        
        // Verifica 2: Widget nelle impostazioni ma non nei contenuti
        foreach (array_keys($settings_data) as $widget_id) {
            if (!isset($content_data[$widget_id])) {
                $issues[] = "Widget $widget_id has settings but no content";
            }
        }
        
        // Verifica 3: Struttura dati corrotta
        foreach ($content_data as $widget_id => $sections) {
            if (!is_array($sections)) {
                $issues[] = "Widget $widget_id has invalid content structure";
            }
        }
        
        return array(
            'consistent' => empty($issues),
            'issues' => $issues,
            'content_widgets' => array_keys($content_data),
            'settings_widgets' => array_keys($settings_data),
            'legacy_widgets' => array_keys($legacy_data)
        );
    }
}

// Funzioni helper per AJAX
add_action('wp_ajax_widget_help_migrate', function() {
    check_ajax_referer('widget_help_nonce', 'nonce');
    
    if (!current_user_can('manage_options')) {
        wp_send_json_error(array('message' => 'Permessi insufficienti'));
    }
    
    $result = Widget_Help_Migration::migrate();
    
    if ($result['success']) {
        wp_send_json_success($result);
    } else {
        wp_send_json_error($result);
    }
});

add_action('wp_ajax_widget_help_rollback', function() {
    check_ajax_referer('widget_help_nonce', 'nonce');
    
    if (!current_user_can('manage_options')) {
        wp_send_json_error(array('message' => 'Permessi insufficienti'));
    }
    
    $result = Widget_Help_Migration::rollback_migration();
    
    if ($result['success']) {
        wp_send_json_success($result);
    } else {
        wp_send_json_error($result);
    }
});
