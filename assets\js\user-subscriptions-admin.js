/**
 * User Subscriptions Admin JS
 * JavaScript for User Subscriptions management interfaces
 * Includes integrated dialog systems: jQuery UI Dialog Fix, Dialog Fallback, Dialog Debug
 */

// Verifica prima la disponibilità di jQuery
(function() {
    // Se jQuery non è disponibile subito, aspettiamo
    if (typeof jQuery === 'undefined') {
        console.error('jQuery non è disponibile per user-subscriptions-admin.js');
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof jQuery !== 'undefined') {
                console.log('jQuery è ora disponibile, inizializzazione del modulo');
                initializeModule(jQuery);
            } else {
                console.error('jQuery non è disponibile nemmeno dopo il caricamento del DOM');
                alert('Errore: jQuery non è disponibile. Alcune funzionalità potrebbero non funzionare correttamente.');
            }
        });
    } else {
        // jQuery è disponibile subito
        initializeModule(jQuery);
    }
})();

function initializeModule($) {
    'use strict';
    
    console.log('Inizializzazione user-subscriptions-admin.js con jQuery', $.fn.jquery);
    
    /**
     * =========================================
     * SISTEMA DI FIX PER JQUERY UI DIALOG
     * =========================================
     * Risolve problemi di visualizzazione e l'errore "Cannot read properties of undefined (reading 'length')"
     */
    
    // Sistema globale per la gestione dei fix dei dialog jQuery UI
    window.jQueryDialogFixSystem = {
        // Registro dei dialog aperti
        openDialogs: [],
        
        // Chiamato quando un dialog viene creato
        onDialogCreate: function(dialog) {
            console.log('Dialog creato, applicazione fix');
        },
        
        // Chiamato quando un dialog viene aperto
        onDialogOpen: function(dialog) {
            console.log('Dialog aperto, applicazione fix di visibilità');
            this.applyVisibilityFix(dialog);
            this.openDialogs.push(dialog);
        },
        
        // Chiamato quando un dialog viene chiuso
        onDialogClose: function(dialog) {
            console.log('Dialog chiuso');
            this.openDialogs = this.openDialogs.filter(function(d) {
                return d !== dialog;
            });
        },
        
        // Applica fix di visibilità
        applyVisibilityFix: function(dialog) {
            try {
                var $dialog = $(dialog).closest('.ui-dialog');
                if ($dialog.length) {
                    $dialog.css({
                        'display': 'block',
                        'visibility': 'visible',
                        'z-index': '100001'
                    });
                    
                    // Fix overlay
                    $('.ui-widget-overlay').css({
                        'display': 'block',
                        'visibility': 'visible',
                        'z-index': '100000'
                    });
                }
            } catch (e) {
                console.error('Errore nell\'applicazione del fix di visibilità:', e);
            }
        }
    };
    
    // Inizializza il sistema di fix per jQuery UI Dialog
    $(document).ready(function() {
        console.log('jQuery UI Dialog fix caricato - v1.0.2');
        
        // Aggiungi stile direttamente al DOM per garantire che sovrascriva tutto
        if (!$('#jquery-ui-style-fix').length) {
            $('head').append(`
                <style id="jquery-ui-style-fix">
                    .ui-dialog {
                        display: block !important;
                        z-index: 100001 !important;
                        position: fixed !important;
                    }
                    .ui-widget-overlay {
                        display: block !important;
                        z-index: 100000 !important;
                        position: fixed !important;
                        top: 0 !important;
                        right: 0 !important;
                        bottom: 0 !important;
                        left: 0 !important;
                    }
                    .ui-dialog .ui-dialog-titlebar-close {
                        display: block !important;
                        position: absolute !important;
                        right: 5px !important;
                        top: 5px !important;
                    }
                    .ui-button {
                        display: inline-block !important;
                    }
                    #subscription-type-modal {
                        display: none;
                        padding: 0 !important;
                    }
                    /* Fix for z-index issues */
                    #wpwrap {
                        z-index: auto !important;
                    }
                    /* Fix for overlay issues */
                    #wpbody-content {
                        position: static !important;
                    }
                    /* Admin bar fix */
                    #wpadminbar {
                        z-index: 99999 !important;
                    }
                </style>
            `);
        }
        
        // Patch per evitare errori di "Cannot read properties of undefined (reading 'length')"
        // RIMOSSO: Non ridefinire $.fn.length, causa errori con jQuery e plugin
    });
    
    /**
     * =========================================
     * SISTEMA DI FALLBACK PER DIALOG
     * =========================================
     * Fornisce un'alternativa quando jQuery UI Dialog non è disponibile o fallisce
     */
    
    // Sistema globale di fallback
    window.DialogFallback = {
        // Flag che indica se jQuery UI Dialog funziona
        jQueryUIWorking: false,
        
        // Flag di integrazione con altri sistemi
        _integrated: false,
        
        // Registro dei dialog aperti con fallback
        _openDialogs: [],
        
        // Inizializzazione del sistema
        init: function() {
            console.log('Inizializzazione Dialog Fallback System');
            
            // Testa se jQuery UI Dialog funziona
            this.testDialog();
            
            // Integra con il sistema di fix se disponibile
            this.integrateWithSystems();
            
            // Installa il fallback se necessario
            if (!this.jQueryUIWorking) {
                this.installFallback();
            }
            
            return this;
        },
        
        // Test di jQuery UI Dialog
        testDialog: function() {
            try {
                // Tenta di aprire un dialog nascosto per verificare il funzionamento
                var $testDiv = $('<div id="dialog-test" style="display:none"></div>').appendTo('body');
                
                if (typeof $.ui === 'undefined' || typeof $.ui.dialog !== 'function') {
                    console.warn('jQuery UI Dialog non è disponibile');
                    this.jQueryUIWorking = false;
                    return false;
                }
                
                $testDiv.dialog({
                    autoOpen: false
                });
                
                this.jQueryUIWorking = true;
                console.log('jQuery UI Dialog funziona correttamente');
                $testDiv.dialog('destroy').remove();
                return true;
            } catch(e) {
                console.error('jQuery UI Dialog non funziona:', e);
                this.jQueryUIWorking = false;
                return false;
            }
        },
        
        // Integrazione con altri sistemi
        integrateWithSystems: function() {
            if (this._integrated) return;
            
            // Integrazione con jQueryDialogFixSystem
            if (window.jQueryDialogFixSystem) {
                console.log('Integrazione con jQueryDialogFixSystem');
                
                // Notifica quando viene aperto un dialog con fallback
                const self = this;
                const originalOpenFallbackDialog = this.openFallbackDialog;
                
                this.openFallbackDialog = function(element, options) {
                    const result = originalOpenFallbackDialog.call(self, element, options);
                    if (result && window.jQueryDialogFixSystem.onFallbackDialogOpen) {
                        window.jQueryDialogFixSystem.onFallbackDialogOpen(element, options);
                    }
                    return result;
                };
            }
            
            // Integrazione con dialog debug
            if (typeof window.DialogDebug !== 'undefined') {
                console.log('Integrazione con DialogDebug');
                if (typeof window.DialogDebug.registerFallbackSystem === 'function') {
                    window.DialogDebug.registerFallbackSystem(this);
                }
            }
            
            this._integrated = true;
        }
    };

    // Funzione globale per aprire i dialog jQuery UI in modo affidabile
    window.openUIDialog = function(selector, options) {
        console.log('Apertura dialog per elemento:', selector);
        
        // Verifica che jQuery sia disponibile
        if (typeof jQuery === 'undefined') {
            console.error('jQuery non è disponibile');
            if (typeof alert === 'function') {
                alert('Errore: jQuery non è disponibile');
            }
            // Se esiste il fallback senza jQuery, usalo
            if (typeof window.DialogFallbackNoJquery === 'function') {
                return window.DialogFallbackNoJquery(selector, options);
            }
            return false;
        }
        
        // Usa jQuery in modo sicuro
        var $ = jQuery;
        
        try {
            // Se il sistema DialogFallback è disponibile, provalo prima
            if (window.DialogFallback && typeof window.DialogFallback.openFallbackDialog === 'function') {
                // Se già sappiamo che jQuery UI non funziona, usa direttamente il fallback
                if (window.DialogFallback.jQueryUIWorking === false) {
                    console.log('Utilizzo diretto del fallback dialog (jQuery UI non funzionante)');
                    return window.DialogFallback.openFallbackDialog(selector, options);
                }
            }
            
            // Verifica che jQuery UI sia presente
            if (typeof $.ui === 'undefined' || typeof $.ui.dialog !== 'function') {
                console.error('jQuery UI Dialog non è disponibile');
                // Prova ad utilizzare il fallback se disponibile
                if (window.DialogFallback && typeof window.DialogFallback.openFallbackDialog === 'function') {
                    console.log('Utilizzo fallback dialog (jQuery UI non disponibile)');
                    return window.DialogFallback.openFallbackDialog(selector, options);
                }
                return false;
            }
            
            // Verifica che il selettore sia valido e che l'elemento esista
            if (!selector || typeof selector !== 'string') {
                console.error('Selettore dialog non valido:', selector);
                return false;
            }
            
            var $element = $(selector);
            if (!$element.length) {
                console.error('Elemento dialog non trovato:', selector);
                return false;
            }
        } catch (e) {
            console.error('Errore nella verifica dell\'elemento:', e);
            return false;
        }
        
        try {
            // Verifica che jQuery UI sia disponibile
            if (typeof $.ui === 'undefined' || typeof $.ui.dialog === 'undefined') {
                console.error('jQuery UI o jQuery UI Dialog non sono disponibili');
                return false;
            }
            
            console.log('jQuery UI disponibile, versione:', $.ui.version);
            
            // Ottieni l'elemento come oggetto jQuery
            var $dialogElement = $(selector);
            
            // Assicurati che il dialog sia prima distrutto per evitare conflitti
            try {
                // Controllo più sicuro se il dialog è già stato inizializzato
                if ($dialogElement.hasClass('ui-dialog-content') || 
                    (typeof $dialogElement.dialog === 'function' && $dialogElement.data('ui-dialog'))) {
                    console.log('Distruggo istanza precedente del dialog');
                    $dialogElement.dialog("destroy");
                }
            } catch (e) {
                console.log('Dialog non era già inizializzato:', e.message);
            }
            
            // Assicurati che l'elemento sia visibile nel DOM ma nascosto
            $dialogElement.css({
                'display': 'block',
                'visibility': 'visible',
                'opacity': '1'
            }).hide();
            
            // Preparazione opzioni complete per il dialog
            var dialogOptions = $.extend({
                autoOpen: true,
                modal: true,
                width: 400,
                closeOnEscape: true,
                create: function() {
                    console.log('Dialog creato');
                    // Notifica al sistema di fix che un dialog è stato creato
                    if (window.jQueryDialogFixSystem && typeof window.jQueryDialogFixSystem.onDialogCreate === 'function') {
                        window.jQueryDialogFixSystem.onDialogCreate(this);
                    }
                },
                open: function() {
                    console.log('Dialog aperto');
                    // Posiziona il dialog al centro dello schermo
                    $(this).dialog("widget").position({
                        my: "center",
                        at: "center", 
                        of: window
                    });
                    
                    // Fix per la sovrapposizione z-index
                    $('.ui-widget-overlay').css('z-index', 100000);
                    $(this).parent().css('z-index', 100001);
                    
                    // Notifica al sistema di fix che un dialog è stato aperto
                    if (window.jQueryDialogFixSystem && typeof window.jQueryDialogFixSystem.onDialogOpen === 'function') {
                        window.jQueryDialogFixSystem.onDialogOpen(this);
                    }
                },
                close: function() {
                    // Notifica al sistema di fix che un dialog è stato chiuso
                    if (window.jQueryDialogFixSystem && typeof window.jQueryDialogFixSystem.onDialogClose === 'function') {
                        window.jQueryDialogFixSystem.onDialogClose(this);
                    }
                }
            }, options || {});
            
            // Inizializza il dialog con le opzioni
            try {
                $dialogElement.dialog(dialogOptions);
            } catch (dialogError) {
                console.error('Errore durante l\'inizializzazione del dialog:', dialogError);
                
                // Prova il fallback se disponibile
                if (window.DialogFallback && typeof window.DialogFallback.openFallbackDialog === 'function') {
                    console.log('Utilizzo fallback dialog dopo errore di inizializzazione');
                    return window.DialogFallback.openFallbackDialog(selector, options);
                }
                
                throw dialogError; // Rilancia l'errore se non è stato gestito
            }
            
            // Assicurati che il dialog sia visibile
            setTimeout(function() {
                try {
                    var $dlg = $(selector);
                    if ($dlg.length && 
                        (typeof $dlg.dialog === 'function') && 
                        (!$dlg.dialog("isOpen") || !$dlg.is(":visible"))) {
                        
                        console.log('Tentativo di apertura forzata del dialog');
                        $dlg.dialog("open");
                        
                        // Posiziona nuovamente il dialog
                        $dlg.dialog("widget").position({
                            my: "center",
                            at: "center", 
                            of: window
                        });
                        
                        // Fix aggiuntivi per la visibilità
                        var $dialogWidget = $dlg.closest('.ui-dialog');
                        if ($dialogWidget.length) {
                            $dialogWidget.css({
                                'display': 'block',
                                'visibility': 'visible',
                                'z-index': '100001'
                            });
                        }
                    }
                } catch (err) {
                    console.error('Errore nel tentativo di forzare apertura dialog:', err);
                    
                    // Ultimo tentativo con il sistema di fallback
                    if (window.DialogFallback && typeof window.DialogFallback.openFallbackDialog === 'function') {
                        console.log('Utilizzo fallback dialog dopo errore di apertura forzata');
                        return window.DialogFallback.openFallbackDialog(selector, options);
                    }
                }
            }, 100);
            
            return true;
        } catch (e) {
            console.error('Errore nell\'apertura del dialog:', e);
            
            // Prova con il fallback come ultima risorsa
            if (window.DialogFallback && typeof window.DialogFallback.openFallbackDialog === 'function') {
                console.log('Utilizzo fallback dialog dopo errore generale');
                return window.DialogFallback.openFallbackDialog(selector, options);
            }
            
            if (typeof alert === 'function') {
                alert('Errore nell\'apertura del popup: ' + e.message);
            }
            return false;
        }
    };

    // Initialize chart.js for statistics
    let usersChart = null;
    
    // Chart.js availability check
    let chartJsLoaded = false;
    
    // Function to check if Chart.js is available
    function checkChartJsAvailability() {
        if (typeof Chart !== 'undefined') {
            chartJsLoaded = true;
            console.log('Chart.js is loaded and available.');
        } else {
            console.error('Chart.js is not loaded! Charts will not render correctly.');
            // Try to reload Chart.js if not available
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/chart.js';
            script.async = true;
            script.onload = function() {
                chartJsLoaded = true;
                console.log('Chart.js dynamically loaded.');
                // Reload charts if we're on the reports page
                if (window.location.search.includes('subscription-reports')) {
                    loadStats();
                }
            };
            document.head.appendChild(script);
        }
    }
    
    // Funzione semplificata per verificare che jQuery UI Dialog sia disponibile
    function ensureJQueryUIDialog() {
        // Verifica che jQuery UI sia caricato
        if (typeof $.ui === 'undefined') {
            console.error('jQuery UI non è caricato!');
            return false;
        }
        
        // Verifica che la funzione dialog sia disponibile
        if (typeof $.fn.dialog !== 'function') {
            console.error('jQuery UI Dialog non è disponibile!');
            return false;
        }
        
        console.log('jQuery UI Dialog disponibile. Versione: ' + $.ui.version);
        return true;
    }
    
    // Document ready
    $(document).ready(function() {
        console.log('User Subscriptions Admin JS initialized');
        
        // Check Chart.js availability
        checkChartJsAvailability();
        
        // Verifica che jQuery UI Dialog sia disponibile
        const jqueryUIReady = ensureJQueryUIDialog();
        
        // Assicurati che la funzione safeDialog sia disponibile
        if (typeof window.safeDialog !== 'function') {
            // Definisci la funzione safeDialog se non esiste già
            window.safeDialog = function(element, options) {
                try {
                    $(element).dialog(options);
                    return true;
                } catch(e) {
                    console.error('Errore apertura dialog:', e);
                    return false;
                }
            };
        }
        
        // Initialize based on current page
        const currentPath = window.location.pathname;
        const currentPage = new URLSearchParams(window.location.search).get('page');
        console.log('Current admin page:', currentPage);
        
        // Load appropriate interface based on current page
        if (currentPage === 'user-subscriptions') {
            initUserManagement();
        } else if (currentPage === 'subscription-types') {
            initSubscriptionTypes();
        } else if (currentPage === 'subscription-reports') {
            initReports();
        }
    });
    
    /**
     * Initialize User Management Page
     */
    function initUserManagement() {
        // Load all users
        loadUsers();
        
        // Search users functionality
        $('#search-users').on('keyup', function() {
            const searchTerm = $(this).val().toLowerCase();
            $('#users-list tr').each(function() {
                const rowText = $(this).text().toLowerCase();
                $(this).toggle(rowText.indexOf(searchTerm) > -1);
            });
        });
        
        // Add new user button
        $('#add-new-user').on('click', function() {
            // Reset form
            $('#user-form')[0].reset();
            $('#user-id').val(0);
            $('.form-errors').text('');
            
            // Open dialog
            $('#user-modal').dialog({
                title: 'Add New User',
                modal: true,
                width: 400,
                buttons: {
                    'Save': function() {
                        saveUser();
                    },
                    'Cancel': function() {
                        $('.form-errors').text('');
                        if (window.DialogFallback && typeof window.DialogFallback.closeFallbackDialog === 'function') {
                            window.DialogFallback.closeFallbackDialog($(this));
                        } else {
                            $(this).dialog('close');
                        }
                    }
                },
                close: function() {
                    $('.form-errors').text('');
                    if (window.DialogFallback && typeof window.DialogFallback.closeFallbackDialog === 'function') {
                        window.DialogFallback.closeFallbackDialog($(this));
                    }
                    // Don't call dialog('close') recursively
                }
            });
        });
        
        // Enable edit and delete buttons (using event delegation)
        $('#users-list').on('click', '.edit-user', function() {
            const userId = $(this).data('id');
            editUser(userId);
        });
        
        $('#users-list').on('click', '.delete-user', function() {
            const userId = $(this).data('id');
            deleteUser(userId);
        });
    }
    
    /**
     * Load users for table display
     */
    function loadUsers() {
        $.ajax({
            url: userSubscriptionsAdmin.ajax_url,
            type: 'POST',
            dataType: 'json',
            data: {
                action: 'get_user_subscriptions',
                nonce: userSubscriptionsAdmin.nonce
            },
            success: function(response) {
                if (response.success && response.data.users) {
                    // Clear and update table
                    $('#users-list').empty();
                      if (response.data.users.length === 0) {
                        $('#users-list').html('<tr><td colspan="13">No users found.</td></tr>');
                        return;
                    }
                    
                    $.each(response.data.users, function(index, user) {
                        const row = $('<tr></tr>');
                        
                        row.append('<td>' + user.id + '</td>');
                        row.append('<td>' + user.username + '</td>');
                        row.append('<td>' + user.name + '</td>');
                        row.append('<td>' + user.surname + '</td>');                        row.append('<td>' + user.email + '</td>');
                        row.append('<td>' + (user.phone || '') + '</td>');
                        row.append('<td>' + user.tipo_subscription + '</td>');
                        row.append('<td>' + parseFloat(user.credit).toFixed(2) + ' €</td>');
                        row.append('<td>' + (user.analysis_count || 0) + '</td>');
                        row.append('<td>' + (user.tokens_used ? user.tokens_used.toLocaleString() : '0') + '</td>');
                        row.append('<td>' + parseFloat(user.actual_cost || 0).toFixed(2) + ' €</td>');
                        row.append('<td>' + parseFloat(user.tot_cost || 0).toFixed(2) + ' €</td>');
                        
                        // Formatta la data per miglior leggibilità
                        let lastUpdate = '';
                        if (user.updated_at) {
                            try {
                                const date = new Date(user.updated_at);
                                lastUpdate = date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
                            } catch(e) {
                                lastUpdate = user.updated_at;
                            }
                        }
                        row.append('<td>' + lastUpdate + '</td>');                        // Action buttons
                        const actions = $('<td class="column-actions"></td>');
                        
                        const actionButtons = $('<div class="action-buttons"></div>');
                        
                        actionButtons.append('<button type="button" class="button button-small edit-user" data-id="' + user.id + '" title="Edit User">' +
                            '<span class="dashicons dashicons-edit"></span>' +
                            '</button>');
                        
                        actionButtons.append('<button type="button" class="button button-small delete-user" data-id="' + user.id + '" title="Delete User">' +
                            '<span class="dashicons dashicons-trash"></span>' +
                            '</button>');
                        
                        actions.append(actionButtons);
                        row.append(actions);
                        $('#users-list').append(row);
                    });                } else {
                    $('#users-list').html('<tr><td colspan="13">Error loading users.</td></tr>');
                }
            },            error: function() {
                $('#users-list').html('<tr><td colspan="13">Error connecting to server.</td></tr>');
            }
        });
    }
    
    /**
     * Save or update user
     */
    function saveUser() {
        const userId = parseInt($('#user-id').val());
        const isUpdate = userId > 0;
        
        const userData = {
            username: $('#username').val(),
            name: $('#name').val(),
            surname: $('#surname').val(),
            email: $('#email').val(),
            phone: $('#phone').val(),
            password: $('#password').val(),
            tipo_subscription: $('#tipo_subscription').val(),
            credit: $('#credit').val()
        };
        
        // Validate form
        let errors = [];
        if (!userData.username) errors.push('Username is required');
        if (!userData.name) errors.push('Name is required');
        if (!userData.surname) errors.push('Surname is required');
        if (!userData.email) errors.push('Email is required');
        if (!userData.tipo_subscription) errors.push('Subscription type is required');
        
        // Special validation for new user - need password
        if (!isUpdate && !userData.password) {
            errors.push('Password is required for new users');
        }
        
        if (errors.length > 0) {
            $('.form-errors').html(errors.join('<br>'));
            return;
        }
        
        // Clear errors
        $('.form-errors').text('');
        
        $.ajax({
            url: userSubscriptionsAdmin.ajax_url,
            type: 'POST',
            dataType: 'json',
            data: {
                action: isUpdate ? 'update_user_subscription' : 'add_user_subscription',
                nonce: userSubscriptionsAdmin.nonce,
                user_id: userId,
                ...userData
            },
            success: function(response) {
                if (response.success) {
                    // Close dialog and reload users
                    $('#user-modal').dialog('close');
                    loadUsers();
                    
                    // Show success message
                    alert(userSubscriptionsAdmin.i18n.save_success);
                } else {
                    // Show error
                    $('.form-errors').text(response.data.message || userSubscriptionsAdmin.i18n.error);
                }
            },
            error: function() {
                $('.form-errors').text(userSubscriptionsAdmin.i18n.error);
            }
        });
    }
    
    /**
     * Edit user - load data into form
     */
    function editUser(userId) {
        // Get all users from the displayed table
        const user = getUserDataFromTable(userId);
        
        if (!user) {
            alert(userSubscriptionsAdmin.i18n.error);
            return;
        }
        
        // Fill form with user data
        $('#user-id').val(user.id);
        $('#username').val(user.username);
        $('#name').val(user.name);
        $('#surname').val(user.surname);
        $('#email').val(user.email);
        $('#phone').val(user.phone);
        $('#password').val(''); // Don't display password, will only update if provided
        $('#tipo_subscription').val(user.tipo_subscription);
        $('#credit').val(parseFloat(user.credit).toFixed(2));
        
        // Clear any previous errors
        $('.form-errors').text('');
        
        // Open dialog
        $('#user-modal').dialog({
            title: 'Edit User: ' + user.username,
            modal: true,
            width: 400,
            buttons: {
                'Update': function() {
                    saveUser();
                },
                'Cancel': function() {
                    $('.form-errors').text('');
                    if (window.DialogFallback && typeof window.DialogFallback.closeFallbackDialog === 'function') {
                        window.DialogFallback.closeFallbackDialog($(this));
                    } else {
                        $(this).dialog('close');
                    }
                }
            },
            close: function() {
                $('.form-errors').text('');
                if (window.DialogFallback && typeof window.DialogFallback.closeFallbackDialog === 'function') {
                    window.DialogFallback.closeFallbackDialog($(this));
                }
                // Don't call dialog('close') again here - it's redundant and causes problems
            }
        });
    }
    
    /**
     * Extract user data from table row
     */    function getUserDataFromTable(userId) {
        let user = null;
        
        $('#users-list tr').each(function() {
            const cells = $(this).find('td');
            if (cells.length > 0 && parseInt(cells.eq(0).text()) === parseInt(userId)) {
                user = {
                    id: cells.eq(0).text(),
                    username: cells.eq(1).text(),
                    name: cells.eq(2).text(),
                    surname: cells.eq(3).text(),
                    email: cells.eq(4).text(),
                    phone: cells.eq(5).text(),
                    tipo_subscription: cells.eq(6).text(),
                    credit: parseFloat(cells.eq(7).text().replace('€', '').trim()),
                    analysis_count: parseInt(cells.eq(8).text() || '0'),
                    tokens_used: parseInt(cells.eq(9).text().replace(/[,\.]/g, '') || '0'),
                    actual_cost: parseFloat(cells.eq(10).text().replace('€', '').trim() || '0'),
                    tot_cost: parseFloat(cells.eq(11).text().replace('€', '').trim() || '0'),
                    last_update: cells.eq(12).text()
                };
                return false; // Break the loop
            }
        });
        
        return user;
    }
    
    /**
     * Delete user
     */
    function deleteUser(userId) {
        if (!confirm(userSubscriptionsAdmin.i18n.confirm_delete)) {
            return;
        }
        
        $.ajax({
            url: userSubscriptionsAdmin.ajax_url,
            type: 'POST',
            dataType: 'json',
            data: {
                action: 'delete_user_subscription',
                nonce: userSubscriptionsAdmin.nonce,
                user_id: userId
            },
            success: function(response) {
                if (response.success) {
                    // Reload users
                    loadUsers();
                    
                    // Show success message
                    alert(userSubscriptionsAdmin.i18n.delete_success);
                } else {
                    // Show error
                    alert(response.data.message || userSubscriptionsAdmin.i18n.error);
                }
            },
            error: function() {
                alert(userSubscriptionsAdmin.i18n.error);
            }
        });
    }
    
    /**
     * Initialize Subscription Types Page
     */
    function initSubscriptionTypes() {
        console.log('Initializing Subscription Types interface');
        
        // Load all subscription types
        loadSubscriptionTypes();
        
        // Add new subscription type button
        $('#add-new-subscription-type').on('click', function() {
            console.log('Add new subscription type button clicked');
            
            // Reset form
            $('#subscription-type-form')[0].reset();
            $('#type-id').val(0);
            $('.form-errors').text('');
            
            // Verifica che l'elemento dialog esista
            if (!$('#subscription-type-modal').length) {
                console.error('Elemento dialog #subscription-type-modal non trovato!');
                alert('Errore: Dialog non trovato. Contatta l\'amministratore.');
                return;
            }
            
            try {
                // Utilizziamo la funzione openUIDialog che abbiamo creato per gestire meglio i dialoghi
                if (typeof window.openUIDialog === 'function') {
                    console.log('Utilizzo openUIDialog per aprire il modal');
                    window.openUIDialog('#subscription-type-modal', {
                        title: 'Add New Subscription Type',
                        autoOpen: true,
                        modal: true,
                        width: 400,
                        buttons: {
                            'Save': function() {
                                saveSubscriptionType();
                            },
                            'Cancel': function() {
                                $('.form-errors').text('');
                                if (window.DialogFallback && typeof window.DialogFallback.closeFallbackDialog === 'function') {
                                    window.DialogFallback.closeFallbackDialog($(this));
                                } else {
                                    $(this).dialog('close');
                                }
                            }
                        },
                        close: function() {
                            $('.form-errors').text('');
                            if (window.DialogFallback && typeof window.DialogFallback.closeFallbackDialog === 'function') {
                                window.DialogFallback.closeFallbackDialog($(this));
                            }
                            // Don't call dialog('close') recursively
                        }
                    });
                } else {
                    // Fallback al metodo standard
                    try {
                        if ($('#subscription-type-modal').dialog("instance")) {
                            $('#subscription-type-modal').dialog("destroy");
                        }
                    } catch (e) {
                        console.log('Dialog non era già inizializzato');
                    }
                    
                    $('#subscription-type-modal').dialog({
                        title: 'Add New Subscription Type',
                        autoOpen: true,
                        modal: true,
                        width: 400,
                        buttons: {
                            'Save': function() {
                                saveSubscriptionType();
                            },
                            'Cancel': function() {
                                $('.form-errors').text('');
                                if (window.DialogFallback && typeof window.DialogFallback.closeFallbackDialog === 'function') {
                                    window.DialogFallback.closeFallbackDialog($(this));
                                } else {
                                    $(this).dialog('close');
                                }
                            }
                        },
                        close: function() {
                            $('.form-errors').text('');
                            if (window.DialogFallback && typeof window.DialogFallback.closeFallbackDialog === 'function') {
                                window.DialogFallback.closeFallbackDialog($(this));
                            }
                            // Don't call dialog('close') recursively
                        }
                    });
                    
                    // Assicuriamoci che il dialog sia visibile
                    setTimeout(function() {
                        if (!$('#subscription-type-modal').dialog("isOpen")) {
                            $('#subscription-type-modal').dialog('open');
                        }
                    }, 100);
                }
            } catch (e) {
                console.error('Errore nell\'apertura del dialog:', e);
                alert('Errore nell\'apertura del popup: ' + e.message);
            }
        });
        
        // Enable edit and delete buttons (using event delegation)
        $('#subscription-types-list').on('click', '.edit-subscription-type', function(e) {
            e.preventDefault();
            const typeId = $(this).data('id');
            console.log('Edit button clicked for ID:', typeId);
            if (typeId) {
                editSubscriptionType(typeId);
            } else {
                console.error('ID tipo abbonamento non valido');
                alert('Errore: ID tipo abbonamento non valido');
            }
        });
        
        $('#subscription-types-list').on('click', '.delete-subscription-type', function(e) {
            e.preventDefault();
            const typeId = $(this).data('id');
            if (typeId) {
                deleteSubscriptionType(typeId);
            } else {
                console.error('ID tipo abbonamento non valido');
                alert('Errore: ID tipo abbonamento non valido');
            }
        });
    }
    
    /**
     * Load subscription types for table display
     */
    function loadSubscriptionTypes() {
        $.ajax({
            url: userSubscriptionsAdmin.ajax_url,
            type: 'POST',
            dataType: 'json',
            data: {
                action: 'get_subscription_types',
                nonce: userSubscriptionsAdmin.nonce
            },
            success: function(response) {
                if (response.success && response.data.types) {
                    // Clear and update table
                    $('#subscription-types-list').empty();
                    
                    if (response.data.types.length === 0) {
                        $('#subscription-types-list').html('<tr><td colspan="5">No subscription types found.</td></tr>');
                        return;
                    }
                    
                    $.each(response.data.types, function(index, type) {
                        const row = $('<tr></tr>');
                        
                        row.append('<td>' + type.id + '</td>');
                        row.append('<td>' + type.type_sub + '</td>');
                        row.append('<td>' + (type.link_redirect || '-') + '</td>');
                        row.append('<td>' + (type.cost_per_token || '0.001') + '</td>');
                        row.append('<td>' + type.created_at + '</td>');                        // Action buttons
                        const actions = $('<td class="column-actions"></td>');
                        
                        const actionButtons = $('<div class="action-buttons"></div>');
                        
                        actionButtons.append('<button type="button" class="button button-small edit-subscription-type" data-id="' + type.id + '" title="Edit Subscription Type">' +
                            '<span class="dashicons dashicons-edit"></span>' +
                            '</button>');
                        
                        actionButtons.append('<button type="button" class="button button-small delete-subscription-type" data-id="' + type.id + '" title="Delete Subscription Type">' +
                            '<span class="dashicons dashicons-trash"></span>' +
                            '</button>');
                        
                        actions.append(actionButtons);
                        row.append(actions);
                        $('#subscription-types-list').append(row);
                    });
                } else {
                    $('#subscription-types-list').html('<tr><td colspan="5">Error loading subscription types.</td></tr>');
                }
            },
            error: function() {
                $('#subscription-types-list').html('<tr><td colspan="5">Error connecting to server.</td></tr>');
            }
        });
    }
    
    /**
     * Save or update subscription type
     */
    function saveSubscriptionType() {
        const typeId = parseInt($('#type-id').val());
        const isUpdate = typeId > 0;
        
        const typeData = {
            type_sub: $('#type_sub').val(),
            link_redirect: $('#link_redirect').val(),
            cost_per_token: $('#cost_per_token').val()
        };
        
        // Validate form
        let errors = [];
        if (!typeData.type_sub) errors.push('Type name is required');
        if (typeData.cost_per_token === '' || isNaN(parseFloat(typeData.cost_per_token)) || parseFloat(typeData.cost_per_token) < 0) {
            errors.push('Cost per token must be a valid non-negative number');
        }
        
        if (errors.length > 0) {
            $('.form-errors').html(errors.join('<br>'));
            return;
        }
        
        // Clear errors
        $('.form-errors').text('');
        
        $.ajax({
            url: userSubscriptionsAdmin.ajax_url,
            type: 'POST',
            dataType: 'json',
            data: {
                action: isUpdate ? 'update_subscription_type' : 'add_subscription_type',
                nonce: userSubscriptionsAdmin.nonce,
                type_id: typeId,
                ...typeData
            },
            success: function(response) {
                if (response.success) {
                    // Close dialog and reload types
                    $('#subscription-type-modal').dialog('close');
                    loadSubscriptionTypes();
                    
                    // Show success message
                    alert(userSubscriptionsAdmin.i18n.save_success);
                } else {
                    // Show error
                    $('.form-errors').text(response.data.message || userSubscriptionsAdmin.i18n.error);
                }
            },
            error: function() {
                $('.form-errors').text(userSubscriptionsAdmin.i18n.error);
            }
        });
    }
    
    /**
     * Edit subscription type - load data into form
     */
    function editSubscriptionType(typeId) {
        console.log('Edit subscription type:', typeId);
        
        // Verifica che l'elemento modal esista prima di procedere
        if (!$('#subscription-type-modal').length) {
            console.error('Dialog #subscription-type-modal non trovato nel DOM');
            alert('Errore: Dialog non trovato.');
            return;
        }
        
        // Get subscription type data from the displayed table
        const type = getSubscriptionTypeFromTable(typeId);
        
        if (!type) {
            console.error('Impossibile trovare i dati per il tipo ID:', typeId);
            
            // Alternativa: carica i dati direttamente via AJAX se non disponibili nella tabella
            $.ajax({
                url: userSubscriptionsAdmin.ajax_url,
                type: 'POST',
                dataType: 'json',
                data: {
                    action: 'get_subscription_type',
                    nonce: userSubscriptionsAdmin.nonce,
                    type_id: typeId
                },
                beforeSend: function() {
                    console.log('Invio richiesta AJAX per tipo sottoscrizione id:', typeId);
                },
                success: function(response) {
                    console.log('Risposta AJAX get_subscription_type:', response);
                    if (response && response.success === true) {
                        try {
                            // La risposta può avere diverse strutture, gestiamole tutte
                            let typeData;
                            if (response.data && typeof response.data === 'object') {
                                if (response.data.data && typeof response.data.data === 'object') {
                                    typeData = response.data.data;
                                } else if (response.data.type && typeof response.data.type === 'object') {
                                    typeData = response.data.type;
                                } else {
                                    typeData = response.data;
                                }
                            } else {
                                typeData = {};
                                console.error('Formato risposta non riconosciuto:', response);
                            }
                            
                            // Assicurati di avere un oggetto valido
                            if (typeof typeData !== 'object') {
                                typeData = {};
                            }
                            
                            console.log('Dati tipo sottoscrizione estratti:', typeData);
                            
                            // Riempi il form e apri il modal
                            fillSubscriptionTypeForm(typeData);
                            openSubscriptionTypeModal('Edit Subscription Type: ' + (typeData.type_sub || ''));
                        } catch (e) {
                            console.error('Errore durante l\'elaborazione della risposta:', e);
                            alert('Errore durante l\'elaborazione dei dati: ' + e.message);
                        }
                    } else {
                        // Errore nella risposta
                        const errorMsg = (response && response.data && response.data.message) 
                            ? response.data.message 
                            : (userSubscriptionsAdmin.i18n.error || 'Errore nel recupero dei dati');
                        console.error('Errore nella risposta AJAX:', response);
                        alert(errorMsg);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Errore AJAX:', status, error);
                    console.log('Dettagli XHR:', xhr);
                    alert(userSubscriptionsAdmin.i18n.error + (error ? ': ' + error : ''));
                }
            });
            return;
        }
        
        // Se siamo qui, abbiamo i dati dalla tabella
        fillSubscriptionTypeForm(type);
        openSubscriptionTypeModal('Edit Subscription Type: ' + type.type_sub);
    }
    
    /**
     * Fill subscription type form with data
     */
    function fillSubscriptionTypeForm(type) {
        console.log('Riempimento form con dati:', type);
        
        // Gestione sicura per null/undefined
        if (!type) {
            console.warn('Dati del tipo di sottoscrizione non forniti, uso valori vuoti');
            type = {id: '', type_sub: '', link_redirect: ''};
        }
        
        try {
            // Accesso sicuro alle proprietà con operatore ||
            $('#type-id').val(type.id || '');
            $('#type_sub').val(type.type_sub || '');
            $('#link_redirect').val(type.link_redirect || '');
            $('#cost_per_token').val(type.cost_per_token || '0.001');
            $('.form-errors').text('');
        } catch (e) {
            console.error('Errore durante il riempimento del form:', e);
            $('.form-errors').text('Errore durante il caricamento dei dati');
        }
    }
    
    /**
     * Open subscription type modal with specific title
     */
    function openSubscriptionTypeModal(title) {
        // Verifica che jQuery sia disponibile
        if (typeof $ === 'undefined') {
            console.error('jQuery non disponibile per openSubscriptionTypeModal');
            alert('Errore: jQuery non è disponibile');
            return false;
        }

        // Verifica che l'elemento esista
        var $modal = $('#subscription-type-modal');
        if (!$modal.length) {
            console.error('Elemento modal non trovato nel DOM');
            alert('Errore: Dialog non trovato');
            return false;
        }

        console.log('Apertura modal di tipo sottoscrizione con titolo:', title);
        
        try {
            // Prima verifica se jQuery UI Dialog è disponibile
            if ($.ui && typeof $.ui.dialog === 'function') {
                // Utilizziamo la funzione openUIDialog che è più robusta se disponibile
                if (typeof window.openUIDialog === 'function') {
                    console.log('Utilizzo openUIDialog per aprire il modal di modifica');
                    return window.openUIDialog('#subscription-type-modal', {
                        title: title || 'Subscription Type',
                        autoOpen: true,
                        modal: true,
                        width: 400,
                        buttons: {
                            'Update': function() {
                                if (typeof saveSubscriptionType === 'function') {
                                    saveSubscriptionType();
                                }
                            },
                            'Cancel': function() {
                                $('.form-errors').text('');
                                if (window.DialogFallback && typeof window.DialogFallback.closeFallbackDialog === 'function') {
                                    window.DialogFallback.closeFallbackDialog($(this));
                                } else {
                                    $(this).dialog('close');
                                }
                            }
                        },
                        close: function() {
                            $('.form-errors').text('');
                            if (window.DialogFallback && typeof window.DialogFallback.closeFallbackDialog === 'function') {
                                window.DialogFallback.closeFallbackDialog($(this));
                            }
                            // Don't call dialog('close') recursively
                        }
                    });
                } else {
                    // Apertura dialog con metodo standard ma più robusta
                    try {
                        // Distruggi l'istanza esistente se presente
                        if ($modal.hasClass('ui-dialog-content')) {
                            $modal.dialog("destroy");
                        }
                    } catch (e) {
                        console.log('Dialog non era già inizializzato');
                    }
                    
                    // Apertura del dialog - una sola volta
                    $('#subscription-type-modal').dialog({
                        title: title,
                        autoOpen: true,
                        modal: true,
                        width: 400,
                        buttons: {
                            'Update': function() {
                                saveSubscriptionType();
                            },
                            'Cancel': function() {
                                $('.form-errors').text('');
                                if (window.DialogFallback && typeof window.DialogFallback.closeFallbackDialog === 'function') {
                                    window.DialogFallback.closeFallbackDialog($(this));
                                } else {
                                    $(this).dialog('close');
                                }
                            }
                        },
                        close: function() {
                            $('.form-errors').text('');
                            if (window.DialogFallback && typeof window.DialogFallback.closeFallbackDialog === 'function') {
                                window.DialogFallback.closeFallbackDialog($(this));
                            }
                            // Don't call dialog('close') recursively
                        }
                    });
                }
                
                // Forza la visualizzazione del dialog
                setTimeout(function() {
                    if ($('#subscription-type-modal').dialog('isOpen') === false) {
                        $('#subscription-type-modal').dialog('open');
                        console.log('Dialog aperto con delay');
                    }
                }, 100);
                
                return true; // Aggiungiamo un return per chiarezza
            }
        } catch (e) {
            console.error('Errore nell\'apertura del dialog di modifica:', e);
            alert('Errore nell\'apertura del popup di modifica: ' + e.message);
        }
        
        return false; // Se arriviamo qui, c'è stato un problema
    }
    
    /**
     * Extract subscription type data from table row
     */
    function getSubscriptionTypeFromTable(typeId) {
        let type = null;
        
        // Log per debug
        console.log('Cercando tipo di abbonamento con ID:', typeId);
        console.log('Numero di righe nella tabella:', $('#subscription-types-list tr').length);
        
        // Verifica che l'elemento esista prima di iterare
        if ($('#subscription-types-list').length === 0) {
            console.error('Tabella dei tipi di abbonamento non trovata nel DOM');
            return null;
        }
        
        $('#subscription-types-list tr').each(function() {
            const cells = $(this).find('td');
            
            // Aggiungi controlli di sicurezza per evitare accessi a proprietà di undefined
            if (cells && cells.length > 0) {
                const cellId = cells.eq(0).text().trim();
                if (cellId && parseInt(cellId) === parseInt(typeId)) {
                    type = {
                        id: cellId,
                        type_sub: cells.eq(1).text().trim(),
                        link_redirect: cells.eq(2).text().trim() !== '-' ? cells.eq(2).text().trim() : '',
                        cost_per_token: cells.eq(3).text().trim(),
                        created_at: cells.length > 4 ? cells.eq(4).text().trim() : ''
                    };
                    return false; // Break the loop
                }
            }
        });
        
        // Log del risultato per debug
        if (type) {
            console.log('Tipo di abbonamento trovato:', type);
        } else {
            console.error('Tipo di abbonamento con ID ' + typeId + ' non trovato nella tabella');
        }
        
        return type;
    }
    
    /**
     * Delete subscription type
     */
    function deleteSubscriptionType(typeId) {
        if (!confirm(userSubscriptionsAdmin.i18n.confirm_delete)) {
            return;
        }
        
        $.ajax({
            url: userSubscriptionsAdmin.ajax_url,
            type: 'POST',
            dataType: 'json',
            data: {
                action: 'delete_subscription_type',
                nonce: userSubscriptionsAdmin.nonce,
                type_id: typeId
            },
            success: function(response) {
                if (response.success) {
                    // Reload subscription types
                    loadSubscriptionTypes();
                    
                    // Show success message
                    alert(userSubscriptionsAdmin.i18n.delete_success);
                } else {
                    // Show error
                    alert(response.data.message || userSubscriptionsAdmin.i18n.error);
                }
            },
            error: function() {
                alert(userSubscriptionsAdmin.i18n.error);
            }
        });
    }
      /**
     * Initialize Reports Page
     */    function initReports() {
        // Ensure chart containers are properly initialized
        prepareChartContainers();
        
        // Carica le statistiche iniziali
        loadStats();
        
        // Gestisci il cambio di selezione utente nel filtro
        $('#user-filter').on('change', function() {
            loadStats();
        });
        
        // Gestisci il pulsante refresh
        $('#refresh-stats').on('click', function() {
            loadStats();
        });
        
        // Add window resize handler to redraw charts
        $(window).on('resize', debounce(function() {
            // Redraw charts when window is resized
            Object.keys(chartInstances).forEach(chartId => {
                if (chartInstances[chartId]) {
                    chartInstances[chartId].resize();
                }
            });
        }, 250));
    }
    
    /**
     * Prepare chart containers to ensure they're visible and properly sized
     */
    function prepareChartContainers() {
        // Make sure chart containers are visible and ready
        $('.stats-card, .stats-chart-card').css({
            'opacity': '1',
            'transform': 'translateY(0)',
            'visibility': 'visible',
            'display': 'block'
        });
        
        $('.chart-container').css({
            'position': 'relative',
            'height': '300px',
            'width': '100%',
            'opacity': '1',
            'visibility': 'visible',
            'min-height': '300px',
            'display': 'block',
            'z-index': '10',
            'box-sizing': 'border-box',
            'background-color': '#fff'
        });
    }
    
    /**
     * Simple debounce function to prevent excessive execution
     */
    function debounce(func, wait) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), wait);
        };
    }/**
     * Load statistics data
     */    function loadStats() {
        const user_filter = $('#user-filter').val() || 'all';
        
        // Show loading spinners and reset chart containers
        $('.stats-value').html('<span class="spinner is-active"></span>');
        
        // Reset any existing charts
        $('.chart-container').each(function() {
            const canvasId = $(this).find('canvas').attr('id');
            if (canvasId && chartInstances[canvasId]) {
                try {
                    chartInstances[canvasId].destroy();
                    chartInstances[canvasId] = null;
                } catch(e) {
                    console.warn('Could not reset chart:', e);
                }
            }
        });
        
        $.ajax({
            url: userSubscriptionsAdmin.ajax_url,
            type: 'POST',
            dataType: 'json',
            data: {
                action: 'get_user_subscription_stats',
                nonce: userSubscriptionsAdmin.nonce,
                user_filter: user_filter
            },
            success: function(response) {
                if (response.success) {
                    const stats = response.data;
                    
                    // Popola il dropdown degli utenti se vuoto
                    if ($('#user-filter option').length <= 1) {
                        populateUserFilter(stats.users_list, stats.filtered_user);
                    }
                    
                    // Ensure stats cards are visible
                    $('.stats-card, .stats-chart-card').css({
                        'opacity': '1',
                        'transform': 'translateY(0)',
                        'visibility': 'visible'
                    });
                      
                    // Update summary stats
                    $('#total-users').text(stats.total_users || '0');
                    $('#total-subscription-types').text(stats.total_subscription_types || '0');
                    $('#total-credits').text(parseFloat(stats.total_credits || 0).toFixed(2) + ' €');
                    
                    // Update new summary stats
                    $('#total-analyses').text((stats.total_analyses || 0).toLocaleString());
                    $('#total-tokens').text((stats.total_tokens || 0).toLocaleString());
                    $('#total-cost').text(parseFloat(stats.total_cost || 0).toFixed(2) + ' €');
                    
                    // Calculate and update new average stats
                    var avgCostPerUser = stats.total_users > 0 ? stats.total_cost / stats.total_users : 0;
                    var avgAnalysesPerUser = stats.total_users > 0 ? stats.total_analyses / stats.total_users : 0;
                    var avgTokensPerAnalysis = stats.total_analyses > 0 ? stats.total_tokens / stats.total_analyses : 0;
                    
                    $('#avg-cost-per-user').text(parseFloat(avgCostPerUser).toFixed(2) + ' €');
                    $('#avg-analyses-per-user').text(parseFloat(avgAnalysesPerUser).toFixed(1));
                    $('#avg-tokens-per-analysis').text(parseFloat(avgTokensPerAnalysis).toFixed(0));
                      // Create subscription charts - wrap in setTimeout to ensure DOM is ready
                    // Increased timeout to ensure DOM is fully rendered
                    setTimeout(function() {
                        // Force visibility of chart containers
                        document.querySelectorAll('.chart-container').forEach(container => {
                            container.style.visibility = 'visible';
                            container.style.opacity = '1';
                            container.style.display = 'block';
                        });
                        
                        // Create charts with sequential delays to prevent rendering conflicts
                        createSubscriptionChart('users-by-subscription-chart', stats.users_by_subscription, 'Users');
                        setTimeout(() => {
                            createSubscriptionChart('analyses-by-subscription-chart', stats.analyses_by_subscription, 'Analyses');
                        }, 100);
                        setTimeout(() => {
                            createSubscriptionChart('cost-by-subscription-chart', stats.cost_by_subscription, 'Cost (€)');
                        }, 200);
                    }, 300);
                    
                    // Update top users table
                    updateTopUsersTable(stats.top_users_by_credit);
                } else {
                    // Handle error but keep UI visible
                    $('.stats-value').text('N/A');
                    console.error(userSubscriptionsAdmin.i18n.error);
                }
            },
            error: function() {
                // Handle error but keep UI visible
                $('.stats-value').text('N/A');
                console.error(userSubscriptionsAdmin.i18n.error);
            }
        });
    }
      /**
     * Popola il dropdown del filtro utenti nella dashboard
     */
    function populateUserFilter(users, selectedUser) {
        const dropdown = $('#user-filter');
        
        // Mantieni l'opzione "Tutti gli utenti"
        const allOption = dropdown.find('option[value="all"]');
        dropdown.empty().append(allOption);
        
        // Ordina gli utenti per nome
        users.sort((a, b) => {
            return a.name.localeCompare(b.name) || a.username.localeCompare(b.username);
        });
        
        // Aggiungi le opzioni per ciascun utente
        users.forEach(function(user) {
            const selected = (user.id == selectedUser) ? ' selected' : '';
            dropdown.append('<option value="' + user.id + '"' + selected + '>' + 
                user.username + ' (' + user.name + ')</option>');
        });
    }    /**
     * Create users by subscription chart
     */    function createSubscriptionChart(chartId, data, dataLabel) {
        // Add error handling for data validation
        if (!data || typeof data !== 'object') {
            console.error(`Invalid data provided for chart ${chartId}`);
            return;
        }
        
        // Chart.js is now properly loaded as a dependency
        // Ensure any container visibility issues are addressed
        const chartContainer = document.getElementById(chartId)?.closest('.chart-container');
        if (chartContainer) {
            // Force the container to be visible
            chartContainer.style.visibility = 'visible';
            chartContainer.style.opacity = '1';
            chartContainer.style.display = 'block';
            chartContainer.style.height = '300px';
        }
        
        // Allow a slight delay to ensure DOM is ready
        setTimeout(() => {
            createChartWithData(chartId, data, dataLabel);
        }, 50);
    }
    
    // Store chart instances for later reference/destruction
    const chartInstances = {};
      /**
     * Create chart with provided data
     */    function createChartWithData(chartId, data, dataLabel) {
        // Ottieni l'elemento canvas con migliore gestione errori
        const canvasEl = document.getElementById(chartId);
        
        // Se il canvas non esiste, esci
        if (!canvasEl) {
            console.error('Canvas element not found:', chartId);
            return;
        }
        
        // Force update canvas dimensions to ensure proper rendering
        canvasEl.style.width = '100%';
        canvasEl.style.height = '100%';
        
        // Make sure the container is visible
        const container = canvasEl.closest('.chart-container');
        if (container) {
            container.style.display = 'block';
            container.style.visibility = 'visible';
            container.style.opacity = '1';
        }
        
        // Get 2D context with error handling
        let ctx;
        try {
            ctx = canvasEl.getContext('2d');
            if (!ctx) {
                console.error('Could not get 2D context for canvas:', chartId);
                return;
            }
        } catch (error) {
            console.error('Error getting canvas context:', error);
            return;
        }
        
        // Clear the canvas first to prevent artifacts
        ctx.clearRect(0, 0, canvasEl.width, canvasEl.height);
        
        // Destroy existing chart if it exists
        // Destroy any existing Chart.js instance on this canvas
        const existingChart = Chart.getChart(canvasEl);
        if (existingChart) {
            existingChart.destroy();
        }
        if (chartInstances[chartId]) {
            try {
                chartInstances[chartId].destroy();
                chartInstances[chartId] = null;
            } catch(e) {
                console.warn('Error destroying previous chart:', e);
            }
        }
        
        // Prepare chart data with better validation
        const labels = Object.keys(data || {});
        const values = Object.values(data || {});
        
        // Better data validation - if no data, show message
        if (!data || !values || values.length === 0 || values.every(v => v === null || v === undefined || parseFloat(v) === 0)) {
            // Visualizza un messaggio nel canvas
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillStyle = '#666';
            ctx.fillText('Nessun dato disponibile', canvasEl.width / 2, canvasEl.height / 2);
            return;
        }
        
        // Generate colors with better visibility
        const colors = [
            '#4e79a7', '#f28e2c', '#e15759', '#76b7b2', '#59a14f',
            '#edc949', '#af7aa1', '#ff9da7', '#9c755f', '#bab0ab'
        ];
        
        // Assicurati di avere abbastanza colori
        while (colors.length < labels.length) {
            colors.push(`hsl(${Math.floor(Math.random() * 360)}, 70%, 60%)`);
        }
        
        // Format values with better data handling
        const formattedValues = values.map(v => {
            if (dataLabel === 'Cost (€)') {
                return parseFloat(v || 0).toFixed(2);
            } else {
                return parseInt(v || 0);
            }
        });
          // Verify Chart.js is available before creating chart
        if (typeof Chart === 'undefined') {
            console.error('Chart.js is not available. Cannot create chart.');
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillStyle = '#d63638';
            ctx.fillText('Chart.js non disponibile', canvasEl.width / 2, canvasEl.height / 2);
            return;
        }
        
        // Create chart with improved error handling
        try {
            chartInstances[chartId] = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: labels,
                    datasets: [{
                        label: dataLabel,
                        data: formattedValues,
                        backgroundColor: colors,
                        borderColor: '#ffffff',
                        borderWidth: 1,
                        hoverOffset: 4, // Slightly increase separation on hover
                        borderRadius: 2 // Rounded edges
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    animation: {
                        duration: 1000,
                        easing: 'easeOutQuart'
                    },
                    layout: {
                        padding: 10
                    },
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                boxWidth: 12,
                                font: {
                                    size: 11
                                },
                                color: '#333',
                                padding: 15
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                            titleColor: '#333',
                            bodyColor: '#333',
                            borderColor: '#ccc',
                            borderWidth: 1,
                            padding: 10,
                            displayColors: true,
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw;
                                    const total = context.chart.data.datasets[0].data.reduce((a, b) => parseFloat(a) + parseFloat(b), 0);
                                    const percentage = ((value * 100) / total).toFixed(1);
                                    if (dataLabel === 'Cost (€)') {
                                        return `${label}: ${value} € (${percentage}%)`;
                                    } else {
                                        return `${label}: ${value} (${percentage}%)`;
                                    }
                                }
                            }
                        }
                    } // closes plugins
                } // closes options
            }); // closes new Chart
            
            // Log success for debugging
            console.log(`Chart created successfully: ${chartId}`);
        } catch (error) {
            console.error('Error creating chart:', error);
            // Visualizza un messaggio di errore
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillStyle = '#d63638';
            ctx.fillText('Errore nella creazione del grafico', canvasEl.width / 2, canvasEl.height / 2);
        }
    }
    
    /**
     * Update top users table
     */
    function updateTopUsersTable(users) {
        const tbody = $('#top-users-by-credit');
        tbody.empty();
        
        if (users.length === 0) {
            tbody.html('<tr><td colspan="9">No data available</td></tr>');
            return;
        }
        
        users.forEach(function(user) {
            const row = $('<tr></tr>');
            row.append('<td class="column-username">' + user.username + '</td>');
            row.append('<td class="column-name">' + user.name + '</td>');
            row.append('<td class="column-subscription">' + user.subscription + '</td>');
            row.append('<td class="column-credit">' + parseFloat(user.credit).toFixed(2) + ' €</td>');
            row.append('<td class="column-analysis-count">' + (user.analysis_count || 0) + '</td>');
            row.append('<td class="column-tokens">' + (user.tokens_used || 0).toLocaleString() + '</td>');
            row.append('<td class="column-actual-cost">' + parseFloat(user.actual_cost || 0).toFixed(2) + ' €</td>');
            row.append('<td class="column-total-cost">' + parseFloat(user.tot_cost || 0).toFixed(2) + ' €</td>');
            
            // Format date for better readability
            let formattedDate = '';
            if (user.last_update) {
                try {
                    const date = new Date(user.last_update);
                    formattedDate = date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
                } catch(e) {
                    formattedDate = user.last_update;
                }
            }
            
            row.append('<td class="column-last-update">' + formattedDate + '</td>');
            tbody.append(row);
        });
    }
    
    /**
     * Generate random color for chart
     */
    function getRandomColor() {
        const letters = '0123456789ABCDEF';
        let color = '#';
        for (let i = 0; i < 6; i++) {
            color += letters[Math.floor(Math.random() * 16)];
        }
        return color;
    }
    
    /**
     * =========================================
     * SISTEMA DI FIX PER JQUERY UI DIALOG
     * =========================================
     * Risolve problemi di visualizzazione e l'errore "Cannot read properties of undefined (reading 'length')"
     */
    
    // Sistema globale per la gestione dei fix dei dialog jQuery UI
    window.jQueryDialogFixSystem = {
        // Registro dei dialog aperti
        openDialogs: [],
        
        // Chiamato quando un dialog viene creato
        onDialogCreate: function(dialog) {
            console.log('Dialog creato, applicazione fix');
        },
        
        // Chiamato quando un dialog viene aperto
        onDialogOpen: function(dialog) {
            console.log('Dialog aperto, applicazione fix di visibilità');
            this.applyVisibilityFix(dialog);
            this.openDialogs.push(dialog);
        },
        
        // Chiamato quando un dialog viene chiuso
        onDialogClose: function(dialog) {
            console.log('Dialog chiuso');
            this.openDialogs = this.openDialogs.filter(function(d) {
                return d !== dialog;
            });
        },
        
        // Applica fix di visibilità
        applyVisibilityFix: function(dialog) {
            try {
                var $dialog = $(dialog).closest('.ui-dialog');
                if ($dialog.length) {
                    $dialog.css({
                        'display': 'block',
                        'visibility': 'visible',
                        'z-index': '100001'
                    });
                    
                    // Fix overlay
                    $('.ui-widget-overlay').css({
                        'display': 'block',
                        'visibility': 'visible',
                        'z-index': '100000'
                    });
                }
            } catch (e) {
                console.error('Errore nell\'applicazione del fix di visibilità:', e);
            }
        }
    };
    
    // Inizializza il sistema di fix per jQuery UI Dialog
    $(document).ready(function() {
        console.log('jQuery UI Dialog fix caricato - v1.0.2');
        
        // Aggiungi stile direttamente al DOM per garantire che sovrascriva tutto
        if (!$('#jquery-ui-style-fix').length) {
            $('head').append(`
                <style id="jquery-ui-style-fix">
                    .ui-dialog {
                        display: block !important;
                        z-index: 100001 !important;
                        position: fixed !important;
                    }
                    .ui-widget-overlay {
                        display: block !important;
                        z-index: 100000 !important;
                        position: fixed !important;
                        top: 0 !important;
                        right: 0 !important;
                        bottom: 0 !important;
                        left: 0 !important;
                    }
                    .ui-dialog .ui-dialog-titlebar-close {
                        display: block !important;
                        position: absolute !important;
                        right: 5px !important;
                        top: 5px !important;
                    }
                    .ui-button {
                        display: inline-block !important;
                    }
                    #subscription-type-modal {
                        display: none;
                        padding: 0 !important;
                    }
                    /* Fix for z-index issues */
                    #wpwrap {
                        z-index: auto !important;
                    }
                    /* Fix for overlay issues */
                    #wpbody-content {
                        position: static !important;
                    }
                    /* Admin bar fix */
                    #wpadminbar {
                        z-index: 99999 !important;
                    }
                </style>
            `);
        }
        
        // Patch per evitare errori di "Cannot read properties of undefined (reading 'length')"
        // RIMOSSO: Non ridefinire $.fn.length, causa errori con jQuery e plugin
    });
    
    /**
     * =========================================
     * SISTEMA DI FALLBACK PER DIALOG
     * =========================================
     * Fornisce un'alternativa quando jQuery UI Dialog non è disponibile o fallisce
     */
     
    // Riferimento al sistema DialogFallback (già definito nell'altra sezione)
    $(document).ready(function() {
        console.log('Dialog Fallback System loaded - v2.0.0');
        window.DialogFallback.init();
        
        // Controlla se ci sono dialog esistenti
        const subscriptionTypeModal = $('#subscription-type-modal');
        if (subscriptionTypeModal.length) {
            console.log('Trovato #subscription-type-modal, inizializzazione fallback');
            // Prepara il dialog per il fallback
            subscriptionTypeModal.css({
                'border': '1px solid #ccc',
                'background-color': '#fff',
                'padding': '20px'
            });
        }
    });

    /**
     * =========================================
     * SISTEMA DI DEBUG PER DIALOG
     * =========================================
     * Fornisce strumenti diagnostici per jQuery UI Dialog (solo in modalità WP_DEBUG)
     */
    
    // Esegui solo in modalità debug
    if (typeof WP_DEBUG !== 'undefined' && WP_DEBUG) {
        // Sistema globale di debug
        window.DialogDebug = {
            // Flag di inizializzazione
            _initialized: false,
            
            // Riferimento ai sistemi integrati
            _fallbackSystem: null,
            _fixSystem: null,
            
            // Inizializzazione del sistema
            init: function() {
                if (this._initialized) return;
                
                // Informazioni sul browser e ambiente
                this.logEnvironmentInfo();
                
                // Verifica jQuery UI e suoi componenti
                this.checkJQueryUI();
                
                // Crea pulsante di test
                this.createTestButton();
                
                // Integra con altri sistemi
                this.integrateWithSystems();
                
                // Monitora errori di jQuery UI
                this.monitorJQueryUIErrors();
                
                this._initialized = true;
                return this;
            },
            
            // Registra informazioni sull'ambiente
            logEnvironmentInfo: function() {
                console.log('=== DIALOG DEBUG: ENVIRONMENT INFO ===');
                console.log('User Agent: ' + navigator.userAgent);
                console.log('jQuery version: ' + $.fn.jquery);
                console.log('WordPress version: ' + (typeof wp !== 'undefined' && wp.version ? wp.version : 'N/A'));
                console.log('Debug mode: ' + (typeof WP_DEBUG !== 'undefined' && WP_DEBUG ? 'Enabled' : 'Disabled'));
            },
            
            // Verifica jQuery UI e i suoi componenti
            checkJQueryUI: function() {
                console.log('=== DIALOG DEBUG: JQUERY UI CHECK ===');
                if (typeof $.ui !== 'undefined') {
                    console.log('✓ jQuery UI is loaded - version: ' + $.ui.version);
                    
                    // Verifica componenti specifici di jQuery UI
                    var uiComponents = ['dialog', 'tabs', 'datepicker', 'button', 'draggable', 'resizable'];
                    uiComponents.forEach(function(component) {
                        if (typeof $.ui[component] !== 'undefined') {
                            console.log('✓ jQuery UI ' + component + ' è disponibile');
                        } else {
                            console.error('✗ jQuery UI ' + component + ' NON è disponibile');
                        }
                    });
                } else {
                    console.error('✗ jQuery UI non è caricato correttamente!');
                }
            },
            
            // Crea pulsante di test
            createTestButton: function() {
                const self = this;
                
                // Aggiungi un pulsante di test per verificare la funzionalità dei dialog
                $('body').append(
                    '<div id="dialog-debug-tools" style="position: fixed; bottom: 10px; right: 10px; z-index: 9999; background-color: #f5f5f5; border: 1px solid #ddd; padding: 10px; border-radius: 5px; box-shadow: 0 0 5px rgba(0,0,0,0.2);">' +
                    '<h3 style="margin: 0 0 10px; padding: 0; font-size: 14px;">Dialog Debug Tools</h3>' +
                    '<button id="test-dialog-btn" style="background-color: #007cba; color: white; border: none; padding: 8px 10px; cursor: pointer; margin-right: 5px;">Test Dialog</button>' +
                    '<button id="test-fallback-btn" style="background-color: #46b450; color: white; border: none; padding: 8px 10px; cursor: pointer;">Test Fallback</button>' +
                    '</div>'
                );
                
                // Crea un dialog di test
                $('body').append('<div id="test-dialog" title="Test Dialog" style="display:none;"><p>If you can see this message, jQuery UI Dialog is working correctly!</p></div>');
                
                // Collega l'evento di click per test dialog normale
                $('#test-dialog-btn').on('click', function() {
                    console.log('Opening test dialog');
                    try {
                        $('#test-dialog').dialog({
                            modal: true,
                            width: 400,
                            buttons: {
                                'Close': function() {
                                    $(this).dialog('close');
                                }
                            }
                        });
                        console.log('Dialog opened successfully');
                        self.checkZIndex();
                    } catch (e) {
                        console.error('Error opening dialog:', e);
                        alert('Error opening dialog: ' + e.message);
                    }
                });
                
                // Collega l'evento di click per test fallback
                $('#test-fallback-btn').on('click', function() {
                    console.log('Testing fallback dialog');
                    // Forza l'uso del fallback
                    if (window.DialogFallback) {
                        const fallbackDialog = window.DialogFallback.openFallbackDialog('#test-dialog', {
                            modal: true,
                            width: 400,
                            title: 'Test Fallback Dialog',
                            buttons: {
                                'Close': function() {
                                    window.DialogFallback.closeFallbackDialog($(this));
                                }
                            }
                        });
                        console.log('Fallback dialog opened successfully', fallbackDialog);
                    } else {
                        alert('Dialog Fallback System not available!');
                    }
                });
            },
            
            // Controlla gli z-index per possibili conflitti
            checkZIndex: function() {
                const dialogOverlay = $('.ui-widget-overlay');
                if (dialogOverlay.length) {
                    console.log('Dialog overlay z-index:', dialogOverlay.css('z-index'));
                    
                    // Controlla se ci sono elementi con z-index più alto del dialog
                    const allElements = $('*').filter(function() {
                        const zIndex = parseInt($(this).css('z-index'));
                        return !isNaN(zIndex) && zIndex > parseInt(dialogOverlay.css('z-index'));
                    });
                    
                    if (allElements.length) {
                        console.warn('Found elements with higher z-index than dialog overlay:', allElements);
                    }
                }
            },
            
            // Integra con altri sistemi
            integrateWithSystems: function() {
                console.log('=== DIALOG DEBUG: INTEGRATING WITH OTHER SYSTEMS ===');
                
                // Cerca il sistema di fallback
                if (typeof window.DialogFallback !== 'undefined') {
                    console.log('✓ Dialog Fallback System trovato e disponibile');
                } else {
                    console.warn('✗ Dialog Fallback System non trovato!');
                }
                
                // Cerca il sistema di fix
                if (typeof window.jQueryDialogFixSystem !== 'undefined') {
                    console.log('✓ jQuery Dialog Fix System trovato e disponibile');
                } else {
                    console.warn('✗ jQuery Dialog Fix System non trovato!');
                }
            },
            
            // Registra il sistema di fallback
            registerFallbackSystem: function(fallbackSystem) {
                this._fallbackSystem = fallbackSystem;
                console.log('Fallback system registered with DialogDebug');
                
                // Monitoraggio eventi di fallback
                if (fallbackSystem._openDialogs) {
                    const originalOpenFallback = fallbackSystem.openFallbackDialog;
                    const self = this;
                    
                    fallbackSystem.openFallbackDialog = function() {
                        const result = originalOpenFallback.apply(fallbackSystem, arguments);
                        console.log('Dialog opened using fallback system', result);
                        self.logDialogEvent('fallback-open', arguments[0], arguments[1]);
                        return result;
                    };
                }
            },
            
            // Registra il sistema di fix
            registerFixSystem: function(fixSystem) {
                this._fixSystem = fixSystem;
                console.log('Fix system registered with DialogDebug');
            },
            
            // Monitora gli errori di jQuery UI
            monitorJQueryUIErrors: function() {
                const originalDialogFn = $.fn.dialog;
                const self = this;
                
                if (originalDialogFn) {
                    $.fn.dialog = function() {
                        try {
                            const result = originalDialogFn.apply(this, arguments);
                            return result;
                        } catch (e) {
                            console.error('jQuery UI Dialog error captured:', e);
                            self.logDialogEvent('error', this, arguments[0], e);
                            
                            // Se è disponibile il fallback, prova ad usarlo
                            if (typeof window.DialogFallback !== 'undefined' && window.DialogFallback.openFallbackDialog) {
                                console.warn('Trying fallback dialog after error');
                                return window.DialogFallback.openFallbackDialog(this, arguments[0]);
                            }
                            
                            throw e;
                        }
                    };
                }
            },
            
            // Registra eventi di dialog per debug
            logDialogEvent: function(eventType, element, options, error) {
                const eventData = {
                    timestamp: new Date().toISOString(),
                    eventType: eventType,
                    elementId: element && $(element).attr('id'),
                    elementClass: element && $(element).attr('class'),
                    options: options,
                    error: error
                };
                
                console.log('Dialog Event:', eventData);
                
                // Se siamo in modalità debug avanzato, invia i dati al server
                if (typeof WP_DEBUG !== 'undefined' && WP_DEBUG && typeof wp !== 'undefined' && wp.ajax) {
                    wp.ajax.post('dialog_debug_log', {
                        event_data: JSON.stringify(eventData),
                        nonce: typeof dialogDebugNonce !== 'undefined' ? dialogDebugNonce : ''
                    });
                }
            }
        };
        
        // Inizializza il sistema di debug
        $(document).ready(function() {
            console.log('Dialog Debug Script Loaded - v2.0.0');
            window.DialogDebug.init();
        });
    }
    
} // Chiude function initializeModule

// Avvia il modulo con jQuery
initializeModule(jQuery);
