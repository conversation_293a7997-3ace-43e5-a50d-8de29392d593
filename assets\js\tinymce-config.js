/**
 * TinyMCE custom configuration to avoid document.write issues
 */
(function() {
  // Check if we need to configure TinyMCE
  if (typeof window.tinymce === 'undefined') return;
  
  // Store the original tinyMCE init function
  var originalInit = window.tinymce.init;
  
  // Override the init function to add our custom configuration
  window.tinymce.init = function(settings) {
    // Force TinyMCE to use more modern DOM methods instead of document.write
    settings.cache_suffix = '?' + new Date().getTime();
    settings.inline = settings.inline || false;
    
    // Add settings to reduce document.write usage
    settings.entity_encoding = 'raw';
    settings.verify_html = false;
    
    // Delay iframe loading to avoid document.write blocking
    settings.init_instance_callback = function(editor) {
      // Allow the editor to finish initialization before any operations
      setTimeout(function() {
        // Trigger a content refresh to ensure proper rendering
        editor.setContent(editor.getContent());
        
        // Execute the original callback if it exists
        if (settings.originalInitCallback) {
          settings.originalInitCallback(editor);
        }
      }, 0);
    };
    
    // Store original callback
    if (settings.init_instance_callback && typeof settings.init_instance_callback === 'function') {
      settings.originalInitCallback = settings.init_instance_callback;
    }
    
    // Call the original init with our modified settings
    return originalInit.call(window.tinymce, settings);
  };
})();
