# QUICK TEST COMMANDS
# Comandi rapidi per esecuzione test Office Add-in

## NAVIGAZIONE
```powershell
# Vai alla directory del progetto
cd "c:\Users\<USER>\Desktop\financial-advisor-V4"
```

## TEST INDIVIDUALI

### CacheManager Tests
```powershell
php .\vendor\bin\phpunit --configuration phpunit-simple.xml tests\Unit\CacheManagerCorrectedTest.php --verbose
```

### ErrorReporter Tests  
```powershell
php .\vendor\bin\phpunit --configuration phpunit-simple.xml tests\Unit\ErrorReporterFixedTest.php --verbose
```

### PerformanceMonitor Tests
```powershell
php .\vendor\bin\phpunit --configuration phpunit-simple.xml tests\Unit\PerformanceMonitorFixedTest.php --verbose
```

## SCRIPT AUTOMATIZZATO

### Tutti i Test
```powershell
.\run-all-tests.ps1
```

### Test Specifico con Verbose
```powershell
.\run-all-tests.ps1 -TestType cache -Verbose
.\run-all-tests.ps1 -TestType error -Verbose  
.\run-all-tests.ps1 -TestType performance -Verbose
```

### Stop al Primo Errore
```powershell
.\run-all-tests.ps1 -StopOnFailure
```

## COMANDI DEBUG

### Test con Output Dettagliato
```powershell
php .\vendor\bin\phpunit --configuration phpunit-simple.xml tests\Unit\CacheManagerCorrectedTest.php --verbose --debug
```

### Test Singolo Metodo
```powershell
php .\vendor\bin\phpunit --configuration phpunit-simple.xml tests\Unit\CacheManagerCorrectedTest.php --filter testSetAndGetCache
```

### Stop al Primo Errore
```powershell
php .\vendor\bin\phpunit --configuration phpunit-simple.xml tests\Unit\ErrorReporterFixedTest.php --stop-on-failure
```

## VALIDAZIONE RAPIDA

### Check PHPUnit
```powershell
php .\vendor\bin\phpunit --version
```

### Check PHP
```powershell
php --version
```

### Verifica File Test
```powershell
ls tests\Unit\*Fixed*Test.php
ls tests\Unit\*Corrected*Test.php
```

## RISULTATI ATTESI

### CacheManager (CacheManagerCorrectedTest.php)
```
Tests: 15, Assertions: 132, Skipped: 1
✅ 14 passing, ⚠️ 1 skipped (testCacheStatistics)
```

### ErrorReporter (ErrorReporterFixedTest.php)  
```
Tests: 15, Assertions: 59
✅ 15 passing
```

### PerformanceMonitor (PerformanceMonitorFixedTest.php)
```
Tests: 24, Assertions: 97
✅ 24 passing
```

### TOTALE ATTESO
```
📊 GRAND TOTAL: 54 tests, 288 assertions
✅ Success Rate: 98.1% (53/54 passing)
⚠️ 1 test skipped (acceptable)
```

## TROUBLESHOOTING RAPIDO

### Se PHPUnit non trova i file:
```powershell
# Verifica path corretti
ls tests\Unit\
ls vendor\bin\phpunit*
```

### Se class not found:
```powershell
# Verifica include paths nel bootstrap
cat tests\bootstrap-simple.php
```

### Se metodi non esistono:
```powershell
# Controlla che stai usando i file corretti
# ✅ Usa: *FixedTest.php e *CorrectedTest.php  
# ❌ Non usare: Test.php originali
```

## PULIZIA CACHE TEST

### Reset completo ambiente test
```powershell
# Nel codice PHP
\TestOptions::clear();
\TestCache::clear();
```

## LOG E DEBUG

### Visualizza errori dettagliati
```powershell
php .\vendor\bin\phpunit --configuration phpunit-simple.xml tests\Unit\ErrorReporterFixedTest.php 2>&1 | Tee-Object -FilePath "test-debug.log"
```

### Controlla log test automatico
```powershell
ls test-results-*.log | Sort-Object LastWriteTime -Descending | Select-Object -First 1
```
