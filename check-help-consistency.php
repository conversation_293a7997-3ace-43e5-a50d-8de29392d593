<?php
/**
 * Script per verificare la consistenza del sistema Help
 * Verifica database, configurazione UI e integrazione
 */

// Prevenire accesso diretto
if (!defined('ABSPATH')) {
    exit('Accesso diretto non consentito');
}

echo '<div style="font-family: Arial, sans-serif; margin: 20px; background: #f9f9f9; padding: 20px; border-radius: 8px;">';
echo '<h1 style="color: #2271b1;">🔍 Verifica Consistenza Sistema Help</h1>';

// 1. Verifica opzioni database
echo '<h2>📊 Stato Database</h2>';
echo '<div style="background: white; padding: 15px; border-radius: 5px; margin: 10px 0;">';

$content_option = get_option('widget_help_content', array());
$settings_option = get_option('widget_help_widget_settings', array());
$legacy_option = get_option('widget_help_contents', array());

echo '<h3>Opzione "widget_help_content":</h3>';
if (empty($content_option)) {
    echo '<p style="color: #d63638;">❌ VUOTA - Nessun contenuto salvato</p>';
} else {
    echo '<p style="color: #00a32a;">✅ PRESENTE - ' . count($content_option) . ' widget configurati</p>';
    echo '<details><summary>Dettagli</summary><pre>' . print_r($content_option, true) . '</pre></details>';
}

echo '<h3>Opzione "widget_help_widget_settings":</h3>';
if (empty($settings_option)) {
    echo '<p style="color: #d63638;">❌ VUOTA - Nessuna impostazione salvata</p>';
} else {
    echo '<p style="color: #00a32a;">✅ PRESENTE - ' . count($settings_option) . ' widget configurati</p>';
    echo '<details><summary>Dettagli</summary><pre>' . print_r($settings_option, true) . '</pre></details>';
}

echo '<h3>Opzione "widget_help_contents" (legacy):</h3>';
if (empty($legacy_option)) {
    echo '<p style="color: #72aee6;">ℹ️ VUOTA - Nessun dato legacy</p>';
} else {
    echo '<p style="color: #dba617;">⚠️ PRESENTE - Dati legacy da migrare</p>';
    echo '<details><summary>Dettagli</summary><pre>' . print_r($legacy_option, true) . '</pre></details>';
}

echo '</div>';

// 2. Verifica classi sistema
echo '<h2>🔧 Stato Classi Sistema</h2>';
echo '<div style="background: white; padding: 15px; border-radius: 5px; margin: 10px 0;">';

$classes_to_check = array(
    'Widget_Help_System' => 'includes/class-widget-help-system.php',
    'Widget_Help_Admin' => 'includes/class-widget-help-admin.php',
    'Widget_Help_Manager' => 'includes/class-widget-help-manager.php',
    'Menu_Manager' => 'includes/class-menu-manager.php'
);

foreach ($classes_to_check as $class_name => $file_path) {
    if (class_exists($class_name)) {
        echo '<p style="color: #00a32a;">✅ ' . $class_name . ' - Caricata</p>';
    } else {
        echo '<p style="color: #d63638;">❌ ' . $class_name . ' - Non caricata</p>';
        if (file_exists(plugin_dir_path(__FILE__) . $file_path)) {
            echo '<p style="margin-left: 20px; color: #72aee6;">ℹ️ File presente: ' . $file_path . '</p>';
        } else {
            echo '<p style="margin-left: 20px; color: #d63638;">❌ File mancante: ' . $file_path . '</p>';
        }
    }
}

echo '</div>';

// 3. Verifica widget registrati
echo '<h2>📋 Widget Registrati</h2>';
echo '<div style="background: white; padding: 15px; border-radius: 5px; margin: 10px 0;">';

if (class_exists('Widget_Help_System')) {
    try {
        global $widget_help_system;
        if (!$widget_help_system) {
            $widget_help_system = new Widget_Help_System();
        }
        
        $widgets = $widget_help_system->get_all_widgets();
        if (empty($widgets)) {
            echo '<p style="color: #d63638;">❌ Nessun widget registrato</p>';
        } else {
            echo '<p style="color: #00a32a;">✅ ' . count($widgets) . ' widget registrati</p>';
            echo '<ul>';
            foreach ($widgets as $widget_id => $config) {
                $status = $config['enabled'] ? '🟢 Attivo' : '🔴 Disattivo';
                echo '<li><strong>' . $widget_id . '</strong>: ' . $config['name'] . ' - ' . $status . '</li>';
            }
            echo '</ul>';
        }
    } catch (Exception $e) {
        echo '<p style="color: #d63638;">❌ Errore nell\'inizializzazione: ' . $e->getMessage() . '</p>';
    }
} else {
    echo '<p style="color: #d63638;">❌ Widget_Help_System non disponibile</p>';
}

echo '</div>';

// 4. Verifica menu admin
echo '<h2>🎛️ Stato Menu Amministrativo</h2>';
echo '<div style="background: white; padding: 15px; border-radius: 5px; margin: 10px 0;">';

// Verifica se il menu è registrato
global $menu, $submenu;

$fa_menu_found = false;
$help_submenu_found = false;

if (isset($menu)) {
    foreach ($menu as $menu_item) {
        if (isset($menu_item[2]) && strpos($menu_item[2], 'financial-advisor') !== false) {
            $fa_menu_found = true;
            echo '<p style="color: #00a32a;">✅ Menu principale Financial Advisor trovato</p>';
            break;
        }
    }
}

if (isset($submenu)) {
    foreach ($submenu as $parent_slug => $submenus) {
        foreach ($submenus as $submenu_item) {
            if (isset($submenu_item[2]) && (strpos($submenu_item[2], 'widget-help') !== false || strpos($submenu_item[2], 'fa-widget-help') !== false)) {
                $help_submenu_found = true;
                echo '<p style="color: #00a32a;">✅ Sottomenu Widget Help trovato sotto: ' . $parent_slug . '</p>';
                break 2;
            }
        }
    }
}

if (!$fa_menu_found) {
    echo '<p style="color: #dba617;">⚠️ Menu principale Financial Advisor non trovato</p>';
}

if (!$help_submenu_found) {
    echo '<p style="color: #d63638;">❌ Sottomenu Widget Help non trovato</p>';
}

echo '</div>';

// 5. Verifica costanti e definizioni
echo '<h2>⚙️ Configurazione Sistema</h2>';
echo '<div style="background: white; padding: 15px; border-radius: 5px; margin: 10px 0;">';

$constants_to_check = array(
    'FA_MENU_MANAGER_ACTIVE' => 'Menu Manager attivo',
    'DOCUMENT_VIEWER_DEBUG' => 'Debug mode',
    'ABSPATH' => 'WordPress root'
);

foreach ($constants_to_check as $constant => $description) {
    if (defined($constant)) {
        $value = constant($constant);
        echo '<p style="color: #00a32a;">✅ ' . $constant . ' (' . $description . '): ' . ($value ? 'true' : 'false') . '</p>';
    } else {
        echo '<p style="color: #72aee6;">ℹ️ ' . $constant . ' (' . $description . '): Non definita</p>';
    }
}

echo '</div>';

// 6. Raccomandazioni
echo '<h2>💡 Raccomandazioni</h2>';
echo '<div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 10px 0;">';

$issues = array();

if (empty($content_option) && empty($settings_option)) {
    $issues[] = 'Database vuoto - Inizializzare il sistema con contenuti predefiniti';
}

if (!empty($legacy_option) && empty($content_option)) {
    $issues[] = 'Migrare i dati legacy alla nuova struttura';
}

if (!class_exists('Widget_Help_System')) {
    $issues[] = 'Caricare la classe Widget_Help_System';
}

if (!$help_submenu_found) {
    $issues[] = 'Registrare il menu amministrativo per Widget Help';
}

if (empty($issues)) {
    echo '<p style="color: #00a32a;">✅ Sistema configurato correttamente!</p>';
} else {
    echo '<h3>Problemi rilevati:</h3>';
    echo '<ol>';
    foreach ($issues as $issue) {
        echo '<li>' . $issue . '</li>';
    }
    echo '</ol>';
}

echo '</div>';

echo '</div>';
?>
