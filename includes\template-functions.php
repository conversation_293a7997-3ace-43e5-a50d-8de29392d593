<?php
/**
 * Template Functions for Financial Advisor
 * 
 * Funzioni di supporto per i template delle pagine frontend
 * 
 * @package Financial_Advisor
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Controlla se l'utente può accedere alle pagine riservate del frontend
 * e reindirizza alla pagina di login in caso negativo
 * 
 * Da usare all'inizio dei template delle pagine riservate.
 * 
 * Esempio:
 * <?php fa_restrict_page_access(); ?>
 * 
 * @param bool $redirect Se true, reindirizza alla pagina di login
 * @return bool True se l'utente può accedere, false altrimenti
 */
function fa_restrict_page_access($redirect = true) {
    $can_access = fa_user_can_access('frontend');
    
    if (!$can_access && $redirect) {
        // Recupera la URL di login dalle opzioni o usa la home
        $login_url = get_option('login_page_url', home_url());
        
        // Aggiungi parametro di redirect per tornare alla pagina corrente dopo il login
        $login_url = add_query_arg('redirect_to', urlencode($_SERVER['REQUEST_URI']), $login_url);
        
        // Reindirizza
        wp_redirect($login_url);
        exit;
    }
    
    return $can_access;
}

/**
 * Restituisce l'HTML per un messaggio di accesso negato
 * 
 * @return string HTML del messaggio di accesso negato
 */
function fa_get_access_denied_message() {
    ob_start();
    ?>
    <div class="fa-access-denied">
        <h3><?php _e('Accesso negato', 'document-viewer-plugin'); ?></h3>
        <p><?php _e('Non hai il permesso di accedere a questa pagina.', 'document-viewer-plugin'); ?></p>
        <p>
            <a href="<?php echo esc_url(home_url()); ?>" class="button">
                <?php _e('Torna alla home', 'document-viewer-plugin'); ?>
            </a>
        </p>
    </div>
    <?php
    return ob_get_clean();
}

/**
 * Ottiene il tipo di sottoscrizione dell'utente corrente
 * 
 * @return string|null Tipo di sottoscrizione o null se non è un sottoscrittore
 */
function fa_get_current_subscription_type() {
    $subscriber_data = fa_get_subscriber_data();
    
    if (!$subscriber_data) {
        return null;
    }
    
    return isset($subscriber_data['type']) ? $subscriber_data['type'] : null;
}

/**
 * Controlla se l'utente corrente ha un tipo di sottoscrizione specifico
 * 
 * @param string|array $types Tipo o tipi di sottoscrizione da verificare
 * @return bool True se l'utente ha uno dei tipi specificati
 */
function fa_user_has_subscription_type($types) {
    $current_type = fa_get_current_subscription_type();
    
    if (!$current_type) {
        return false;
    }
    
    if (is_array($types)) {
        return in_array($current_type, $types);
    }
    
    return $current_type === $types;
}
