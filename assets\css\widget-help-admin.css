.widget-help-widgets-manager {
    margin-top: 20px;
}

.widget-help-toggle {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.widget-help-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #2196F3;
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.widget-help-content-editor {
    margin-top: 20px;
}

.widget-selector {
    margin-bottom: 20px;
    padding: 15px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.widget-selector label {
    display: inline-block;
    margin-right: 10px;
    font-weight: bold;
}

.widget-selector select {
    min-width: 200px;
}

.content-sections {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.section-tabs {
    display: flex;
    background: #f1f1f1;
    border-bottom: 1px solid #ddd;
}

.section-tab {
    background: none;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    border-right: 1px solid #ddd;
    transition: background-color 0.3s;
}

.section-tab:hover {
    background: #e0e0e0;
}

.section-tab.active {
    background: #fff;
    border-bottom: 1px solid #fff;
    position: relative;
    z-index: 1;
}

.section-tab:last-child {
    border-right: none;
}

.section-content {
    padding: 20px;
}

.tab-content {
    margin-top: 20px;
}

/* Widget table styles */
.wp-list-table .widget-enabled-toggle {
    margin: 0;
}

.wp-list-table code {
    background: #f1f1f1;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
}

.edit-help-content {
    font-size: 12px;
}

/* Responsive admin layout */
@media (max-width: 768px) {
    .section-tabs {
        flex-wrap: wrap;
    }
    
    .section-tab {
        flex: 1;
        text-align: center;
        border-right: none;
        border-bottom: 1px solid #ddd;
    }
    
    .section-tab:last-child {
        border-bottom: none;
    }
    
    .widget-selector {
        text-align: center;
    }
    
    .widget-selector label {
        display: block;
        margin-bottom: 10px;
    }
    
    .widget-selector select {
        width: 100%;
        max-width: 300px;
    }
}

/* Enhanced form styling */
.form-table th {
    width: 200px;
}

.form-table td input[type="checkbox"] {
    margin-right: 8px;
}

.form-table td select {
    min-width: 150px;
}

/* Notice positioning */
.notice {
    margin: 20px 0;
}

/* Loading states */
.saving-content {
    opacity: 0.6;
    pointer-events: none;
}

.saving-content::after {
    content: " (Salvando...)";
    color: #666;
    font-style: italic;
}

/* Preview button styling */
.preview-help-content {
    margin-left: 10px;
}

/* Widget status indicators */
.widget-status-enabled {
    color: #46b450;
    font-weight: bold;
}

.widget-status-disabled {
    color: #dc3232;
    font-weight: bold;
}

/* Accessibility improvements */
.section-tab:focus {
    outline: 2px solid #0073aa;
    outline-offset: -2px;
}

.toggle-slider:focus-within {
    box-shadow: 0 0 0 2px #0073aa;
}

.edit-help-content:focus {
    box-shadow: 0 0 0 2px #0073aa;
}
