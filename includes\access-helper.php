<?php
/**
 * Access Helper Functions
 * 
 * Funzioni di supporto per il controllo degli accessi
 * 
 * @package Financial_Advisor
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Verifica se l'utente corrente può accedere a una pagina o funzionalità
 * 
 * @param string $area Area o funzionalità ('frontend', 'admin', ecc.)
 * @return bool True se l'utente può accedere, false altrimenti
 */
function fa_user_can_access($area = 'frontend') {
    return apply_filters('fa_user_can_access', false, $area);
}

/**
 * Verifica se l'utente corrente è un sottoscrittore
 * 
 * @return bool True se l'utente è un sottoscrittore
 */
function fa_is_subscriber() {
    if (function_exists('fa_access_control')) {
        return fa_access_control()->is_current_user_subscriber();
    }
    
    // Fallback se il controllo accessi non è disponibile
    return isset($_COOKIE['fa_subscriber_login']);
}

/**
 * <PERSON><PERSON><PERSON> i dati del sottoscrittore corrente
 * 
 * @return array|null Dati del sottoscrittore o null se non è un sottoscrittore
 */
function fa_get_subscriber_data() {
    if (function_exists('fa_access_control')) {
        return fa_access_control()->get_current_subscriber_data();
    }
    
    // Fallback se il controllo accessi non è disponibile
    if (!isset($_COOKIE['fa_subscriber_login'])) {
        return null;
    }
    
    try {
        return json_decode(base64_decode($_COOKIE['fa_subscriber_login']), true);
    } catch (Exception $e) {
        return null;
    }
}

/**
 * Restringe l'accesso a una pagina solo agli utenti autorizzati
 * Mostra un messaggio di errore e reindirizza alla home gli utenti non autorizzati
 * 
 * @param string $area Area o funzionalità ('frontend', 'admin', ecc.)
 * @param string $redirect_url URL a cui reindirizzare (default: home)
 * @return void
 */
function fa_restrict_access($area = 'frontend', $redirect_url = '') {
    if (!fa_user_can_access($area)) {
        $message = __('Non hai il permesso di accedere a questa pagina.', 'document-viewer-plugin');
        
        if (empty($redirect_url)) {
            $redirect_url = home_url();
        }
        
        // Se è una richiesta AJAX
        if (wp_doing_ajax()) {
            wp_send_json_error(['message' => $message]);
            exit;
        }
        
        // Altrimenti reindirizza con un messaggio
        wp_die(
            $message,
            __('Accesso negato', 'document-viewer-plugin'),
            [
                'response' => 403,
                'back_link' => true,
            ]
        );
    }
}
