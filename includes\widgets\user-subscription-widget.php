<?php
if (!defined('ABSPATH')) {
    exit;
}

/**
 * User Subscription Widget
 *
 * Widget per permettere agli utenti di registrarsi al servizio
 */
class User_Subscription_Widget extends WP_Widget {
    public function __construct() {
        parent::__construct(
            'user_subscription_widget',
            __('User Subscription Widget', 'document-viewer-plugin'),
            array('description' => __('Un widget per permettere agli utenti di registrarsi al servizio.', 'document-viewer-plugin'))
        );

        // Aggiungi script e stili
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
    }

    /**
     * Carica gli script e gli stili necessari
     */
    public function enqueue_scripts() {
        // Carica il CSS con priorità alta per evitare sovrascritture
        wp_enqueue_style(
            'user-subscription-widget-css',
            plugin_dir_url(dirname(dirname(__FILE__))) . 'assets/css/user-subscription-widget.css',
            array(),
            '2.0.0', // Incrementa la versione per forzare il refresh
            'all'
        );

        // Carica Font Awesome se non già presente
        if (!wp_style_is('font-awesome', 'enqueued')) {
            wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css', array(), '6.0.0');
        }

        wp_enqueue_script(
            'user-subscription-widget-js',
            plugin_dir_url(dirname(dirname(__FILE__))) . 'assets/js/user-subscription-widget.js',
            array('jquery'),
            '2.0.0',
            true
        );

        wp_localize_script('user-subscription-widget-js', 'userSubscriptionAjax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('document_viewer_nonce'),
            'messages' => array(
                'required' => __('Campo obbligatorio', 'document-viewer-plugin'),
                'email_invalid' => __('Email non valida', 'document-viewer-plugin'),
                'password_short' => __('La password deve contenere almeno 8 caratteri', 'document-viewer-plugin'),
                'password_match' => __('Le password non corrispondono', 'document-viewer-plugin'),
                'password_strength' => __('La password deve contenere almeno un numero, una lettera maiuscola e un carattere speciale', 'document-viewer-plugin'),
                'success' => __('Registrazione completata con successo!', 'document-viewer-plugin'),
                'error' => __('Errore durante la registrazione. Riprova.', 'document-viewer-plugin')
            )
        ));

        // Aggiungi CSS inline per garantire la corretta applicazione degli stili
        $this->add_inline_styles();
    }

    /**
     * Aggiunge stili inline per garantire la corretta visualizzazione
     */
    private function add_inline_styles() {
        $custom_css = "
        /* Stili critici per il widget user subscription */
        .user-subscription-widget-container {
            max-width: 100% !important;
            margin: 20px 0 !important;
            padding: 0 !important;
            background: #fff !important;
            border: 1px solid #e1e1e1 !important;
            border-radius: 8px !important;
            box-shadow: 0 3px 10px rgba(0,0,0,0.08) !important;
            display: flex !important;
            flex-wrap: wrap !important;
            gap: 0 !important;
            position: relative !important;
            overflow: hidden !important;
            min-height: 500px !important;
        }

        .user-subscription-form input[type='text'],
        .user-subscription-form input[type='email'],
        .user-subscription-form input[type='tel'],
        .user-subscription-form input[type='password'],
        .user-subscription-form select {
            width: 100% !important;
            padding: 12px 15px !important;
            border: 2px solid #e1e1e1 !important;
            border-radius: 6px !important;
            font-size: 0.95rem !important;
            color: #333 !important;
            background-color: #fff !important;
            transition: all 0.3s ease !important;
            box-sizing: border-box !important;
        }

        .user-subscription-form .submit-button {
            padding: 15px 25px !important;
            background: linear-gradient(135deg, #0073aa 0%, #005177 100%) !important;
            color: white !important;
            border: none !important;
            border-radius: 6px !important;
            font-size: 16px !important;
            font-weight: 600 !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
            text-transform: uppercase !important;
            letter-spacing: 0.5px !important;
            box-shadow: 0 4px 15px rgba(0, 115, 170, 0.3) !important;
            width: 100% !important;
            position: relative !important;
            overflow: hidden !important;
        }
        ";

        wp_add_inline_style('user-subscription-widget-css', $custom_css);
    }    /**
     * Front-end display del widget
     */
    public function widget($args, $instance) {
        echo $args['before_widget'];
        if (!empty($instance['title'])) {
            echo $args['before_title'] . apply_filters('widget_title', $instance['title']) . $args['after_title'];
        }

        // Ottieni i tipi di sottoscrizione
        global $wpdb;
        $subscription_types_table = $wpdb->prefix . 'type_subscription';
        $subscription_types = $wpdb->get_results("SELECT * FROM {$subscription_types_table} ORDER BY type_sub", ARRAY_A);

        // Mostra il form di registrazione
        ?>
        <div class="user-subscription-widget-container">
            <!-- Colonna informativa a sinistra -->
            <div class="dossier-column">
                <h3><?php _e('Info Registrazione', 'document-viewer-plugin'); ?></h3>
                <div class="dossier-content">
                    <p><?php _e('Compila il modulo di registrazione per accedere al servizio. I campi con asterisco (*) sono obbligatori.', 'document-viewer-plugin'); ?></p>
                    <p><?php _e('La password deve contenere almeno:', 'document-viewer-plugin'); ?></p>
                    <ul>
                        <li><?php _e('8 caratteri', 'document-viewer-plugin'); ?></li>
                        <li><?php _e('Una lettera maiuscola', 'document-viewer-plugin'); ?></li>
                        <li><?php _e('Un numero', 'document-viewer-plugin'); ?></li>
                        <li><?php _e('Un carattere speciale', 'document-viewer-plugin'); ?></li>
                    </ul>
                </div>
            </div>

            <!-- Colonna centrale con il form -->
            <div class="document-form-column">
                <h3><?php _e('Modulo di Registrazione', 'document-viewer-plugin'); ?></h3>
                <form id="user-subscription-form" class="user-subscription-form">                <div class="form-group column-left">
                    <label for="username"><?php _e('Username', 'document-viewer-plugin'); ?> <span class="required">*</span></label>
                    <input type="text" id="username" name="username" required>
                    <span class="error-message" id="username-error"></span>
                </div>

                <div class="form-group column-right">
                    <label for="email"><?php _e('Email', 'document-viewer-plugin'); ?> <span class="required">*</span></label>
                    <input type="email" id="email" name="email" required>
                    <span class="error-message" id="email-error"></span>
                </div>

                <div class="form-group column-left">
                    <label for="name"><?php _e('Nome', 'document-viewer-plugin'); ?> <span class="required">*</span></label>
                    <input type="text" id="name" name="name" required>
                    <span class="error-message" id="name-error"></span>
                </div>

                <div class="form-group column-right">
                    <label for="surname"><?php _e('Cognome', 'document-viewer-plugin'); ?> <span class="required">*</span></label>
                    <input type="text" id="surname" name="surname" required>
                    <span class="error-message" id="surname-error"></span>
                </div>

                <div class="form-group column-left">
                    <label for="phone"><?php _e('Telefono', 'document-viewer-plugin'); ?></label>
                    <input type="tel" id="phone" name="phone">
                    <span class="error-message" id="phone-error"></span>
                </div>
                  <div class="form-group full-width">
                    <label for="password"><?php _e('Password', 'document-viewer-plugin'); ?> <span class="required">*</span></label>
                    <input type="password" id="password" name="password" required>
                    <span class="error-message" id="password-error"></span>
                    <div class="password-strength-meter">
                        <div class="strength-bar"></div>
                        <span class="strength-text"></span>
                    </div>
                </div>

                <div class="form-group full-width">
                    <label for="confirm-password"><?php _e('Conferma Password', 'document-viewer-plugin'); ?> <span class="required">*</span></label>
                    <input type="password" id="confirm-password" name="confirm-password" required>
                    <span class="error-message" id="confirm-password-error"></span>
                </div>
                  <div class="form-group full-width">
                    <label for="tipo-subscription"><?php _e('Tipo Sottoscrizione', 'document-viewer-plugin'); ?> <span class="required">*</span></label>
                    <select id="tipo-subscription" name="tipo_subscription" required>
                        <option value=""><?php _e('-- Seleziona --', 'document-viewer-plugin'); ?></option>
                        <?php foreach ($subscription_types as $type) : ?>
                            <option value="<?php echo esc_attr($type['type_sub']); ?>"><?php echo esc_html($type['type_sub']); ?></option>
                        <?php endforeach; ?>
                    </select>
                    <span class="error-message" id="tipo-subscription-error"></span>
                </div>

                <div class="form-group terms-group">
                    <input type="checkbox" id="privacy-terms" name="privacy-terms" required>
                    <label for="privacy-terms"><?php _e('Accetto i termini e le condizioni sulla privacy', 'document-viewer-plugin'); ?> <span class="required">*</span></label>
                    <span class="error-message" id="privacy-terms-error"></span>
                </div>

                <div class="form-submit">
                    <button type="submit" class="submit-button"><?php _e('Registrati', 'document-viewer-plugin'); ?></button>
                </div>
                  <div class="response-message" style="display: none;"></div>
                </form>
            </div>

            <!-- Colonna destra con informazioni aggiuntive -->
            <div class="document-display-column">
                <h3><?php _e('Vantaggi', 'document-viewer-plugin'); ?></h3>
                <div class="subscription-benefits">
                    <ul>
                        <li><?php _e('Accesso a contenuti esclusivi', 'document-viewer-plugin'); ?></li>
                        <li><?php _e('Supporto dedicato', 'document-viewer-plugin'); ?></li>
                        <li><?php _e('Aggiornamenti prioritari', 'document-viewer-plugin'); ?></li>
                        <li><?php _e('Funzionalità aggiuntive', 'document-viewer-plugin'); ?></li>
                    </ul>
                    <p><?php _e('Registrandoti accetti i termini e le condizioni del servizio.', 'document-viewer-plugin'); ?></p>
                </div>
            </div>
        </div>
        <?php

        echo $args['after_widget'];
    }

    /**
     * Back-end widget form
     */
    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : __('Registrazione Utente', 'document-viewer-plugin');
        ?>
        <p>
            <label for="<?php echo $this->get_field_id('title'); ?>"><?php _e('Titolo:', 'document-viewer-plugin'); ?></label>
            <input class="widefat" id="<?php echo $this->get_field_id('title'); ?>" name="<?php echo $this->get_field_name('title'); ?>" type="text" value="<?php echo esc_attr($title); ?>">
        </p>
        <?php
    }

    /**
     * Processa il salvataggio delle opzioni del widget
     */
    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? sanitize_text_field($new_instance['title']) : '';
        return $instance;
    }
}

/**
 * Registra il widget
 */
function register_user_subscription_widget() {
    register_widget('User_Subscription_Widget');
}
add_action('widgets_init', 'register_user_subscription_widget');

/**
 * Shortcode per il form di registrazione utente
 */
function user_subscription_shortcode($atts) {
    $atts = shortcode_atts(array(
        'title' => __('Registrazione Utente', 'document-viewer-plugin'),
        'layout' => 'full', // Opzioni: 'full' o 'compact'
    ), $atts);

    // Inizia l'output del buffer
    ob_start();

    // Crea un'istanza del widget
    $widget = new User_Subscription_Widget();

    // Visualizza il contenuto del widget (simula il metodo widget)
    if ($atts['layout'] === 'compact') {
        // Layout compatto (solo il form senza colonne laterali)
        echo '<div class="user-subscription-shortcode">';
        if (!empty($atts['title'])) {
            echo '<h3>' . esc_html($atts['title']) . '</h3>';
        }
        echo '<div class="document-form-column" style="width:100%;">';
        $widget->widget(array('before_widget' => '', 'after_widget' => '', 'before_title' => '<h3>', 'after_title' => '</h3>'), $atts);
        echo '</div></div>';
    } else {
        // Layout completo (a tre colonne come il widget)
        echo '<div class="document-viewer-widget user-subscription-shortcode">';
        if (!empty($atts['title'])) {
            echo '<h3>' . esc_html($atts['title']) . '</h3>';
        }
        $widget->widget(array('before_widget' => '', 'after_widget' => '', 'before_title' => '<h3>', 'after_title' => '</h3>'), $atts);
        echo '</div>';
    }

    // Restituisci il contenuto dal buffer
    return ob_get_clean();
}
add_shortcode('user_subscription', 'user_subscription_shortcode');

/**
 * Gestisce la richiesta AJAX per registrare un nuovo utente
 */
function handle_user_subscription_registration() {
    // Verifica nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'document_viewer_nonce')) {
        wp_send_json_error(['message' => __('Security check failed.', 'document-viewer-plugin')]);
    }

    // Recupera e sanifica i dati
    $data = [
        'username' => isset($_POST['username']) ? sanitize_user($_POST['username']) : '',
        'name' => isset($_POST['name']) ? sanitize_text_field($_POST['name']) : '',
        'surname' => isset($_POST['surname']) ? sanitize_text_field($_POST['surname']) : '',
        'phone' => isset($_POST['phone']) ? sanitize_text_field($_POST['phone']) : '',
        'email' => isset($_POST['email']) ? sanitize_email($_POST['email']) : '',
        'password' => isset($_POST['password']) ? $_POST['password'] : '',
        'tipo_subscription' => isset($_POST['tipo_subscription']) ? sanitize_text_field($_POST['tipo_subscription']) : ''
    ];

    // Verifica campi obbligatori
    if (empty($data['username']) || empty($data['name']) || empty($data['surname']) ||
        empty($data['email']) || empty($data['password']) || empty($data['tipo_subscription'])) {
        wp_send_json_error(['message' => __('Tutti i campi obbligatori devono essere compilati.', 'document-viewer-plugin')]);
    }

    // Verifica che l'email sia valida
    if (!is_email($data['email'])) {
        wp_send_json_error(['message' => __('L\'indirizzo email non è valido.', 'document-viewer-plugin')]);
    }

    // Usa la classe User_Subscription_Manager per aggiungere l'utente
    $user_manager = new User_Subscription_Manager();
    $result = $user_manager->add_user_subscription($data);

    if ($result['success']) {
        wp_send_json_success(['message' => $result['message']]);
    } else {
        wp_send_json_error(['message' => $result['message']]);
    }
}
add_action('wp_ajax_nopriv_user_subscription_registration', 'handle_user_subscription_registration');
add_action('wp_ajax_user_subscription_registration', 'handle_user_subscription_registration');
