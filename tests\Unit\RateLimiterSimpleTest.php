<?php

namespace Tests\Unit;

use Tests\BaseTestCase;

/**
 * Simplified Unit tests for Office_Addin_Rate_Limiter class
 */
class RateLimiterSimpleTest extends BaseTestCase
{
    private static $rate_limiter;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
        
        // Include the rate limiter class once
        require_once __DIR__ . '/../../includes/class-office-addin-rate-limiter.php';
        
        // Create rate limiter instance once
        self::$rate_limiter = new \Office_Addin_Rate_Limiter();
    }    protected function setUp(): void
    {
        parent::setUp();
        
        // Set up default $_SERVER values for IP testing
        $_SERVER['REMOTE_ADDR'] = '*************';
        $_SERVER['HTTP_X_FORWARDED_FOR'] = '';
        $_SERVER['HTTP_X_REAL_IP'] = '';
    }

    /**
     * Test constructor initializes default limits
     */
    public function testConstructorInitializesDefaultLimits(): void
    {
        $limits = $this->getProperty(self::$rate_limiter, 'default_limits');
        
        $this->assertIsArray($limits);
        $this->assertArrayHasKey('analyze_excel_data', $limits);
        $this->assertArrayHasKey('get_settings', $limits);
        $this->assertArrayHasKey('get_queries', $limits);
        $this->assertArrayHasKey('test_connection', $limits);
        
        // Check default values
        $this->assertEquals(30, $limits['analyze_excel_data']);
        $this->assertEquals(60, $limits['get_settings']);
        $this->assertEquals(60, $limits['get_queries']);
        $this->assertEquals(10, $limits['test_connection']);
    }    /**
     * Test is_request_allowed returns true for new client
     */
    public function testIsRequestAllowedReturnsTrueForNewClient(): void
    {
        // Mock cache functions to return false (no previous requests)
        \Brain\Monkey\Functions\when('wp_cache_get')->justReturn(false);
        \Brain\Monkey\Functions\when('wp_cache_set')->justReturn(true);
        \Brain\Monkey\Functions\when('get_option')->justReturn([]);
        
        $result = self::$rate_limiter->is_request_allowed('analyze_excel_data');
        
        $this->assertTrue($result['allowed']);
        $this->assertEquals(29, $result['remaining']); // 30 - 1
        $this->assertArrayHasKey('reset_time', $result);
        $this->assertEquals(30, $result['limit']);
    }    /**
     * Test is_request_allowed returns false when limit exceeded
     */
    public function testIsRequestAllowedReturnsFalseWhenLimitExceeded(): void
    {
        // Mock cache to return count that exceeds limit (30)
        \Brain\Monkey\Functions\when('wp_cache_get')->justReturn(30);
        \Brain\Monkey\Functions\when('get_option')->justReturn([]);
        
        $result = self::$rate_limiter->is_request_allowed('analyze_excel_data');
        
        $this->assertFalse($result['allowed']);
        $this->assertEquals(0, $result['remaining']);
        $this->assertArrayHasKey('error', $result);
        $this->assertEquals(30, $result['limit']);
    }    /**
     * Test get_rate_limit_status returns correct data
     */
    public function testGetRateLimitStatusReturnsCorrectData(): void
    {
        // Mock cache data
        \Brain\Monkey\Functions\when('wp_cache_get')->justReturn(15);
        \Brain\Monkey\Functions\when('get_option')->justReturn([]);
        
        $status = self::$rate_limiter->get_rate_limit_status('analyze_excel_data');
        
        $this->assertIsArray($status);
        $this->assertArrayHasKey('limit', $status);
        $this->assertArrayHasKey('used', $status);
        $this->assertArrayHasKey('remaining', $status);
        $this->assertArrayHasKey('reset_time', $status);
        $this->assertArrayHasKey('window_seconds', $status);
        
        $this->assertEquals(30, $status['limit']);
        $this->assertEquals(15, $status['used']);
        $this->assertEquals(15, $status['remaining']); // 30 - 15
    }/**
     * Test get_client_identifier uses user ID when available
     */
    public function testGetClientIdentifierUsesUserIdWhenAvailable(): void
    {
        // Override the default bootstrap mocks
        \Brain\Monkey\Functions\when('is_user_logged_in')->justReturn(true);
        \Brain\Monkey\Functions\when('get_current_user_id')->justReturn(123);
        
        $identifier = $this->callMethod(self::$rate_limiter, 'get_client_identifier');
        
        $this->assertEquals('user_123', $identifier);
    }    /**
     * Test get_client_identifier uses IP when no user
     */
    public function testGetClientIdentifierUsesIpWhenNoUser(): void
    {
        \Brain\Monkey\Functions\when('is_user_logged_in')->justReturn(false);
        $_SERVER['REMOTE_ADDR'] = '*************';
        
        $identifier = $this->callMethod(self::$rate_limiter, 'get_client_identifier');
        
        $this->assertEquals('ip_*************', $identifier);
    }    /**
     * Test reset_rate_limit removes cache entry
     */
    public function testResetRateLimitRemovesCacheEntry(): void
    {
        \Brain\Monkey\Functions\when('wp_cache_delete')->justReturn(true);
        
        $result = self::$rate_limiter->reset_rate_limit('analyze_excel_data', 'test_identifier');
        
        $this->assertTrue($result);
    }    /**
     * Test update_rate_limits updates configuration
     */
    public function testUpdateRateLimitsUpdatesConfiguration(): void
    {
        \Brain\Monkey\Functions\when('update_option')->justReturn(true);
        
        $new_limits = [
            'analyze_excel_data' => 50,
            'get_settings' => 100
        ];
        
        $result = self::$rate_limiter->update_rate_limits($new_limits);
        
        $this->assertTrue($result);
    }

    /**
     * Test clear_all_rate_limits returns success
     */
    public function testClearAllRateLimitsReturnsSuccess(): void
    {
        $result = self::$rate_limiter->clear_all_rate_limits();
        
        $this->assertTrue($result);
    }    /**
     * Test with different action types
     */
    public function testDifferentActionTypes(): void
    {
        $actions = ['analyze_excel_data', 'get_settings', 'get_queries', 'test_connection'];
        
        // Set mocks for all actions
        \Brain\Monkey\Functions\when('wp_cache_get')->justReturn(false);
        \Brain\Monkey\Functions\when('wp_cache_set')->justReturn(true);
        \Brain\Monkey\Functions\when('get_option')->justReturn([]);
        
        foreach ($actions as $action) {
            $result = self::$rate_limiter->is_request_allowed($action);
            
            $this->assertTrue($result['allowed'], "Action $action should be allowed for new client");
            $this->assertIsInt($result['limit'], "Action $action should have a numeric limit");
            $this->assertGreaterThan(0, $result['limit'], "Action $action should have a positive limit");
        }
    }

    /**
     * Test cache key generation is consistent
     */
    public function testCacheKeyGenerationIsConsistent(): void
    {
        $key1 = $this->callMethod(self::$rate_limiter, 'generate_cache_key', ['test_action', 'test_id']);
        $key2 = $this->callMethod(self::$rate_limiter, 'generate_cache_key', ['test_action', 'test_id']);
        
        $this->assertEquals($key1, $key2, 'Cache keys should be consistent for same parameters');
        $this->assertIsString($key1, 'Cache key should be a string');
        $this->assertNotEmpty($key1, 'Cache key should not be empty');
    }

    protected function tearDown(): void
    {
        // Clean up server variables
        unset($_SERVER['REMOTE_ADDR']);
        unset($_SERVER['HTTP_X_FORWARDED_FOR']);
        unset($_SERVER['HTTP_X_REAL_IP']);
        
        parent::tearDown();
    }
}
