/**
 * Office Add-in Editor CSS
 *
 * <PERSON>ili per l'editor avan<PERSON>to dell'Office Add-in
 */

/* Stili per l'editor */
.wp-editor-container {
    border: 1px solid #ddd;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.wp-editor-area {
    font-family: Consolas, Monaco, monospace;
    font-size: 14px;
    padding: 10px;
    line-height: 1.6;
}

/* Stili per gli strumenti dell'editor */
.addin-editor-tools {
    margin-top: 20px;
    padding: 15px;
    background-color: #f9f9f9;
    border: 1px solid #e5e5e5;
    border-radius: 3px;
}

.addin-editor-tools h4 {
    margin-top: 0;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    color: #23282d;
}

.addin-editor-color-tools {
    margin-bottom: 15px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
}

.addin-editor-color-tools label {
    margin-right: 10px;
    font-weight: bold;
}

.addin-editor-color-tools .wp-color-picker {
    margin-right: 10px;
}

.addin-editor-color-tools .button {
    margin-left: 5px;
}

.addin-editor-template-tools {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
}

.addin-editor-template-tools label {
    margin-right: 10px;
    font-weight: bold;
}

.addin-editor-template-tools select {
    margin-right: 10px;
}

/* Preview container */
.office-addin-preview {
    margin-top: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    background-color: #fff;
}

.office-addin-preview h4 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 14px;
    font-weight: 600;
}

.office-addin-preview-content {
    border: 1px solid #ddd;
    padding: 15px;
    background: #f9f9f9;
    max-height: 500px;
    overflow-y: auto;
}

/* Stili per il color picker */
.wp-picker-container {
    display: inline-block;
}

.wp-picker-container .wp-color-result.button {
    margin: 0 6px 0 0;
}

.wp-picker-container .wp-color-picker {
    width: 80px;
}

/* Excel Add-in container styles */
.excel-addin-container {
    font-family: "Segoe UI", Arial, sans-serif;
    padding: 15px;
    max-width: 800px;
    margin: 0 auto;
}

.section {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #f9f9f9;
}

h2 {
    color: #2c3e50;
    margin-top: 0;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

h3 {
    color: #3498db;
    margin-top: 0;
    margin-bottom: 15px;
}

.form-group {
    margin-bottom: 15px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.full-width {
    width: 100%;
    box-sizing: border-box;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.primary-button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
}

.secondary-button {
    background-color: #95a5a6;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
}

.text-display {
    border: 1px solid #ddd;
    padding: 10px;
    background-color: white;
    min-height: 100px;
    max-height: 200px;
    overflow-y: auto;
    margin-top: 10px;
}

.results-container {
    border: 1px solid #ddd;
    padding: 15px;
    background-color: white;
    min-height: 150px;
    max-height: 300px;
    overflow-y: auto;
}

.debug-info {
    font-size: 12px;
    color: #7f8c8d;
}

/* Error message */
.error {
    color: #e74c3c;
    font-weight: bold;
}

/* Placeholder text */
.placeholder {
    color: #95a5a6;
    font-style: italic;
}

/* Loading indicator */
.loading {
    color: #3498db;
    font-style: italic;
}

/* Success message */
.success {
    color: #2ecc71;
    font-weight: bold;
}
