/**
 * Chat Model Widget - ChatGPT-style Theme
 */

.chat-widget {
    background-color: #ffffff;
    border: 1px solid #e5e5e5;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin: 15px 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    overflow: hidden;
    display: flex;
    min-height: 500px;
}

.chat-widget h3 {
    background-color: #f7f7f8;
    padding: 15px;
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid #e5e5e5;
    display: flex;
    align-items: center;
}

.chat-widget h3::before {
    content: "💬";
    margin-right: 8px;
    font-size: 18px;
}

/* Chat form */
.chat-form {
    display: flex;
    flex-direction: row;
    height: 100%;
    flex: 1;
}

/* Chat main area (right side) - 2/3 width */
.chat-main {
    display: flex;
    flex-direction: column;
    flex: 1; /* Occupa il resto dello spazio (circa 2/3) */
    min-width: 350px; /* Ridotto per permettere più spazio al mega menu */
    background-color: #ffffff;
}

/* Chat log area */
.chat-log {
    background-color: #ffffff;
    flex: 1;
    margin: 0;
    overflow-y: auto;
    padding: 15px;
    display: flex;
    flex-direction: column;
}

/* Chat input container - the parent that holds all input elements */
.input-container {
    position: relative;
    margin: 0;
    padding: 15px;
    background-color: #f7f7f8;
    border-top: 1px solid #e5e5e5;
    display: flex;
    align-items: center;
    gap: 10px;
    width: 100%;
    box-sizing: border-box; /* Include padding nel calcolo width */
}

/* Input wrapper - contains the text input */
.input-wrapper {
    flex: 1;
    position: relative;
    width: 100%; /* Assicura che occupi tutto lo spazio disponibile */
}

.chat-input {
    width: 100%;
    max-width: 100%; /* Forza l'estensione completa */
    padding: 12px 15px;
    border: 1px solid #d1d5db;
    border-radius: 25px;
    font-size: 14px;
    outline: none;
    background-color: #ffffff;
    transition: border-color 0.2s ease;
    box-sizing: border-box; /* Include padding nel calcolo width */
}

.chat-input:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 2px rgba(0, 115, 170, 0.1);
}

/* Send button */
.send-btn {
    width: 42px;
    height: 42px;
    border: none;
    border-radius: 50%;
    background-color: #0073aa;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
    font-size: 16px;
    box-shadow: 0 2px 4px rgba(0, 115, 170, 0.2);
}

.send-btn:hover {
    background-color: #004182;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 115, 170, 0.3);
}

.send-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 115, 170, 0.2);
}

.send-btn::before {
    content: "➤";
    font-size: 14px;
}

.send-btn::before {
    content: '➤';
    font-size: 16px;
}

/* Academy icon - now decorative (menu is always visible) */
.academy-icon {
    display: none; /* Hide since menu is always visible */
}

/* Financial questions dropdown */
.financial-questions-menu {
    position: absolute;
    bottom: calc(100% + 10px);
    left: 15px;
    right: 15px;
    background: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    z-index: 1000;
    overflow: hidden;
    max-height: 400px;
    min-width: 400px;
    display: none; /* Hidden by default */
}

.financial-questions-menu.active {
    display: block !important;
}

.financial-questions-menu h4 {
    margin: 0;
    padding: 8px 12px;
    font-size: 14px;
    color: #666;
    border-bottom: 1px solid #eee;
}

.financial-questions-menu ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.financial-questions-menu li {
    padding: 8px 12px;
    cursor: pointer;
    font-size: 13px;
    transition: background-color 0.2s ease;
}

.financial-questions-menu li:hover {
    background-color: #f0f7ff;
    color: #0a66c2;
}

/* Input wrapper - container for just the input field */
.input-wrapper {
    flex: 0 1 70%;
    position: relative;
    max-width: 70%;
}

/* Chat input field - now with more rounded corners and smaller height */
.chat-input {
    border: 1px solid #e5e5e5;
    border-radius: 25px;
    font-size: 14px;
    padding: 8px 16px;
    width: 100%;
    height: 44px;
    transition: border-color 0.2s, box-shadow 0.2s;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    outline: none;
    background-color: #ffffff;
}

.chat-input:focus {
    border-color: #10a37f;
    box-shadow: 0 0 0 2px rgba(16, 163, 127, 0.2);
    outline: none;
}

/* Send button now positioned outside the input field */
.send-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #10a37f;
    border: none;
    cursor: pointer;
    padding: 0;
    margin-left: 10px;
    transition: background-color 0.2s ease;
    flex-shrink: 0;
    position: relative;
}

.send-btn:hover {
    background-color: #0e8f6f;
}

.send-btn:active {
    background-color: #0c7d60;
}

/* Send icon */
.send-btn::before {
    content: "";
    display: block;
    width: 16px;
    height: 16px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='white'%3E%3Cpath d='M15.964.686a.5.5 0 0 0-.65-.65L.767 5.855H.766l-.452.18a.5.5 0 0 0-.082.887l.41.26.001.002 4.995 3.178 3.178 4.995.002.002.26.41a.5.5 0 0 0 .886-.083l6-15zm-1.833 1.89L6.637 10.07l-.215-.338a.5.5 0 0 0-.154-.154l-.338-.215 7.494-7.494 1.178-.471-.47 1.178z'%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    position: absolute;
    top: 8px;
    left: 8px;
    right: 8px;
    bottom: 8px;
    margin: auto;
}

/* Hide the original chat controls section */
.chat-controls {
    display: none;
}

/* Message styling */
.message {
    border-radius: 0;
    clear: both;
    margin: 0;
    padding: 20px;
    position: relative;
    display: flex;
    align-items: flex-start;
    width: 100%;
    box-sizing: border-box;
}

.user-message {
    background-color: #f7f7f8;
}

.ai-message {
    background-color: #ffffff;
}

.system-message, .error-message {
    background-color: #fff8e1;
    color: #8a6d3b;
    padding: 10px 20px;
    text-align: center;
    font-size: 13px;
    border-top: 1px solid rgba(0,0,0,0.05);
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.error-message {
    background-color: #fdecea;
    color: #a94442;
}

/* Avatar styling */
.message-avatar {
    width: 30px;
    height: 30px;
    border-radius: 6px;
    background-size: cover;
    margin-right: 16px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.user-avatar {
    background-color: #10a37f;
    color: white;
}

.user-avatar::after {
    content: "U";
    font-weight: bold;
}

.ai-avatar {
    background-color: #19c37d;
    color: white;
    position: relative;
}

.ai-avatar::after {
    content: "AI";
    font-size: 12px;
    font-weight: 700;
}

.message-content {
    flex-grow: 1;
    line-height: 1.6;
    overflow-wrap: break-word;
    font-size: 15px;
}

/* Mathematical formulas styles */
.math-formula {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
    text-align: center;
    font-family: 'Times New Roman', Times, serif;
    font-size: 16px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.math-inline {
    background: #f1f3f4;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Times New Roman', Times, serif;
    font-size: 14px;
    margin: 0 2px;
}

.fraction {
    display: inline-block;
    text-align: center;
    vertical-align: middle;
}

.numerator, .denominator {
    display: block;
    font-size: 14px;
    line-height: 1.2;
}

.fraction-line {
    display: block;
    border-top: 1px solid #333;
    margin: 2px 0;
    min-width: 30px;
}

.sqrt-content {
    border-top: 1px solid #333;
    padding-top: 2px;
}

/* Highlight financial terms */
.math-formula strong, .math-inline strong {
    color: #0073aa;
    font-weight: 600;
}

.math-formula em, .math-inline em {
    color: #666;
    font-style: normal;
    font-weight: 500;
}

/* Academy mega menu sempre visibile (no animation needed) */
.academy-mega-menu {
    opacity: 1;
    transform: translateY(0);
}

/* Solo il questions menu ha animazione */
.financial-questions-menu {
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.financial-questions-menu.active {
    opacity: 1;
    transform: translateY(0);
}

/* ==============================
   MEGA MENU STYLES (inspirato al login widget)
   ============================== */

/* Academy Mega Menu - Sidebar (always visible) - Extended to 1/3 width */
.academy-mega-menu {
    flex: 0 0 33.333%; /* 1/3 della larghezza totale */
    max-width: 450px; /* Aumentato a 450px per permettere più spazio */
    min-width: 300px; /* Aumentato minimo per migliore usabilità */
    background: #f8f9fa;
    border-right: 1px solid #e1e1e1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    max-height: 100%;
    min-height: 400px;
}

/* Mega Menu Header */
.mega-menu-header {
    background-color: #f7f7f8;
    padding: 16px 20px;
    border-bottom: 1px solid #e5e5e5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.mega-menu-header h4 {
    margin: 0;
    font-size: 15px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
}

.mega-menu-header h4::before {
    content: "📂";
    margin-right: 8px;
    font-size: 16px;
}

.close-mega-menu {
    display: none; /* Hide close button since it's always visible */
}

/* Mega Menu Content */
.mega-menu-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.close-mega-menu:hover {
    background-color: #f0f0f0;
    color: #333;
}

/* Categories Grid - Adapted for wider sidebar (can show 2 columns) */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(130px, 1fr)); /* Griglia responsiva con min-width aumentato */
    gap: 10px; /* Gap aumentato per spazio maggiore */
    padding: 20px; /* Padding aumentato */
    flex: 1;
}

/* Su sidebar larghe (>320px), usa sempre 2 colonne */
@media (min-width: 320px) {
    .academy-mega-menu .categories-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }
}

/* Su sidebar molto larghe (>400px), ottimizza lo spazio */
@media (min-width: 400px) {
    .academy-mega-menu .categories-grid {
        padding: 25px;
        gap: 15px;
    }
}

.category-card {
    background: #fff;
    border: 1px solid #e5e5e5;
    border-radius: 8px; /* Bordi più arrotondati */
    padding: 15px; /* Padding aumentato */
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column; /* Layout verticale per griglia */
    align-items: center;
    text-align: center;
    gap: 10px; /* Gap aumentato */
    position: relative;
    overflow: hidden;
    min-height: 90px; /* Altezza minima aumentata */
    box-shadow: 0 1px 3px rgba(0,0,0,0.1); /* Ombra sottile */
}

.category-card:hover {
    border-color: #0073aa;
    box-shadow: 0 2px 8px rgba(0,115,170,0.15);
    transform: translateY(-2px); /* Movimento verso l'alto invece che laterale */
}

.category-card.active {
    border-color: #0073aa;
    background-color: #f0f8ff;
}

.category-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #0073aa, #004182);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    flex-shrink: 0;
    color: white;
    font-weight: bold;
    transition: transform 0.2s ease;
}

.category-card:hover .category-icon {
    transform: scale(1.1);
}

.category-name {
    font-weight: 600;
    color: #333;
    margin: 0;
    font-size: 13px;
    flex: 1;
}

.category-count {
    font-size: 12px;
    color: #666;
    font-weight: normal;
    background: #f0f0f0;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: auto;
}

/* Financial Questions Menu - Overlay nella sidebar */
.financial-questions-menu {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #f8f9fa;
    display: none;
    flex-direction: column;
    overflow: hidden;
}

.financial-questions-menu.active {
    display: flex !important;
}

/* Questions Header */
.questions-header {
    background-color: #ffffff;
    padding: 15px 20px;
    border-bottom: 1px solid #e5e5e5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.category-title {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.back-to-categories {
    background: none;
    border: none;
    color: #0073aa;
    cursor: pointer;
    font-size: 12px;
    padding: 5px 10px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.back-to-categories:hover {
    background-color: #f0f0f0;
}

.back-to-categories {
    background: none;
    border: none;
    color: #0073aa;
    cursor: pointer;
    font-size: 14px;
    padding: 8px 12px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.back-to-categories:hover {
    background-color: #f0f8ff;
}

/* Questions List - Adapted for sidebar */
.questions-list {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

.question-item {
    padding: 12px 15px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.2s ease;
    font-size: 13px;
    color: #333;
    line-height: 1.4;
    background: #fff;
    margin: 2px 5px;
    border-radius: 4px;
}

.question-item:hover {
    background-color: #f0f8ff;
    color: #0073aa;
    transform: translateX(2px);
}

.question-item:last-child {
    border-bottom: none;
}

/* Loading state */
.loading-categories,
.loading-questions {
    padding: 40px 20px;
    text-align: center;
    color: #666;
    font-style: italic;
}

/* Responsive adjustments - Layout esteso */
@media (max-width: 768px) {
    .chat-widget {
        flex-direction: column; /* Stack verticale su tablet */
        min-height: auto;
    }
    
    .chat-form {
        flex-direction: column;
    }
    
    .academy-mega-menu {
        flex: none; /* Rimuove flex su mobile */
        max-width: none;
        min-width: auto;
        min-height: 200px; /* Altezza ridotta su mobile */
        border-right: none;
        border-bottom: 1px solid #e1e1e1;
    }
    
    .chat-main {
        min-width: auto;
        flex: 1;
    }
    
    .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 8px;
        padding: 15px;
    }
    
    .category-card {
        padding: 12px 8px;
        min-height: 70px;
    }
}

@media (max-width: 600px) {
    .categories-grid {
        grid-template-columns: repeat(2, 1fr); /* Sempre 2 colonne su mobile */
        gap: 10px;
        padding: 15px;
    }
    
    .category-card {
        padding: 15px 10px;
    }
    
    .mega-menu-header,
    .questions-header {
        padding: 12px 15px;
    }
    
    .question-item {
        padding: 12px 15px;
        font-size: 13px;
    }
}

/* Academy mega menu sempre visibile (no animation needed) */
.academy-mega-menu {
    opacity: 1;
    transform: translateY(0);
}

/* Solo il questions menu ha animazione */
.financial-questions-menu {
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.financial-questions-menu.active {
    opacity: 1;
    transform: translateY(0);
}

/* ==============================
   END MEGA MENU STYLES
   ============================== */