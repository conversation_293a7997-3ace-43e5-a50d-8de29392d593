<?php
/**
 * Class Help_Line
 * 
 * Gestisce la funzionalità di Help Line (ex dossier) che fornisce aiuto contestuale
 * agli utenti durante l'analisi dei documenti.
 * 
 * @package Financial_Advisor
 */

if (!defined('ABSPATH')) {
    exit;
}

class Help_Line {
    /**
     * Contenuto dell'help line
     */
    private $help_content;
    
    /**
     * Costruttore
     */
    public function __construct() {
        // Carica il contenuto dell'help line dalle impostazioni
        $this->help_content = get_option('help_line_content', $this->get_default_content());
        
        // Aggiungi i necessari hook
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_ajax_save_help_line_content', array($this, 'save_help_line_content'));
        add_action('wp_ajax_nopriv_save_help_line_content', array($this, 'save_help_line_content'));
        
        // Aggiungi il footer per outputtare l'help line
        add_action('wp_footer', array($this, 'output_help_line'));
        
        // Registra shortcode per utilizzare l'help line in contenuti specifici
        add_shortcode('help_line', array($this, 'shortcode_help_line'));
    }
    
    /**
     * Registra e carica gli script e gli stili necessari
     */
    public function enqueue_scripts() {
        // Registra e carica lo stile CSS
        wp_register_style(
            'help-line-style',
            plugin_dir_url(dirname(__FILE__)) . 'assets/css/help-line.css',
            array(),
            '1.0'
        );
        wp_enqueue_style('help-line-style');
        
        // Registra e carica lo script JS
        wp_register_script(
            'help-line-script',
            plugin_dir_url(dirname(__FILE__)) . 'assets/js/help-line.js',
            array('jquery'),
            '1.0',
            true
        );
        
        // Localizza lo script con i dati necessari
        wp_localize_script(
            'help-line-script',
            'helpLineParams',
            array(
                'ajaxUrl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('help_line_nonce')
            )
        );
        
        wp_enqueue_script('help-line-script');
    }
    
    /**
     * Salva il contenuto dell'help line
     */
    public function save_help_line_content() {
        // Verifica il nonce per sicurezza
        check_ajax_referer('help_line_nonce', 'nonce');
        
        // Verifica i permessi
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'Permessi insufficienti.'));
            return;
        }
        
        // Recupera e sanitizza il contenuto
        $content = isset($_POST['content']) ? wp_kses_post($_POST['content']) : '';
        
        // Salva il contenuto nelle opzioni
        update_option('help_line_content', $content);
        
        wp_send_json_success(array('message' => 'Contenuto dell\'Help Line salvato con successo.'));
    }
    
    /**
     * Ottiene il contenuto predefinito dell'help line
     */
    public function get_default_content() {
        return '<h4>Come funziona:</h4>
        <ol>
            <li>Carica un documento PDF o Word</li>
            <li>Inserisci la tua domanda o descrizione</li>
            <li>Clicca su "Analizza" per elaborare il documento</li>
            <li>Visualizza l\'analisi e modifica se necessario</li>
            <li>Esporta il risultato in formato PDF</li>
        </ol>
        <h4>Suggerimenti:</h4>
        <ul>
            <li>Per ottenere risultati migliori, sii specifico nelle tue domande</li>
            <li>I documenti PDF senza restrizioni funzionano meglio</li>
            <li>Puoi salvare le analisi per consultarle in seguito</li>
        </ul>';
    }
    
    /**
     * Genera l'HTML dell'help line
     */
    public function render_help_line() {
        ob_start();
        ?>
        <div class="help-line-panel">
            <div class="help-line-toggle">
                <span class="help-icon">?</span>
            </div>
            <div class="help-line-content">
                <h3>Help Line</h3>
                <div class="help-line-body">
                    <?php echo wp_kses_post($this->help_content); ?>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Output dell'help line nel footer
     */
    public function output_help_line() {
        echo $this->render_help_line();
    }
    
    /**
     * Shortcode per inserire l'help line in un contenuto specifico
     */
    public function shortcode_help_line($atts) {
        return $this->render_help_line();
    }
    
    /**
     * Funzione statica per ottenere l'istanza
     */
    public static function instance() {
        static $instance = null;
        
        if ($instance === null) {
            $instance = new self();
        }
        
        return $instance;
    }
}

// Funzione helper per accedere all'istanza
function help_line() {
    return Help_Line::instance();
}