# Widget Gestione Report e Riclassificazione Aziendale

## Descrizione tecnica
Dashboard riepilogativa per azienda, workflow revisione, gestione permessi granulari.

---

## Fasi di sviluppo e test

### 1. Archivio e dashboard azienda
- UI archivio report per azienda, KPI, ultimi report.
- Filtri e navigazione.

**Prompt sviluppo:**  
- Implementa archivio report e dashboard azienda.

**Test:**  
- Navigazione, filtri, dashboard.

---

### 2. Riclassificazione manuale/automatica
- UI revisione mapping, salvataggio versioni.
- API update mapping/versioning.

**Prompt sviluppo:**  
- Implementa UI revisione mapping e versioning.

**Test:**  
- Modifica mapping, versioning, ripristino.

---

### 3. Workflow revisione e approvazione
- Stato report (bozza, revisione, approvato), commenti, firma digitale.
- Notifiche cambio stato.

**Prompt sviluppo:**  
- Implementa workflow revisione, commenti e firma digitale.

**Test:**  
- Cambio stato, commenti, firma.

---

### 4. Sicurezza e permessi
- G<PERSON><PERSON> ruoli (admin, advisor, cliente), permessi granulari su report.
- Middleware API per controllo accessi.

**Prompt sviluppo:**  
- Implementa gestione ruoli e permessi granulari.

**Test:**  
- Accesso utenti, restrizioni.

---

## Checklist validazione fase
- Ogni fase deve essere testabile singolarmente (mock/demo).
- Test unitari e funzionali completati.
- Documentazione aggiornata.
