<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Attribute' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/Attribute.php',
    'Brain\\Monkey\\Container' => $vendorDir . '/brain/monkey/src/Container.php',
    'Brain\\Monkey\\Exception' => $vendorDir . '/brain/monkey/src/Exception.php',
    'Brain\\Monkey\\Expectation\\EscapeHelper' => $vendorDir . '/brain/monkey/src/Expectation/EscapeHelper.php',
    'Brain\\Monkey\\Expectation\\Exception\\Exception' => $vendorDir . '/brain/monkey/src/Expectation/Exception/Exception.php',
    'Brain\\Monkey\\Expectation\\Exception\\ExpectationArgsRequired' => $vendorDir . '/brain/monkey/src/Expectation/Exception/ExpectationArgsRequired.php',
    'Brain\\Monkey\\Expectation\\Exception\\InvalidArgumentForStub' => $vendorDir . '/brain/monkey/src/Expectation/Exception/InvalidArgumentForStub.php',
    'Brain\\Monkey\\Expectation\\Exception\\InvalidExpectationName' => $vendorDir . '/brain/monkey/src/Expectation/Exception/InvalidExpectationName.php',
    'Brain\\Monkey\\Expectation\\Exception\\InvalidExpectationType' => $vendorDir . '/brain/monkey/src/Expectation/Exception/InvalidExpectationType.php',
    'Brain\\Monkey\\Expectation\\Exception\\MissedPatchworkReplace' => $vendorDir . '/brain/monkey/src/Expectation/Exception/MissedPatchworkReplace.php',
    'Brain\\Monkey\\Expectation\\Exception\\MissingFunctionExpectations' => $vendorDir . '/brain/monkey/src/Expectation/Exception/MissingFunctionExpectations.php',
    'Brain\\Monkey\\Expectation\\Exception\\NotAllowedMethod' => $vendorDir . '/brain/monkey/src/Expectation/Exception/NotAllowedMethod.php',
    'Brain\\Monkey\\Expectation\\Expectation' => $vendorDir . '/brain/monkey/src/Expectation/Expectation.php',
    'Brain\\Monkey\\Expectation\\ExpectationFactory' => $vendorDir . '/brain/monkey/src/Expectation/ExpectationFactory.php',
    'Brain\\Monkey\\Expectation\\ExpectationTarget' => $vendorDir . '/brain/monkey/src/Expectation/ExpectationTarget.php',
    'Brain\\Monkey\\Expectation\\FunctionStub' => $vendorDir . '/brain/monkey/src/Expectation/FunctionStub.php',
    'Brain\\Monkey\\Expectation\\FunctionStubFactory' => $vendorDir . '/brain/monkey/src/Expectation/FunctionStubFactory.php',
    'Brain\\Monkey\\Hook\\Exception\\Exception' => $vendorDir . '/brain/monkey/src/Hook/Exception/Exception.php',
    'Brain\\Monkey\\Hook\\Exception\\InvalidAddedHookArgument' => $vendorDir . '/brain/monkey/src/Hook/Exception/InvalidAddedHookArgument.php',
    'Brain\\Monkey\\Hook\\Exception\\InvalidHookArgument' => $vendorDir . '/brain/monkey/src/Hook/Exception/InvalidHookArgument.php',
    'Brain\\Monkey\\Hook\\HookExpectationExecutor' => $vendorDir . '/brain/monkey/src/Hook/HookExpectationExecutor.php',
    'Brain\\Monkey\\Hook\\HookRunningStack' => $vendorDir . '/brain/monkey/src/Hook/HookRunningStack.php',
    'Brain\\Monkey\\Hook\\HookStorage' => $vendorDir . '/brain/monkey/src/Hook/HookStorage.php',
    'Brain\\Monkey\\Name\\CallbackStringForm' => $vendorDir . '/brain/monkey/src/Name/CallbackStringForm.php',
    'Brain\\Monkey\\Name\\ClassName' => $vendorDir . '/brain/monkey/src/Name/ClassName.php',
    'Brain\\Monkey\\Name\\ClosureParamStringForm' => $vendorDir . '/brain/monkey/src/Name/ClosureParamStringForm.php',
    'Brain\\Monkey\\Name\\ClosureStringForm' => $vendorDir . '/brain/monkey/src/Name/ClosureStringForm.php',
    'Brain\\Monkey\\Name\\Exception\\Exception' => $vendorDir . '/brain/monkey/src/Name/Exception/Exception.php',
    'Brain\\Monkey\\Name\\Exception\\InvalidCallable' => $vendorDir . '/brain/monkey/src/Name/Exception/InvalidCallable.php',
    'Brain\\Monkey\\Name\\Exception\\InvalidClosureParam' => $vendorDir . '/brain/monkey/src/Name/Exception/InvalidClosureParam.php',
    'Brain\\Monkey\\Name\\Exception\\InvalidName' => $vendorDir . '/brain/monkey/src/Name/Exception/InvalidName.php',
    'Brain\\Monkey\\Name\\Exception\\NotInvokableObjectAsCallback' => $vendorDir . '/brain/monkey/src/Name/Exception/NotInvokableObjectAsCallback.php',
    'Brain\\Monkey\\Name\\FunctionName' => $vendorDir . '/brain/monkey/src/Name/FunctionName.php',
    'Brain\\Monkey\\Name\\MethodName' => $vendorDir . '/brain/monkey/src/Name/MethodName.php',
    'CURLStringFile' => $vendorDir . '/symfony/polyfill-php81/Resources/stubs/CURLStringFile.php',
    'Cache_Command' => $vendorDir . '/wp-cli/cache-command/src/Cache_Command.php',
    'Capabilities_Command' => $vendorDir . '/wp-cli/role-command/src/Capabilities_Command.php',
    'Checksum_Base_Command' => $vendorDir . '/wp-cli/checksum-command/src/Checksum_Base_Command.php',
    'Checksum_Core_Command' => $vendorDir . '/wp-cli/checksum-command/src/Checksum_Core_Command.php',
    'Checksum_Plugin_Command' => $vendorDir . '/wp-cli/checksum-command/src/Checksum_Plugin_Command.php',
    'Comment_Command' => $vendorDir . '/wp-cli/entity-command/src/Comment_Command.php',
    'Comment_Meta_Command' => $vendorDir . '/wp-cli/entity-command/src/Comment_Meta_Command.php',
    'Composer\\Advisory\\Auditor' => $vendorDir . '/composer/composer/src/Composer/Advisory/Auditor.php',
    'Composer\\Advisory\\IgnoredSecurityAdvisory' => $vendorDir . '/composer/composer/src/Composer/Advisory/IgnoredSecurityAdvisory.php',
    'Composer\\Advisory\\PartialSecurityAdvisory' => $vendorDir . '/composer/composer/src/Composer/Advisory/PartialSecurityAdvisory.php',
    'Composer\\Advisory\\SecurityAdvisory' => $vendorDir . '/composer/composer/src/Composer/Advisory/SecurityAdvisory.php',
    'Composer\\Autoload\\AutoloadGenerator' => $vendorDir . '/composer/composer/src/Composer/Autoload/AutoloadGenerator.php',
    'Composer\\Autoload\\ClassLoader' => $vendorDir . '/composer/composer/src/Composer/Autoload/ClassLoader.php',
    'Composer\\Autoload\\ClassMapGenerator' => $vendorDir . '/composer/composer/src/Composer/Autoload/ClassMapGenerator.php',
    'Composer\\CaBundle\\CaBundle' => $vendorDir . '/composer/ca-bundle/src/CaBundle.php',
    'Composer\\Cache' => $vendorDir . '/composer/composer/src/Composer/Cache.php',
    'Composer\\ClassMapGenerator\\ClassMap' => $vendorDir . '/composer/class-map-generator/src/ClassMap.php',
    'Composer\\ClassMapGenerator\\ClassMapGenerator' => $vendorDir . '/composer/class-map-generator/src/ClassMapGenerator.php',
    'Composer\\ClassMapGenerator\\FileList' => $vendorDir . '/composer/class-map-generator/src/FileList.php',
    'Composer\\ClassMapGenerator\\PhpFileCleaner' => $vendorDir . '/composer/class-map-generator/src/PhpFileCleaner.php',
    'Composer\\ClassMapGenerator\\PhpFileParser' => $vendorDir . '/composer/class-map-generator/src/PhpFileParser.php',
    'Composer\\Command\\AboutCommand' => $vendorDir . '/composer/composer/src/Composer/Command/AboutCommand.php',
    'Composer\\Command\\ArchiveCommand' => $vendorDir . '/composer/composer/src/Composer/Command/ArchiveCommand.php',
    'Composer\\Command\\AuditCommand' => $vendorDir . '/composer/composer/src/Composer/Command/AuditCommand.php',
    'Composer\\Command\\BaseCommand' => $vendorDir . '/composer/composer/src/Composer/Command/BaseCommand.php',
    'Composer\\Command\\BaseDependencyCommand' => $vendorDir . '/composer/composer/src/Composer/Command/BaseDependencyCommand.php',
    'Composer\\Command\\BumpCommand' => $vendorDir . '/composer/composer/src/Composer/Command/BumpCommand.php',
    'Composer\\Command\\CheckPlatformReqsCommand' => $vendorDir . '/composer/composer/src/Composer/Command/CheckPlatformReqsCommand.php',
    'Composer\\Command\\ClearCacheCommand' => $vendorDir . '/composer/composer/src/Composer/Command/ClearCacheCommand.php',
    'Composer\\Command\\CompletionTrait' => $vendorDir . '/composer/composer/src/Composer/Command/CompletionTrait.php',
    'Composer\\Command\\ConfigCommand' => $vendorDir . '/composer/composer/src/Composer/Command/ConfigCommand.php',
    'Composer\\Command\\CreateProjectCommand' => $vendorDir . '/composer/composer/src/Composer/Command/CreateProjectCommand.php',
    'Composer\\Command\\DependsCommand' => $vendorDir . '/composer/composer/src/Composer/Command/DependsCommand.php',
    'Composer\\Command\\DiagnoseCommand' => $vendorDir . '/composer/composer/src/Composer/Command/DiagnoseCommand.php',
    'Composer\\Command\\DumpAutoloadCommand' => $vendorDir . '/composer/composer/src/Composer/Command/DumpAutoloadCommand.php',
    'Composer\\Command\\ExecCommand' => $vendorDir . '/composer/composer/src/Composer/Command/ExecCommand.php',
    'Composer\\Command\\FundCommand' => $vendorDir . '/composer/composer/src/Composer/Command/FundCommand.php',
    'Composer\\Command\\GlobalCommand' => $vendorDir . '/composer/composer/src/Composer/Command/GlobalCommand.php',
    'Composer\\Command\\HomeCommand' => $vendorDir . '/composer/composer/src/Composer/Command/HomeCommand.php',
    'Composer\\Command\\InitCommand' => $vendorDir . '/composer/composer/src/Composer/Command/InitCommand.php',
    'Composer\\Command\\InstallCommand' => $vendorDir . '/composer/composer/src/Composer/Command/InstallCommand.php',
    'Composer\\Command\\LicensesCommand' => $vendorDir . '/composer/composer/src/Composer/Command/LicensesCommand.php',
    'Composer\\Command\\OutdatedCommand' => $vendorDir . '/composer/composer/src/Composer/Command/OutdatedCommand.php',
    'Composer\\Command\\PackageDiscoveryTrait' => $vendorDir . '/composer/composer/src/Composer/Command/PackageDiscoveryTrait.php',
    'Composer\\Command\\ProhibitsCommand' => $vendorDir . '/composer/composer/src/Composer/Command/ProhibitsCommand.php',
    'Composer\\Command\\ReinstallCommand' => $vendorDir . '/composer/composer/src/Composer/Command/ReinstallCommand.php',
    'Composer\\Command\\RemoveCommand' => $vendorDir . '/composer/composer/src/Composer/Command/RemoveCommand.php',
    'Composer\\Command\\RequireCommand' => $vendorDir . '/composer/composer/src/Composer/Command/RequireCommand.php',
    'Composer\\Command\\RunScriptCommand' => $vendorDir . '/composer/composer/src/Composer/Command/RunScriptCommand.php',
    'Composer\\Command\\ScriptAliasCommand' => $vendorDir . '/composer/composer/src/Composer/Command/ScriptAliasCommand.php',
    'Composer\\Command\\SearchCommand' => $vendorDir . '/composer/composer/src/Composer/Command/SearchCommand.php',
    'Composer\\Command\\SelfUpdateCommand' => $vendorDir . '/composer/composer/src/Composer/Command/SelfUpdateCommand.php',
    'Composer\\Command\\ShowCommand' => $vendorDir . '/composer/composer/src/Composer/Command/ShowCommand.php',
    'Composer\\Command\\StatusCommand' => $vendorDir . '/composer/composer/src/Composer/Command/StatusCommand.php',
    'Composer\\Command\\SuggestsCommand' => $vendorDir . '/composer/composer/src/Composer/Command/SuggestsCommand.php',
    'Composer\\Command\\UpdateCommand' => $vendorDir . '/composer/composer/src/Composer/Command/UpdateCommand.php',
    'Composer\\Command\\ValidateCommand' => $vendorDir . '/composer/composer/src/Composer/Command/ValidateCommand.php',
    'Composer\\Compiler' => $vendorDir . '/composer/composer/src/Composer/Compiler.php',
    'Composer\\Composer' => $vendorDir . '/composer/composer/src/Composer/Composer.php',
    'Composer\\Config' => $vendorDir . '/composer/composer/src/Composer/Config.php',
    'Composer\\Config\\ConfigSourceInterface' => $vendorDir . '/composer/composer/src/Composer/Config/ConfigSourceInterface.php',
    'Composer\\Config\\JsonConfigSource' => $vendorDir . '/composer/composer/src/Composer/Config/JsonConfigSource.php',
    'Composer\\Console\\Application' => $vendorDir . '/composer/composer/src/Composer/Console/Application.php',
    'Composer\\Console\\GithubActionError' => $vendorDir . '/composer/composer/src/Composer/Console/GithubActionError.php',
    'Composer\\Console\\HtmlOutputFormatter' => $vendorDir . '/composer/composer/src/Composer/Console/HtmlOutputFormatter.php',
    'Composer\\Console\\Input\\InputArgument' => $vendorDir . '/composer/composer/src/Composer/Console/Input/InputArgument.php',
    'Composer\\Console\\Input\\InputOption' => $vendorDir . '/composer/composer/src/Composer/Console/Input/InputOption.php',
    'Composer\\DependencyResolver\\Decisions' => $vendorDir . '/composer/composer/src/Composer/DependencyResolver/Decisions.php',
    'Composer\\DependencyResolver\\DefaultPolicy' => $vendorDir . '/composer/composer/src/Composer/DependencyResolver/DefaultPolicy.php',
    'Composer\\DependencyResolver\\GenericRule' => $vendorDir . '/composer/composer/src/Composer/DependencyResolver/GenericRule.php',
    'Composer\\DependencyResolver\\LocalRepoTransaction' => $vendorDir . '/composer/composer/src/Composer/DependencyResolver/LocalRepoTransaction.php',
    'Composer\\DependencyResolver\\LockTransaction' => $vendorDir . '/composer/composer/src/Composer/DependencyResolver/LockTransaction.php',
    'Composer\\DependencyResolver\\MultiConflictRule' => $vendorDir . '/composer/composer/src/Composer/DependencyResolver/MultiConflictRule.php',
    'Composer\\DependencyResolver\\Operation\\InstallOperation' => $vendorDir . '/composer/composer/src/Composer/DependencyResolver/Operation/InstallOperation.php',
    'Composer\\DependencyResolver\\Operation\\MarkAliasInstalledOperation' => $vendorDir . '/composer/composer/src/Composer/DependencyResolver/Operation/MarkAliasInstalledOperation.php',
    'Composer\\DependencyResolver\\Operation\\MarkAliasUninstalledOperation' => $vendorDir . '/composer/composer/src/Composer/DependencyResolver/Operation/MarkAliasUninstalledOperation.php',
    'Composer\\DependencyResolver\\Operation\\OperationInterface' => $vendorDir . '/composer/composer/src/Composer/DependencyResolver/Operation/OperationInterface.php',
    'Composer\\DependencyResolver\\Operation\\SolverOperation' => $vendorDir . '/composer/composer/src/Composer/DependencyResolver/Operation/SolverOperation.php',
    'Composer\\DependencyResolver\\Operation\\UninstallOperation' => $vendorDir . '/composer/composer/src/Composer/DependencyResolver/Operation/UninstallOperation.php',
    'Composer\\DependencyResolver\\Operation\\UpdateOperation' => $vendorDir . '/composer/composer/src/Composer/DependencyResolver/Operation/UpdateOperation.php',
    'Composer\\DependencyResolver\\PolicyInterface' => $vendorDir . '/composer/composer/src/Composer/DependencyResolver/PolicyInterface.php',
    'Composer\\DependencyResolver\\Pool' => $vendorDir . '/composer/composer/src/Composer/DependencyResolver/Pool.php',
    'Composer\\DependencyResolver\\PoolBuilder' => $vendorDir . '/composer/composer/src/Composer/DependencyResolver/PoolBuilder.php',
    'Composer\\DependencyResolver\\PoolOptimizer' => $vendorDir . '/composer/composer/src/Composer/DependencyResolver/PoolOptimizer.php',
    'Composer\\DependencyResolver\\Problem' => $vendorDir . '/composer/composer/src/Composer/DependencyResolver/Problem.php',
    'Composer\\DependencyResolver\\Request' => $vendorDir . '/composer/composer/src/Composer/DependencyResolver/Request.php',
    'Composer\\DependencyResolver\\Rule' => $vendorDir . '/composer/composer/src/Composer/DependencyResolver/Rule.php',
    'Composer\\DependencyResolver\\Rule2Literals' => $vendorDir . '/composer/composer/src/Composer/DependencyResolver/Rule2Literals.php',
    'Composer\\DependencyResolver\\RuleSet' => $vendorDir . '/composer/composer/src/Composer/DependencyResolver/RuleSet.php',
    'Composer\\DependencyResolver\\RuleSetGenerator' => $vendorDir . '/composer/composer/src/Composer/DependencyResolver/RuleSetGenerator.php',
    'Composer\\DependencyResolver\\RuleSetIterator' => $vendorDir . '/composer/composer/src/Composer/DependencyResolver/RuleSetIterator.php',
    'Composer\\DependencyResolver\\RuleWatchChain' => $vendorDir . '/composer/composer/src/Composer/DependencyResolver/RuleWatchChain.php',
    'Composer\\DependencyResolver\\RuleWatchGraph' => $vendorDir . '/composer/composer/src/Composer/DependencyResolver/RuleWatchGraph.php',
    'Composer\\DependencyResolver\\RuleWatchNode' => $vendorDir . '/composer/composer/src/Composer/DependencyResolver/RuleWatchNode.php',
    'Composer\\DependencyResolver\\Solver' => $vendorDir . '/composer/composer/src/Composer/DependencyResolver/Solver.php',
    'Composer\\DependencyResolver\\SolverBugException' => $vendorDir . '/composer/composer/src/Composer/DependencyResolver/SolverBugException.php',
    'Composer\\DependencyResolver\\SolverProblemsException' => $vendorDir . '/composer/composer/src/Composer/DependencyResolver/SolverProblemsException.php',
    'Composer\\DependencyResolver\\Transaction' => $vendorDir . '/composer/composer/src/Composer/DependencyResolver/Transaction.php',
    'Composer\\Downloader\\ArchiveDownloader' => $vendorDir . '/composer/composer/src/Composer/Downloader/ArchiveDownloader.php',
    'Composer\\Downloader\\ChangeReportInterface' => $vendorDir . '/composer/composer/src/Composer/Downloader/ChangeReportInterface.php',
    'Composer\\Downloader\\DownloadManager' => $vendorDir . '/composer/composer/src/Composer/Downloader/DownloadManager.php',
    'Composer\\Downloader\\DownloaderInterface' => $vendorDir . '/composer/composer/src/Composer/Downloader/DownloaderInterface.php',
    'Composer\\Downloader\\DvcsDownloaderInterface' => $vendorDir . '/composer/composer/src/Composer/Downloader/DvcsDownloaderInterface.php',
    'Composer\\Downloader\\FileDownloader' => $vendorDir . '/composer/composer/src/Composer/Downloader/FileDownloader.php',
    'Composer\\Downloader\\FilesystemException' => $vendorDir . '/composer/composer/src/Composer/Downloader/FilesystemException.php',
    'Composer\\Downloader\\FossilDownloader' => $vendorDir . '/composer/composer/src/Composer/Downloader/FossilDownloader.php',
    'Composer\\Downloader\\GitDownloader' => $vendorDir . '/composer/composer/src/Composer/Downloader/GitDownloader.php',
    'Composer\\Downloader\\GzipDownloader' => $vendorDir . '/composer/composer/src/Composer/Downloader/GzipDownloader.php',
    'Composer\\Downloader\\HgDownloader' => $vendorDir . '/composer/composer/src/Composer/Downloader/HgDownloader.php',
    'Composer\\Downloader\\MaxFileSizeExceededException' => $vendorDir . '/composer/composer/src/Composer/Downloader/MaxFileSizeExceededException.php',
    'Composer\\Downloader\\PathDownloader' => $vendorDir . '/composer/composer/src/Composer/Downloader/PathDownloader.php',
    'Composer\\Downloader\\PerforceDownloader' => $vendorDir . '/composer/composer/src/Composer/Downloader/PerforceDownloader.php',
    'Composer\\Downloader\\PharDownloader' => $vendorDir . '/composer/composer/src/Composer/Downloader/PharDownloader.php',
    'Composer\\Downloader\\RarDownloader' => $vendorDir . '/composer/composer/src/Composer/Downloader/RarDownloader.php',
    'Composer\\Downloader\\SvnDownloader' => $vendorDir . '/composer/composer/src/Composer/Downloader/SvnDownloader.php',
    'Composer\\Downloader\\TarDownloader' => $vendorDir . '/composer/composer/src/Composer/Downloader/TarDownloader.php',
    'Composer\\Downloader\\TransportException' => $vendorDir . '/composer/composer/src/Composer/Downloader/TransportException.php',
    'Composer\\Downloader\\VcsCapableDownloaderInterface' => $vendorDir . '/composer/composer/src/Composer/Downloader/VcsCapableDownloaderInterface.php',
    'Composer\\Downloader\\VcsDownloader' => $vendorDir . '/composer/composer/src/Composer/Downloader/VcsDownloader.php',
    'Composer\\Downloader\\XzDownloader' => $vendorDir . '/composer/composer/src/Composer/Downloader/XzDownloader.php',
    'Composer\\Downloader\\ZipDownloader' => $vendorDir . '/composer/composer/src/Composer/Downloader/ZipDownloader.php',
    'Composer\\EventDispatcher\\Event' => $vendorDir . '/composer/composer/src/Composer/EventDispatcher/Event.php',
    'Composer\\EventDispatcher\\EventDispatcher' => $vendorDir . '/composer/composer/src/Composer/EventDispatcher/EventDispatcher.php',
    'Composer\\EventDispatcher\\EventSubscriberInterface' => $vendorDir . '/composer/composer/src/Composer/EventDispatcher/EventSubscriberInterface.php',
    'Composer\\EventDispatcher\\ScriptExecutionException' => $vendorDir . '/composer/composer/src/Composer/EventDispatcher/ScriptExecutionException.php',
    'Composer\\Exception\\IrrecoverableDownloadException' => $vendorDir . '/composer/composer/src/Composer/Exception/IrrecoverableDownloadException.php',
    'Composer\\Exception\\NoSslException' => $vendorDir . '/composer/composer/src/Composer/Exception/NoSslException.php',
    'Composer\\Factory' => $vendorDir . '/composer/composer/src/Composer/Factory.php',
    'Composer\\Filter\\PlatformRequirementFilter\\IgnoreAllPlatformRequirementFilter' => $vendorDir . '/composer/composer/src/Composer/Filter/PlatformRequirementFilter/IgnoreAllPlatformRequirementFilter.php',
    'Composer\\Filter\\PlatformRequirementFilter\\IgnoreListPlatformRequirementFilter' => $vendorDir . '/composer/composer/src/Composer/Filter/PlatformRequirementFilter/IgnoreListPlatformRequirementFilter.php',
    'Composer\\Filter\\PlatformRequirementFilter\\IgnoreNothingPlatformRequirementFilter' => $vendorDir . '/composer/composer/src/Composer/Filter/PlatformRequirementFilter/IgnoreNothingPlatformRequirementFilter.php',
    'Composer\\Filter\\PlatformRequirementFilter\\PlatformRequirementFilterFactory' => $vendorDir . '/composer/composer/src/Composer/Filter/PlatformRequirementFilter/PlatformRequirementFilterFactory.php',
    'Composer\\Filter\\PlatformRequirementFilter\\PlatformRequirementFilterInterface' => $vendorDir . '/composer/composer/src/Composer/Filter/PlatformRequirementFilter/PlatformRequirementFilterInterface.php',
    'Composer\\IO\\BaseIO' => $vendorDir . '/composer/composer/src/Composer/IO/BaseIO.php',
    'Composer\\IO\\BufferIO' => $vendorDir . '/composer/composer/src/Composer/IO/BufferIO.php',
    'Composer\\IO\\ConsoleIO' => $vendorDir . '/composer/composer/src/Composer/IO/ConsoleIO.php',
    'Composer\\IO\\IOInterface' => $vendorDir . '/composer/composer/src/Composer/IO/IOInterface.php',
    'Composer\\IO\\NullIO' => $vendorDir . '/composer/composer/src/Composer/IO/NullIO.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'Composer\\Installer' => $vendorDir . '/composer/composer/src/Composer/Installer.php',
    'Composer\\Installer\\BinaryInstaller' => $vendorDir . '/composer/composer/src/Composer/Installer/BinaryInstaller.php',
    'Composer\\Installer\\BinaryPresenceInterface' => $vendorDir . '/composer/composer/src/Composer/Installer/BinaryPresenceInterface.php',
    'Composer\\Installer\\InstallationManager' => $vendorDir . '/composer/composer/src/Composer/Installer/InstallationManager.php',
    'Composer\\Installer\\InstallerEvent' => $vendorDir . '/composer/composer/src/Composer/Installer/InstallerEvent.php',
    'Composer\\Installer\\InstallerEvents' => $vendorDir . '/composer/composer/src/Composer/Installer/InstallerEvents.php',
    'Composer\\Installer\\InstallerInterface' => $vendorDir . '/composer/composer/src/Composer/Installer/InstallerInterface.php',
    'Composer\\Installer\\LibraryInstaller' => $vendorDir . '/composer/composer/src/Composer/Installer/LibraryInstaller.php',
    'Composer\\Installer\\MetapackageInstaller' => $vendorDir . '/composer/composer/src/Composer/Installer/MetapackageInstaller.php',
    'Composer\\Installer\\NoopInstaller' => $vendorDir . '/composer/composer/src/Composer/Installer/NoopInstaller.php',
    'Composer\\Installer\\PackageEvent' => $vendorDir . '/composer/composer/src/Composer/Installer/PackageEvent.php',
    'Composer\\Installer\\PackageEvents' => $vendorDir . '/composer/composer/src/Composer/Installer/PackageEvents.php',
    'Composer\\Installer\\PluginInstaller' => $vendorDir . '/composer/composer/src/Composer/Installer/PluginInstaller.php',
    'Composer\\Installer\\ProjectInstaller' => $vendorDir . '/composer/composer/src/Composer/Installer/ProjectInstaller.php',
    'Composer\\Installer\\SuggestedPackagesReporter' => $vendorDir . '/composer/composer/src/Composer/Installer/SuggestedPackagesReporter.php',
    'Composer\\Json\\JsonFile' => $vendorDir . '/composer/composer/src/Composer/Json/JsonFile.php',
    'Composer\\Json\\JsonFormatter' => $vendorDir . '/composer/composer/src/Composer/Json/JsonFormatter.php',
    'Composer\\Json\\JsonManipulator' => $vendorDir . '/composer/composer/src/Composer/Json/JsonManipulator.php',
    'Composer\\Json\\JsonValidationException' => $vendorDir . '/composer/composer/src/Composer/Json/JsonValidationException.php',
    'Composer\\MetadataMinifier\\MetadataMinifier' => $vendorDir . '/composer/metadata-minifier/src/MetadataMinifier.php',
    'Composer\\PHPStan\\ConfigReturnTypeExtension' => $vendorDir . '/composer/composer/src/Composer/PHPStan/ConfigReturnTypeExtension.php',
    'Composer\\PHPStan\\RuleReasonDataReturnTypeExtension' => $vendorDir . '/composer/composer/src/Composer/PHPStan/RuleReasonDataReturnTypeExtension.php',
    'Composer\\Package\\AliasPackage' => $vendorDir . '/composer/composer/src/Composer/Package/AliasPackage.php',
    'Composer\\Package\\Archiver\\ArchivableFilesFilter' => $vendorDir . '/composer/composer/src/Composer/Package/Archiver/ArchivableFilesFilter.php',
    'Composer\\Package\\Archiver\\ArchivableFilesFinder' => $vendorDir . '/composer/composer/src/Composer/Package/Archiver/ArchivableFilesFinder.php',
    'Composer\\Package\\Archiver\\ArchiveManager' => $vendorDir . '/composer/composer/src/Composer/Package/Archiver/ArchiveManager.php',
    'Composer\\Package\\Archiver\\ArchiverInterface' => $vendorDir . '/composer/composer/src/Composer/Package/Archiver/ArchiverInterface.php',
    'Composer\\Package\\Archiver\\BaseExcludeFilter' => $vendorDir . '/composer/composer/src/Composer/Package/Archiver/BaseExcludeFilter.php',
    'Composer\\Package\\Archiver\\ComposerExcludeFilter' => $vendorDir . '/composer/composer/src/Composer/Package/Archiver/ComposerExcludeFilter.php',
    'Composer\\Package\\Archiver\\GitExcludeFilter' => $vendorDir . '/composer/composer/src/Composer/Package/Archiver/GitExcludeFilter.php',
    'Composer\\Package\\Archiver\\PharArchiver' => $vendorDir . '/composer/composer/src/Composer/Package/Archiver/PharArchiver.php',
    'Composer\\Package\\Archiver\\ZipArchiver' => $vendorDir . '/composer/composer/src/Composer/Package/Archiver/ZipArchiver.php',
    'Composer\\Package\\BasePackage' => $vendorDir . '/composer/composer/src/Composer/Package/BasePackage.php',
    'Composer\\Package\\Comparer\\Comparer' => $vendorDir . '/composer/composer/src/Composer/Package/Comparer/Comparer.php',
    'Composer\\Package\\CompleteAliasPackage' => $vendorDir . '/composer/composer/src/Composer/Package/CompleteAliasPackage.php',
    'Composer\\Package\\CompletePackage' => $vendorDir . '/composer/composer/src/Composer/Package/CompletePackage.php',
    'Composer\\Package\\CompletePackageInterface' => $vendorDir . '/composer/composer/src/Composer/Package/CompletePackageInterface.php',
    'Composer\\Package\\Dumper\\ArrayDumper' => $vendorDir . '/composer/composer/src/Composer/Package/Dumper/ArrayDumper.php',
    'Composer\\Package\\Link' => $vendorDir . '/composer/composer/src/Composer/Package/Link.php',
    'Composer\\Package\\Loader\\ArrayLoader' => $vendorDir . '/composer/composer/src/Composer/Package/Loader/ArrayLoader.php',
    'Composer\\Package\\Loader\\InvalidPackageException' => $vendorDir . '/composer/composer/src/Composer/Package/Loader/InvalidPackageException.php',
    'Composer\\Package\\Loader\\JsonLoader' => $vendorDir . '/composer/composer/src/Composer/Package/Loader/JsonLoader.php',
    'Composer\\Package\\Loader\\LoaderInterface' => $vendorDir . '/composer/composer/src/Composer/Package/Loader/LoaderInterface.php',
    'Composer\\Package\\Loader\\RootPackageLoader' => $vendorDir . '/composer/composer/src/Composer/Package/Loader/RootPackageLoader.php',
    'Composer\\Package\\Loader\\ValidatingArrayLoader' => $vendorDir . '/composer/composer/src/Composer/Package/Loader/ValidatingArrayLoader.php',
    'Composer\\Package\\Locker' => $vendorDir . '/composer/composer/src/Composer/Package/Locker.php',
    'Composer\\Package\\Package' => $vendorDir . '/composer/composer/src/Composer/Package/Package.php',
    'Composer\\Package\\PackageInterface' => $vendorDir . '/composer/composer/src/Composer/Package/PackageInterface.php',
    'Composer\\Package\\RootAliasPackage' => $vendorDir . '/composer/composer/src/Composer/Package/RootAliasPackage.php',
    'Composer\\Package\\RootPackage' => $vendorDir . '/composer/composer/src/Composer/Package/RootPackage.php',
    'Composer\\Package\\RootPackageInterface' => $vendorDir . '/composer/composer/src/Composer/Package/RootPackageInterface.php',
    'Composer\\Package\\Version\\StabilityFilter' => $vendorDir . '/composer/composer/src/Composer/Package/Version/StabilityFilter.php',
    'Composer\\Package\\Version\\VersionBumper' => $vendorDir . '/composer/composer/src/Composer/Package/Version/VersionBumper.php',
    'Composer\\Package\\Version\\VersionGuesser' => $vendorDir . '/composer/composer/src/Composer/Package/Version/VersionGuesser.php',
    'Composer\\Package\\Version\\VersionParser' => $vendorDir . '/composer/composer/src/Composer/Package/Version/VersionParser.php',
    'Composer\\Package\\Version\\VersionSelector' => $vendorDir . '/composer/composer/src/Composer/Package/Version/VersionSelector.php',
    'Composer\\PartialComposer' => $vendorDir . '/composer/composer/src/Composer/PartialComposer.php',
    'Composer\\Pcre\\MatchAllResult' => $vendorDir . '/composer/pcre/src/MatchAllResult.php',
    'Composer\\Pcre\\MatchAllStrictGroupsResult' => $vendorDir . '/composer/pcre/src/MatchAllStrictGroupsResult.php',
    'Composer\\Pcre\\MatchAllWithOffsetsResult' => $vendorDir . '/composer/pcre/src/MatchAllWithOffsetsResult.php',
    'Composer\\Pcre\\MatchResult' => $vendorDir . '/composer/pcre/src/MatchResult.php',
    'Composer\\Pcre\\MatchStrictGroupsResult' => $vendorDir . '/composer/pcre/src/MatchStrictGroupsResult.php',
    'Composer\\Pcre\\MatchWithOffsetsResult' => $vendorDir . '/composer/pcre/src/MatchWithOffsetsResult.php',
    'Composer\\Pcre\\PHPStan\\InvalidRegexPatternRule' => $vendorDir . '/composer/pcre/src/PHPStan/InvalidRegexPatternRule.php',
    'Composer\\Pcre\\PHPStan\\PregMatchFlags' => $vendorDir . '/composer/pcre/src/PHPStan/PregMatchFlags.php',
    'Composer\\Pcre\\PHPStan\\PregMatchParameterOutTypeExtension' => $vendorDir . '/composer/pcre/src/PHPStan/PregMatchParameterOutTypeExtension.php',
    'Composer\\Pcre\\PHPStan\\PregMatchTypeSpecifyingExtension' => $vendorDir . '/composer/pcre/src/PHPStan/PregMatchTypeSpecifyingExtension.php',
    'Composer\\Pcre\\PHPStan\\PregReplaceCallbackClosureTypeExtension' => $vendorDir . '/composer/pcre/src/PHPStan/PregReplaceCallbackClosureTypeExtension.php',
    'Composer\\Pcre\\PHPStan\\UnsafeStrictGroupsCallRule' => $vendorDir . '/composer/pcre/src/PHPStan/UnsafeStrictGroupsCallRule.php',
    'Composer\\Pcre\\PcreException' => $vendorDir . '/composer/pcre/src/PcreException.php',
    'Composer\\Pcre\\Preg' => $vendorDir . '/composer/pcre/src/Preg.php',
    'Composer\\Pcre\\Regex' => $vendorDir . '/composer/pcre/src/Regex.php',
    'Composer\\Pcre\\ReplaceResult' => $vendorDir . '/composer/pcre/src/ReplaceResult.php',
    'Composer\\Pcre\\UnexpectedNullMatchException' => $vendorDir . '/composer/pcre/src/UnexpectedNullMatchException.php',
    'Composer\\Platform\\HhvmDetector' => $vendorDir . '/composer/composer/src/Composer/Platform/HhvmDetector.php',
    'Composer\\Platform\\Runtime' => $vendorDir . '/composer/composer/src/Composer/Platform/Runtime.php',
    'Composer\\Platform\\Version' => $vendorDir . '/composer/composer/src/Composer/Platform/Version.php',
    'Composer\\Plugin\\Capability\\Capability' => $vendorDir . '/composer/composer/src/Composer/Plugin/Capability/Capability.php',
    'Composer\\Plugin\\Capability\\CommandProvider' => $vendorDir . '/composer/composer/src/Composer/Plugin/Capability/CommandProvider.php',
    'Composer\\Plugin\\Capable' => $vendorDir . '/composer/composer/src/Composer/Plugin/Capable.php',
    'Composer\\Plugin\\CommandEvent' => $vendorDir . '/composer/composer/src/Composer/Plugin/CommandEvent.php',
    'Composer\\Plugin\\PluginBlockedException' => $vendorDir . '/composer/composer/src/Composer/Plugin/PluginBlockedException.php',
    'Composer\\Plugin\\PluginEvents' => $vendorDir . '/composer/composer/src/Composer/Plugin/PluginEvents.php',
    'Composer\\Plugin\\PluginInterface' => $vendorDir . '/composer/composer/src/Composer/Plugin/PluginInterface.php',
    'Composer\\Plugin\\PluginManager' => $vendorDir . '/composer/composer/src/Composer/Plugin/PluginManager.php',
    'Composer\\Plugin\\PostFileDownloadEvent' => $vendorDir . '/composer/composer/src/Composer/Plugin/PostFileDownloadEvent.php',
    'Composer\\Plugin\\PreCommandRunEvent' => $vendorDir . '/composer/composer/src/Composer/Plugin/PreCommandRunEvent.php',
    'Composer\\Plugin\\PreFileDownloadEvent' => $vendorDir . '/composer/composer/src/Composer/Plugin/PreFileDownloadEvent.php',
    'Composer\\Plugin\\PrePoolCreateEvent' => $vendorDir . '/composer/composer/src/Composer/Plugin/PrePoolCreateEvent.php',
    'Composer\\Question\\StrictConfirmationQuestion' => $vendorDir . '/composer/composer/src/Composer/Question/StrictConfirmationQuestion.php',
    'Composer\\Repository\\AdvisoryProviderInterface' => $vendorDir . '/composer/composer/src/Composer/Repository/AdvisoryProviderInterface.php',
    'Composer\\Repository\\ArrayRepository' => $vendorDir . '/composer/composer/src/Composer/Repository/ArrayRepository.php',
    'Composer\\Repository\\ArtifactRepository' => $vendorDir . '/composer/composer/src/Composer/Repository/ArtifactRepository.php',
    'Composer\\Repository\\CanonicalPackagesTrait' => $vendorDir . '/composer/composer/src/Composer/Repository/CanonicalPackagesTrait.php',
    'Composer\\Repository\\ComposerRepository' => $vendorDir . '/composer/composer/src/Composer/Repository/ComposerRepository.php',
    'Composer\\Repository\\CompositeRepository' => $vendorDir . '/composer/composer/src/Composer/Repository/CompositeRepository.php',
    'Composer\\Repository\\ConfigurableRepositoryInterface' => $vendorDir . '/composer/composer/src/Composer/Repository/ConfigurableRepositoryInterface.php',
    'Composer\\Repository\\FilesystemRepository' => $vendorDir . '/composer/composer/src/Composer/Repository/FilesystemRepository.php',
    'Composer\\Repository\\FilterRepository' => $vendorDir . '/composer/composer/src/Composer/Repository/FilterRepository.php',
    'Composer\\Repository\\InstalledArrayRepository' => $vendorDir . '/composer/composer/src/Composer/Repository/InstalledArrayRepository.php',
    'Composer\\Repository\\InstalledFilesystemRepository' => $vendorDir . '/composer/composer/src/Composer/Repository/InstalledFilesystemRepository.php',
    'Composer\\Repository\\InstalledRepository' => $vendorDir . '/composer/composer/src/Composer/Repository/InstalledRepository.php',
    'Composer\\Repository\\InstalledRepositoryInterface' => $vendorDir . '/composer/composer/src/Composer/Repository/InstalledRepositoryInterface.php',
    'Composer\\Repository\\InvalidRepositoryException' => $vendorDir . '/composer/composer/src/Composer/Repository/InvalidRepositoryException.php',
    'Composer\\Repository\\LockArrayRepository' => $vendorDir . '/composer/composer/src/Composer/Repository/LockArrayRepository.php',
    'Composer\\Repository\\PackageRepository' => $vendorDir . '/composer/composer/src/Composer/Repository/PackageRepository.php',
    'Composer\\Repository\\PathRepository' => $vendorDir . '/composer/composer/src/Composer/Repository/PathRepository.php',
    'Composer\\Repository\\PearRepository' => $vendorDir . '/composer/composer/src/Composer/Repository/PearRepository.php',
    'Composer\\Repository\\PlatformRepository' => $vendorDir . '/composer/composer/src/Composer/Repository/PlatformRepository.php',
    'Composer\\Repository\\RepositoryFactory' => $vendorDir . '/composer/composer/src/Composer/Repository/RepositoryFactory.php',
    'Composer\\Repository\\RepositoryInterface' => $vendorDir . '/composer/composer/src/Composer/Repository/RepositoryInterface.php',
    'Composer\\Repository\\RepositoryManager' => $vendorDir . '/composer/composer/src/Composer/Repository/RepositoryManager.php',
    'Composer\\Repository\\RepositorySecurityException' => $vendorDir . '/composer/composer/src/Composer/Repository/RepositorySecurityException.php',
    'Composer\\Repository\\RepositorySet' => $vendorDir . '/composer/composer/src/Composer/Repository/RepositorySet.php',
    'Composer\\Repository\\RepositoryUtils' => $vendorDir . '/composer/composer/src/Composer/Repository/RepositoryUtils.php',
    'Composer\\Repository\\RootPackageRepository' => $vendorDir . '/composer/composer/src/Composer/Repository/RootPackageRepository.php',
    'Composer\\Repository\\VcsRepository' => $vendorDir . '/composer/composer/src/Composer/Repository/VcsRepository.php',
    'Composer\\Repository\\Vcs\\FossilDriver' => $vendorDir . '/composer/composer/src/Composer/Repository/Vcs/FossilDriver.php',
    'Composer\\Repository\\Vcs\\GitBitbucketDriver' => $vendorDir . '/composer/composer/src/Composer/Repository/Vcs/GitBitbucketDriver.php',
    'Composer\\Repository\\Vcs\\GitDriver' => $vendorDir . '/composer/composer/src/Composer/Repository/Vcs/GitDriver.php',
    'Composer\\Repository\\Vcs\\GitHubDriver' => $vendorDir . '/composer/composer/src/Composer/Repository/Vcs/GitHubDriver.php',
    'Composer\\Repository\\Vcs\\GitLabDriver' => $vendorDir . '/composer/composer/src/Composer/Repository/Vcs/GitLabDriver.php',
    'Composer\\Repository\\Vcs\\HgDriver' => $vendorDir . '/composer/composer/src/Composer/Repository/Vcs/HgDriver.php',
    'Composer\\Repository\\Vcs\\PerforceDriver' => $vendorDir . '/composer/composer/src/Composer/Repository/Vcs/PerforceDriver.php',
    'Composer\\Repository\\Vcs\\SvnDriver' => $vendorDir . '/composer/composer/src/Composer/Repository/Vcs/SvnDriver.php',
    'Composer\\Repository\\Vcs\\VcsDriver' => $vendorDir . '/composer/composer/src/Composer/Repository/Vcs/VcsDriver.php',
    'Composer\\Repository\\Vcs\\VcsDriverInterface' => $vendorDir . '/composer/composer/src/Composer/Repository/Vcs/VcsDriverInterface.php',
    'Composer\\Repository\\VersionCacheInterface' => $vendorDir . '/composer/composer/src/Composer/Repository/VersionCacheInterface.php',
    'Composer\\Repository\\WritableArrayRepository' => $vendorDir . '/composer/composer/src/Composer/Repository/WritableArrayRepository.php',
    'Composer\\Repository\\WritableRepositoryInterface' => $vendorDir . '/composer/composer/src/Composer/Repository/WritableRepositoryInterface.php',
    'Composer\\Script\\Event' => $vendorDir . '/composer/composer/src/Composer/Script/Event.php',
    'Composer\\Script\\ScriptEvents' => $vendorDir . '/composer/composer/src/Composer/Script/ScriptEvents.php',
    'Composer\\SelfUpdate\\Keys' => $vendorDir . '/composer/composer/src/Composer/SelfUpdate/Keys.php',
    'Composer\\SelfUpdate\\Versions' => $vendorDir . '/composer/composer/src/Composer/SelfUpdate/Versions.php',
    'Composer\\Semver\\Comparator' => $vendorDir . '/composer/semver/src/Comparator.php',
    'Composer\\Semver\\CompilingMatcher' => $vendorDir . '/composer/semver/src/CompilingMatcher.php',
    'Composer\\Semver\\Constraint\\Bound' => $vendorDir . '/composer/semver/src/Constraint/Bound.php',
    'Composer\\Semver\\Constraint\\Constraint' => $vendorDir . '/composer/semver/src/Constraint/Constraint.php',
    'Composer\\Semver\\Constraint\\ConstraintInterface' => $vendorDir . '/composer/semver/src/Constraint/ConstraintInterface.php',
    'Composer\\Semver\\Constraint\\MatchAllConstraint' => $vendorDir . '/composer/semver/src/Constraint/MatchAllConstraint.php',
    'Composer\\Semver\\Constraint\\MatchNoneConstraint' => $vendorDir . '/composer/semver/src/Constraint/MatchNoneConstraint.php',
    'Composer\\Semver\\Constraint\\MultiConstraint' => $vendorDir . '/composer/semver/src/Constraint/MultiConstraint.php',
    'Composer\\Semver\\Interval' => $vendorDir . '/composer/semver/src/Interval.php',
    'Composer\\Semver\\Intervals' => $vendorDir . '/composer/semver/src/Intervals.php',
    'Composer\\Semver\\Semver' => $vendorDir . '/composer/semver/src/Semver.php',
    'Composer\\Semver\\VersionParser' => $vendorDir . '/composer/semver/src/VersionParser.php',
    'Composer\\Spdx\\SpdxLicenses' => $vendorDir . '/composer/spdx-licenses/src/SpdxLicenses.php',
    'Composer\\Util\\AuthHelper' => $vendorDir . '/composer/composer/src/Composer/Util/AuthHelper.php',
    'Composer\\Util\\Bitbucket' => $vendorDir . '/composer/composer/src/Composer/Util/Bitbucket.php',
    'Composer\\Util\\ComposerMirror' => $vendorDir . '/composer/composer/src/Composer/Util/ComposerMirror.php',
    'Composer\\Util\\ConfigValidator' => $vendorDir . '/composer/composer/src/Composer/Util/ConfigValidator.php',
    'Composer\\Util\\ErrorHandler' => $vendorDir . '/composer/composer/src/Composer/Util/ErrorHandler.php',
    'Composer\\Util\\Filesystem' => $vendorDir . '/composer/composer/src/Composer/Util/Filesystem.php',
    'Composer\\Util\\Git' => $vendorDir . '/composer/composer/src/Composer/Util/Git.php',
    'Composer\\Util\\GitHub' => $vendorDir . '/composer/composer/src/Composer/Util/GitHub.php',
    'Composer\\Util\\GitLab' => $vendorDir . '/composer/composer/src/Composer/Util/GitLab.php',
    'Composer\\Util\\Hg' => $vendorDir . '/composer/composer/src/Composer/Util/Hg.php',
    'Composer\\Util\\HttpDownloader' => $vendorDir . '/composer/composer/src/Composer/Util/HttpDownloader.php',
    'Composer\\Util\\Http\\CurlDownloader' => $vendorDir . '/composer/composer/src/Composer/Util/Http/CurlDownloader.php',
    'Composer\\Util\\Http\\CurlResponse' => $vendorDir . '/composer/composer/src/Composer/Util/Http/CurlResponse.php',
    'Composer\\Util\\Http\\ProxyItem' => $vendorDir . '/composer/composer/src/Composer/Util/Http/ProxyItem.php',
    'Composer\\Util\\Http\\ProxyManager' => $vendorDir . '/composer/composer/src/Composer/Util/Http/ProxyManager.php',
    'Composer\\Util\\Http\\RequestProxy' => $vendorDir . '/composer/composer/src/Composer/Util/Http/RequestProxy.php',
    'Composer\\Util\\Http\\Response' => $vendorDir . '/composer/composer/src/Composer/Util/Http/Response.php',
    'Composer\\Util\\IniHelper' => $vendorDir . '/composer/composer/src/Composer/Util/IniHelper.php',
    'Composer\\Util\\Loop' => $vendorDir . '/composer/composer/src/Composer/Util/Loop.php',
    'Composer\\Util\\MetadataMinifier' => $vendorDir . '/composer/composer/src/Composer/Util/MetadataMinifier.php',
    'Composer\\Util\\NoProxyPattern' => $vendorDir . '/composer/composer/src/Composer/Util/NoProxyPattern.php',
    'Composer\\Util\\PackageInfo' => $vendorDir . '/composer/composer/src/Composer/Util/PackageInfo.php',
    'Composer\\Util\\PackageSorter' => $vendorDir . '/composer/composer/src/Composer/Util/PackageSorter.php',
    'Composer\\Util\\Perforce' => $vendorDir . '/composer/composer/src/Composer/Util/Perforce.php',
    'Composer\\Util\\Platform' => $vendorDir . '/composer/composer/src/Composer/Util/Platform.php',
    'Composer\\Util\\ProcessExecutor' => $vendorDir . '/composer/composer/src/Composer/Util/ProcessExecutor.php',
    'Composer\\Util\\RemoteFilesystem' => $vendorDir . '/composer/composer/src/Composer/Util/RemoteFilesystem.php',
    'Composer\\Util\\Silencer' => $vendorDir . '/composer/composer/src/Composer/Util/Silencer.php',
    'Composer\\Util\\StreamContextFactory' => $vendorDir . '/composer/composer/src/Composer/Util/StreamContextFactory.php',
    'Composer\\Util\\Svn' => $vendorDir . '/composer/composer/src/Composer/Util/Svn.php',
    'Composer\\Util\\SyncHelper' => $vendorDir . '/composer/composer/src/Composer/Util/SyncHelper.php',
    'Composer\\Util\\Tar' => $vendorDir . '/composer/composer/src/Composer/Util/Tar.php',
    'Composer\\Util\\TlsHelper' => $vendorDir . '/composer/composer/src/Composer/Util/TlsHelper.php',
    'Composer\\Util\\Url' => $vendorDir . '/composer/composer/src/Composer/Util/Url.php',
    'Composer\\Util\\Zip' => $vendorDir . '/composer/composer/src/Composer/Util/Zip.php',
    'Composer\\XdebugHandler\\PhpConfig' => $vendorDir . '/composer/xdebug-handler/src/PhpConfig.php',
    'Composer\\XdebugHandler\\Process' => $vendorDir . '/composer/xdebug-handler/src/Process.php',
    'Composer\\XdebugHandler\\Status' => $vendorDir . '/composer/xdebug-handler/src/Status.php',
    'Composer\\XdebugHandler\\XdebugHandler' => $vendorDir . '/composer/xdebug-handler/src/XdebugHandler.php',
    'Config_Command' => $vendorDir . '/wp-cli/config-command/src/Config_Command.php',
    'Core_Command' => $vendorDir . '/wp-cli/core-command/src/Core_Command.php',
    'Core_Command_Namespace' => $vendorDir . '/wp-cli/checksum-command/src/Core_Command_Namespace.php',
    'Core_Language_Command' => $vendorDir . '/wp-cli/language-command/src/Core_Language_Command.php',
    'Cron_Command' => $vendorDir . '/wp-cli/cron-command/src/Cron_Command.php',
    'Cron_Event_Command' => $vendorDir . '/wp-cli/cron-command/src/Cron_Event_Command.php',
    'Cron_Schedule_Command' => $vendorDir . '/wp-cli/cron-command/src/Cron_Schedule_Command.php',
    'DB_Command' => $vendorDir . '/wp-cli/db-command/src/DB_Command.php',
    'DeepCopy\\DeepCopy' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/DeepCopy.php',
    'DeepCopy\\Exception\\CloneException' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Exception/CloneException.php',
    'DeepCopy\\Exception\\PropertyException' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Exception/PropertyException.php',
    'DeepCopy\\Filter\\ChainableFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Filter/ChainableFilter.php',
    'DeepCopy\\Filter\\Doctrine\\DoctrineCollectionFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Filter/Doctrine/DoctrineCollectionFilter.php',
    'DeepCopy\\Filter\\Doctrine\\DoctrineEmptyCollectionFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Filter/Doctrine/DoctrineEmptyCollectionFilter.php',
    'DeepCopy\\Filter\\Doctrine\\DoctrineProxyFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Filter/Doctrine/DoctrineProxyFilter.php',
    'DeepCopy\\Filter\\Filter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Filter/Filter.php',
    'DeepCopy\\Filter\\KeepFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Filter/KeepFilter.php',
    'DeepCopy\\Filter\\ReplaceFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Filter/ReplaceFilter.php',
    'DeepCopy\\Filter\\SetNullFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Filter/SetNullFilter.php',
    'DeepCopy\\Matcher\\Doctrine\\DoctrineProxyMatcher' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Matcher/Doctrine/DoctrineProxyMatcher.php',
    'DeepCopy\\Matcher\\Matcher' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Matcher/Matcher.php',
    'DeepCopy\\Matcher\\PropertyMatcher' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Matcher/PropertyMatcher.php',
    'DeepCopy\\Matcher\\PropertyNameMatcher' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Matcher/PropertyNameMatcher.php',
    'DeepCopy\\Matcher\\PropertyTypeMatcher' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Matcher/PropertyTypeMatcher.php',
    'DeepCopy\\Reflection\\ReflectionHelper' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/Reflection/ReflectionHelper.php',
    'DeepCopy\\TypeFilter\\Date\\DateIntervalFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/TypeFilter/Date/DateIntervalFilter.php',
    'DeepCopy\\TypeFilter\\Date\\DatePeriodFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/TypeFilter/Date/DatePeriodFilter.php',
    'DeepCopy\\TypeFilter\\ReplaceFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/TypeFilter/ReplaceFilter.php',
    'DeepCopy\\TypeFilter\\ShallowCopyFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/TypeFilter/ShallowCopyFilter.php',
    'DeepCopy\\TypeFilter\\Spl\\ArrayObjectFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/TypeFilter/Spl/ArrayObjectFilter.php',
    'DeepCopy\\TypeFilter\\Spl\\SplDoublyLinkedList' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/TypeFilter/Spl/SplDoublyLinkedList.php',
    'DeepCopy\\TypeFilter\\Spl\\SplDoublyLinkedListFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/TypeFilter/Spl/SplDoublyLinkedListFilter.php',
    'DeepCopy\\TypeFilter\\TypeFilter' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/TypeFilter/TypeFilter.php',
    'DeepCopy\\TypeMatcher\\TypeMatcher' => $vendorDir . '/myclabs/deep-copy/src/DeepCopy/TypeMatcher/TypeMatcher.php',
    'Doctrine\\Instantiator\\Exception\\ExceptionInterface' => $vendorDir . '/doctrine/instantiator/src/Doctrine/Instantiator/Exception/ExceptionInterface.php',
    'Doctrine\\Instantiator\\Exception\\InvalidArgumentException' => $vendorDir . '/doctrine/instantiator/src/Doctrine/Instantiator/Exception/InvalidArgumentException.php',
    'Doctrine\\Instantiator\\Exception\\UnexpectedValueException' => $vendorDir . '/doctrine/instantiator/src/Doctrine/Instantiator/Exception/UnexpectedValueException.php',
    'Doctrine\\Instantiator\\Instantiator' => $vendorDir . '/doctrine/instantiator/src/Doctrine/Instantiator/Instantiator.php',
    'Doctrine\\Instantiator\\InstantiatorInterface' => $vendorDir . '/doctrine/instantiator/src/Doctrine/Instantiator/InstantiatorInterface.php',
    'EvalFile_Command' => $vendorDir . '/wp-cli/eval-command/src/EvalFile_Command.php',
    'Eval_Command' => $vendorDir . '/wp-cli/eval-command/src/Eval_Command.php',
    'Export_Command' => $vendorDir . '/wp-cli/export-command/src/Export_Command.php',
    'Gettext\\BaseTranslator' => $vendorDir . '/gettext/gettext/src/BaseTranslator.php',
    'Gettext\\Extractors\\Blade' => $vendorDir . '/gettext/gettext/src/Extractors/Blade.php',
    'Gettext\\Extractors\\Csv' => $vendorDir . '/gettext/gettext/src/Extractors/Csv.php',
    'Gettext\\Extractors\\CsvDictionary' => $vendorDir . '/gettext/gettext/src/Extractors/CsvDictionary.php',
    'Gettext\\Extractors\\Extractor' => $vendorDir . '/gettext/gettext/src/Extractors/Extractor.php',
    'Gettext\\Extractors\\ExtractorInterface' => $vendorDir . '/gettext/gettext/src/Extractors/ExtractorInterface.php',
    'Gettext\\Extractors\\ExtractorMultiInterface' => $vendorDir . '/gettext/gettext/src/Extractors/ExtractorMultiInterface.php',
    'Gettext\\Extractors\\Jed' => $vendorDir . '/gettext/gettext/src/Extractors/Jed.php',
    'Gettext\\Extractors\\JsCode' => $vendorDir . '/gettext/gettext/src/Extractors/JsCode.php',
    'Gettext\\Extractors\\Json' => $vendorDir . '/gettext/gettext/src/Extractors/Json.php',
    'Gettext\\Extractors\\JsonDictionary' => $vendorDir . '/gettext/gettext/src/Extractors/JsonDictionary.php',
    'Gettext\\Extractors\\Mo' => $vendorDir . '/gettext/gettext/src/Extractors/Mo.php',
    'Gettext\\Extractors\\PhpArray' => $vendorDir . '/gettext/gettext/src/Extractors/PhpArray.php',
    'Gettext\\Extractors\\PhpCode' => $vendorDir . '/gettext/gettext/src/Extractors/PhpCode.php',
    'Gettext\\Extractors\\Po' => $vendorDir . '/gettext/gettext/src/Extractors/Po.php',
    'Gettext\\Extractors\\Twig' => $vendorDir . '/gettext/gettext/src/Extractors/Twig.php',
    'Gettext\\Extractors\\VueJs' => $vendorDir . '/gettext/gettext/src/Extractors/VueJs.php',
    'Gettext\\Extractors\\Xliff' => $vendorDir . '/gettext/gettext/src/Extractors/Xliff.php',
    'Gettext\\Extractors\\Yaml' => $vendorDir . '/gettext/gettext/src/Extractors/Yaml.php',
    'Gettext\\Extractors\\YamlDictionary' => $vendorDir . '/gettext/gettext/src/Extractors/YamlDictionary.php',
    'Gettext\\Generators\\Csv' => $vendorDir . '/gettext/gettext/src/Generators/Csv.php',
    'Gettext\\Generators\\CsvDictionary' => $vendorDir . '/gettext/gettext/src/Generators/CsvDictionary.php',
    'Gettext\\Generators\\Generator' => $vendorDir . '/gettext/gettext/src/Generators/Generator.php',
    'Gettext\\Generators\\GeneratorInterface' => $vendorDir . '/gettext/gettext/src/Generators/GeneratorInterface.php',
    'Gettext\\Generators\\Jed' => $vendorDir . '/gettext/gettext/src/Generators/Jed.php',
    'Gettext\\Generators\\Json' => $vendorDir . '/gettext/gettext/src/Generators/Json.php',
    'Gettext\\Generators\\JsonDictionary' => $vendorDir . '/gettext/gettext/src/Generators/JsonDictionary.php',
    'Gettext\\Generators\\Mo' => $vendorDir . '/gettext/gettext/src/Generators/Mo.php',
    'Gettext\\Generators\\PhpArray' => $vendorDir . '/gettext/gettext/src/Generators/PhpArray.php',
    'Gettext\\Generators\\Po' => $vendorDir . '/gettext/gettext/src/Generators/Po.php',
    'Gettext\\Generators\\Xliff' => $vendorDir . '/gettext/gettext/src/Generators/Xliff.php',
    'Gettext\\Generators\\Yaml' => $vendorDir . '/gettext/gettext/src/Generators/Yaml.php',
    'Gettext\\Generators\\YamlDictionary' => $vendorDir . '/gettext/gettext/src/Generators/YamlDictionary.php',
    'Gettext\\GettextTranslator' => $vendorDir . '/gettext/gettext/src/GettextTranslator.php',
    'Gettext\\Languages\\Category' => $vendorDir . '/gettext/languages/src/Category.php',
    'Gettext\\Languages\\CldrData' => $vendorDir . '/gettext/languages/src/CldrData.php',
    'Gettext\\Languages\\Exporter\\Exporter' => $vendorDir . '/gettext/languages/src/Exporter/Exporter.php',
    'Gettext\\Languages\\Exporter\\Html' => $vendorDir . '/gettext/languages/src/Exporter/Html.php',
    'Gettext\\Languages\\Exporter\\Json' => $vendorDir . '/gettext/languages/src/Exporter/Json.php',
    'Gettext\\Languages\\Exporter\\Php' => $vendorDir . '/gettext/languages/src/Exporter/Php.php',
    'Gettext\\Languages\\Exporter\\Po' => $vendorDir . '/gettext/languages/src/Exporter/Po.php',
    'Gettext\\Languages\\Exporter\\Prettyjson' => $vendorDir . '/gettext/languages/src/Exporter/Prettyjson.php',
    'Gettext\\Languages\\Exporter\\Ruby' => $vendorDir . '/gettext/languages/src/Exporter/Ruby.php',
    'Gettext\\Languages\\Exporter\\Xml' => $vendorDir . '/gettext/languages/src/Exporter/Xml.php',
    'Gettext\\Languages\\FormulaConverter' => $vendorDir . '/gettext/languages/src/FormulaConverter.php',
    'Gettext\\Languages\\Language' => $vendorDir . '/gettext/languages/src/Language.php',
    'Gettext\\Merge' => $vendorDir . '/gettext/gettext/src/Merge.php',
    'Gettext\\Translation' => $vendorDir . '/gettext/gettext/src/Translation.php',
    'Gettext\\Translations' => $vendorDir . '/gettext/gettext/src/Translations.php',
    'Gettext\\Translator' => $vendorDir . '/gettext/gettext/src/Translator.php',
    'Gettext\\TranslatorInterface' => $vendorDir . '/gettext/gettext/src/TranslatorInterface.php',
    'Gettext\\Utils\\CsvTrait' => $vendorDir . '/gettext/gettext/src/Utils/CsvTrait.php',
    'Gettext\\Utils\\DictionaryTrait' => $vendorDir . '/gettext/gettext/src/Utils/DictionaryTrait.php',
    'Gettext\\Utils\\FunctionsScanner' => $vendorDir . '/gettext/gettext/src/Utils/FunctionsScanner.php',
    'Gettext\\Utils\\HeadersExtractorTrait' => $vendorDir . '/gettext/gettext/src/Utils/HeadersExtractorTrait.php',
    'Gettext\\Utils\\HeadersGeneratorTrait' => $vendorDir . '/gettext/gettext/src/Utils/HeadersGeneratorTrait.php',
    'Gettext\\Utils\\JsFunctionsScanner' => $vendorDir . '/gettext/gettext/src/Utils/JsFunctionsScanner.php',
    'Gettext\\Utils\\MultidimensionalArrayTrait' => $vendorDir . '/gettext/gettext/src/Utils/MultidimensionalArrayTrait.php',
    'Gettext\\Utils\\ParsedComment' => $vendorDir . '/gettext/gettext/src/Utils/ParsedComment.php',
    'Gettext\\Utils\\ParsedFunction' => $vendorDir . '/gettext/gettext/src/Utils/ParsedFunction.php',
    'Gettext\\Utils\\PhpFunctionsScanner' => $vendorDir . '/gettext/gettext/src/Utils/PhpFunctionsScanner.php',
    'Gettext\\Utils\\StringReader' => $vendorDir . '/gettext/gettext/src/Utils/StringReader.php',
    'Hamcrest\\Arrays\\IsArray' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Arrays/IsArray.php',
    'Hamcrest\\Arrays\\IsArrayContaining' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Arrays/IsArrayContaining.php',
    'Hamcrest\\Arrays\\IsArrayContainingInAnyOrder' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Arrays/IsArrayContainingInAnyOrder.php',
    'Hamcrest\\Arrays\\IsArrayContainingInOrder' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Arrays/IsArrayContainingInOrder.php',
    'Hamcrest\\Arrays\\IsArrayContainingKey' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Arrays/IsArrayContainingKey.php',
    'Hamcrest\\Arrays\\IsArrayContainingKeyValuePair' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Arrays/IsArrayContainingKeyValuePair.php',
    'Hamcrest\\Arrays\\IsArrayWithSize' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Arrays/IsArrayWithSize.php',
    'Hamcrest\\Arrays\\MatchingOnce' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Arrays/MatchingOnce.php',
    'Hamcrest\\Arrays\\SeriesMatchingOnce' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Arrays/SeriesMatchingOnce.php',
    'Hamcrest\\AssertionError' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/AssertionError.php',
    'Hamcrest\\BaseDescription' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/BaseDescription.php',
    'Hamcrest\\BaseMatcher' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/BaseMatcher.php',
    'Hamcrest\\Collection\\IsEmptyTraversable' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Collection/IsEmptyTraversable.php',
    'Hamcrest\\Collection\\IsTraversableWithSize' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Collection/IsTraversableWithSize.php',
    'Hamcrest\\Core\\AllOf' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Core/AllOf.php',
    'Hamcrest\\Core\\AnyOf' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Core/AnyOf.php',
    'Hamcrest\\Core\\CombinableMatcher' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Core/CombinableMatcher.php',
    'Hamcrest\\Core\\DescribedAs' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Core/DescribedAs.php',
    'Hamcrest\\Core\\Every' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Core/Every.php',
    'Hamcrest\\Core\\HasToString' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Core/HasToString.php',
    'Hamcrest\\Core\\Is' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Core/Is.php',
    'Hamcrest\\Core\\IsAnything' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Core/IsAnything.php',
    'Hamcrest\\Core\\IsCollectionContaining' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Core/IsCollectionContaining.php',
    'Hamcrest\\Core\\IsEqual' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Core/IsEqual.php',
    'Hamcrest\\Core\\IsIdentical' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Core/IsIdentical.php',
    'Hamcrest\\Core\\IsInstanceOf' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Core/IsInstanceOf.php',
    'Hamcrest\\Core\\IsNot' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Core/IsNot.php',
    'Hamcrest\\Core\\IsNull' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Core/IsNull.php',
    'Hamcrest\\Core\\IsSame' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Core/IsSame.php',
    'Hamcrest\\Core\\IsTypeOf' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Core/IsTypeOf.php',
    'Hamcrest\\Core\\Set' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Core/Set.php',
    'Hamcrest\\Core\\ShortcutCombination' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Core/ShortcutCombination.php',
    'Hamcrest\\Description' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Description.php',
    'Hamcrest\\DiagnosingMatcher' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/DiagnosingMatcher.php',
    'Hamcrest\\FeatureMatcher' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/FeatureMatcher.php',
    'Hamcrest\\Internal\\SelfDescribingValue' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Internal/SelfDescribingValue.php',
    'Hamcrest\\Matcher' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Matcher.php',
    'Hamcrest\\MatcherAssert' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/MatcherAssert.php',
    'Hamcrest\\Matchers' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Matchers.php',
    'Hamcrest\\NullDescription' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/NullDescription.php',
    'Hamcrest\\Number\\IsCloseTo' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Number/IsCloseTo.php',
    'Hamcrest\\Number\\OrderingComparison' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Number/OrderingComparison.php',
    'Hamcrest\\SelfDescribing' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/SelfDescribing.php',
    'Hamcrest\\StringDescription' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/StringDescription.php',
    'Hamcrest\\Text\\IsEmptyString' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Text/IsEmptyString.php',
    'Hamcrest\\Text\\IsEqualIgnoringCase' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Text/IsEqualIgnoringCase.php',
    'Hamcrest\\Text\\IsEqualIgnoringWhiteSpace' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Text/IsEqualIgnoringWhiteSpace.php',
    'Hamcrest\\Text\\MatchesPattern' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Text/MatchesPattern.php',
    'Hamcrest\\Text\\StringContains' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Text/StringContains.php',
    'Hamcrest\\Text\\StringContainsIgnoringCase' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Text/StringContainsIgnoringCase.php',
    'Hamcrest\\Text\\StringContainsInOrder' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Text/StringContainsInOrder.php',
    'Hamcrest\\Text\\StringEndsWith' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Text/StringEndsWith.php',
    'Hamcrest\\Text\\StringStartsWith' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Text/StringStartsWith.php',
    'Hamcrest\\Text\\SubstringMatcher' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Text/SubstringMatcher.php',
    'Hamcrest\\TypeSafeDiagnosingMatcher' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/TypeSafeDiagnosingMatcher.php',
    'Hamcrest\\TypeSafeMatcher' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/TypeSafeMatcher.php',
    'Hamcrest\\Type\\IsArray' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Type/IsArray.php',
    'Hamcrest\\Type\\IsBoolean' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Type/IsBoolean.php',
    'Hamcrest\\Type\\IsCallable' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Type/IsCallable.php',
    'Hamcrest\\Type\\IsDouble' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Type/IsDouble.php',
    'Hamcrest\\Type\\IsInteger' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Type/IsInteger.php',
    'Hamcrest\\Type\\IsNumeric' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Type/IsNumeric.php',
    'Hamcrest\\Type\\IsObject' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Type/IsObject.php',
    'Hamcrest\\Type\\IsResource' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Type/IsResource.php',
    'Hamcrest\\Type\\IsScalar' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Type/IsScalar.php',
    'Hamcrest\\Type\\IsString' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Type/IsString.php',
    'Hamcrest\\Util' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Util.php',
    'Hamcrest\\Xml\\HasXPath' => $vendorDir . '/hamcrest/hamcrest-php/hamcrest/Hamcrest/Xml/HasXPath.php',
    'Import_Command' => $vendorDir . '/wp-cli/import-command/src/Import_Command.php',
    'JsonException' => $vendorDir . '/symfony/polyfill-php73/Resources/stubs/JsonException.php',
    'JsonSchema\\ConstraintError' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/ConstraintError.php',
    'JsonSchema\\Constraints\\BaseConstraint' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Constraints/BaseConstraint.php',
    'JsonSchema\\Constraints\\CollectionConstraint' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Constraints/CollectionConstraint.php',
    'JsonSchema\\Constraints\\ConstConstraint' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Constraints/ConstConstraint.php',
    'JsonSchema\\Constraints\\Constraint' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Constraints/Constraint.php',
    'JsonSchema\\Constraints\\ConstraintInterface' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Constraints/ConstraintInterface.php',
    'JsonSchema\\Constraints\\EnumConstraint' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Constraints/EnumConstraint.php',
    'JsonSchema\\Constraints\\Factory' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Constraints/Factory.php',
    'JsonSchema\\Constraints\\FormatConstraint' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Constraints/FormatConstraint.php',
    'JsonSchema\\Constraints\\NumberConstraint' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Constraints/NumberConstraint.php',
    'JsonSchema\\Constraints\\ObjectConstraint' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Constraints/ObjectConstraint.php',
    'JsonSchema\\Constraints\\SchemaConstraint' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Constraints/SchemaConstraint.php',
    'JsonSchema\\Constraints\\StringConstraint' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Constraints/StringConstraint.php',
    'JsonSchema\\Constraints\\TypeCheck\\LooseTypeCheck' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Constraints/TypeCheck/LooseTypeCheck.php',
    'JsonSchema\\Constraints\\TypeCheck\\StrictTypeCheck' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Constraints/TypeCheck/StrictTypeCheck.php',
    'JsonSchema\\Constraints\\TypeCheck\\TypeCheckInterface' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Constraints/TypeCheck/TypeCheckInterface.php',
    'JsonSchema\\Constraints\\TypeConstraint' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Constraints/TypeConstraint.php',
    'JsonSchema\\Constraints\\UndefinedConstraint' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Constraints/UndefinedConstraint.php',
    'JsonSchema\\Entity\\JsonPointer' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Entity/JsonPointer.php',
    'JsonSchema\\Enum' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Enum.php',
    'JsonSchema\\Exception\\ExceptionInterface' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Exception/ExceptionInterface.php',
    'JsonSchema\\Exception\\InvalidArgumentException' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Exception/InvalidArgumentException.php',
    'JsonSchema\\Exception\\InvalidConfigException' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Exception/InvalidConfigException.php',
    'JsonSchema\\Exception\\InvalidSchemaException' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Exception/InvalidSchemaException.php',
    'JsonSchema\\Exception\\InvalidSchemaMediaTypeException' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Exception/InvalidSchemaMediaTypeException.php',
    'JsonSchema\\Exception\\InvalidSourceUriException' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Exception/InvalidSourceUriException.php',
    'JsonSchema\\Exception\\JsonDecodingException' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Exception/JsonDecodingException.php',
    'JsonSchema\\Exception\\ResourceNotFoundException' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Exception/ResourceNotFoundException.php',
    'JsonSchema\\Exception\\RuntimeException' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Exception/RuntimeException.php',
    'JsonSchema\\Exception\\UnresolvableJsonPointerException' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Exception/UnresolvableJsonPointerException.php',
    'JsonSchema\\Exception\\UriResolverException' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Exception/UriResolverException.php',
    'JsonSchema\\Exception\\ValidationException' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Exception/ValidationException.php',
    'JsonSchema\\Iterator\\ObjectIterator' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Iterator/ObjectIterator.php',
    'JsonSchema\\Rfc3339' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Rfc3339.php',
    'JsonSchema\\SchemaStorage' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/SchemaStorage.php',
    'JsonSchema\\SchemaStorageInterface' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/SchemaStorageInterface.php',
    'JsonSchema\\Tool\\DeepComparer' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Tool/DeepComparer.php',
    'JsonSchema\\Tool\\DeepCopy' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Tool/DeepCopy.php',
    'JsonSchema\\Tool\\Validator\\RelativeReferenceValidator' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Tool/Validator/RelativeReferenceValidator.php',
    'JsonSchema\\Tool\\Validator\\UriValidator' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Tool/Validator/UriValidator.php',
    'JsonSchema\\UriResolverInterface' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/UriResolverInterface.php',
    'JsonSchema\\UriRetrieverInterface' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/UriRetrieverInterface.php',
    'JsonSchema\\Uri\\Retrievers\\AbstractRetriever' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Uri/Retrievers/AbstractRetriever.php',
    'JsonSchema\\Uri\\Retrievers\\Curl' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Uri/Retrievers/Curl.php',
    'JsonSchema\\Uri\\Retrievers\\FileGetContents' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Uri/Retrievers/FileGetContents.php',
    'JsonSchema\\Uri\\Retrievers\\PredefinedArray' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Uri/Retrievers/PredefinedArray.php',
    'JsonSchema\\Uri\\Retrievers\\UriRetrieverInterface' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Uri/Retrievers/UriRetrieverInterface.php',
    'JsonSchema\\Uri\\UriResolver' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Uri/UriResolver.php',
    'JsonSchema\\Uri\\UriRetriever' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Uri/UriRetriever.php',
    'JsonSchema\\Validator' => $vendorDir . '/justinrainbow/json-schema/src/JsonSchema/Validator.php',
    'Language_Namespace' => $vendorDir . '/wp-cli/language-command/src/Language_Namespace.php',
    'MabeEnum\\Enum' => $vendorDir . '/marc-mabe/php-enum/src/Enum.php',
    'MabeEnum\\EnumMap' => $vendorDir . '/marc-mabe/php-enum/src/EnumMap.php',
    'MabeEnum\\EnumSerializableTrait' => $vendorDir . '/marc-mabe/php-enum/src/EnumSerializableTrait.php',
    'MabeEnum\\EnumSet' => $vendorDir . '/marc-mabe/php-enum/src/EnumSet.php',
    'Media_Command' => $vendorDir . '/wp-cli/media-command/src/Media_Command.php',
    'Menu_Command' => $vendorDir . '/wp-cli/entity-command/src/Menu_Command.php',
    'Menu_Item_Command' => $vendorDir . '/wp-cli/entity-command/src/Menu_Item_Command.php',
    'Menu_Location_Command' => $vendorDir . '/wp-cli/entity-command/src/Menu_Location_Command.php',
    'Mockery\\Adapter\\Phpunit\\MockeryPHPUnitIntegration' => $vendorDir . '/mockery/mockery/library/Mockery/Adapter/Phpunit/MockeryPHPUnitIntegration.php',
    'Mockery\\Adapter\\Phpunit\\MockeryPHPUnitIntegrationAssertPostConditions' => $vendorDir . '/mockery/mockery/library/Mockery/Adapter/Phpunit/MockeryPHPUnitIntegrationAssertPostConditions.php',
    'Mockery\\Adapter\\Phpunit\\MockeryTestCase' => $vendorDir . '/mockery/mockery/library/Mockery/Adapter/Phpunit/MockeryTestCase.php',
    'Mockery\\Adapter\\Phpunit\\MockeryTestCaseSetUp' => $vendorDir . '/mockery/mockery/library/Mockery/Adapter/Phpunit/MockeryTestCaseSetUp.php',
    'Mockery\\Adapter\\Phpunit\\TestListener' => $vendorDir . '/mockery/mockery/library/Mockery/Adapter/Phpunit/TestListener.php',
    'Mockery\\Adapter\\Phpunit\\TestListenerTrait' => $vendorDir . '/mockery/mockery/library/Mockery/Adapter/Phpunit/TestListenerTrait.php',
    'Mockery\\ClosureWrapper' => $vendorDir . '/mockery/mockery/library/Mockery/ClosureWrapper.php',
    'Mockery\\CompositeExpectation' => $vendorDir . '/mockery/mockery/library/Mockery/CompositeExpectation.php',
    'Mockery\\Configuration' => $vendorDir . '/mockery/mockery/library/Mockery/Configuration.php',
    'Mockery\\Container' => $vendorDir . '/mockery/mockery/library/Mockery/Container.php',
    'Mockery\\CountValidator\\AtLeast' => $vendorDir . '/mockery/mockery/library/Mockery/CountValidator/AtLeast.php',
    'Mockery\\CountValidator\\AtMost' => $vendorDir . '/mockery/mockery/library/Mockery/CountValidator/AtMost.php',
    'Mockery\\CountValidator\\CountValidatorAbstract' => $vendorDir . '/mockery/mockery/library/Mockery/CountValidator/CountValidatorAbstract.php',
    'Mockery\\CountValidator\\CountValidatorInterface' => $vendorDir . '/mockery/mockery/library/Mockery/CountValidator/CountValidatorInterface.php',
    'Mockery\\CountValidator\\Exact' => $vendorDir . '/mockery/mockery/library/Mockery/CountValidator/Exact.php',
    'Mockery\\CountValidator\\Exception' => $vendorDir . '/mockery/mockery/library/Mockery/CountValidator/Exception.php',
    'Mockery\\Exception' => $vendorDir . '/mockery/mockery/library/Mockery/Exception.php',
    'Mockery\\Exception\\BadMethodCallException' => $vendorDir . '/mockery/mockery/library/Mockery/Exception/BadMethodCallException.php',
    'Mockery\\Exception\\InvalidArgumentException' => $vendorDir . '/mockery/mockery/library/Mockery/Exception/InvalidArgumentException.php',
    'Mockery\\Exception\\InvalidCountException' => $vendorDir . '/mockery/mockery/library/Mockery/Exception/InvalidCountException.php',
    'Mockery\\Exception\\InvalidOrderException' => $vendorDir . '/mockery/mockery/library/Mockery/Exception/InvalidOrderException.php',
    'Mockery\\Exception\\MockeryExceptionInterface' => $vendorDir . '/mockery/mockery/library/Mockery/Exception/MockeryExceptionInterface.php',
    'Mockery\\Exception\\NoMatchingExpectationException' => $vendorDir . '/mockery/mockery/library/Mockery/Exception/NoMatchingExpectationException.php',
    'Mockery\\Exception\\RuntimeException' => $vendorDir . '/mockery/mockery/library/Mockery/Exception/RuntimeException.php',
    'Mockery\\Expectation' => $vendorDir . '/mockery/mockery/library/Mockery/Expectation.php',
    'Mockery\\ExpectationDirector' => $vendorDir . '/mockery/mockery/library/Mockery/ExpectationDirector.php',
    'Mockery\\ExpectationInterface' => $vendorDir . '/mockery/mockery/library/Mockery/ExpectationInterface.php',
    'Mockery\\ExpectsHigherOrderMessage' => $vendorDir . '/mockery/mockery/library/Mockery/ExpectsHigherOrderMessage.php',
    'Mockery\\Generator\\CachingGenerator' => $vendorDir . '/mockery/mockery/library/Mockery/Generator/CachingGenerator.php',
    'Mockery\\Generator\\DefinedTargetClass' => $vendorDir . '/mockery/mockery/library/Mockery/Generator/DefinedTargetClass.php',
    'Mockery\\Generator\\Generator' => $vendorDir . '/mockery/mockery/library/Mockery/Generator/Generator.php',
    'Mockery\\Generator\\Method' => $vendorDir . '/mockery/mockery/library/Mockery/Generator/Method.php',
    'Mockery\\Generator\\MockConfiguration' => $vendorDir . '/mockery/mockery/library/Mockery/Generator/MockConfiguration.php',
    'Mockery\\Generator\\MockConfigurationBuilder' => $vendorDir . '/mockery/mockery/library/Mockery/Generator/MockConfigurationBuilder.php',
    'Mockery\\Generator\\MockDefinition' => $vendorDir . '/mockery/mockery/library/Mockery/Generator/MockDefinition.php',
    'Mockery\\Generator\\MockNameBuilder' => $vendorDir . '/mockery/mockery/library/Mockery/Generator/MockNameBuilder.php',
    'Mockery\\Generator\\Parameter' => $vendorDir . '/mockery/mockery/library/Mockery/Generator/Parameter.php',
    'Mockery\\Generator\\StringManipulationGenerator' => $vendorDir . '/mockery/mockery/library/Mockery/Generator/StringManipulationGenerator.php',
    'Mockery\\Generator\\StringManipulation\\Pass\\AvoidMethodClashPass' => $vendorDir . '/mockery/mockery/library/Mockery/Generator/StringManipulation/Pass/AvoidMethodClashPass.php',
    'Mockery\\Generator\\StringManipulation\\Pass\\CallTypeHintPass' => $vendorDir . '/mockery/mockery/library/Mockery/Generator/StringManipulation/Pass/CallTypeHintPass.php',
    'Mockery\\Generator\\StringManipulation\\Pass\\ClassAttributesPass' => $vendorDir . '/mockery/mockery/library/Mockery/Generator/StringManipulation/Pass/ClassAttributesPass.php',
    'Mockery\\Generator\\StringManipulation\\Pass\\ClassNamePass' => $vendorDir . '/mockery/mockery/library/Mockery/Generator/StringManipulation/Pass/ClassNamePass.php',
    'Mockery\\Generator\\StringManipulation\\Pass\\ClassPass' => $vendorDir . '/mockery/mockery/library/Mockery/Generator/StringManipulation/Pass/ClassPass.php',
    'Mockery\\Generator\\StringManipulation\\Pass\\ConstantsPass' => $vendorDir . '/mockery/mockery/library/Mockery/Generator/StringManipulation/Pass/ConstantsPass.php',
    'Mockery\\Generator\\StringManipulation\\Pass\\InstanceMockPass' => $vendorDir . '/mockery/mockery/library/Mockery/Generator/StringManipulation/Pass/InstanceMockPass.php',
    'Mockery\\Generator\\StringManipulation\\Pass\\InterfacePass' => $vendorDir . '/mockery/mockery/library/Mockery/Generator/StringManipulation/Pass/InterfacePass.php',
    'Mockery\\Generator\\StringManipulation\\Pass\\MagicMethodTypeHintsPass' => $vendorDir . '/mockery/mockery/library/Mockery/Generator/StringManipulation/Pass/MagicMethodTypeHintsPass.php',
    'Mockery\\Generator\\StringManipulation\\Pass\\MethodDefinitionPass' => $vendorDir . '/mockery/mockery/library/Mockery/Generator/StringManipulation/Pass/MethodDefinitionPass.php',
    'Mockery\\Generator\\StringManipulation\\Pass\\Pass' => $vendorDir . '/mockery/mockery/library/Mockery/Generator/StringManipulation/Pass/Pass.php',
    'Mockery\\Generator\\StringManipulation\\Pass\\RemoveBuiltinMethodsThatAreFinalPass' => $vendorDir . '/mockery/mockery/library/Mockery/Generator/StringManipulation/Pass/RemoveBuiltinMethodsThatAreFinalPass.php',
    'Mockery\\Generator\\StringManipulation\\Pass\\RemoveDestructorPass' => $vendorDir . '/mockery/mockery/library/Mockery/Generator/StringManipulation/Pass/RemoveDestructorPass.php',
    'Mockery\\Generator\\StringManipulation\\Pass\\RemoveUnserializeForInternalSerializableClassesPass' => $vendorDir . '/mockery/mockery/library/Mockery/Generator/StringManipulation/Pass/RemoveUnserializeForInternalSerializableClassesPass.php',
    'Mockery\\Generator\\StringManipulation\\Pass\\TraitPass' => $vendorDir . '/mockery/mockery/library/Mockery/Generator/StringManipulation/Pass/TraitPass.php',
    'Mockery\\Generator\\TargetClassInterface' => $vendorDir . '/mockery/mockery/library/Mockery/Generator/TargetClassInterface.php',
    'Mockery\\Generator\\UndefinedTargetClass' => $vendorDir . '/mockery/mockery/library/Mockery/Generator/UndefinedTargetClass.php',
    'Mockery\\HigherOrderMessage' => $vendorDir . '/mockery/mockery/library/Mockery/HigherOrderMessage.php',
    'Mockery\\Instantiator' => $vendorDir . '/mockery/mockery/library/Mockery/Instantiator.php',
    'Mockery\\LegacyMockInterface' => $vendorDir . '/mockery/mockery/library/Mockery/LegacyMockInterface.php',
    'Mockery\\Loader\\EvalLoader' => $vendorDir . '/mockery/mockery/library/Mockery/Loader/EvalLoader.php',
    'Mockery\\Loader\\Loader' => $vendorDir . '/mockery/mockery/library/Mockery/Loader/Loader.php',
    'Mockery\\Loader\\RequireLoader' => $vendorDir . '/mockery/mockery/library/Mockery/Loader/RequireLoader.php',
    'Mockery\\Matcher\\AndAnyOtherArgs' => $vendorDir . '/mockery/mockery/library/Mockery/Matcher/AndAnyOtherArgs.php',
    'Mockery\\Matcher\\Any' => $vendorDir . '/mockery/mockery/library/Mockery/Matcher/Any.php',
    'Mockery\\Matcher\\AnyArgs' => $vendorDir . '/mockery/mockery/library/Mockery/Matcher/AnyArgs.php',
    'Mockery\\Matcher\\AnyOf' => $vendorDir . '/mockery/mockery/library/Mockery/Matcher/AnyOf.php',
    'Mockery\\Matcher\\ArgumentListMatcher' => $vendorDir . '/mockery/mockery/library/Mockery/Matcher/ArgumentListMatcher.php',
    'Mockery\\Matcher\\Closure' => $vendorDir . '/mockery/mockery/library/Mockery/Matcher/Closure.php',
    'Mockery\\Matcher\\Contains' => $vendorDir . '/mockery/mockery/library/Mockery/Matcher/Contains.php',
    'Mockery\\Matcher\\Ducktype' => $vendorDir . '/mockery/mockery/library/Mockery/Matcher/Ducktype.php',
    'Mockery\\Matcher\\HasKey' => $vendorDir . '/mockery/mockery/library/Mockery/Matcher/HasKey.php',
    'Mockery\\Matcher\\HasValue' => $vendorDir . '/mockery/mockery/library/Mockery/Matcher/HasValue.php',
    'Mockery\\Matcher\\IsEqual' => $vendorDir . '/mockery/mockery/library/Mockery/Matcher/IsEqual.php',
    'Mockery\\Matcher\\IsSame' => $vendorDir . '/mockery/mockery/library/Mockery/Matcher/IsSame.php',
    'Mockery\\Matcher\\MatcherAbstract' => $vendorDir . '/mockery/mockery/library/Mockery/Matcher/MatcherAbstract.php',
    'Mockery\\Matcher\\MatcherInterface' => $vendorDir . '/mockery/mockery/library/Mockery/Matcher/MatcherInterface.php',
    'Mockery\\Matcher\\MultiArgumentClosure' => $vendorDir . '/mockery/mockery/library/Mockery/Matcher/MultiArgumentClosure.php',
    'Mockery\\Matcher\\MustBe' => $vendorDir . '/mockery/mockery/library/Mockery/Matcher/MustBe.php',
    'Mockery\\Matcher\\NoArgs' => $vendorDir . '/mockery/mockery/library/Mockery/Matcher/NoArgs.php',
    'Mockery\\Matcher\\Not' => $vendorDir . '/mockery/mockery/library/Mockery/Matcher/Not.php',
    'Mockery\\Matcher\\NotAnyOf' => $vendorDir . '/mockery/mockery/library/Mockery/Matcher/NotAnyOf.php',
    'Mockery\\Matcher\\Pattern' => $vendorDir . '/mockery/mockery/library/Mockery/Matcher/Pattern.php',
    'Mockery\\Matcher\\Subset' => $vendorDir . '/mockery/mockery/library/Mockery/Matcher/Subset.php',
    'Mockery\\Matcher\\Type' => $vendorDir . '/mockery/mockery/library/Mockery/Matcher/Type.php',
    'Mockery\\MethodCall' => $vendorDir . '/mockery/mockery/library/Mockery/MethodCall.php',
    'Mockery\\Mock' => $vendorDir . '/mockery/mockery/library/Mockery/Mock.php',
    'Mockery\\MockInterface' => $vendorDir . '/mockery/mockery/library/Mockery/MockInterface.php',
    'Mockery\\QuickDefinitionsConfiguration' => $vendorDir . '/mockery/mockery/library/Mockery/QuickDefinitionsConfiguration.php',
    'Mockery\\ReceivedMethodCalls' => $vendorDir . '/mockery/mockery/library/Mockery/ReceivedMethodCalls.php',
    'Mockery\\Reflector' => $vendorDir . '/mockery/mockery/library/Mockery/Reflector.php',
    'Mockery\\Undefined' => $vendorDir . '/mockery/mockery/library/Mockery/Undefined.php',
    'Mockery\\VerificationDirector' => $vendorDir . '/mockery/mockery/library/Mockery/VerificationDirector.php',
    'Mockery\\VerificationExpectation' => $vendorDir . '/mockery/mockery/library/Mockery/VerificationExpectation.php',
    'Mpdf\\AssetFetcher' => $vendorDir . '/mpdf/mpdf/src/AssetFetcher.php',
    'Mpdf\\Barcode' => $vendorDir . '/mpdf/mpdf/src/Barcode.php',
    'Mpdf\\Barcode\\AbstractBarcode' => $vendorDir . '/mpdf/mpdf/src/Barcode/AbstractBarcode.php',
    'Mpdf\\Barcode\\BarcodeException' => $vendorDir . '/mpdf/mpdf/src/Barcode/BarcodeException.php',
    'Mpdf\\Barcode\\BarcodeInterface' => $vendorDir . '/mpdf/mpdf/src/Barcode/BarcodeInterface.php',
    'Mpdf\\Barcode\\Codabar' => $vendorDir . '/mpdf/mpdf/src/Barcode/Codabar.php',
    'Mpdf\\Barcode\\Code11' => $vendorDir . '/mpdf/mpdf/src/Barcode/Code11.php',
    'Mpdf\\Barcode\\Code128' => $vendorDir . '/mpdf/mpdf/src/Barcode/Code128.php',
    'Mpdf\\Barcode\\Code39' => $vendorDir . '/mpdf/mpdf/src/Barcode/Code39.php',
    'Mpdf\\Barcode\\Code93' => $vendorDir . '/mpdf/mpdf/src/Barcode/Code93.php',
    'Mpdf\\Barcode\\EanExt' => $vendorDir . '/mpdf/mpdf/src/Barcode/EanExt.php',
    'Mpdf\\Barcode\\EanUpc' => $vendorDir . '/mpdf/mpdf/src/Barcode/EanUpc.php',
    'Mpdf\\Barcode\\I25' => $vendorDir . '/mpdf/mpdf/src/Barcode/I25.php',
    'Mpdf\\Barcode\\Imb' => $vendorDir . '/mpdf/mpdf/src/Barcode/Imb.php',
    'Mpdf\\Barcode\\Msi' => $vendorDir . '/mpdf/mpdf/src/Barcode/Msi.php',
    'Mpdf\\Barcode\\Postnet' => $vendorDir . '/mpdf/mpdf/src/Barcode/Postnet.php',
    'Mpdf\\Barcode\\Rm4Scc' => $vendorDir . '/mpdf/mpdf/src/Barcode/Rm4Scc.php',
    'Mpdf\\Barcode\\S25' => $vendorDir . '/mpdf/mpdf/src/Barcode/S25.php',
    'Mpdf\\Cache' => $vendorDir . '/mpdf/mpdf/src/Cache.php',
    'Mpdf\\Color\\ColorConverter' => $vendorDir . '/mpdf/mpdf/src/Color/ColorConverter.php',
    'Mpdf\\Color\\ColorModeConverter' => $vendorDir . '/mpdf/mpdf/src/Color/ColorModeConverter.php',
    'Mpdf\\Color\\ColorSpaceRestrictor' => $vendorDir . '/mpdf/mpdf/src/Color/ColorSpaceRestrictor.php',
    'Mpdf\\Color\\NamedColors' => $vendorDir . '/mpdf/mpdf/src/Color/NamedColors.php',
    'Mpdf\\Config\\ConfigVariables' => $vendorDir . '/mpdf/mpdf/src/Config/ConfigVariables.php',
    'Mpdf\\Config\\FontVariables' => $vendorDir . '/mpdf/mpdf/src/Config/FontVariables.php',
    'Mpdf\\Container\\ContainerInterface' => $vendorDir . '/mpdf/mpdf/src/Container/ContainerInterface.php',
    'Mpdf\\Container\\NotFoundException' => $vendorDir . '/mpdf/mpdf/src/Container/NotFoundException.php',
    'Mpdf\\Container\\SimpleContainer' => $vendorDir . '/mpdf/mpdf/src/Container/SimpleContainer.php',
    'Mpdf\\Conversion\\DecToAlpha' => $vendorDir . '/mpdf/mpdf/src/Conversion/DecToAlpha.php',
    'Mpdf\\Conversion\\DecToCjk' => $vendorDir . '/mpdf/mpdf/src/Conversion/DecToCjk.php',
    'Mpdf\\Conversion\\DecToHebrew' => $vendorDir . '/mpdf/mpdf/src/Conversion/DecToHebrew.php',
    'Mpdf\\Conversion\\DecToOther' => $vendorDir . '/mpdf/mpdf/src/Conversion/DecToOther.php',
    'Mpdf\\Conversion\\DecToRoman' => $vendorDir . '/mpdf/mpdf/src/Conversion/DecToRoman.php',
    'Mpdf\\CssManager' => $vendorDir . '/mpdf/mpdf/src/CssManager.php',
    'Mpdf\\Css\\Border' => $vendorDir . '/mpdf/mpdf/src/Css/Border.php',
    'Mpdf\\Css\\DefaultCss' => $vendorDir . '/mpdf/mpdf/src/Css/DefaultCss.php',
    'Mpdf\\Css\\TextVars' => $vendorDir . '/mpdf/mpdf/src/Css/TextVars.php',
    'Mpdf\\DirectWrite' => $vendorDir . '/mpdf/mpdf/src/DirectWrite.php',
    'Mpdf\\Exception\\AssetFetchingException' => $vendorDir . '/mpdf/mpdf/src/Exception/AssetFetchingException.php',
    'Mpdf\\Exception\\FontException' => $vendorDir . '/mpdf/mpdf/src/Exception/FontException.php',
    'Mpdf\\Exception\\InvalidArgumentException' => $vendorDir . '/mpdf/mpdf/src/Exception/InvalidArgumentException.php',
    'Mpdf\\File\\LocalContentLoader' => $vendorDir . '/mpdf/mpdf/src/File/LocalContentLoader.php',
    'Mpdf\\File\\LocalContentLoaderInterface' => $vendorDir . '/mpdf/mpdf/src/File/LocalContentLoaderInterface.php',
    'Mpdf\\File\\StreamWrapperChecker' => $vendorDir . '/mpdf/mpdf/src/File/StreamWrapperChecker.php',
    'Mpdf\\Fonts\\FontCache' => $vendorDir . '/mpdf/mpdf/src/Fonts/FontCache.php',
    'Mpdf\\Fonts\\FontFileFinder' => $vendorDir . '/mpdf/mpdf/src/Fonts/FontFileFinder.php',
    'Mpdf\\Fonts\\GlyphOperator' => $vendorDir . '/mpdf/mpdf/src/Fonts/GlyphOperator.php',
    'Mpdf\\Fonts\\MetricsGenerator' => $vendorDir . '/mpdf/mpdf/src/Fonts/MetricsGenerator.php',
    'Mpdf\\Form' => $vendorDir . '/mpdf/mpdf/src/Form.php',
    'Mpdf\\FpdiTrait' => $vendorDir . '/mpdf/mpdf/src/FpdiTrait.php',
    'Mpdf\\Gif\\ColorTable' => $vendorDir . '/mpdf/mpdf/src/Gif/ColorTable.php',
    'Mpdf\\Gif\\FileHeader' => $vendorDir . '/mpdf/mpdf/src/Gif/FileHeader.php',
    'Mpdf\\Gif\\Gif' => $vendorDir . '/mpdf/mpdf/src/Gif/Gif.php',
    'Mpdf\\Gif\\Image' => $vendorDir . '/mpdf/mpdf/src/Gif/Image.php',
    'Mpdf\\Gif\\ImageHeader' => $vendorDir . '/mpdf/mpdf/src/Gif/ImageHeader.php',
    'Mpdf\\Gif\\Lzw' => $vendorDir . '/mpdf/mpdf/src/Gif/Lzw.php',
    'Mpdf\\Gradient' => $vendorDir . '/mpdf/mpdf/src/Gradient.php',
    'Mpdf\\HTMLParserMode' => $vendorDir . '/mpdf/mpdf/src/HTMLParserMode.php',
    'Mpdf\\Http\\ClientInterface' => $vendorDir . '/mpdf/mpdf/src/Http/ClientInterface.php',
    'Mpdf\\Http\\CurlHttpClient' => $vendorDir . '/mpdf/mpdf/src/Http/CurlHttpClient.php',
    'Mpdf\\Http\\Exception\\ClientException' => $vendorDir . '/mpdf/mpdf/src/Http/Exception/ClientException.php',
    'Mpdf\\Http\\Exception\\ForbiddenRequestException' => $vendorDir . '/mpdf/mpdf/src/Http/Exception/ForbiddenRequestException.php',
    'Mpdf\\Http\\Exception\\NetworkException' => $vendorDir . '/mpdf/mpdf/src/Http/Exception/NetworkException.php',
    'Mpdf\\Http\\Exception\\RequestException' => $vendorDir . '/mpdf/mpdf/src/Http/Exception/RequestException.php',
    'Mpdf\\Http\\SocketHttpClient' => $vendorDir . '/mpdf/mpdf/src/Http/SocketHttpClient.php',
    'Mpdf\\Hyphenator' => $vendorDir . '/mpdf/mpdf/src/Hyphenator.php',
    'Mpdf\\Image\\Bmp' => $vendorDir . '/mpdf/mpdf/src/Image/Bmp.php',
    'Mpdf\\Image\\ImageProcessor' => $vendorDir . '/mpdf/mpdf/src/Image/ImageProcessor.php',
    'Mpdf\\Image\\ImageTypeGuesser' => $vendorDir . '/mpdf/mpdf/src/Image/ImageTypeGuesser.php',
    'Mpdf\\Image\\Svg' => $vendorDir . '/mpdf/mpdf/src/Image/Svg.php',
    'Mpdf\\Image\\Wmf' => $vendorDir . '/mpdf/mpdf/src/Image/Wmf.php',
    'Mpdf\\Language\\LanguageToFont' => $vendorDir . '/mpdf/mpdf/src/Language/LanguageToFont.php',
    'Mpdf\\Language\\LanguageToFontInterface' => $vendorDir . '/mpdf/mpdf/src/Language/LanguageToFontInterface.php',
    'Mpdf\\Language\\ScriptToLanguage' => $vendorDir . '/mpdf/mpdf/src/Language/ScriptToLanguage.php',
    'Mpdf\\Language\\ScriptToLanguageInterface' => $vendorDir . '/mpdf/mpdf/src/Language/ScriptToLanguageInterface.php',
    'Mpdf\\Log\\Context' => $vendorDir . '/mpdf/mpdf/src/Log/Context.php',
    'Mpdf\\Mpdf' => $vendorDir . '/mpdf/mpdf/src/Mpdf.php',
    'Mpdf\\MpdfException' => $vendorDir . '/mpdf/mpdf/src/MpdfException.php',
    'Mpdf\\MpdfImageException' => $vendorDir . '/mpdf/mpdf/src/MpdfImageException.php',
    'Mpdf\\Otl' => $vendorDir . '/mpdf/mpdf/src/Otl.php',
    'Mpdf\\OtlDump' => $vendorDir . '/mpdf/mpdf/src/OtlDump.php',
    'Mpdf\\Output\\Destination' => $vendorDir . '/mpdf/mpdf/src/Output/Destination.php',
    'Mpdf\\PageBox' => $vendorDir . '/mpdf/mpdf/src/PageBox.php',
    'Mpdf\\PageFormat' => $vendorDir . '/mpdf/mpdf/src/PageFormat.php',
    'Mpdf\\Pdf\\Protection' => $vendorDir . '/mpdf/mpdf/src/Pdf/Protection.php',
    'Mpdf\\Pdf\\Protection\\UniqidGenerator' => $vendorDir . '/mpdf/mpdf/src/Pdf/Protection/UniqidGenerator.php',
    'Mpdf\\PsrHttpMessageShim\\Request' => $vendorDir . '/mpdf/psr-http-message-shim/src/Request.php',
    'Mpdf\\PsrHttpMessageShim\\Response' => $vendorDir . '/mpdf/psr-http-message-shim/src/Response.php',
    'Mpdf\\PsrHttpMessageShim\\Stream' => $vendorDir . '/mpdf/psr-http-message-shim/src/Stream.php',
    'Mpdf\\PsrHttpMessageShim\\Uri' => $vendorDir . '/mpdf/psr-http-message-shim/src/Uri.php',
    'Mpdf\\PsrLogAwareTrait\\MpdfPsrLogAwareTrait' => $vendorDir . '/mpdf/psr-log-aware-trait/src/MpdfPsrLogAwareTrait.php',
    'Mpdf\\PsrLogAwareTrait\\PsrLogAwareTrait' => $vendorDir . '/mpdf/psr-log-aware-trait/src/PsrLogAwareTrait.php',
    'Mpdf\\ServiceFactory' => $vendorDir . '/mpdf/mpdf/src/ServiceFactory.php',
    'Mpdf\\Shaper\\Indic' => $vendorDir . '/mpdf/mpdf/src/Shaper/Indic.php',
    'Mpdf\\Shaper\\Myanmar' => $vendorDir . '/mpdf/mpdf/src/Shaper/Myanmar.php',
    'Mpdf\\Shaper\\Sea' => $vendorDir . '/mpdf/mpdf/src/Shaper/Sea.php',
    'Mpdf\\SizeConverter' => $vendorDir . '/mpdf/mpdf/src/SizeConverter.php',
    'Mpdf\\Strict' => $vendorDir . '/mpdf/mpdf/src/Strict.php',
    'Mpdf\\TTFontFile' => $vendorDir . '/mpdf/mpdf/src/TTFontFile.php',
    'Mpdf\\TTFontFileAnalysis' => $vendorDir . '/mpdf/mpdf/src/TTFontFileAnalysis.php',
    'Mpdf\\TableOfContents' => $vendorDir . '/mpdf/mpdf/src/TableOfContents.php',
    'Mpdf\\Tag' => $vendorDir . '/mpdf/mpdf/src/Tag.php',
    'Mpdf\\Tag\\A' => $vendorDir . '/mpdf/mpdf/src/Tag/A.php',
    'Mpdf\\Tag\\Acronym' => $vendorDir . '/mpdf/mpdf/src/Tag/Acronym.php',
    'Mpdf\\Tag\\Address' => $vendorDir . '/mpdf/mpdf/src/Tag/Address.php',
    'Mpdf\\Tag\\Annotation' => $vendorDir . '/mpdf/mpdf/src/Tag/Annotation.php',
    'Mpdf\\Tag\\Article' => $vendorDir . '/mpdf/mpdf/src/Tag/Article.php',
    'Mpdf\\Tag\\Aside' => $vendorDir . '/mpdf/mpdf/src/Tag/Aside.php',
    'Mpdf\\Tag\\B' => $vendorDir . '/mpdf/mpdf/src/Tag/B.php',
    'Mpdf\\Tag\\BarCode' => $vendorDir . '/mpdf/mpdf/src/Tag/BarCode.php',
    'Mpdf\\Tag\\Bdi' => $vendorDir . '/mpdf/mpdf/src/Tag/Bdi.php',
    'Mpdf\\Tag\\Bdo' => $vendorDir . '/mpdf/mpdf/src/Tag/Bdo.php',
    'Mpdf\\Tag\\Big' => $vendorDir . '/mpdf/mpdf/src/Tag/Big.php',
    'Mpdf\\Tag\\BlockQuote' => $vendorDir . '/mpdf/mpdf/src/Tag/BlockQuote.php',
    'Mpdf\\Tag\\BlockTag' => $vendorDir . '/mpdf/mpdf/src/Tag/BlockTag.php',
    'Mpdf\\Tag\\Bookmark' => $vendorDir . '/mpdf/mpdf/src/Tag/Bookmark.php',
    'Mpdf\\Tag\\Br' => $vendorDir . '/mpdf/mpdf/src/Tag/Br.php',
    'Mpdf\\Tag\\Caption' => $vendorDir . '/mpdf/mpdf/src/Tag/Caption.php',
    'Mpdf\\Tag\\Center' => $vendorDir . '/mpdf/mpdf/src/Tag/Center.php',
    'Mpdf\\Tag\\Cite' => $vendorDir . '/mpdf/mpdf/src/Tag/Cite.php',
    'Mpdf\\Tag\\Code' => $vendorDir . '/mpdf/mpdf/src/Tag/Code.php',
    'Mpdf\\Tag\\ColumnBreak' => $vendorDir . '/mpdf/mpdf/src/Tag/ColumnBreak.php',
    'Mpdf\\Tag\\Columns' => $vendorDir . '/mpdf/mpdf/src/Tag/Columns.php',
    'Mpdf\\Tag\\Dd' => $vendorDir . '/mpdf/mpdf/src/Tag/Dd.php',
    'Mpdf\\Tag\\Del' => $vendorDir . '/mpdf/mpdf/src/Tag/Del.php',
    'Mpdf\\Tag\\Details' => $vendorDir . '/mpdf/mpdf/src/Tag/Details.php',
    'Mpdf\\Tag\\Div' => $vendorDir . '/mpdf/mpdf/src/Tag/Div.php',
    'Mpdf\\Tag\\Dl' => $vendorDir . '/mpdf/mpdf/src/Tag/Dl.php',
    'Mpdf\\Tag\\DotTab' => $vendorDir . '/mpdf/mpdf/src/Tag/DotTab.php',
    'Mpdf\\Tag\\Dt' => $vendorDir . '/mpdf/mpdf/src/Tag/Dt.php',
    'Mpdf\\Tag\\Em' => $vendorDir . '/mpdf/mpdf/src/Tag/Em.php',
    'Mpdf\\Tag\\FieldSet' => $vendorDir . '/mpdf/mpdf/src/Tag/FieldSet.php',
    'Mpdf\\Tag\\FigCaption' => $vendorDir . '/mpdf/mpdf/src/Tag/FigCaption.php',
    'Mpdf\\Tag\\Figure' => $vendorDir . '/mpdf/mpdf/src/Tag/Figure.php',
    'Mpdf\\Tag\\Font' => $vendorDir . '/mpdf/mpdf/src/Tag/Font.php',
    'Mpdf\\Tag\\Footer' => $vendorDir . '/mpdf/mpdf/src/Tag/Footer.php',
    'Mpdf\\Tag\\Form' => $vendorDir . '/mpdf/mpdf/src/Tag/Form.php',
    'Mpdf\\Tag\\FormFeed' => $vendorDir . '/mpdf/mpdf/src/Tag/FormFeed.php',
    'Mpdf\\Tag\\H1' => $vendorDir . '/mpdf/mpdf/src/Tag/H1.php',
    'Mpdf\\Tag\\H2' => $vendorDir . '/mpdf/mpdf/src/Tag/H2.php',
    'Mpdf\\Tag\\H3' => $vendorDir . '/mpdf/mpdf/src/Tag/H3.php',
    'Mpdf\\Tag\\H4' => $vendorDir . '/mpdf/mpdf/src/Tag/H4.php',
    'Mpdf\\Tag\\H5' => $vendorDir . '/mpdf/mpdf/src/Tag/H5.php',
    'Mpdf\\Tag\\H6' => $vendorDir . '/mpdf/mpdf/src/Tag/H6.php',
    'Mpdf\\Tag\\HGroup' => $vendorDir . '/mpdf/mpdf/src/Tag/HGroup.php',
    'Mpdf\\Tag\\Header' => $vendorDir . '/mpdf/mpdf/src/Tag/Header.php',
    'Mpdf\\Tag\\Hr' => $vendorDir . '/mpdf/mpdf/src/Tag/Hr.php',
    'Mpdf\\Tag\\I' => $vendorDir . '/mpdf/mpdf/src/Tag/I.php',
    'Mpdf\\Tag\\Img' => $vendorDir . '/mpdf/mpdf/src/Tag/Img.php',
    'Mpdf\\Tag\\IndexEntry' => $vendorDir . '/mpdf/mpdf/src/Tag/IndexEntry.php',
    'Mpdf\\Tag\\IndexInsert' => $vendorDir . '/mpdf/mpdf/src/Tag/IndexInsert.php',
    'Mpdf\\Tag\\InlineTag' => $vendorDir . '/mpdf/mpdf/src/Tag/InlineTag.php',
    'Mpdf\\Tag\\Input' => $vendorDir . '/mpdf/mpdf/src/Tag/Input.php',
    'Mpdf\\Tag\\Ins' => $vendorDir . '/mpdf/mpdf/src/Tag/Ins.php',
    'Mpdf\\Tag\\Kbd' => $vendorDir . '/mpdf/mpdf/src/Tag/Kbd.php',
    'Mpdf\\Tag\\Legend' => $vendorDir . '/mpdf/mpdf/src/Tag/Legend.php',
    'Mpdf\\Tag\\Li' => $vendorDir . '/mpdf/mpdf/src/Tag/Li.php',
    'Mpdf\\Tag\\Main' => $vendorDir . '/mpdf/mpdf/src/Tag/Main.php',
    'Mpdf\\Tag\\Mark' => $vendorDir . '/mpdf/mpdf/src/Tag/Mark.php',
    'Mpdf\\Tag\\Meter' => $vendorDir . '/mpdf/mpdf/src/Tag/Meter.php',
    'Mpdf\\Tag\\Nav' => $vendorDir . '/mpdf/mpdf/src/Tag/Nav.php',
    'Mpdf\\Tag\\NewColumn' => $vendorDir . '/mpdf/mpdf/src/Tag/NewColumn.php',
    'Mpdf\\Tag\\NewPage' => $vendorDir . '/mpdf/mpdf/src/Tag/NewPage.php',
    'Mpdf\\Tag\\Ol' => $vendorDir . '/mpdf/mpdf/src/Tag/Ol.php',
    'Mpdf\\Tag\\Option' => $vendorDir . '/mpdf/mpdf/src/Tag/Option.php',
    'Mpdf\\Tag\\P' => $vendorDir . '/mpdf/mpdf/src/Tag/P.php',
    'Mpdf\\Tag\\PageBreak' => $vendorDir . '/mpdf/mpdf/src/Tag/PageBreak.php',
    'Mpdf\\Tag\\PageFooter' => $vendorDir . '/mpdf/mpdf/src/Tag/PageFooter.php',
    'Mpdf\\Tag\\PageHeader' => $vendorDir . '/mpdf/mpdf/src/Tag/PageHeader.php',
    'Mpdf\\Tag\\Pre' => $vendorDir . '/mpdf/mpdf/src/Tag/Pre.php',
    'Mpdf\\Tag\\Progress' => $vendorDir . '/mpdf/mpdf/src/Tag/Progress.php',
    'Mpdf\\Tag\\Q' => $vendorDir . '/mpdf/mpdf/src/Tag/Q.php',
    'Mpdf\\Tag\\S' => $vendorDir . '/mpdf/mpdf/src/Tag/S.php',
    'Mpdf\\Tag\\Samp' => $vendorDir . '/mpdf/mpdf/src/Tag/Samp.php',
    'Mpdf\\Tag\\Section' => $vendorDir . '/mpdf/mpdf/src/Tag/Section.php',
    'Mpdf\\Tag\\Select' => $vendorDir . '/mpdf/mpdf/src/Tag/Select.php',
    'Mpdf\\Tag\\SetHtmlPageFooter' => $vendorDir . '/mpdf/mpdf/src/Tag/SetHtmlPageFooter.php',
    'Mpdf\\Tag\\SetHtmlPageHeader' => $vendorDir . '/mpdf/mpdf/src/Tag/SetHtmlPageHeader.php',
    'Mpdf\\Tag\\SetPageFooter' => $vendorDir . '/mpdf/mpdf/src/Tag/SetPageFooter.php',
    'Mpdf\\Tag\\SetPageHeader' => $vendorDir . '/mpdf/mpdf/src/Tag/SetPageHeader.php',
    'Mpdf\\Tag\\Small' => $vendorDir . '/mpdf/mpdf/src/Tag/Small.php',
    'Mpdf\\Tag\\Span' => $vendorDir . '/mpdf/mpdf/src/Tag/Span.php',
    'Mpdf\\Tag\\Strike' => $vendorDir . '/mpdf/mpdf/src/Tag/Strike.php',
    'Mpdf\\Tag\\Strong' => $vendorDir . '/mpdf/mpdf/src/Tag/Strong.php',
    'Mpdf\\Tag\\Sub' => $vendorDir . '/mpdf/mpdf/src/Tag/Sub.php',
    'Mpdf\\Tag\\SubstituteTag' => $vendorDir . '/mpdf/mpdf/src/Tag/SubstituteTag.php',
    'Mpdf\\Tag\\Summary' => $vendorDir . '/mpdf/mpdf/src/Tag/Summary.php',
    'Mpdf\\Tag\\Sup' => $vendorDir . '/mpdf/mpdf/src/Tag/Sup.php',
    'Mpdf\\Tag\\TBody' => $vendorDir . '/mpdf/mpdf/src/Tag/TBody.php',
    'Mpdf\\Tag\\TFoot' => $vendorDir . '/mpdf/mpdf/src/Tag/TFoot.php',
    'Mpdf\\Tag\\THead' => $vendorDir . '/mpdf/mpdf/src/Tag/THead.php',
    'Mpdf\\Tag\\Table' => $vendorDir . '/mpdf/mpdf/src/Tag/Table.php',
    'Mpdf\\Tag\\Tag' => $vendorDir . '/mpdf/mpdf/src/Tag/Tag.php',
    'Mpdf\\Tag\\Td' => $vendorDir . '/mpdf/mpdf/src/Tag/Td.php',
    'Mpdf\\Tag\\TextArea' => $vendorDir . '/mpdf/mpdf/src/Tag/TextArea.php',
    'Mpdf\\Tag\\TextCircle' => $vendorDir . '/mpdf/mpdf/src/Tag/TextCircle.php',
    'Mpdf\\Tag\\Th' => $vendorDir . '/mpdf/mpdf/src/Tag/Th.php',
    'Mpdf\\Tag\\Time' => $vendorDir . '/mpdf/mpdf/src/Tag/Time.php',
    'Mpdf\\Tag\\Toc' => $vendorDir . '/mpdf/mpdf/src/Tag/Toc.php',
    'Mpdf\\Tag\\TocEntry' => $vendorDir . '/mpdf/mpdf/src/Tag/TocEntry.php',
    'Mpdf\\Tag\\TocPageBreak' => $vendorDir . '/mpdf/mpdf/src/Tag/TocPageBreak.php',
    'Mpdf\\Tag\\Tr' => $vendorDir . '/mpdf/mpdf/src/Tag/Tr.php',
    'Mpdf\\Tag\\Tt' => $vendorDir . '/mpdf/mpdf/src/Tag/Tt.php',
    'Mpdf\\Tag\\Tta' => $vendorDir . '/mpdf/mpdf/src/Tag/Tta.php',
    'Mpdf\\Tag\\Tts' => $vendorDir . '/mpdf/mpdf/src/Tag/Tts.php',
    'Mpdf\\Tag\\Ttz' => $vendorDir . '/mpdf/mpdf/src/Tag/Ttz.php',
    'Mpdf\\Tag\\U' => $vendorDir . '/mpdf/mpdf/src/Tag/U.php',
    'Mpdf\\Tag\\Ul' => $vendorDir . '/mpdf/mpdf/src/Tag/Ul.php',
    'Mpdf\\Tag\\VarTag' => $vendorDir . '/mpdf/mpdf/src/Tag/VarTag.php',
    'Mpdf\\Tag\\WatermarkImage' => $vendorDir . '/mpdf/mpdf/src/Tag/WatermarkImage.php',
    'Mpdf\\Tag\\WatermarkText' => $vendorDir . '/mpdf/mpdf/src/Tag/WatermarkText.php',
    'Mpdf\\Ucdn' => $vendorDir . '/mpdf/mpdf/src/Ucdn.php',
    'Mpdf\\Utils\\Arrays' => $vendorDir . '/mpdf/mpdf/src/Utils/Arrays.php',
    'Mpdf\\Utils\\NumericString' => $vendorDir . '/mpdf/mpdf/src/Utils/NumericString.php',
    'Mpdf\\Utils\\PdfDate' => $vendorDir . '/mpdf/mpdf/src/Utils/PdfDate.php',
    'Mpdf\\Utils\\UtfString' => $vendorDir . '/mpdf/mpdf/src/Utils/UtfString.php',
    'Mpdf\\Watermark' => $vendorDir . '/mpdf/mpdf/src/Watermark.php',
    'Mpdf\\WatermarkImage' => $vendorDir . '/mpdf/mpdf/src/WatermarkImage.php',
    'Mpdf\\WatermarkText' => $vendorDir . '/mpdf/mpdf/src/WatermarkText.php',
    'Mpdf\\Writer\\BackgroundWriter' => $vendorDir . '/mpdf/mpdf/src/Writer/BackgroundWriter.php',
    'Mpdf\\Writer\\BaseWriter' => $vendorDir . '/mpdf/mpdf/src/Writer/BaseWriter.php',
    'Mpdf\\Writer\\BookmarkWriter' => $vendorDir . '/mpdf/mpdf/src/Writer/BookmarkWriter.php',
    'Mpdf\\Writer\\ColorWriter' => $vendorDir . '/mpdf/mpdf/src/Writer/ColorWriter.php',
    'Mpdf\\Writer\\FontWriter' => $vendorDir . '/mpdf/mpdf/src/Writer/FontWriter.php',
    'Mpdf\\Writer\\FormWriter' => $vendorDir . '/mpdf/mpdf/src/Writer/FormWriter.php',
    'Mpdf\\Writer\\ImageWriter' => $vendorDir . '/mpdf/mpdf/src/Writer/ImageWriter.php',
    'Mpdf\\Writer\\JavaScriptWriter' => $vendorDir . '/mpdf/mpdf/src/Writer/JavaScriptWriter.php',
    'Mpdf\\Writer\\MetadataWriter' => $vendorDir . '/mpdf/mpdf/src/Writer/MetadataWriter.php',
    'Mpdf\\Writer\\ObjectWriter' => $vendorDir . '/mpdf/mpdf/src/Writer/ObjectWriter.php',
    'Mpdf\\Writer\\OptionalContentWriter' => $vendorDir . '/mpdf/mpdf/src/Writer/OptionalContentWriter.php',
    'Mpdf\\Writer\\PageWriter' => $vendorDir . '/mpdf/mpdf/src/Writer/PageWriter.php',
    'Mpdf\\Writer\\ResourceWriter' => $vendorDir . '/mpdf/mpdf/src/Writer/ResourceWriter.php',
    'Mustache_Autoloader' => $vendorDir . '/wp-cli/mustache/src/Mustache/Autoloader.php',
    'Mustache_Cache' => $vendorDir . '/wp-cli/mustache/src/Mustache/Cache.php',
    'Mustache_Cache_AbstractCache' => $vendorDir . '/wp-cli/mustache/src/Mustache/Cache/AbstractCache.php',
    'Mustache_Cache_FilesystemCache' => $vendorDir . '/wp-cli/mustache/src/Mustache/Cache/FilesystemCache.php',
    'Mustache_Cache_NoopCache' => $vendorDir . '/wp-cli/mustache/src/Mustache/Cache/NoopCache.php',
    'Mustache_Compiler' => $vendorDir . '/wp-cli/mustache/src/Mustache/Compiler.php',
    'Mustache_Context' => $vendorDir . '/wp-cli/mustache/src/Mustache/Context.php',
    'Mustache_Engine' => $vendorDir . '/wp-cli/mustache/src/Mustache/Engine.php',
    'Mustache_Exception' => $vendorDir . '/wp-cli/mustache/src/Mustache/Exception.php',
    'Mustache_Exception_InvalidArgumentException' => $vendorDir . '/wp-cli/mustache/src/Mustache/Exception/InvalidArgumentException.php',
    'Mustache_Exception_LogicException' => $vendorDir . '/wp-cli/mustache/src/Mustache/Exception/LogicException.php',
    'Mustache_Exception_RuntimeException' => $vendorDir . '/wp-cli/mustache/src/Mustache/Exception/RuntimeException.php',
    'Mustache_Exception_SyntaxException' => $vendorDir . '/wp-cli/mustache/src/Mustache/Exception/SyntaxException.php',
    'Mustache_Exception_UnknownFilterException' => $vendorDir . '/wp-cli/mustache/src/Mustache/Exception/UnknownFilterException.php',
    'Mustache_Exception_UnknownHelperException' => $vendorDir . '/wp-cli/mustache/src/Mustache/Exception/UnknownHelperException.php',
    'Mustache_Exception_UnknownTemplateException' => $vendorDir . '/wp-cli/mustache/src/Mustache/Exception/UnknownTemplateException.php',
    'Mustache_HelperCollection' => $vendorDir . '/wp-cli/mustache/src/Mustache/HelperCollection.php',
    'Mustache_LambdaHelper' => $vendorDir . '/wp-cli/mustache/src/Mustache/LambdaHelper.php',
    'Mustache_Loader' => $vendorDir . '/wp-cli/mustache/src/Mustache/Loader.php',
    'Mustache_Loader_ArrayLoader' => $vendorDir . '/wp-cli/mustache/src/Mustache/Loader/ArrayLoader.php',
    'Mustache_Loader_CascadingLoader' => $vendorDir . '/wp-cli/mustache/src/Mustache/Loader/CascadingLoader.php',
    'Mustache_Loader_FilesystemLoader' => $vendorDir . '/wp-cli/mustache/src/Mustache/Loader/FilesystemLoader.php',
    'Mustache_Loader_InlineLoader' => $vendorDir . '/wp-cli/mustache/src/Mustache/Loader/InlineLoader.php',
    'Mustache_Loader_MutableLoader' => $vendorDir . '/wp-cli/mustache/src/Mustache/Loader/MutableLoader.php',
    'Mustache_Loader_ProductionFilesystemLoader' => $vendorDir . '/wp-cli/mustache/src/Mustache/Loader/ProductionFilesystemLoader.php',
    'Mustache_Loader_StringLoader' => $vendorDir . '/wp-cli/mustache/src/Mustache/Loader/StringLoader.php',
    'Mustache_Logger' => $vendorDir . '/wp-cli/mustache/src/Mustache/Logger.php',
    'Mustache_Logger_AbstractLogger' => $vendorDir . '/wp-cli/mustache/src/Mustache/Logger/AbstractLogger.php',
    'Mustache_Logger_StreamLogger' => $vendorDir . '/wp-cli/mustache/src/Mustache/Logger/StreamLogger.php',
    'Mustache_Parser' => $vendorDir . '/wp-cli/mustache/src/Mustache/Parser.php',
    'Mustache_Source' => $vendorDir . '/wp-cli/mustache/src/Mustache/Source.php',
    'Mustache_Source_FilesystemSource' => $vendorDir . '/wp-cli/mustache/src/Mustache/Source/FilesystemSource.php',
    'Mustache_Template' => $vendorDir . '/wp-cli/mustache/src/Mustache/Template.php',
    'Mustache_Tokenizer' => $vendorDir . '/wp-cli/mustache/src/Mustache/Tokenizer.php',
    'Mustangostang\\Spyc' => $vendorDir . '/wp-cli/mustangostang-spyc/src/Spyc.php',
    'Network_Meta_Command' => $vendorDir . '/wp-cli/entity-command/src/Network_Meta_Command.php',
    'Network_Namespace' => $vendorDir . '/wp-cli/entity-command/src/Network_Namespace.php',
    'Normalizer' => $vendorDir . '/symfony/polyfill-intl-normalizer/Resources/stubs/Normalizer.php',
    'Option_Command' => $vendorDir . '/wp-cli/entity-command/src/Option_Command.php',
    'Oxymel' => $vendorDir . '/nb/oxymel/Oxymel.php',
    'OxymelTest' => $vendorDir . '/nb/oxymel/OxymelTest.php',
    'PHPUnit\\Exception' => $vendorDir . '/phpunit/phpunit/src/Exception.php',
    'PHPUnit\\Framework\\ActualValueIsNotAnObjectException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/ActualValueIsNotAnObjectException.php',
    'PHPUnit\\Framework\\Assert' => $vendorDir . '/phpunit/phpunit/src/Framework/Assert.php',
    'PHPUnit\\Framework\\AssertionFailedError' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/AssertionFailedError.php',
    'PHPUnit\\Framework\\CodeCoverageException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/CodeCoverageException.php',
    'PHPUnit\\Framework\\ComparisonMethodDoesNotAcceptParameterTypeException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/ComparisonMethodDoesNotAcceptParameterTypeException.php',
    'PHPUnit\\Framework\\ComparisonMethodDoesNotDeclareBoolReturnTypeException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/ComparisonMethodDoesNotDeclareBoolReturnTypeException.php',
    'PHPUnit\\Framework\\ComparisonMethodDoesNotDeclareExactlyOneParameterException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/ComparisonMethodDoesNotDeclareExactlyOneParameterException.php',
    'PHPUnit\\Framework\\ComparisonMethodDoesNotDeclareParameterTypeException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/ComparisonMethodDoesNotDeclareParameterTypeException.php',
    'PHPUnit\\Framework\\ComparisonMethodDoesNotExistException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/ComparisonMethodDoesNotExistException.php',
    'PHPUnit\\Framework\\Constraint\\ArrayHasKey' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Traversable/ArrayHasKey.php',
    'PHPUnit\\Framework\\Constraint\\BinaryOperator' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Operator/BinaryOperator.php',
    'PHPUnit\\Framework\\Constraint\\Callback' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Callback.php',
    'PHPUnit\\Framework\\Constraint\\ClassHasAttribute' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Object/ClassHasAttribute.php',
    'PHPUnit\\Framework\\Constraint\\ClassHasStaticAttribute' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Object/ClassHasStaticAttribute.php',
    'PHPUnit\\Framework\\Constraint\\Constraint' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Constraint.php',
    'PHPUnit\\Framework\\Constraint\\Count' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Cardinality/Count.php',
    'PHPUnit\\Framework\\Constraint\\DirectoryExists' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Filesystem/DirectoryExists.php',
    'PHPUnit\\Framework\\Constraint\\Exception' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Exception/Exception.php',
    'PHPUnit\\Framework\\Constraint\\ExceptionCode' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Exception/ExceptionCode.php',
    'PHPUnit\\Framework\\Constraint\\ExceptionMessage' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Exception/ExceptionMessage.php',
    'PHPUnit\\Framework\\Constraint\\ExceptionMessageRegularExpression' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Exception/ExceptionMessageRegularExpression.php',
    'PHPUnit\\Framework\\Constraint\\FileExists' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Filesystem/FileExists.php',
    'PHPUnit\\Framework\\Constraint\\GreaterThan' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Cardinality/GreaterThan.php',
    'PHPUnit\\Framework\\Constraint\\IsAnything' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/IsAnything.php',
    'PHPUnit\\Framework\\Constraint\\IsEmpty' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Cardinality/IsEmpty.php',
    'PHPUnit\\Framework\\Constraint\\IsEqual' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Equality/IsEqual.php',
    'PHPUnit\\Framework\\Constraint\\IsEqualCanonicalizing' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Equality/IsEqualCanonicalizing.php',
    'PHPUnit\\Framework\\Constraint\\IsEqualIgnoringCase' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Equality/IsEqualIgnoringCase.php',
    'PHPUnit\\Framework\\Constraint\\IsEqualWithDelta' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Equality/IsEqualWithDelta.php',
    'PHPUnit\\Framework\\Constraint\\IsFalse' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Boolean/IsFalse.php',
    'PHPUnit\\Framework\\Constraint\\IsFinite' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Math/IsFinite.php',
    'PHPUnit\\Framework\\Constraint\\IsIdentical' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/IsIdentical.php',
    'PHPUnit\\Framework\\Constraint\\IsInfinite' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Math/IsInfinite.php',
    'PHPUnit\\Framework\\Constraint\\IsInstanceOf' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Type/IsInstanceOf.php',
    'PHPUnit\\Framework\\Constraint\\IsJson' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/String/IsJson.php',
    'PHPUnit\\Framework\\Constraint\\IsNan' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Math/IsNan.php',
    'PHPUnit\\Framework\\Constraint\\IsNull' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Type/IsNull.php',
    'PHPUnit\\Framework\\Constraint\\IsReadable' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Filesystem/IsReadable.php',
    'PHPUnit\\Framework\\Constraint\\IsTrue' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Boolean/IsTrue.php',
    'PHPUnit\\Framework\\Constraint\\IsType' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Type/IsType.php',
    'PHPUnit\\Framework\\Constraint\\IsWritable' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Filesystem/IsWritable.php',
    'PHPUnit\\Framework\\Constraint\\JsonMatches' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/JsonMatches.php',
    'PHPUnit\\Framework\\Constraint\\JsonMatchesErrorMessageProvider' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/JsonMatchesErrorMessageProvider.php',
    'PHPUnit\\Framework\\Constraint\\LessThan' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Cardinality/LessThan.php',
    'PHPUnit\\Framework\\Constraint\\LogicalAnd' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Operator/LogicalAnd.php',
    'PHPUnit\\Framework\\Constraint\\LogicalNot' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Operator/LogicalNot.php',
    'PHPUnit\\Framework\\Constraint\\LogicalOr' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Operator/LogicalOr.php',
    'PHPUnit\\Framework\\Constraint\\LogicalXor' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Operator/LogicalXor.php',
    'PHPUnit\\Framework\\Constraint\\ObjectEquals' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Object/ObjectEquals.php',
    'PHPUnit\\Framework\\Constraint\\ObjectHasAttribute' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Object/ObjectHasAttribute.php',
    'PHPUnit\\Framework\\Constraint\\ObjectHasProperty' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Object/ObjectHasProperty.php',
    'PHPUnit\\Framework\\Constraint\\Operator' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Operator/Operator.php',
    'PHPUnit\\Framework\\Constraint\\RegularExpression' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/String/RegularExpression.php',
    'PHPUnit\\Framework\\Constraint\\SameSize' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Cardinality/SameSize.php',
    'PHPUnit\\Framework\\Constraint\\StringContains' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/String/StringContains.php',
    'PHPUnit\\Framework\\Constraint\\StringEndsWith' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/String/StringEndsWith.php',
    'PHPUnit\\Framework\\Constraint\\StringMatchesFormatDescription' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/String/StringMatchesFormatDescription.php',
    'PHPUnit\\Framework\\Constraint\\StringStartsWith' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/String/StringStartsWith.php',
    'PHPUnit\\Framework\\Constraint\\TraversableContains' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Traversable/TraversableContains.php',
    'PHPUnit\\Framework\\Constraint\\TraversableContainsEqual' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Traversable/TraversableContainsEqual.php',
    'PHPUnit\\Framework\\Constraint\\TraversableContainsIdentical' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Traversable/TraversableContainsIdentical.php',
    'PHPUnit\\Framework\\Constraint\\TraversableContainsOnly' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Traversable/TraversableContainsOnly.php',
    'PHPUnit\\Framework\\Constraint\\UnaryOperator' => $vendorDir . '/phpunit/phpunit/src/Framework/Constraint/Operator/UnaryOperator.php',
    'PHPUnit\\Framework\\CoveredCodeNotExecutedException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/CoveredCodeNotExecutedException.php',
    'PHPUnit\\Framework\\DataProviderTestSuite' => $vendorDir . '/phpunit/phpunit/src/Framework/DataProviderTestSuite.php',
    'PHPUnit\\Framework\\Error' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/Error.php',
    'PHPUnit\\Framework\\ErrorTestCase' => $vendorDir . '/phpunit/phpunit/src/Framework/ErrorTestCase.php',
    'PHPUnit\\Framework\\Error\\Deprecated' => $vendorDir . '/phpunit/phpunit/src/Framework/Error/Deprecated.php',
    'PHPUnit\\Framework\\Error\\Error' => $vendorDir . '/phpunit/phpunit/src/Framework/Error/Error.php',
    'PHPUnit\\Framework\\Error\\Notice' => $vendorDir . '/phpunit/phpunit/src/Framework/Error/Notice.php',
    'PHPUnit\\Framework\\Error\\Warning' => $vendorDir . '/phpunit/phpunit/src/Framework/Error/Warning.php',
    'PHPUnit\\Framework\\Exception' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/Exception.php',
    'PHPUnit\\Framework\\ExceptionWrapper' => $vendorDir . '/phpunit/phpunit/src/Framework/ExceptionWrapper.php',
    'PHPUnit\\Framework\\ExecutionOrderDependency' => $vendorDir . '/phpunit/phpunit/src/Framework/ExecutionOrderDependency.php',
    'PHPUnit\\Framework\\ExpectationFailedException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/ExpectationFailedException.php',
    'PHPUnit\\Framework\\IncompleteTest' => $vendorDir . '/phpunit/phpunit/src/Framework/IncompleteTest.php',
    'PHPUnit\\Framework\\IncompleteTestCase' => $vendorDir . '/phpunit/phpunit/src/Framework/IncompleteTestCase.php',
    'PHPUnit\\Framework\\IncompleteTestError' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/IncompleteTestError.php',
    'PHPUnit\\Framework\\InvalidArgumentException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/InvalidArgumentException.php',
    'PHPUnit\\Framework\\InvalidCoversTargetException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/InvalidCoversTargetException.php',
    'PHPUnit\\Framework\\InvalidDataProviderException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/InvalidDataProviderException.php',
    'PHPUnit\\Framework\\InvalidParameterGroupException' => $vendorDir . '/phpunit/phpunit/src/Framework/InvalidParameterGroupException.php',
    'PHPUnit\\Framework\\MissingCoversAnnotationException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/MissingCoversAnnotationException.php',
    'PHPUnit\\Framework\\MockObject\\Api' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Api/Api.php',
    'PHPUnit\\Framework\\MockObject\\BadMethodCallException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/BadMethodCallException.php',
    'PHPUnit\\Framework\\MockObject\\Builder\\Identity' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Builder/Identity.php',
    'PHPUnit\\Framework\\MockObject\\Builder\\InvocationMocker' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Builder/InvocationMocker.php',
    'PHPUnit\\Framework\\MockObject\\Builder\\InvocationStubber' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Builder/InvocationStubber.php',
    'PHPUnit\\Framework\\MockObject\\Builder\\MethodNameMatch' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Builder/MethodNameMatch.php',
    'PHPUnit\\Framework\\MockObject\\Builder\\ParametersMatch' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Builder/ParametersMatch.php',
    'PHPUnit\\Framework\\MockObject\\Builder\\Stub' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Builder/Stub.php',
    'PHPUnit\\Framework\\MockObject\\CannotUseAddMethodsException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/CannotUseAddMethodsException.php',
    'PHPUnit\\Framework\\MockObject\\CannotUseOnlyMethodsException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/CannotUseOnlyMethodsException.php',
    'PHPUnit\\Framework\\MockObject\\ClassAlreadyExistsException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/ClassAlreadyExistsException.php',
    'PHPUnit\\Framework\\MockObject\\ClassIsFinalException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/ClassIsFinalException.php',
    'PHPUnit\\Framework\\MockObject\\ClassIsReadonlyException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/ClassIsReadonlyException.php',
    'PHPUnit\\Framework\\MockObject\\ConfigurableMethod' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/ConfigurableMethod.php',
    'PHPUnit\\Framework\\MockObject\\ConfigurableMethodsAlreadyInitializedException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/ConfigurableMethodsAlreadyInitializedException.php',
    'PHPUnit\\Framework\\MockObject\\DuplicateMethodException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/DuplicateMethodException.php',
    'PHPUnit\\Framework\\MockObject\\Exception' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/Exception.php',
    'PHPUnit\\Framework\\MockObject\\Generator' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Generator.php',
    'PHPUnit\\Framework\\MockObject\\IncompatibleReturnValueException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/IncompatibleReturnValueException.php',
    'PHPUnit\\Framework\\MockObject\\InvalidMethodNameException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/InvalidMethodNameException.php',
    'PHPUnit\\Framework\\MockObject\\Invocation' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Invocation.php',
    'PHPUnit\\Framework\\MockObject\\InvocationHandler' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/InvocationHandler.php',
    'PHPUnit\\Framework\\MockObject\\MatchBuilderNotFoundException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/MatchBuilderNotFoundException.php',
    'PHPUnit\\Framework\\MockObject\\Matcher' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Matcher.php',
    'PHPUnit\\Framework\\MockObject\\MatcherAlreadyRegisteredException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/MatcherAlreadyRegisteredException.php',
    'PHPUnit\\Framework\\MockObject\\Method' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Api/Method.php',
    'PHPUnit\\Framework\\MockObject\\MethodCannotBeConfiguredException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/MethodCannotBeConfiguredException.php',
    'PHPUnit\\Framework\\MockObject\\MethodNameAlreadyConfiguredException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/MethodNameAlreadyConfiguredException.php',
    'PHPUnit\\Framework\\MockObject\\MethodNameConstraint' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/MethodNameConstraint.php',
    'PHPUnit\\Framework\\MockObject\\MethodNameNotConfiguredException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/MethodNameNotConfiguredException.php',
    'PHPUnit\\Framework\\MockObject\\MethodParametersAlreadyConfiguredException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/MethodParametersAlreadyConfiguredException.php',
    'PHPUnit\\Framework\\MockObject\\MockBuilder' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/MockBuilder.php',
    'PHPUnit\\Framework\\MockObject\\MockClass' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/MockClass.php',
    'PHPUnit\\Framework\\MockObject\\MockMethod' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/MockMethod.php',
    'PHPUnit\\Framework\\MockObject\\MockMethodSet' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/MockMethodSet.php',
    'PHPUnit\\Framework\\MockObject\\MockObject' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/MockObject.php',
    'PHPUnit\\Framework\\MockObject\\MockTrait' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/MockTrait.php',
    'PHPUnit\\Framework\\MockObject\\MockType' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/MockType.php',
    'PHPUnit\\Framework\\MockObject\\OriginalConstructorInvocationRequiredException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/OriginalConstructorInvocationRequiredException.php',
    'PHPUnit\\Framework\\MockObject\\ReflectionException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/ReflectionException.php',
    'PHPUnit\\Framework\\MockObject\\ReturnValueNotConfiguredException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/ReturnValueNotConfiguredException.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\AnyInvokedCount' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Rule/AnyInvokedCount.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\AnyParameters' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Rule/AnyParameters.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\ConsecutiveParameters' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Rule/ConsecutiveParameters.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\InvocationOrder' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Rule/InvocationOrder.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\InvokedAtIndex' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Rule/InvokedAtIndex.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\InvokedAtLeastCount' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Rule/InvokedAtLeastCount.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\InvokedAtLeastOnce' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Rule/InvokedAtLeastOnce.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\InvokedAtMostCount' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Rule/InvokedAtMostCount.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\InvokedCount' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Rule/InvokedCount.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\MethodName' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Rule/MethodName.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\Parameters' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Rule/Parameters.php',
    'PHPUnit\\Framework\\MockObject\\Rule\\ParametersRule' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Rule/ParametersRule.php',
    'PHPUnit\\Framework\\MockObject\\RuntimeException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/RuntimeException.php',
    'PHPUnit\\Framework\\MockObject\\SoapExtensionNotAvailableException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/SoapExtensionNotAvailableException.php',
    'PHPUnit\\Framework\\MockObject\\Stub' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Stub.php',
    'PHPUnit\\Framework\\MockObject\\Stub\\ConsecutiveCalls' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Stub/ConsecutiveCalls.php',
    'PHPUnit\\Framework\\MockObject\\Stub\\Exception' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Stub/Exception.php',
    'PHPUnit\\Framework\\MockObject\\Stub\\ReturnArgument' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Stub/ReturnArgument.php',
    'PHPUnit\\Framework\\MockObject\\Stub\\ReturnCallback' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Stub/ReturnCallback.php',
    'PHPUnit\\Framework\\MockObject\\Stub\\ReturnReference' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Stub/ReturnReference.php',
    'PHPUnit\\Framework\\MockObject\\Stub\\ReturnSelf' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Stub/ReturnSelf.php',
    'PHPUnit\\Framework\\MockObject\\Stub\\ReturnStub' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Stub/ReturnStub.php',
    'PHPUnit\\Framework\\MockObject\\Stub\\ReturnValueMap' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Stub/ReturnValueMap.php',
    'PHPUnit\\Framework\\MockObject\\Stub\\Stub' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Stub/Stub.php',
    'PHPUnit\\Framework\\MockObject\\UnknownClassException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/UnknownClassException.php',
    'PHPUnit\\Framework\\MockObject\\UnknownTraitException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/UnknownTraitException.php',
    'PHPUnit\\Framework\\MockObject\\UnknownTypeException' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Exception/UnknownTypeException.php',
    'PHPUnit\\Framework\\MockObject\\Verifiable' => $vendorDir . '/phpunit/phpunit/src/Framework/MockObject/Verifiable.php',
    'PHPUnit\\Framework\\NoChildTestSuiteException' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/NoChildTestSuiteException.php',
    'PHPUnit\\Framework\\OutputError' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/OutputError.php',
    'PHPUnit\\Framework\\PHPTAssertionFailedError' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/PHPTAssertionFailedError.php',
    'PHPUnit\\Framework\\Reorderable' => $vendorDir . '/phpunit/phpunit/src/Framework/Reorderable.php',
    'PHPUnit\\Framework\\RiskyTestError' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/RiskyTestError.php',
    'PHPUnit\\Framework\\SelfDescribing' => $vendorDir . '/phpunit/phpunit/src/Framework/SelfDescribing.php',
    'PHPUnit\\Framework\\SkippedTest' => $vendorDir . '/phpunit/phpunit/src/Framework/SkippedTest.php',
    'PHPUnit\\Framework\\SkippedTestCase' => $vendorDir . '/phpunit/phpunit/src/Framework/SkippedTestCase.php',
    'PHPUnit\\Framework\\SkippedTestError' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/SkippedTestError.php',
    'PHPUnit\\Framework\\SkippedTestSuiteError' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/SkippedTestSuiteError.php',
    'PHPUnit\\Framework\\SyntheticError' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/SyntheticError.php',
    'PHPUnit\\Framework\\SyntheticSkippedError' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/SyntheticSkippedError.php',
    'PHPUnit\\Framework\\Test' => $vendorDir . '/phpunit/phpunit/src/Framework/Test.php',
    'PHPUnit\\Framework\\TestBuilder' => $vendorDir . '/phpunit/phpunit/src/Framework/TestBuilder.php',
    'PHPUnit\\Framework\\TestCase' => $vendorDir . '/phpunit/phpunit/src/Framework/TestCase.php',
    'PHPUnit\\Framework\\TestFailure' => $vendorDir . '/phpunit/phpunit/src/Framework/TestFailure.php',
    'PHPUnit\\Framework\\TestListener' => $vendorDir . '/phpunit/phpunit/src/Framework/TestListener.php',
    'PHPUnit\\Framework\\TestListenerDefaultImplementation' => $vendorDir . '/phpunit/phpunit/src/Framework/TestListenerDefaultImplementation.php',
    'PHPUnit\\Framework\\TestResult' => $vendorDir . '/phpunit/phpunit/src/Framework/TestResult.php',
    'PHPUnit\\Framework\\TestSuite' => $vendorDir . '/phpunit/phpunit/src/Framework/TestSuite.php',
    'PHPUnit\\Framework\\TestSuiteIterator' => $vendorDir . '/phpunit/phpunit/src/Framework/TestSuiteIterator.php',
    'PHPUnit\\Framework\\UnintentionallyCoveredCodeError' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/UnintentionallyCoveredCodeError.php',
    'PHPUnit\\Framework\\Warning' => $vendorDir . '/phpunit/phpunit/src/Framework/Exception/Warning.php',
    'PHPUnit\\Framework\\WarningTestCase' => $vendorDir . '/phpunit/phpunit/src/Framework/WarningTestCase.php',
    'PHPUnit\\Runner\\AfterIncompleteTestHook' => $vendorDir . '/phpunit/phpunit/src/Runner/Hook/AfterIncompleteTestHook.php',
    'PHPUnit\\Runner\\AfterLastTestHook' => $vendorDir . '/phpunit/phpunit/src/Runner/Hook/AfterLastTestHook.php',
    'PHPUnit\\Runner\\AfterRiskyTestHook' => $vendorDir . '/phpunit/phpunit/src/Runner/Hook/AfterRiskyTestHook.php',
    'PHPUnit\\Runner\\AfterSkippedTestHook' => $vendorDir . '/phpunit/phpunit/src/Runner/Hook/AfterSkippedTestHook.php',
    'PHPUnit\\Runner\\AfterSuccessfulTestHook' => $vendorDir . '/phpunit/phpunit/src/Runner/Hook/AfterSuccessfulTestHook.php',
    'PHPUnit\\Runner\\AfterTestErrorHook' => $vendorDir . '/phpunit/phpunit/src/Runner/Hook/AfterTestErrorHook.php',
    'PHPUnit\\Runner\\AfterTestFailureHook' => $vendorDir . '/phpunit/phpunit/src/Runner/Hook/AfterTestFailureHook.php',
    'PHPUnit\\Runner\\AfterTestHook' => $vendorDir . '/phpunit/phpunit/src/Runner/Hook/AfterTestHook.php',
    'PHPUnit\\Runner\\AfterTestWarningHook' => $vendorDir . '/phpunit/phpunit/src/Runner/Hook/AfterTestWarningHook.php',
    'PHPUnit\\Runner\\BaseTestRunner' => $vendorDir . '/phpunit/phpunit/src/Runner/BaseTestRunner.php',
    'PHPUnit\\Runner\\BeforeFirstTestHook' => $vendorDir . '/phpunit/phpunit/src/Runner/Hook/BeforeFirstTestHook.php',
    'PHPUnit\\Runner\\BeforeTestHook' => $vendorDir . '/phpunit/phpunit/src/Runner/Hook/BeforeTestHook.php',
    'PHPUnit\\Runner\\DefaultTestResultCache' => $vendorDir . '/phpunit/phpunit/src/Runner/DefaultTestResultCache.php',
    'PHPUnit\\Runner\\Exception' => $vendorDir . '/phpunit/phpunit/src/Runner/Exception.php',
    'PHPUnit\\Runner\\Extension\\ExtensionHandler' => $vendorDir . '/phpunit/phpunit/src/Runner/Extension/ExtensionHandler.php',
    'PHPUnit\\Runner\\Extension\\PharLoader' => $vendorDir . '/phpunit/phpunit/src/Runner/Extension/PharLoader.php',
    'PHPUnit\\Runner\\Filter\\ExcludeGroupFilterIterator' => $vendorDir . '/phpunit/phpunit/src/Runner/Filter/ExcludeGroupFilterIterator.php',
    'PHPUnit\\Runner\\Filter\\Factory' => $vendorDir . '/phpunit/phpunit/src/Runner/Filter/Factory.php',
    'PHPUnit\\Runner\\Filter\\GroupFilterIterator' => $vendorDir . '/phpunit/phpunit/src/Runner/Filter/GroupFilterIterator.php',
    'PHPUnit\\Runner\\Filter\\IncludeGroupFilterIterator' => $vendorDir . '/phpunit/phpunit/src/Runner/Filter/IncludeGroupFilterIterator.php',
    'PHPUnit\\Runner\\Filter\\NameFilterIterator' => $vendorDir . '/phpunit/phpunit/src/Runner/Filter/NameFilterIterator.php',
    'PHPUnit\\Runner\\Hook' => $vendorDir . '/phpunit/phpunit/src/Runner/Hook/Hook.php',
    'PHPUnit\\Runner\\NullTestResultCache' => $vendorDir . '/phpunit/phpunit/src/Runner/NullTestResultCache.php',
    'PHPUnit\\Runner\\PhptTestCase' => $vendorDir . '/phpunit/phpunit/src/Runner/PhptTestCase.php',
    'PHPUnit\\Runner\\ResultCacheExtension' => $vendorDir . '/phpunit/phpunit/src/Runner/ResultCacheExtension.php',
    'PHPUnit\\Runner\\StandardTestSuiteLoader' => $vendorDir . '/phpunit/phpunit/src/Runner/StandardTestSuiteLoader.php',
    'PHPUnit\\Runner\\TestHook' => $vendorDir . '/phpunit/phpunit/src/Runner/Hook/TestHook.php',
    'PHPUnit\\Runner\\TestListenerAdapter' => $vendorDir . '/phpunit/phpunit/src/Runner/Hook/TestListenerAdapter.php',
    'PHPUnit\\Runner\\TestResultCache' => $vendorDir . '/phpunit/phpunit/src/Runner/TestResultCache.php',
    'PHPUnit\\Runner\\TestSuiteLoader' => $vendorDir . '/phpunit/phpunit/src/Runner/TestSuiteLoader.php',
    'PHPUnit\\Runner\\TestSuiteSorter' => $vendorDir . '/phpunit/phpunit/src/Runner/TestSuiteSorter.php',
    'PHPUnit\\Runner\\Version' => $vendorDir . '/phpunit/phpunit/src/Runner/Version.php',
    'PHPUnit\\TextUI\\CliArguments\\Builder' => $vendorDir . '/phpunit/phpunit/src/TextUI/CliArguments/Builder.php',
    'PHPUnit\\TextUI\\CliArguments\\Configuration' => $vendorDir . '/phpunit/phpunit/src/TextUI/CliArguments/Configuration.php',
    'PHPUnit\\TextUI\\CliArguments\\Exception' => $vendorDir . '/phpunit/phpunit/src/TextUI/CliArguments/Exception.php',
    'PHPUnit\\TextUI\\CliArguments\\Mapper' => $vendorDir . '/phpunit/phpunit/src/TextUI/CliArguments/Mapper.php',
    'PHPUnit\\TextUI\\Command' => $vendorDir . '/phpunit/phpunit/src/TextUI/Command.php',
    'PHPUnit\\TextUI\\DefaultResultPrinter' => $vendorDir . '/phpunit/phpunit/src/TextUI/DefaultResultPrinter.php',
    'PHPUnit\\TextUI\\Exception' => $vendorDir . '/phpunit/phpunit/src/TextUI/Exception/Exception.php',
    'PHPUnit\\TextUI\\Help' => $vendorDir . '/phpunit/phpunit/src/TextUI/Help.php',
    'PHPUnit\\TextUI\\ReflectionException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Exception/ReflectionException.php',
    'PHPUnit\\TextUI\\ResultPrinter' => $vendorDir . '/phpunit/phpunit/src/TextUI/ResultPrinter.php',
    'PHPUnit\\TextUI\\RuntimeException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Exception/RuntimeException.php',
    'PHPUnit\\TextUI\\TestDirectoryNotFoundException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Exception/TestDirectoryNotFoundException.php',
    'PHPUnit\\TextUI\\TestFileNotFoundException' => $vendorDir . '/phpunit/phpunit/src/TextUI/Exception/TestFileNotFoundException.php',
    'PHPUnit\\TextUI\\TestRunner' => $vendorDir . '/phpunit/phpunit/src/TextUI/TestRunner.php',
    'PHPUnit\\TextUI\\TestSuiteMapper' => $vendorDir . '/phpunit/phpunit/src/TextUI/TestSuiteMapper.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CodeCoverage\\CodeCoverage' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/CodeCoverage/CodeCoverage.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CodeCoverage\\FilterMapper' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/CodeCoverage/FilterMapper.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CodeCoverage\\Filter\\Directory' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/CodeCoverage/Filter/Directory.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CodeCoverage\\Filter\\DirectoryCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/CodeCoverage/Filter/DirectoryCollection.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CodeCoverage\\Filter\\DirectoryCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/CodeCoverage/Filter/DirectoryCollectionIterator.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CodeCoverage\\Report\\Clover' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/CodeCoverage/Report/Clover.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CodeCoverage\\Report\\Cobertura' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/CodeCoverage/Report/Cobertura.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CodeCoverage\\Report\\Crap4j' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/CodeCoverage/Report/Crap4j.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CodeCoverage\\Report\\Html' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/CodeCoverage/Report/Html.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CodeCoverage\\Report\\Php' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/CodeCoverage/Report/Php.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CodeCoverage\\Report\\Text' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/CodeCoverage/Report/Text.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CodeCoverage\\Report\\Xml' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/CodeCoverage/Report/Xml.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Configuration' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Configuration.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Constant' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/PHP/Constant.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\ConstantCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/PHP/ConstantCollection.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\ConstantCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/PHP/ConstantCollectionIterator.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\ConvertLogTypes' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Migration/Migrations/ConvertLogTypes.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CoverageCloverToReport' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Migration/Migrations/CoverageCloverToReport.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CoverageCrap4jToReport' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Migration/Migrations/CoverageCrap4jToReport.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CoverageHtmlToReport' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Migration/Migrations/CoverageHtmlToReport.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CoveragePhpToReport' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Migration/Migrations/CoveragePhpToReport.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CoverageTextToReport' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Migration/Migrations/CoverageTextToReport.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\CoverageXmlToReport' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Migration/Migrations/CoverageXmlToReport.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Directory' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Filesystem/Directory.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\DirectoryCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Filesystem/DirectoryCollection.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\DirectoryCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Filesystem/DirectoryCollectionIterator.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Exception' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Exception.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Extension' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/PHPUnit/Extension.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\ExtensionCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/PHPUnit/ExtensionCollection.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\ExtensionCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/PHPUnit/ExtensionCollectionIterator.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\File' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Filesystem/File.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\FileCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Filesystem/FileCollection.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\FileCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Filesystem/FileCollectionIterator.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Generator' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Generator.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Group' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Group/Group.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\GroupCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Group/GroupCollection.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\GroupCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Group/GroupCollectionIterator.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Groups' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Group/Groups.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\IniSetting' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/PHP/IniSetting.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\IniSettingCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/PHP/IniSettingCollection.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\IniSettingCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/PHP/IniSettingCollectionIterator.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\IntroduceCoverageElement' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Migration/Migrations/IntroduceCoverageElement.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Loader' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Loader.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\LogToReportMigration' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Migration/Migrations/LogToReportMigration.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Logging\\Junit' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Logging/Junit.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Logging\\Logging' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Logging/Logging.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Logging\\TeamCity' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Logging/TeamCity.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Logging\\TestDox\\Html' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Logging/TestDox/Html.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Logging\\TestDox\\Text' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Logging/TestDox/Text.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Logging\\TestDox\\Xml' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Logging/TestDox/Xml.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Logging\\Text' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Logging/Text.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Migration' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Migration/Migrations/Migration.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\MigrationBuilder' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Migration/MigrationBuilder.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\MigrationBuilderException' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Migration/MigrationBuilderException.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\MigrationException' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Migration/MigrationException.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Migrator' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Migration/Migrator.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\MoveAttributesFromFilterWhitelistToCoverage' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Migration/Migrations/MoveAttributesFromFilterWhitelistToCoverage.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\MoveAttributesFromRootToCoverage' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Migration/Migrations/MoveAttributesFromRootToCoverage.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\MoveWhitelistExcludesToCoverage' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Migration/Migrations/MoveWhitelistExcludesToCoverage.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\MoveWhitelistIncludesToCoverage' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Migration/Migrations/MoveWhitelistIncludesToCoverage.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\PHPUnit' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/PHPUnit/PHPUnit.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Php' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/PHP/Php.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\PhpHandler' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/PHP/PhpHandler.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveCacheTokensAttribute' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Migration/Migrations/RemoveCacheTokensAttribute.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveEmptyFilter' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Migration/Migrations/RemoveEmptyFilter.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\RemoveLogTypes' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Migration/Migrations/RemoveLogTypes.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\TestDirectory' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/TestSuite/TestDirectory.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\TestDirectoryCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/TestSuite/TestDirectoryCollection.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\TestDirectoryCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/TestSuite/TestDirectoryCollectionIterator.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\TestFile' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/TestSuite/TestFile.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\TestFileCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/TestSuite/TestFileCollection.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\TestFileCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/TestSuite/TestFileCollectionIterator.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\TestSuite' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/TestSuite/TestSuite.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\TestSuiteCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/TestSuite/TestSuiteCollection.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\TestSuiteCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/TestSuite/TestSuiteCollectionIterator.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\UpdateSchemaLocationTo93' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/Migration/Migrations/UpdateSchemaLocationTo93.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\Variable' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/PHP/Variable.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\VariableCollection' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/PHP/VariableCollection.php',
    'PHPUnit\\TextUI\\XmlConfiguration\\VariableCollectionIterator' => $vendorDir . '/phpunit/phpunit/src/TextUI/XmlConfiguration/PHP/VariableCollectionIterator.php',
    'PHPUnit\\Util\\Annotation\\DocBlock' => $vendorDir . '/phpunit/phpunit/src/Util/Annotation/DocBlock.php',
    'PHPUnit\\Util\\Annotation\\Registry' => $vendorDir . '/phpunit/phpunit/src/Util/Annotation/Registry.php',
    'PHPUnit\\Util\\Blacklist' => $vendorDir . '/phpunit/phpunit/src/Util/Blacklist.php',
    'PHPUnit\\Util\\Cloner' => $vendorDir . '/phpunit/phpunit/src/Util/Cloner.php',
    'PHPUnit\\Util\\Color' => $vendorDir . '/phpunit/phpunit/src/Util/Color.php',
    'PHPUnit\\Util\\ErrorHandler' => $vendorDir . '/phpunit/phpunit/src/Util/ErrorHandler.php',
    'PHPUnit\\Util\\Exception' => $vendorDir . '/phpunit/phpunit/src/Util/Exception.php',
    'PHPUnit\\Util\\ExcludeList' => $vendorDir . '/phpunit/phpunit/src/Util/ExcludeList.php',
    'PHPUnit\\Util\\FileLoader' => $vendorDir . '/phpunit/phpunit/src/Util/FileLoader.php',
    'PHPUnit\\Util\\Filesystem' => $vendorDir . '/phpunit/phpunit/src/Util/Filesystem.php',
    'PHPUnit\\Util\\Filter' => $vendorDir . '/phpunit/phpunit/src/Util/Filter.php',
    'PHPUnit\\Util\\GlobalState' => $vendorDir . '/phpunit/phpunit/src/Util/GlobalState.php',
    'PHPUnit\\Util\\InvalidDataSetException' => $vendorDir . '/phpunit/phpunit/src/Util/InvalidDataSetException.php',
    'PHPUnit\\Util\\Json' => $vendorDir . '/phpunit/phpunit/src/Util/Json.php',
    'PHPUnit\\Util\\Log\\JUnit' => $vendorDir . '/phpunit/phpunit/src/Util/Log/JUnit.php',
    'PHPUnit\\Util\\Log\\TeamCity' => $vendorDir . '/phpunit/phpunit/src/Util/Log/TeamCity.php',
    'PHPUnit\\Util\\PHP\\AbstractPhpProcess' => $vendorDir . '/phpunit/phpunit/src/Util/PHP/AbstractPhpProcess.php',
    'PHPUnit\\Util\\PHP\\DefaultPhpProcess' => $vendorDir . '/phpunit/phpunit/src/Util/PHP/DefaultPhpProcess.php',
    'PHPUnit\\Util\\PHP\\WindowsPhpProcess' => $vendorDir . '/phpunit/phpunit/src/Util/PHP/WindowsPhpProcess.php',
    'PHPUnit\\Util\\Printer' => $vendorDir . '/phpunit/phpunit/src/Util/Printer.php',
    'PHPUnit\\Util\\Reflection' => $vendorDir . '/phpunit/phpunit/src/Util/Reflection.php',
    'PHPUnit\\Util\\RegularExpression' => $vendorDir . '/phpunit/phpunit/src/Util/RegularExpression.php',
    'PHPUnit\\Util\\Test' => $vendorDir . '/phpunit/phpunit/src/Util/Test.php',
    'PHPUnit\\Util\\TestDox\\CliTestDoxPrinter' => $vendorDir . '/phpunit/phpunit/src/Util/TestDox/CliTestDoxPrinter.php',
    'PHPUnit\\Util\\TestDox\\HtmlResultPrinter' => $vendorDir . '/phpunit/phpunit/src/Util/TestDox/HtmlResultPrinter.php',
    'PHPUnit\\Util\\TestDox\\NamePrettifier' => $vendorDir . '/phpunit/phpunit/src/Util/TestDox/NamePrettifier.php',
    'PHPUnit\\Util\\TestDox\\ResultPrinter' => $vendorDir . '/phpunit/phpunit/src/Util/TestDox/ResultPrinter.php',
    'PHPUnit\\Util\\TestDox\\TestDoxPrinter' => $vendorDir . '/phpunit/phpunit/src/Util/TestDox/TestDoxPrinter.php',
    'PHPUnit\\Util\\TestDox\\TextResultPrinter' => $vendorDir . '/phpunit/phpunit/src/Util/TestDox/TextResultPrinter.php',
    'PHPUnit\\Util\\TestDox\\XmlResultPrinter' => $vendorDir . '/phpunit/phpunit/src/Util/TestDox/XmlResultPrinter.php',
    'PHPUnit\\Util\\TextTestListRenderer' => $vendorDir . '/phpunit/phpunit/src/Util/TextTestListRenderer.php',
    'PHPUnit\\Util\\Type' => $vendorDir . '/phpunit/phpunit/src/Util/Type.php',
    'PHPUnit\\Util\\VersionComparisonOperator' => $vendorDir . '/phpunit/phpunit/src/Util/VersionComparisonOperator.php',
    'PHPUnit\\Util\\XdebugFilterScriptGenerator' => $vendorDir . '/phpunit/phpunit/src/Util/XdebugFilterScriptGenerator.php',
    'PHPUnit\\Util\\Xml' => $vendorDir . '/phpunit/phpunit/src/Util/Xml.php',
    'PHPUnit\\Util\\XmlTestListRenderer' => $vendorDir . '/phpunit/phpunit/src/Util/XmlTestListRenderer.php',
    'PHPUnit\\Util\\Xml\\Exception' => $vendorDir . '/phpunit/phpunit/src/Util/Xml/Exception.php',
    'PHPUnit\\Util\\Xml\\FailedSchemaDetectionResult' => $vendorDir . '/phpunit/phpunit/src/Util/Xml/FailedSchemaDetectionResult.php',
    'PHPUnit\\Util\\Xml\\Loader' => $vendorDir . '/phpunit/phpunit/src/Util/Xml/Loader.php',
    'PHPUnit\\Util\\Xml\\SchemaDetectionResult' => $vendorDir . '/phpunit/phpunit/src/Util/Xml/SchemaDetectionResult.php',
    'PHPUnit\\Util\\Xml\\SchemaDetector' => $vendorDir . '/phpunit/phpunit/src/Util/Xml/SchemaDetector.php',
    'PHPUnit\\Util\\Xml\\SchemaFinder' => $vendorDir . '/phpunit/phpunit/src/Util/Xml/SchemaFinder.php',
    'PHPUnit\\Util\\Xml\\SnapshotNodeList' => $vendorDir . '/phpunit/phpunit/src/Util/Xml/SnapshotNodeList.php',
    'PHPUnit\\Util\\Xml\\SuccessfulSchemaDetectionResult' => $vendorDir . '/phpunit/phpunit/src/Util/Xml/SuccessfulSchemaDetectionResult.php',
    'PHPUnit\\Util\\Xml\\ValidationResult' => $vendorDir . '/phpunit/phpunit/src/Util/Xml/ValidationResult.php',
    'PHPUnit\\Util\\Xml\\Validator' => $vendorDir . '/phpunit/phpunit/src/Util/Xml/Validator.php',
    'Package_Command' => $vendorDir . '/wp-cli/package-command/src/Package_Command.php',
    'Peast\\Formatter\\Base' => $vendorDir . '/mck89/peast/lib/Peast/Formatter/Base.php',
    'Peast\\Formatter\\Compact' => $vendorDir . '/mck89/peast/lib/Peast/Formatter/Compact.php',
    'Peast\\Formatter\\Expanded' => $vendorDir . '/mck89/peast/lib/Peast/Formatter/Expanded.php',
    'Peast\\Formatter\\PrettyPrint' => $vendorDir . '/mck89/peast/lib/Peast/Formatter/PrettyPrint.php',
    'Peast\\Peast' => $vendorDir . '/mck89/peast/lib/Peast/Peast.php',
    'Peast\\Query' => $vendorDir . '/mck89/peast/lib/Peast/Query.php',
    'Peast\\Renderer' => $vendorDir . '/mck89/peast/lib/Peast/Renderer.php',
    'Peast\\Selector\\Exception' => $vendorDir . '/mck89/peast/lib/Peast/Selector/Exception.php',
    'Peast\\Selector\\Matches' => $vendorDir . '/mck89/peast/lib/Peast/Selector/Matches.php',
    'Peast\\Selector\\Node\\Combinator' => $vendorDir . '/mck89/peast/lib/Peast/Selector/Node/Combinator.php',
    'Peast\\Selector\\Node\\Group' => $vendorDir . '/mck89/peast/lib/Peast/Selector/Node/Group.php',
    'Peast\\Selector\\Node\\Part\\Attribute' => $vendorDir . '/mck89/peast/lib/Peast/Selector/Node/Part/Attribute.php',
    'Peast\\Selector\\Node\\Part\\Part' => $vendorDir . '/mck89/peast/lib/Peast/Selector/Node/Part/Part.php',
    'Peast\\Selector\\Node\\Part\\Pseudo' => $vendorDir . '/mck89/peast/lib/Peast/Selector/Node/Part/Pseudo.php',
    'Peast\\Selector\\Node\\Part\\PseudoIndex' => $vendorDir . '/mck89/peast/lib/Peast/Selector/Node/Part/PseudoIndex.php',
    'Peast\\Selector\\Node\\Part\\PseudoSelector' => $vendorDir . '/mck89/peast/lib/Peast/Selector/Node/Part/PseudoSelector.php',
    'Peast\\Selector\\Node\\Part\\PseudoSimple' => $vendorDir . '/mck89/peast/lib/Peast/Selector/Node/Part/PseudoSimple.php',
    'Peast\\Selector\\Node\\Part\\Type' => $vendorDir . '/mck89/peast/lib/Peast/Selector/Node/Part/Type.php',
    'Peast\\Selector\\Node\\Selector' => $vendorDir . '/mck89/peast/lib/Peast/Selector/Node/Selector.php',
    'Peast\\Selector\\Parser' => $vendorDir . '/mck89/peast/lib/Peast/Selector/Parser.php',
    'Peast\\Syntax\\CommentsRegistry' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/CommentsRegistry.php',
    'Peast\\Syntax\\ES2015\\Features' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/ES2015/Features.php',
    'Peast\\Syntax\\ES2016\\Features' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/ES2016/Features.php',
    'Peast\\Syntax\\ES2017\\Features' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/ES2017/Features.php',
    'Peast\\Syntax\\ES2018\\Features' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/ES2018/Features.php',
    'Peast\\Syntax\\ES2019\\Features' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/ES2019/Features.php',
    'Peast\\Syntax\\ES2020\\Features' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/ES2020/Features.php',
    'Peast\\Syntax\\ES2021\\Features' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/ES2021/Features.php',
    'Peast\\Syntax\\ES2022\\Features' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/ES2022/Features.php',
    'Peast\\Syntax\\ES2023\\Features' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/ES2023/Features.php',
    'Peast\\Syntax\\ES2024\\Features' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/ES2024/Features.php',
    'Peast\\Syntax\\ES2025\\Features' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/ES2025/Features.php',
    'Peast\\Syntax\\EncodingException' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/EncodingException.php',
    'Peast\\Syntax\\EventsEmitter' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/EventsEmitter.php',
    'Peast\\Syntax\\Exception' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Exception.php',
    'Peast\\Syntax\\Features' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Features.php',
    'Peast\\Syntax\\JSX\\Parser' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/JSX/Parser.php',
    'Peast\\Syntax\\JSX\\Scanner' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/JSX/Scanner.php',
    'Peast\\Syntax\\LSM' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/LSM.php',
    'Peast\\Syntax\\Node\\ArrayExpression' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ArrayExpression.php',
    'Peast\\Syntax\\Node\\ArrayPattern' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ArrayPattern.php',
    'Peast\\Syntax\\Node\\ArrowFunctionExpression' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ArrowFunctionExpression.php',
    'Peast\\Syntax\\Node\\AssignmentExpression' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/AssignmentExpression.php',
    'Peast\\Syntax\\Node\\AssignmentPattern' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/AssignmentPattern.php',
    'Peast\\Syntax\\Node\\AssignmentProperty' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/AssignmentProperty.php',
    'Peast\\Syntax\\Node\\AwaitExpression' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/AwaitExpression.php',
    'Peast\\Syntax\\Node\\BigIntLiteral' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/BigIntLiteral.php',
    'Peast\\Syntax\\Node\\BinaryExpression' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/BinaryExpression.php',
    'Peast\\Syntax\\Node\\BlockStatement' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/BlockStatement.php',
    'Peast\\Syntax\\Node\\BooleanLiteral' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/BooleanLiteral.php',
    'Peast\\Syntax\\Node\\BreakStatement' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/BreakStatement.php',
    'Peast\\Syntax\\Node\\CallExpression' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/CallExpression.php',
    'Peast\\Syntax\\Node\\CatchClause' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/CatchClause.php',
    'Peast\\Syntax\\Node\\ChainElement' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ChainElement.php',
    'Peast\\Syntax\\Node\\ChainExpression' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ChainExpression.php',
    'Peast\\Syntax\\Node\\ClassBody' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ClassBody.php',
    'Peast\\Syntax\\Node\\ClassDeclaration' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ClassDeclaration.php',
    'Peast\\Syntax\\Node\\ClassExpression' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ClassExpression.php',
    'Peast\\Syntax\\Node\\Class_' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/Class_.php',
    'Peast\\Syntax\\Node\\Comment' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/Comment.php',
    'Peast\\Syntax\\Node\\ConditionalExpression' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ConditionalExpression.php',
    'Peast\\Syntax\\Node\\ContinueStatement' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ContinueStatement.php',
    'Peast\\Syntax\\Node\\DebuggerStatement' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/DebuggerStatement.php',
    'Peast\\Syntax\\Node\\Declaration' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/Declaration.php',
    'Peast\\Syntax\\Node\\DoWhileStatement' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/DoWhileStatement.php',
    'Peast\\Syntax\\Node\\EmptyStatement' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/EmptyStatement.php',
    'Peast\\Syntax\\Node\\ExportAllDeclaration' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ExportAllDeclaration.php',
    'Peast\\Syntax\\Node\\ExportDefaultDeclaration' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ExportDefaultDeclaration.php',
    'Peast\\Syntax\\Node\\ExportNamedDeclaration' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ExportNamedDeclaration.php',
    'Peast\\Syntax\\Node\\ExportSpecifier' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ExportSpecifier.php',
    'Peast\\Syntax\\Node\\Expression' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/Expression.php',
    'Peast\\Syntax\\Node\\ExpressionStatement' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ExpressionStatement.php',
    'Peast\\Syntax\\Node\\ForInStatement' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ForInStatement.php',
    'Peast\\Syntax\\Node\\ForOfStatement' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ForOfStatement.php',
    'Peast\\Syntax\\Node\\ForStatement' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ForStatement.php',
    'Peast\\Syntax\\Node\\FunctionDeclaration' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/FunctionDeclaration.php',
    'Peast\\Syntax\\Node\\FunctionExpression' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/FunctionExpression.php',
    'Peast\\Syntax\\Node\\Function_' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/Function_.php',
    'Peast\\Syntax\\Node\\Identifier' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/Identifier.php',
    'Peast\\Syntax\\Node\\IfStatement' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/IfStatement.php',
    'Peast\\Syntax\\Node\\ImportAttribute' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ImportAttribute.php',
    'Peast\\Syntax\\Node\\ImportDeclaration' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ImportDeclaration.php',
    'Peast\\Syntax\\Node\\ImportDefaultSpecifier' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ImportDefaultSpecifier.php',
    'Peast\\Syntax\\Node\\ImportExpression' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ImportExpression.php',
    'Peast\\Syntax\\Node\\ImportNamespaceSpecifier' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ImportNamespaceSpecifier.php',
    'Peast\\Syntax\\Node\\ImportSpecifier' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ImportSpecifier.php',
    'Peast\\Syntax\\Node\\JSX\\JSXAttribute' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/JSX/JSXAttribute.php',
    'Peast\\Syntax\\Node\\JSX\\JSXBoundaryElement' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/JSX/JSXBoundaryElement.php',
    'Peast\\Syntax\\Node\\JSX\\JSXClosingElement' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/JSX/JSXClosingElement.php',
    'Peast\\Syntax\\Node\\JSX\\JSXClosingFragment' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/JSX/JSXClosingFragment.php',
    'Peast\\Syntax\\Node\\JSX\\JSXElement' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/JSX/JSXElement.php',
    'Peast\\Syntax\\Node\\JSX\\JSXEmptyExpression' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/JSX/JSXEmptyExpression.php',
    'Peast\\Syntax\\Node\\JSX\\JSXExpressionContainer' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/JSX/JSXExpressionContainer.php',
    'Peast\\Syntax\\Node\\JSX\\JSXFragment' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/JSX/JSXFragment.php',
    'Peast\\Syntax\\Node\\JSX\\JSXIdentifier' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/JSX/JSXIdentifier.php',
    'Peast\\Syntax\\Node\\JSX\\JSXMemberExpression' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/JSX/JSXMemberExpression.php',
    'Peast\\Syntax\\Node\\JSX\\JSXNamespacedName' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/JSX/JSXNamespacedName.php',
    'Peast\\Syntax\\Node\\JSX\\JSXOpeningElement' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/JSX/JSXOpeningElement.php',
    'Peast\\Syntax\\Node\\JSX\\JSXOpeningFragment' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/JSX/JSXOpeningFragment.php',
    'Peast\\Syntax\\Node\\JSX\\JSXSpreadAttribute' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/JSX/JSXSpreadAttribute.php',
    'Peast\\Syntax\\Node\\JSX\\JSXSpreadChild' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/JSX/JSXSpreadChild.php',
    'Peast\\Syntax\\Node\\JSX\\JSXText' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/JSX/JSXText.php',
    'Peast\\Syntax\\Node\\LabeledStatement' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/LabeledStatement.php',
    'Peast\\Syntax\\Node\\Literal' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/Literal.php',
    'Peast\\Syntax\\Node\\LogicalExpression' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/LogicalExpression.php',
    'Peast\\Syntax\\Node\\MemberExpression' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/MemberExpression.php',
    'Peast\\Syntax\\Node\\MetaProperty' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/MetaProperty.php',
    'Peast\\Syntax\\Node\\MethodDefinition' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/MethodDefinition.php',
    'Peast\\Syntax\\Node\\ModuleDeclaration' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ModuleDeclaration.php',
    'Peast\\Syntax\\Node\\ModuleSpecifier' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ModuleSpecifier.php',
    'Peast\\Syntax\\Node\\NewExpression' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/NewExpression.php',
    'Peast\\Syntax\\Node\\Node' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/Node.php',
    'Peast\\Syntax\\Node\\NullLiteral' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/NullLiteral.php',
    'Peast\\Syntax\\Node\\NumericLiteral' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/NumericLiteral.php',
    'Peast\\Syntax\\Node\\ObjectExpression' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ObjectExpression.php',
    'Peast\\Syntax\\Node\\ObjectPattern' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ObjectPattern.php',
    'Peast\\Syntax\\Node\\ParenthesizedExpression' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ParenthesizedExpression.php',
    'Peast\\Syntax\\Node\\Pattern' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/Pattern.php',
    'Peast\\Syntax\\Node\\PrivateIdentifier' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/PrivateIdentifier.php',
    'Peast\\Syntax\\Node\\Program' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/Program.php',
    'Peast\\Syntax\\Node\\Property' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/Property.php',
    'Peast\\Syntax\\Node\\PropertyDefinition' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/PropertyDefinition.php',
    'Peast\\Syntax\\Node\\RegExpLiteral' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/RegExpLiteral.php',
    'Peast\\Syntax\\Node\\RestElement' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/RestElement.php',
    'Peast\\Syntax\\Node\\ReturnStatement' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ReturnStatement.php',
    'Peast\\Syntax\\Node\\SequenceExpression' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/SequenceExpression.php',
    'Peast\\Syntax\\Node\\SpreadElement' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/SpreadElement.php',
    'Peast\\Syntax\\Node\\Statement' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/Statement.php',
    'Peast\\Syntax\\Node\\StaticBlock' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/StaticBlock.php',
    'Peast\\Syntax\\Node\\StringLiteral' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/StringLiteral.php',
    'Peast\\Syntax\\Node\\Super' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/Super.php',
    'Peast\\Syntax\\Node\\SwitchCase' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/SwitchCase.php',
    'Peast\\Syntax\\Node\\SwitchStatement' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/SwitchStatement.php',
    'Peast\\Syntax\\Node\\TaggedTemplateExpression' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/TaggedTemplateExpression.php',
    'Peast\\Syntax\\Node\\TemplateElement' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/TemplateElement.php',
    'Peast\\Syntax\\Node\\TemplateLiteral' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/TemplateLiteral.php',
    'Peast\\Syntax\\Node\\ThisExpression' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ThisExpression.php',
    'Peast\\Syntax\\Node\\ThrowStatement' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/ThrowStatement.php',
    'Peast\\Syntax\\Node\\TryStatement' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/TryStatement.php',
    'Peast\\Syntax\\Node\\UnaryExpression' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/UnaryExpression.php',
    'Peast\\Syntax\\Node\\UpdateExpression' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/UpdateExpression.php',
    'Peast\\Syntax\\Node\\VariableDeclaration' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/VariableDeclaration.php',
    'Peast\\Syntax\\Node\\VariableDeclarator' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/VariableDeclarator.php',
    'Peast\\Syntax\\Node\\WhileStatement' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/WhileStatement.php',
    'Peast\\Syntax\\Node\\WithStatement' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/WithStatement.php',
    'Peast\\Syntax\\Node\\YieldExpression' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Node/YieldExpression.php',
    'Peast\\Syntax\\Parser' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Parser.php',
    'Peast\\Syntax\\ParserAbstract' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/ParserAbstract.php',
    'Peast\\Syntax\\Position' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Position.php',
    'Peast\\Syntax\\Scanner' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Scanner.php',
    'Peast\\Syntax\\SourceLocation' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/SourceLocation.php',
    'Peast\\Syntax\\Token' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Token.php',
    'Peast\\Syntax\\Utils' => $vendorDir . '/mck89/peast/lib/Peast/Syntax/Utils.php',
    'Peast\\Traverser' => $vendorDir . '/mck89/peast/lib/Peast/Traverser.php',
    'PharIo\\Manifest\\Application' => $vendorDir . '/phar-io/manifest/src/values/Application.php',
    'PharIo\\Manifest\\ApplicationName' => $vendorDir . '/phar-io/manifest/src/values/ApplicationName.php',
    'PharIo\\Manifest\\Author' => $vendorDir . '/phar-io/manifest/src/values/Author.php',
    'PharIo\\Manifest\\AuthorCollection' => $vendorDir . '/phar-io/manifest/src/values/AuthorCollection.php',
    'PharIo\\Manifest\\AuthorCollectionIterator' => $vendorDir . '/phar-io/manifest/src/values/AuthorCollectionIterator.php',
    'PharIo\\Manifest\\AuthorElement' => $vendorDir . '/phar-io/manifest/src/xml/AuthorElement.php',
    'PharIo\\Manifest\\AuthorElementCollection' => $vendorDir . '/phar-io/manifest/src/xml/AuthorElementCollection.php',
    'PharIo\\Manifest\\BundledComponent' => $vendorDir . '/phar-io/manifest/src/values/BundledComponent.php',
    'PharIo\\Manifest\\BundledComponentCollection' => $vendorDir . '/phar-io/manifest/src/values/BundledComponentCollection.php',
    'PharIo\\Manifest\\BundledComponentCollectionIterator' => $vendorDir . '/phar-io/manifest/src/values/BundledComponentCollectionIterator.php',
    'PharIo\\Manifest\\BundlesElement' => $vendorDir . '/phar-io/manifest/src/xml/BundlesElement.php',
    'PharIo\\Manifest\\ComponentElement' => $vendorDir . '/phar-io/manifest/src/xml/ComponentElement.php',
    'PharIo\\Manifest\\ComponentElementCollection' => $vendorDir . '/phar-io/manifest/src/xml/ComponentElementCollection.php',
    'PharIo\\Manifest\\ContainsElement' => $vendorDir . '/phar-io/manifest/src/xml/ContainsElement.php',
    'PharIo\\Manifest\\CopyrightElement' => $vendorDir . '/phar-io/manifest/src/xml/CopyrightElement.php',
    'PharIo\\Manifest\\CopyrightInformation' => $vendorDir . '/phar-io/manifest/src/values/CopyrightInformation.php',
    'PharIo\\Manifest\\ElementCollection' => $vendorDir . '/phar-io/manifest/src/xml/ElementCollection.php',
    'PharIo\\Manifest\\ElementCollectionException' => $vendorDir . '/phar-io/manifest/src/exceptions/ElementCollectionException.php',
    'PharIo\\Manifest\\Email' => $vendorDir . '/phar-io/manifest/src/values/Email.php',
    'PharIo\\Manifest\\Exception' => $vendorDir . '/phar-io/manifest/src/exceptions/Exception.php',
    'PharIo\\Manifest\\ExtElement' => $vendorDir . '/phar-io/manifest/src/xml/ExtElement.php',
    'PharIo\\Manifest\\ExtElementCollection' => $vendorDir . '/phar-io/manifest/src/xml/ExtElementCollection.php',
    'PharIo\\Manifest\\Extension' => $vendorDir . '/phar-io/manifest/src/values/Extension.php',
    'PharIo\\Manifest\\ExtensionElement' => $vendorDir . '/phar-io/manifest/src/xml/ExtensionElement.php',
    'PharIo\\Manifest\\InvalidApplicationNameException' => $vendorDir . '/phar-io/manifest/src/exceptions/InvalidApplicationNameException.php',
    'PharIo\\Manifest\\InvalidEmailException' => $vendorDir . '/phar-io/manifest/src/exceptions/InvalidEmailException.php',
    'PharIo\\Manifest\\InvalidUrlException' => $vendorDir . '/phar-io/manifest/src/exceptions/InvalidUrlException.php',
    'PharIo\\Manifest\\Library' => $vendorDir . '/phar-io/manifest/src/values/Library.php',
    'PharIo\\Manifest\\License' => $vendorDir . '/phar-io/manifest/src/values/License.php',
    'PharIo\\Manifest\\LicenseElement' => $vendorDir . '/phar-io/manifest/src/xml/LicenseElement.php',
    'PharIo\\Manifest\\Manifest' => $vendorDir . '/phar-io/manifest/src/values/Manifest.php',
    'PharIo\\Manifest\\ManifestDocument' => $vendorDir . '/phar-io/manifest/src/xml/ManifestDocument.php',
    'PharIo\\Manifest\\ManifestDocumentException' => $vendorDir . '/phar-io/manifest/src/exceptions/ManifestDocumentException.php',
    'PharIo\\Manifest\\ManifestDocumentLoadingException' => $vendorDir . '/phar-io/manifest/src/exceptions/ManifestDocumentLoadingException.php',
    'PharIo\\Manifest\\ManifestDocumentMapper' => $vendorDir . '/phar-io/manifest/src/ManifestDocumentMapper.php',
    'PharIo\\Manifest\\ManifestDocumentMapperException' => $vendorDir . '/phar-io/manifest/src/exceptions/ManifestDocumentMapperException.php',
    'PharIo\\Manifest\\ManifestElement' => $vendorDir . '/phar-io/manifest/src/xml/ManifestElement.php',
    'PharIo\\Manifest\\ManifestElementException' => $vendorDir . '/phar-io/manifest/src/exceptions/ManifestElementException.php',
    'PharIo\\Manifest\\ManifestLoader' => $vendorDir . '/phar-io/manifest/src/ManifestLoader.php',
    'PharIo\\Manifest\\ManifestLoaderException' => $vendorDir . '/phar-io/manifest/src/exceptions/ManifestLoaderException.php',
    'PharIo\\Manifest\\ManifestSerializer' => $vendorDir . '/phar-io/manifest/src/ManifestSerializer.php',
    'PharIo\\Manifest\\NoEmailAddressException' => $vendorDir . '/phar-io/manifest/src/exceptions/NoEmailAddressException.php',
    'PharIo\\Manifest\\PhpElement' => $vendorDir . '/phar-io/manifest/src/xml/PhpElement.php',
    'PharIo\\Manifest\\PhpExtensionRequirement' => $vendorDir . '/phar-io/manifest/src/values/PhpExtensionRequirement.php',
    'PharIo\\Manifest\\PhpVersionRequirement' => $vendorDir . '/phar-io/manifest/src/values/PhpVersionRequirement.php',
    'PharIo\\Manifest\\Requirement' => $vendorDir . '/phar-io/manifest/src/values/Requirement.php',
    'PharIo\\Manifest\\RequirementCollection' => $vendorDir . '/phar-io/manifest/src/values/RequirementCollection.php',
    'PharIo\\Manifest\\RequirementCollectionIterator' => $vendorDir . '/phar-io/manifest/src/values/RequirementCollectionIterator.php',
    'PharIo\\Manifest\\RequiresElement' => $vendorDir . '/phar-io/manifest/src/xml/RequiresElement.php',
    'PharIo\\Manifest\\Type' => $vendorDir . '/phar-io/manifest/src/values/Type.php',
    'PharIo\\Manifest\\Url' => $vendorDir . '/phar-io/manifest/src/values/Url.php',
    'PharIo\\Version\\AbstractVersionConstraint' => $vendorDir . '/phar-io/version/src/constraints/AbstractVersionConstraint.php',
    'PharIo\\Version\\AndVersionConstraintGroup' => $vendorDir . '/phar-io/version/src/constraints/AndVersionConstraintGroup.php',
    'PharIo\\Version\\AnyVersionConstraint' => $vendorDir . '/phar-io/version/src/constraints/AnyVersionConstraint.php',
    'PharIo\\Version\\BuildMetaData' => $vendorDir . '/phar-io/version/src/BuildMetaData.php',
    'PharIo\\Version\\ExactVersionConstraint' => $vendorDir . '/phar-io/version/src/constraints/ExactVersionConstraint.php',
    'PharIo\\Version\\Exception' => $vendorDir . '/phar-io/version/src/exceptions/Exception.php',
    'PharIo\\Version\\GreaterThanOrEqualToVersionConstraint' => $vendorDir . '/phar-io/version/src/constraints/GreaterThanOrEqualToVersionConstraint.php',
    'PharIo\\Version\\InvalidPreReleaseSuffixException' => $vendorDir . '/phar-io/version/src/exceptions/InvalidPreReleaseSuffixException.php',
    'PharIo\\Version\\InvalidVersionException' => $vendorDir . '/phar-io/version/src/exceptions/InvalidVersionException.php',
    'PharIo\\Version\\NoBuildMetaDataException' => $vendorDir . '/phar-io/version/src/exceptions/NoBuildMetaDataException.php',
    'PharIo\\Version\\NoPreReleaseSuffixException' => $vendorDir . '/phar-io/version/src/exceptions/NoPreReleaseSuffixException.php',
    'PharIo\\Version\\OrVersionConstraintGroup' => $vendorDir . '/phar-io/version/src/constraints/OrVersionConstraintGroup.php',
    'PharIo\\Version\\PreReleaseSuffix' => $vendorDir . '/phar-io/version/src/PreReleaseSuffix.php',
    'PharIo\\Version\\SpecificMajorAndMinorVersionConstraint' => $vendorDir . '/phar-io/version/src/constraints/SpecificMajorAndMinorVersionConstraint.php',
    'PharIo\\Version\\SpecificMajorVersionConstraint' => $vendorDir . '/phar-io/version/src/constraints/SpecificMajorVersionConstraint.php',
    'PharIo\\Version\\UnsupportedVersionConstraintException' => $vendorDir . '/phar-io/version/src/exceptions/UnsupportedVersionConstraintException.php',
    'PharIo\\Version\\Version' => $vendorDir . '/phar-io/version/src/Version.php',
    'PharIo\\Version\\VersionConstraint' => $vendorDir . '/phar-io/version/src/constraints/VersionConstraint.php',
    'PharIo\\Version\\VersionConstraintParser' => $vendorDir . '/phar-io/version/src/VersionConstraintParser.php',
    'PharIo\\Version\\VersionConstraintValue' => $vendorDir . '/phar-io/version/src/VersionConstraintValue.php',
    'PharIo\\Version\\VersionNumber' => $vendorDir . '/phar-io/version/src/VersionNumber.php',
    'PhpOffice\\Math\\Element\\AbstractElement' => $vendorDir . '/phpoffice/math/src/Math/Element/AbstractElement.php',
    'PhpOffice\\Math\\Element\\AbstractGroupElement' => $vendorDir . '/phpoffice/math/src/Math/Element/AbstractGroupElement.php',
    'PhpOffice\\Math\\Element\\Fraction' => $vendorDir . '/phpoffice/math/src/Math/Element/Fraction.php',
    'PhpOffice\\Math\\Element\\Identifier' => $vendorDir . '/phpoffice/math/src/Math/Element/Identifier.php',
    'PhpOffice\\Math\\Element\\Numeric' => $vendorDir . '/phpoffice/math/src/Math/Element/Numeric.php',
    'PhpOffice\\Math\\Element\\Operator' => $vendorDir . '/phpoffice/math/src/Math/Element/Operator.php',
    'PhpOffice\\Math\\Element\\Row' => $vendorDir . '/phpoffice/math/src/Math/Element/Row.php',
    'PhpOffice\\Math\\Element\\Semantics' => $vendorDir . '/phpoffice/math/src/Math/Element/Semantics.php',
    'PhpOffice\\Math\\Element\\Superscript' => $vendorDir . '/phpoffice/math/src/Math/Element/Superscript.php',
    'PhpOffice\\Math\\Exception\\InvalidInputException' => $vendorDir . '/phpoffice/math/src/Math/Exception/InvalidInputException.php',
    'PhpOffice\\Math\\Exception\\MathException' => $vendorDir . '/phpoffice/math/src/Math/Exception/MathException.php',
    'PhpOffice\\Math\\Exception\\NotImplementedException' => $vendorDir . '/phpoffice/math/src/Math/Exception/NotImplementedException.php',
    'PhpOffice\\Math\\Math' => $vendorDir . '/phpoffice/math/src/Math/Math.php',
    'PhpOffice\\Math\\Reader\\MathML' => $vendorDir . '/phpoffice/math/src/Math/Reader/MathML.php',
    'PhpOffice\\Math\\Reader\\OfficeMathML' => $vendorDir . '/phpoffice/math/src/Math/Reader/OfficeMathML.php',
    'PhpOffice\\Math\\Reader\\ReaderInterface' => $vendorDir . '/phpoffice/math/src/Math/Reader/ReaderInterface.php',
    'PhpOffice\\Math\\Writer\\MathML' => $vendorDir . '/phpoffice/math/src/Math/Writer/MathML.php',
    'PhpOffice\\Math\\Writer\\OfficeMathML' => $vendorDir . '/phpoffice/math/src/Math/Writer/OfficeMathML.php',
    'PhpOffice\\Math\\Writer\\WriterInterface' => $vendorDir . '/phpoffice/math/src/Math/Writer/WriterInterface.php',
    'PhpOffice\\PhpWord\\Collection\\AbstractCollection' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Collection/AbstractCollection.php',
    'PhpOffice\\PhpWord\\Collection\\Bookmarks' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Collection/Bookmarks.php',
    'PhpOffice\\PhpWord\\Collection\\Charts' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Collection/Charts.php',
    'PhpOffice\\PhpWord\\Collection\\Comments' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Collection/Comments.php',
    'PhpOffice\\PhpWord\\Collection\\Endnotes' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Collection/Endnotes.php',
    'PhpOffice\\PhpWord\\Collection\\Footnotes' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Collection/Footnotes.php',
    'PhpOffice\\PhpWord\\Collection\\Titles' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Collection/Titles.php',
    'PhpOffice\\PhpWord\\ComplexType\\FootnoteProperties' => $vendorDir . '/phpoffice/phpword/src/PhpWord/ComplexType/FootnoteProperties.php',
    'PhpOffice\\PhpWord\\ComplexType\\ProofState' => $vendorDir . '/phpoffice/phpword/src/PhpWord/ComplexType/ProofState.php',
    'PhpOffice\\PhpWord\\ComplexType\\TblWidth' => $vendorDir . '/phpoffice/phpword/src/PhpWord/ComplexType/TblWidth.php',
    'PhpOffice\\PhpWord\\ComplexType\\TrackChangesView' => $vendorDir . '/phpoffice/phpword/src/PhpWord/ComplexType/TrackChangesView.php',
    'PhpOffice\\PhpWord\\Element\\AbstractContainer' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/AbstractContainer.php',
    'PhpOffice\\PhpWord\\Element\\AbstractElement' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/AbstractElement.php',
    'PhpOffice\\PhpWord\\Element\\Bookmark' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/Bookmark.php',
    'PhpOffice\\PhpWord\\Element\\Cell' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/Cell.php',
    'PhpOffice\\PhpWord\\Element\\Chart' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/Chart.php',
    'PhpOffice\\PhpWord\\Element\\CheckBox' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/CheckBox.php',
    'PhpOffice\\PhpWord\\Element\\Comment' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/Comment.php',
    'PhpOffice\\PhpWord\\Element\\Endnote' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/Endnote.php',
    'PhpOffice\\PhpWord\\Element\\Field' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/Field.php',
    'PhpOffice\\PhpWord\\Element\\Footer' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/Footer.php',
    'PhpOffice\\PhpWord\\Element\\Footnote' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/Footnote.php',
    'PhpOffice\\PhpWord\\Element\\FormField' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/FormField.php',
    'PhpOffice\\PhpWord\\Element\\Formula' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/Formula.php',
    'PhpOffice\\PhpWord\\Element\\Header' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/Header.php',
    'PhpOffice\\PhpWord\\Element\\Image' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/Image.php',
    'PhpOffice\\PhpWord\\Element\\Line' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/Line.php',
    'PhpOffice\\PhpWord\\Element\\Link' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/Link.php',
    'PhpOffice\\PhpWord\\Element\\ListItem' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/ListItem.php',
    'PhpOffice\\PhpWord\\Element\\ListItemRun' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/ListItemRun.php',
    'PhpOffice\\PhpWord\\Element\\OLEObject' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/OLEObject.php',
    'PhpOffice\\PhpWord\\Element\\PageBreak' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/PageBreak.php',
    'PhpOffice\\PhpWord\\Element\\PreserveText' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/PreserveText.php',
    'PhpOffice\\PhpWord\\Element\\Row' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/Row.php',
    'PhpOffice\\PhpWord\\Element\\SDT' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/SDT.php',
    'PhpOffice\\PhpWord\\Element\\Section' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/Section.php',
    'PhpOffice\\PhpWord\\Element\\Shape' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/Shape.php',
    'PhpOffice\\PhpWord\\Element\\TOC' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/TOC.php',
    'PhpOffice\\PhpWord\\Element\\Table' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/Table.php',
    'PhpOffice\\PhpWord\\Element\\Text' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/Text.php',
    'PhpOffice\\PhpWord\\Element\\TextBox' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/TextBox.php',
    'PhpOffice\\PhpWord\\Element\\TextBreak' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/TextBreak.php',
    'PhpOffice\\PhpWord\\Element\\TextRun' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/TextRun.php',
    'PhpOffice\\PhpWord\\Element\\Title' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/Title.php',
    'PhpOffice\\PhpWord\\Element\\TrackChange' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Element/TrackChange.php',
    'PhpOffice\\PhpWord\\Escaper\\AbstractEscaper' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Escaper/AbstractEscaper.php',
    'PhpOffice\\PhpWord\\Escaper\\EscaperInterface' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Escaper/EscaperInterface.php',
    'PhpOffice\\PhpWord\\Escaper\\RegExp' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Escaper/RegExp.php',
    'PhpOffice\\PhpWord\\Escaper\\Rtf' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Escaper/Rtf.php',
    'PhpOffice\\PhpWord\\Escaper\\Xml' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Escaper/Xml.php',
    'PhpOffice\\PhpWord\\Exception\\CopyFileException' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Exception/CopyFileException.php',
    'PhpOffice\\PhpWord\\Exception\\CreateTemporaryFileException' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Exception/CreateTemporaryFileException.php',
    'PhpOffice\\PhpWord\\Exception\\Exception' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Exception/Exception.php',
    'PhpOffice\\PhpWord\\Exception\\InvalidImageException' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Exception/InvalidImageException.php',
    'PhpOffice\\PhpWord\\Exception\\InvalidObjectException' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Exception/InvalidObjectException.php',
    'PhpOffice\\PhpWord\\Exception\\InvalidStyleException' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Exception/InvalidStyleException.php',
    'PhpOffice\\PhpWord\\Exception\\UnsupportedImageTypeException' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Exception/UnsupportedImageTypeException.php',
    'PhpOffice\\PhpWord\\IOFactory' => $vendorDir . '/phpoffice/phpword/src/PhpWord/IOFactory.php',
    'PhpOffice\\PhpWord\\Media' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Media.php',
    'PhpOffice\\PhpWord\\Metadata\\Compatibility' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Metadata/Compatibility.php',
    'PhpOffice\\PhpWord\\Metadata\\DocInfo' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Metadata/DocInfo.php',
    'PhpOffice\\PhpWord\\Metadata\\Protection' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Metadata/Protection.php',
    'PhpOffice\\PhpWord\\Metadata\\Settings' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Metadata/Settings.php',
    'PhpOffice\\PhpWord\\PhpWord' => $vendorDir . '/phpoffice/phpword/src/PhpWord/PhpWord.php',
    'PhpOffice\\PhpWord\\Reader\\AbstractReader' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Reader/AbstractReader.php',
    'PhpOffice\\PhpWord\\Reader\\HTML' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Reader/HTML.php',
    'PhpOffice\\PhpWord\\Reader\\MsDoc' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Reader/MsDoc.php',
    'PhpOffice\\PhpWord\\Reader\\ODText' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Reader/ODText.php',
    'PhpOffice\\PhpWord\\Reader\\ODText\\AbstractPart' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Reader/ODText/AbstractPart.php',
    'PhpOffice\\PhpWord\\Reader\\ODText\\Content' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Reader/ODText/Content.php',
    'PhpOffice\\PhpWord\\Reader\\ODText\\Meta' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Reader/ODText/Meta.php',
    'PhpOffice\\PhpWord\\Reader\\RTF' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Reader/RTF.php',
    'PhpOffice\\PhpWord\\Reader\\RTF\\Document' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Reader/RTF/Document.php',
    'PhpOffice\\PhpWord\\Reader\\ReaderInterface' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Reader/ReaderInterface.php',
    'PhpOffice\\PhpWord\\Reader\\Word2007' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Reader/Word2007.php',
    'PhpOffice\\PhpWord\\Reader\\Word2007\\AbstractPart' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Reader/Word2007/AbstractPart.php',
    'PhpOffice\\PhpWord\\Reader\\Word2007\\Comments' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Reader/Word2007/Comments.php',
    'PhpOffice\\PhpWord\\Reader\\Word2007\\DocPropsApp' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Reader/Word2007/DocPropsApp.php',
    'PhpOffice\\PhpWord\\Reader\\Word2007\\DocPropsCore' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Reader/Word2007/DocPropsCore.php',
    'PhpOffice\\PhpWord\\Reader\\Word2007\\DocPropsCustom' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Reader/Word2007/DocPropsCustom.php',
    'PhpOffice\\PhpWord\\Reader\\Word2007\\Document' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Reader/Word2007/Document.php',
    'PhpOffice\\PhpWord\\Reader\\Word2007\\Endnotes' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Reader/Word2007/Endnotes.php',
    'PhpOffice\\PhpWord\\Reader\\Word2007\\Footnotes' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Reader/Word2007/Footnotes.php',
    'PhpOffice\\PhpWord\\Reader\\Word2007\\Numbering' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Reader/Word2007/Numbering.php',
    'PhpOffice\\PhpWord\\Reader\\Word2007\\Settings' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Reader/Word2007/Settings.php',
    'PhpOffice\\PhpWord\\Reader\\Word2007\\Styles' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Reader/Word2007/Styles.php',
    'PhpOffice\\PhpWord\\Settings' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Settings.php',
    'PhpOffice\\PhpWord\\Shared\\AbstractEnum' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Shared/AbstractEnum.php',
    'PhpOffice\\PhpWord\\Shared\\Converter' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Shared/Converter.php',
    'PhpOffice\\PhpWord\\Shared\\Css' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Shared/Css.php',
    'PhpOffice\\PhpWord\\Shared\\Drawing' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Shared/Drawing.php',
    'PhpOffice\\PhpWord\\Shared\\Html' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Shared/Html.php',
    'PhpOffice\\PhpWord\\Shared\\Microsoft\\PasswordEncoder' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Shared/Microsoft/PasswordEncoder.php',
    'PhpOffice\\PhpWord\\Shared\\OLERead' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Shared/OLERead.php',
    'PhpOffice\\PhpWord\\Shared\\Text' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Shared/Text.php',
    'PhpOffice\\PhpWord\\Shared\\Validate' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Shared/Validate.php',
    'PhpOffice\\PhpWord\\Shared\\XMLReader' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Shared/XMLReader.php',
    'PhpOffice\\PhpWord\\Shared\\XMLWriter' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Shared/XMLWriter.php',
    'PhpOffice\\PhpWord\\Shared\\ZipArchive' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Shared/ZipArchive.php',
    'PhpOffice\\PhpWord\\SimpleType\\Border' => $vendorDir . '/phpoffice/phpword/src/PhpWord/SimpleType/Border.php',
    'PhpOffice\\PhpWord\\SimpleType\\DocProtect' => $vendorDir . '/phpoffice/phpword/src/PhpWord/SimpleType/DocProtect.php',
    'PhpOffice\\PhpWord\\SimpleType\\Jc' => $vendorDir . '/phpoffice/phpword/src/PhpWord/SimpleType/Jc.php',
    'PhpOffice\\PhpWord\\SimpleType\\JcTable' => $vendorDir . '/phpoffice/phpword/src/PhpWord/SimpleType/JcTable.php',
    'PhpOffice\\PhpWord\\SimpleType\\LineSpacingRule' => $vendorDir . '/phpoffice/phpword/src/PhpWord/SimpleType/LineSpacingRule.php',
    'PhpOffice\\PhpWord\\SimpleType\\NumberFormat' => $vendorDir . '/phpoffice/phpword/src/PhpWord/SimpleType/NumberFormat.php',
    'PhpOffice\\PhpWord\\SimpleType\\TblWidth' => $vendorDir . '/phpoffice/phpword/src/PhpWord/SimpleType/TblWidth.php',
    'PhpOffice\\PhpWord\\SimpleType\\TextAlignment' => $vendorDir . '/phpoffice/phpword/src/PhpWord/SimpleType/TextAlignment.php',
    'PhpOffice\\PhpWord\\SimpleType\\VerticalJc' => $vendorDir . '/phpoffice/phpword/src/PhpWord/SimpleType/VerticalJc.php',
    'PhpOffice\\PhpWord\\SimpleType\\Zoom' => $vendorDir . '/phpoffice/phpword/src/PhpWord/SimpleType/Zoom.php',
    'PhpOffice\\PhpWord\\Style' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Style.php',
    'PhpOffice\\PhpWord\\Style\\AbstractStyle' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Style/AbstractStyle.php',
    'PhpOffice\\PhpWord\\Style\\Border' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Style/Border.php',
    'PhpOffice\\PhpWord\\Style\\Cell' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Style/Cell.php',
    'PhpOffice\\PhpWord\\Style\\Chart' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Style/Chart.php',
    'PhpOffice\\PhpWord\\Style\\Extrusion' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Style/Extrusion.php',
    'PhpOffice\\PhpWord\\Style\\Fill' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Style/Fill.php',
    'PhpOffice\\PhpWord\\Style\\Font' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Style/Font.php',
    'PhpOffice\\PhpWord\\Style\\Frame' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Style/Frame.php',
    'PhpOffice\\PhpWord\\Style\\Image' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Style/Image.php',
    'PhpOffice\\PhpWord\\Style\\Indentation' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Style/Indentation.php',
    'PhpOffice\\PhpWord\\Style\\Language' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Style/Language.php',
    'PhpOffice\\PhpWord\\Style\\Line' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Style/Line.php',
    'PhpOffice\\PhpWord\\Style\\LineNumbering' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Style/LineNumbering.php',
    'PhpOffice\\PhpWord\\Style\\ListItem' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Style/ListItem.php',
    'PhpOffice\\PhpWord\\Style\\Numbering' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Style/Numbering.php',
    'PhpOffice\\PhpWord\\Style\\NumberingLevel' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Style/NumberingLevel.php',
    'PhpOffice\\PhpWord\\Style\\Outline' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Style/Outline.php',
    'PhpOffice\\PhpWord\\Style\\Paper' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Style/Paper.php',
    'PhpOffice\\PhpWord\\Style\\Paragraph' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Style/Paragraph.php',
    'PhpOffice\\PhpWord\\Style\\Row' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Style/Row.php',
    'PhpOffice\\PhpWord\\Style\\Section' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Style/Section.php',
    'PhpOffice\\PhpWord\\Style\\Shading' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Style/Shading.php',
    'PhpOffice\\PhpWord\\Style\\Shadow' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Style/Shadow.php',
    'PhpOffice\\PhpWord\\Style\\Shape' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Style/Shape.php',
    'PhpOffice\\PhpWord\\Style\\Spacing' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Style/Spacing.php',
    'PhpOffice\\PhpWord\\Style\\TOC' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Style/TOC.php',
    'PhpOffice\\PhpWord\\Style\\Tab' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Style/Tab.php',
    'PhpOffice\\PhpWord\\Style\\Table' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Style/Table.php',
    'PhpOffice\\PhpWord\\Style\\TablePosition' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Style/TablePosition.php',
    'PhpOffice\\PhpWord\\Style\\TextBox' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Style/TextBox.php',
    'PhpOffice\\PhpWord\\TemplateProcessor' => $vendorDir . '/phpoffice/phpword/src/PhpWord/TemplateProcessor.php',
    'PhpOffice\\PhpWord\\Writer\\AbstractWriter' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/AbstractWriter.php',
    'PhpOffice\\PhpWord\\Writer\\HTML' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/HTML.php',
    'PhpOffice\\PhpWord\\Writer\\HTML\\Element\\AbstractElement' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/HTML/Element/AbstractElement.php',
    'PhpOffice\\PhpWord\\Writer\\HTML\\Element\\Bookmark' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/HTML/Element/Bookmark.php',
    'PhpOffice\\PhpWord\\Writer\\HTML\\Element\\Container' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/HTML/Element/Container.php',
    'PhpOffice\\PhpWord\\Writer\\HTML\\Element\\Endnote' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/HTML/Element/Endnote.php',
    'PhpOffice\\PhpWord\\Writer\\HTML\\Element\\Footnote' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/HTML/Element/Footnote.php',
    'PhpOffice\\PhpWord\\Writer\\HTML\\Element\\Image' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/HTML/Element/Image.php',
    'PhpOffice\\PhpWord\\Writer\\HTML\\Element\\Link' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/HTML/Element/Link.php',
    'PhpOffice\\PhpWord\\Writer\\HTML\\Element\\ListItem' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/HTML/Element/ListItem.php',
    'PhpOffice\\PhpWord\\Writer\\HTML\\Element\\ListItemRun' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/HTML/Element/ListItemRun.php',
    'PhpOffice\\PhpWord\\Writer\\HTML\\Element\\PageBreak' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/HTML/Element/PageBreak.php',
    'PhpOffice\\PhpWord\\Writer\\HTML\\Element\\Table' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/HTML/Element/Table.php',
    'PhpOffice\\PhpWord\\Writer\\HTML\\Element\\Text' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/HTML/Element/Text.php',
    'PhpOffice\\PhpWord\\Writer\\HTML\\Element\\TextBreak' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/HTML/Element/TextBreak.php',
    'PhpOffice\\PhpWord\\Writer\\HTML\\Element\\TextRun' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/HTML/Element/TextRun.php',
    'PhpOffice\\PhpWord\\Writer\\HTML\\Element\\Title' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/HTML/Element/Title.php',
    'PhpOffice\\PhpWord\\Writer\\HTML\\Part\\AbstractPart' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/HTML/Part/AbstractPart.php',
    'PhpOffice\\PhpWord\\Writer\\HTML\\Part\\Body' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/HTML/Part/Body.php',
    'PhpOffice\\PhpWord\\Writer\\HTML\\Part\\Head' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/HTML/Part/Head.php',
    'PhpOffice\\PhpWord\\Writer\\HTML\\Style\\AbstractStyle' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/HTML/Style/AbstractStyle.php',
    'PhpOffice\\PhpWord\\Writer\\HTML\\Style\\Font' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/HTML/Style/Font.php',
    'PhpOffice\\PhpWord\\Writer\\HTML\\Style\\Generic' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/HTML/Style/Generic.php',
    'PhpOffice\\PhpWord\\Writer\\HTML\\Style\\Image' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/HTML/Style/Image.php',
    'PhpOffice\\PhpWord\\Writer\\HTML\\Style\\Paragraph' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/HTML/Style/Paragraph.php',
    'PhpOffice\\PhpWord\\Writer\\HTML\\Style\\Table' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/HTML/Style/Table.php',
    'PhpOffice\\PhpWord\\Writer\\ODText' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/ODText.php',
    'PhpOffice\\PhpWord\\Writer\\ODText\\Element\\AbstractElement' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/ODText/Element/AbstractElement.php',
    'PhpOffice\\PhpWord\\Writer\\ODText\\Element\\Container' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/ODText/Element/Container.php',
    'PhpOffice\\PhpWord\\Writer\\ODText\\Element\\Field' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/ODText/Element/Field.php',
    'PhpOffice\\PhpWord\\Writer\\ODText\\Element\\Formula' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/ODText/Element/Formula.php',
    'PhpOffice\\PhpWord\\Writer\\ODText\\Element\\Image' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/ODText/Element/Image.php',
    'PhpOffice\\PhpWord\\Writer\\ODText\\Element\\Link' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/ODText/Element/Link.php',
    'PhpOffice\\PhpWord\\Writer\\ODText\\Element\\PageBreak' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/ODText/Element/PageBreak.php',
    'PhpOffice\\PhpWord\\Writer\\ODText\\Element\\Table' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/ODText/Element/Table.php',
    'PhpOffice\\PhpWord\\Writer\\ODText\\Element\\Text' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/ODText/Element/Text.php',
    'PhpOffice\\PhpWord\\Writer\\ODText\\Element\\TextBreak' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/ODText/Element/TextBreak.php',
    'PhpOffice\\PhpWord\\Writer\\ODText\\Element\\TextRun' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/ODText/Element/TextRun.php',
    'PhpOffice\\PhpWord\\Writer\\ODText\\Element\\Title' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/ODText/Element/Title.php',
    'PhpOffice\\PhpWord\\Writer\\ODText\\Part\\AbstractPart' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/ODText/Part/AbstractPart.php',
    'PhpOffice\\PhpWord\\Writer\\ODText\\Part\\Content' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/ODText/Part/Content.php',
    'PhpOffice\\PhpWord\\Writer\\ODText\\Part\\Manifest' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/ODText/Part/Manifest.php',
    'PhpOffice\\PhpWord\\Writer\\ODText\\Part\\Meta' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/ODText/Part/Meta.php',
    'PhpOffice\\PhpWord\\Writer\\ODText\\Part\\Mimetype' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/ODText/Part/Mimetype.php',
    'PhpOffice\\PhpWord\\Writer\\ODText\\Part\\Styles' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/ODText/Part/Styles.php',
    'PhpOffice\\PhpWord\\Writer\\ODText\\Style\\AbstractStyle' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/ODText/Style/AbstractStyle.php',
    'PhpOffice\\PhpWord\\Writer\\ODText\\Style\\Font' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/ODText/Style/Font.php',
    'PhpOffice\\PhpWord\\Writer\\ODText\\Style\\Image' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/ODText/Style/Image.php',
    'PhpOffice\\PhpWord\\Writer\\ODText\\Style\\Paragraph' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/ODText/Style/Paragraph.php',
    'PhpOffice\\PhpWord\\Writer\\ODText\\Style\\Section' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/ODText/Style/Section.php',
    'PhpOffice\\PhpWord\\Writer\\ODText\\Style\\Table' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/ODText/Style/Table.php',
    'PhpOffice\\PhpWord\\Writer\\PDF' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/PDF.php',
    'PhpOffice\\PhpWord\\Writer\\PDF\\AbstractRenderer' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/PDF/AbstractRenderer.php',
    'PhpOffice\\PhpWord\\Writer\\PDF\\DomPDF' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/PDF/DomPDF.php',
    'PhpOffice\\PhpWord\\Writer\\PDF\\MPDF' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/PDF/MPDF.php',
    'PhpOffice\\PhpWord\\Writer\\PDF\\TCPDF' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/PDF/TCPDF.php',
    'PhpOffice\\PhpWord\\Writer\\RTF' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/RTF.php',
    'PhpOffice\\PhpWord\\Writer\\RTF\\Element\\AbstractElement' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/RTF/Element/AbstractElement.php',
    'PhpOffice\\PhpWord\\Writer\\RTF\\Element\\Container' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/RTF/Element/Container.php',
    'PhpOffice\\PhpWord\\Writer\\RTF\\Element\\Field' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/RTF/Element/Field.php',
    'PhpOffice\\PhpWord\\Writer\\RTF\\Element\\Image' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/RTF/Element/Image.php',
    'PhpOffice\\PhpWord\\Writer\\RTF\\Element\\Link' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/RTF/Element/Link.php',
    'PhpOffice\\PhpWord\\Writer\\RTF\\Element\\ListItem' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/RTF/Element/ListItem.php',
    'PhpOffice\\PhpWord\\Writer\\RTF\\Element\\PageBreak' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/RTF/Element/PageBreak.php',
    'PhpOffice\\PhpWord\\Writer\\RTF\\Element\\Table' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/RTF/Element/Table.php',
    'PhpOffice\\PhpWord\\Writer\\RTF\\Element\\Text' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/RTF/Element/Text.php',
    'PhpOffice\\PhpWord\\Writer\\RTF\\Element\\TextBreak' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/RTF/Element/TextBreak.php',
    'PhpOffice\\PhpWord\\Writer\\RTF\\Element\\TextRun' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/RTF/Element/TextRun.php',
    'PhpOffice\\PhpWord\\Writer\\RTF\\Element\\Title' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/RTF/Element/Title.php',
    'PhpOffice\\PhpWord\\Writer\\RTF\\Part\\AbstractPart' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/RTF/Part/AbstractPart.php',
    'PhpOffice\\PhpWord\\Writer\\RTF\\Part\\Document' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/RTF/Part/Document.php',
    'PhpOffice\\PhpWord\\Writer\\RTF\\Part\\Header' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/RTF/Part/Header.php',
    'PhpOffice\\PhpWord\\Writer\\RTF\\Style\\AbstractStyle' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/RTF/Style/AbstractStyle.php',
    'PhpOffice\\PhpWord\\Writer\\RTF\\Style\\Border' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/RTF/Style/Border.php',
    'PhpOffice\\PhpWord\\Writer\\RTF\\Style\\Font' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/RTF/Style/Font.php',
    'PhpOffice\\PhpWord\\Writer\\RTF\\Style\\Indentation' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/RTF/Style/Indentation.php',
    'PhpOffice\\PhpWord\\Writer\\RTF\\Style\\Paragraph' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/RTF/Style/Paragraph.php',
    'PhpOffice\\PhpWord\\Writer\\RTF\\Style\\Section' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/RTF/Style/Section.php',
    'PhpOffice\\PhpWord\\Writer\\RTF\\Style\\Tab' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/RTF/Style/Tab.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Element\\AbstractElement' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Element/AbstractElement.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Element\\Bookmark' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Element/Bookmark.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Element\\Chart' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Element/Chart.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Element\\CheckBox' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Element/CheckBox.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Element\\Container' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Element/Container.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Element\\Endnote' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Element/Endnote.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Element\\Field' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Element/Field.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Element\\Footnote' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Element/Footnote.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Element\\FormField' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Element/FormField.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Element\\Formula' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Element/Formula.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Element\\Image' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Element/Image.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Element\\Line' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Element/Line.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Element\\Link' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Element/Link.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Element\\ListItem' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Element/ListItem.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Element\\ListItemRun' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Element/ListItemRun.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Element\\OLEObject' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Element/OLEObject.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Element\\PageBreak' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Element/PageBreak.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Element\\ParagraphAlignment' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Element/ParagraphAlignment.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Element\\PreserveText' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Element/PreserveText.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Element\\SDT' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Element/SDT.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Element\\Shape' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Element/Shape.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Element\\TOC' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Element/TOC.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Element\\Table' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Element/Table.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Element\\TableAlignment' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Element/TableAlignment.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Element\\Text' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Element/Text.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Element\\TextBox' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Element/TextBox.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Element\\TextBreak' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Element/TextBreak.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Element\\TextRun' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Element/TextRun.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Element\\Title' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Element/Title.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Part\\AbstractPart' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Part/AbstractPart.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Part\\Chart' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Part/Chart.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Part\\Comments' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Part/Comments.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Part\\ContentTypes' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Part/ContentTypes.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Part\\DocPropsApp' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Part/DocPropsApp.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Part\\DocPropsCore' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Part/DocPropsCore.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Part\\DocPropsCustom' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Part/DocPropsCustom.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Part\\Document' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Part/Document.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Part\\Endnotes' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Part/Endnotes.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Part\\FontTable' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Part/FontTable.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Part\\Footer' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Part/Footer.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Part\\Footnotes' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Part/Footnotes.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Part\\Header' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Part/Header.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Part\\Numbering' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Part/Numbering.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Part\\Rels' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Part/Rels.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Part\\RelsDocument' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Part/RelsDocument.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Part\\RelsPart' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Part/RelsPart.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Part\\Settings' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Part/Settings.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Part\\Styles' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Part/Styles.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Part\\Theme' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Part/Theme.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Part\\WebSettings' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Part/WebSettings.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Style\\AbstractStyle' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Style/AbstractStyle.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Style\\Cell' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Style/Cell.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Style\\Extrusion' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Style/Extrusion.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Style\\Fill' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Style/Fill.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Style\\Font' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Style/Font.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Style\\Frame' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Style/Frame.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Style\\Image' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Style/Image.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Style\\Indentation' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Style/Indentation.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Style\\Line' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Style/Line.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Style\\LineNumbering' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Style/LineNumbering.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Style\\MarginBorder' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Style/MarginBorder.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Style\\Outline' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Style/Outline.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Style\\Paragraph' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Style/Paragraph.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Style\\Row' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Style/Row.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Style\\Section' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Style/Section.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Style\\Shading' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Style/Shading.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Style\\Shadow' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Style/Shadow.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Style\\Shape' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Style/Shape.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Style\\Spacing' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Style/Spacing.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Style\\Tab' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Style/Tab.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Style\\Table' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Style/Table.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Style\\TablePosition' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Style/TablePosition.php',
    'PhpOffice\\PhpWord\\Writer\\Word2007\\Style\\TextBox' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/Word2007/Style/TextBox.php',
    'PhpOffice\\PhpWord\\Writer\\WriterInterface' => $vendorDir . '/phpoffice/phpword/src/PhpWord/Writer/WriterInterface.php',
    'PhpParser\\Builder' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder.php',
    'PhpParser\\BuilderFactory' => $vendorDir . '/nikic/php-parser/lib/PhpParser/BuilderFactory.php',
    'PhpParser\\BuilderHelpers' => $vendorDir . '/nikic/php-parser/lib/PhpParser/BuilderHelpers.php',
    'PhpParser\\Builder\\ClassConst' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/ClassConst.php',
    'PhpParser\\Builder\\Class_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Class_.php',
    'PhpParser\\Builder\\Declaration' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Declaration.php',
    'PhpParser\\Builder\\EnumCase' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/EnumCase.php',
    'PhpParser\\Builder\\Enum_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Enum_.php',
    'PhpParser\\Builder\\FunctionLike' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/FunctionLike.php',
    'PhpParser\\Builder\\Function_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Function_.php',
    'PhpParser\\Builder\\Interface_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Interface_.php',
    'PhpParser\\Builder\\Method' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Method.php',
    'PhpParser\\Builder\\Namespace_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Namespace_.php',
    'PhpParser\\Builder\\Param' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Param.php',
    'PhpParser\\Builder\\Property' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Property.php',
    'PhpParser\\Builder\\TraitUse' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/TraitUse.php',
    'PhpParser\\Builder\\TraitUseAdaptation' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/TraitUseAdaptation.php',
    'PhpParser\\Builder\\Trait_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Trait_.php',
    'PhpParser\\Builder\\Use_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Builder/Use_.php',
    'PhpParser\\Comment' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Comment.php',
    'PhpParser\\Comment\\Doc' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Comment/Doc.php',
    'PhpParser\\ConstExprEvaluationException' => $vendorDir . '/nikic/php-parser/lib/PhpParser/ConstExprEvaluationException.php',
    'PhpParser\\ConstExprEvaluator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/ConstExprEvaluator.php',
    'PhpParser\\Error' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Error.php',
    'PhpParser\\ErrorHandler' => $vendorDir . '/nikic/php-parser/lib/PhpParser/ErrorHandler.php',
    'PhpParser\\ErrorHandler\\Collecting' => $vendorDir . '/nikic/php-parser/lib/PhpParser/ErrorHandler/Collecting.php',
    'PhpParser\\ErrorHandler\\Throwing' => $vendorDir . '/nikic/php-parser/lib/PhpParser/ErrorHandler/Throwing.php',
    'PhpParser\\Internal\\DiffElem' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Internal/DiffElem.php',
    'PhpParser\\Internal\\Differ' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Internal/Differ.php',
    'PhpParser\\Internal\\PrintableNewAnonClassNode' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Internal/PrintableNewAnonClassNode.php',
    'PhpParser\\Internal\\TokenPolyfill' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Internal/TokenPolyfill.php',
    'PhpParser\\Internal\\TokenStream' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Internal/TokenStream.php',
    'PhpParser\\JsonDecoder' => $vendorDir . '/nikic/php-parser/lib/PhpParser/JsonDecoder.php',
    'PhpParser\\Lexer' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer.php',
    'PhpParser\\Lexer\\Emulative' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/Emulative.php',
    'PhpParser\\Lexer\\TokenEmulator\\AsymmetricVisibilityTokenEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/AsymmetricVisibilityTokenEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\AttributeEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/AttributeEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\EnumTokenEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/EnumTokenEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\ExplicitOctalEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/ExplicitOctalEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\KeywordEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/KeywordEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\MatchTokenEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/MatchTokenEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\NullsafeTokenEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/NullsafeTokenEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\PropertyTokenEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/PropertyTokenEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\ReadonlyFunctionTokenEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/ReadonlyFunctionTokenEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\ReadonlyTokenEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/ReadonlyTokenEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\ReverseEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/ReverseEmulator.php',
    'PhpParser\\Lexer\\TokenEmulator\\TokenEmulator' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Lexer/TokenEmulator/TokenEmulator.php',
    'PhpParser\\Modifiers' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Modifiers.php',
    'PhpParser\\NameContext' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NameContext.php',
    'PhpParser\\Node' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node.php',
    'PhpParser\\NodeAbstract' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeAbstract.php',
    'PhpParser\\NodeDumper' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeDumper.php',
    'PhpParser\\NodeFinder' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeFinder.php',
    'PhpParser\\NodeTraverser' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeTraverser.php',
    'PhpParser\\NodeTraverserInterface' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeTraverserInterface.php',
    'PhpParser\\NodeVisitor' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeVisitor.php',
    'PhpParser\\NodeVisitorAbstract' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeVisitorAbstract.php',
    'PhpParser\\NodeVisitor\\CloningVisitor' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeVisitor/CloningVisitor.php',
    'PhpParser\\NodeVisitor\\CommentAnnotatingVisitor' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeVisitor/CommentAnnotatingVisitor.php',
    'PhpParser\\NodeVisitor\\FindingVisitor' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeVisitor/FindingVisitor.php',
    'PhpParser\\NodeVisitor\\FirstFindingVisitor' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeVisitor/FirstFindingVisitor.php',
    'PhpParser\\NodeVisitor\\NameResolver' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeVisitor/NameResolver.php',
    'PhpParser\\NodeVisitor\\NodeConnectingVisitor' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeVisitor/NodeConnectingVisitor.php',
    'PhpParser\\NodeVisitor\\ParentConnectingVisitor' => $vendorDir . '/nikic/php-parser/lib/PhpParser/NodeVisitor/ParentConnectingVisitor.php',
    'PhpParser\\Node\\Arg' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Arg.php',
    'PhpParser\\Node\\ArrayItem' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/ArrayItem.php',
    'PhpParser\\Node\\Attribute' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Attribute.php',
    'PhpParser\\Node\\AttributeGroup' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/AttributeGroup.php',
    'PhpParser\\Node\\ClosureUse' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/ClosureUse.php',
    'PhpParser\\Node\\ComplexType' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/ComplexType.php',
    'PhpParser\\Node\\Const_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Const_.php',
    'PhpParser\\Node\\DeclareItem' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/DeclareItem.php',
    'PhpParser\\Node\\Expr' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr.php',
    'PhpParser\\Node\\Expr\\ArrayDimFetch' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/ArrayDimFetch.php',
    'PhpParser\\Node\\Expr\\ArrayItem' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/ArrayItem.php',
    'PhpParser\\Node\\Expr\\Array_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Array_.php',
    'PhpParser\\Node\\Expr\\ArrowFunction' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/ArrowFunction.php',
    'PhpParser\\Node\\Expr\\Assign' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Assign.php',
    'PhpParser\\Node\\Expr\\AssignOp' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp.php',
    'PhpParser\\Node\\Expr\\AssignOp\\BitwiseAnd' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/BitwiseAnd.php',
    'PhpParser\\Node\\Expr\\AssignOp\\BitwiseOr' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/BitwiseOr.php',
    'PhpParser\\Node\\Expr\\AssignOp\\BitwiseXor' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/BitwiseXor.php',
    'PhpParser\\Node\\Expr\\AssignOp\\Coalesce' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/Coalesce.php',
    'PhpParser\\Node\\Expr\\AssignOp\\Concat' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/Concat.php',
    'PhpParser\\Node\\Expr\\AssignOp\\Div' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/Div.php',
    'PhpParser\\Node\\Expr\\AssignOp\\Minus' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/Minus.php',
    'PhpParser\\Node\\Expr\\AssignOp\\Mod' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/Mod.php',
    'PhpParser\\Node\\Expr\\AssignOp\\Mul' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/Mul.php',
    'PhpParser\\Node\\Expr\\AssignOp\\Plus' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/Plus.php',
    'PhpParser\\Node\\Expr\\AssignOp\\Pow' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/Pow.php',
    'PhpParser\\Node\\Expr\\AssignOp\\ShiftLeft' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/ShiftLeft.php',
    'PhpParser\\Node\\Expr\\AssignOp\\ShiftRight' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignOp/ShiftRight.php',
    'PhpParser\\Node\\Expr\\AssignRef' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/AssignRef.php',
    'PhpParser\\Node\\Expr\\BinaryOp' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\BitwiseAnd' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/BitwiseAnd.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\BitwiseOr' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/BitwiseOr.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\BitwiseXor' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/BitwiseXor.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\BooleanAnd' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/BooleanAnd.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\BooleanOr' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/BooleanOr.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Coalesce' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Coalesce.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Concat' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Concat.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Div' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Div.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Equal' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Equal.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Greater' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Greater.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\GreaterOrEqual' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/GreaterOrEqual.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Identical' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Identical.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\LogicalAnd' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/LogicalAnd.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\LogicalOr' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/LogicalOr.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\LogicalXor' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/LogicalXor.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Minus' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Minus.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Mod' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Mod.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Mul' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Mul.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\NotEqual' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/NotEqual.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\NotIdentical' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/NotIdentical.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Plus' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Plus.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Pow' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Pow.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\ShiftLeft' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/ShiftLeft.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\ShiftRight' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/ShiftRight.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Smaller' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Smaller.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\SmallerOrEqual' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/SmallerOrEqual.php',
    'PhpParser\\Node\\Expr\\BinaryOp\\Spaceship' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BinaryOp/Spaceship.php',
    'PhpParser\\Node\\Expr\\BitwiseNot' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BitwiseNot.php',
    'PhpParser\\Node\\Expr\\BooleanNot' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/BooleanNot.php',
    'PhpParser\\Node\\Expr\\CallLike' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/CallLike.php',
    'PhpParser\\Node\\Expr\\Cast' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Cast.php',
    'PhpParser\\Node\\Expr\\Cast\\Array_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Cast/Array_.php',
    'PhpParser\\Node\\Expr\\Cast\\Bool_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Cast/Bool_.php',
    'PhpParser\\Node\\Expr\\Cast\\Double' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Cast/Double.php',
    'PhpParser\\Node\\Expr\\Cast\\Int_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Cast/Int_.php',
    'PhpParser\\Node\\Expr\\Cast\\Object_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Cast/Object_.php',
    'PhpParser\\Node\\Expr\\Cast\\String_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Cast/String_.php',
    'PhpParser\\Node\\Expr\\Cast\\Unset_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Cast/Unset_.php',
    'PhpParser\\Node\\Expr\\ClassConstFetch' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/ClassConstFetch.php',
    'PhpParser\\Node\\Expr\\Clone_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Clone_.php',
    'PhpParser\\Node\\Expr\\Closure' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Closure.php',
    'PhpParser\\Node\\Expr\\ClosureUse' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/ClosureUse.php',
    'PhpParser\\Node\\Expr\\ConstFetch' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/ConstFetch.php',
    'PhpParser\\Node\\Expr\\Empty_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Empty_.php',
    'PhpParser\\Node\\Expr\\Error' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Error.php',
    'PhpParser\\Node\\Expr\\ErrorSuppress' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/ErrorSuppress.php',
    'PhpParser\\Node\\Expr\\Eval_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Eval_.php',
    'PhpParser\\Node\\Expr\\Exit_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Exit_.php',
    'PhpParser\\Node\\Expr\\FuncCall' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/FuncCall.php',
    'PhpParser\\Node\\Expr\\Include_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Include_.php',
    'PhpParser\\Node\\Expr\\Instanceof_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Instanceof_.php',
    'PhpParser\\Node\\Expr\\Isset_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Isset_.php',
    'PhpParser\\Node\\Expr\\List_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/List_.php',
    'PhpParser\\Node\\Expr\\Match_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Match_.php',
    'PhpParser\\Node\\Expr\\MethodCall' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/MethodCall.php',
    'PhpParser\\Node\\Expr\\New_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/New_.php',
    'PhpParser\\Node\\Expr\\NullsafeMethodCall' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/NullsafeMethodCall.php',
    'PhpParser\\Node\\Expr\\NullsafePropertyFetch' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/NullsafePropertyFetch.php',
    'PhpParser\\Node\\Expr\\PostDec' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/PostDec.php',
    'PhpParser\\Node\\Expr\\PostInc' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/PostInc.php',
    'PhpParser\\Node\\Expr\\PreDec' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/PreDec.php',
    'PhpParser\\Node\\Expr\\PreInc' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/PreInc.php',
    'PhpParser\\Node\\Expr\\Print_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Print_.php',
    'PhpParser\\Node\\Expr\\PropertyFetch' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/PropertyFetch.php',
    'PhpParser\\Node\\Expr\\ShellExec' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/ShellExec.php',
    'PhpParser\\Node\\Expr\\StaticCall' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/StaticCall.php',
    'PhpParser\\Node\\Expr\\StaticPropertyFetch' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/StaticPropertyFetch.php',
    'PhpParser\\Node\\Expr\\Ternary' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Ternary.php',
    'PhpParser\\Node\\Expr\\Throw_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Throw_.php',
    'PhpParser\\Node\\Expr\\UnaryMinus' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/UnaryMinus.php',
    'PhpParser\\Node\\Expr\\UnaryPlus' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/UnaryPlus.php',
    'PhpParser\\Node\\Expr\\Variable' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Variable.php',
    'PhpParser\\Node\\Expr\\YieldFrom' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/YieldFrom.php',
    'PhpParser\\Node\\Expr\\Yield_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Expr/Yield_.php',
    'PhpParser\\Node\\FunctionLike' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/FunctionLike.php',
    'PhpParser\\Node\\Identifier' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Identifier.php',
    'PhpParser\\Node\\InterpolatedStringPart' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/InterpolatedStringPart.php',
    'PhpParser\\Node\\IntersectionType' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/IntersectionType.php',
    'PhpParser\\Node\\MatchArm' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/MatchArm.php',
    'PhpParser\\Node\\Name' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Name.php',
    'PhpParser\\Node\\Name\\FullyQualified' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Name/FullyQualified.php',
    'PhpParser\\Node\\Name\\Relative' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Name/Relative.php',
    'PhpParser\\Node\\NullableType' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/NullableType.php',
    'PhpParser\\Node\\Param' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Param.php',
    'PhpParser\\Node\\PropertyHook' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/PropertyHook.php',
    'PhpParser\\Node\\PropertyItem' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/PropertyItem.php',
    'PhpParser\\Node\\Scalar' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar.php',
    'PhpParser\\Node\\Scalar\\DNumber' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/DNumber.php',
    'PhpParser\\Node\\Scalar\\Encapsed' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/Encapsed.php',
    'PhpParser\\Node\\Scalar\\EncapsedStringPart' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/EncapsedStringPart.php',
    'PhpParser\\Node\\Scalar\\Float_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/Float_.php',
    'PhpParser\\Node\\Scalar\\Int_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/Int_.php',
    'PhpParser\\Node\\Scalar\\InterpolatedString' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/InterpolatedString.php',
    'PhpParser\\Node\\Scalar\\LNumber' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/LNumber.php',
    'PhpParser\\Node\\Scalar\\MagicConst' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/MagicConst.php',
    'PhpParser\\Node\\Scalar\\MagicConst\\Class_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/MagicConst/Class_.php',
    'PhpParser\\Node\\Scalar\\MagicConst\\Dir' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/MagicConst/Dir.php',
    'PhpParser\\Node\\Scalar\\MagicConst\\File' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/MagicConst/File.php',
    'PhpParser\\Node\\Scalar\\MagicConst\\Function_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/MagicConst/Function_.php',
    'PhpParser\\Node\\Scalar\\MagicConst\\Line' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/MagicConst/Line.php',
    'PhpParser\\Node\\Scalar\\MagicConst\\Method' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/MagicConst/Method.php',
    'PhpParser\\Node\\Scalar\\MagicConst\\Namespace_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/MagicConst/Namespace_.php',
    'PhpParser\\Node\\Scalar\\MagicConst\\Property' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/MagicConst/Property.php',
    'PhpParser\\Node\\Scalar\\MagicConst\\Trait_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/MagicConst/Trait_.php',
    'PhpParser\\Node\\Scalar\\String_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Scalar/String_.php',
    'PhpParser\\Node\\StaticVar' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/StaticVar.php',
    'PhpParser\\Node\\Stmt' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt.php',
    'PhpParser\\Node\\Stmt\\Block' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Block.php',
    'PhpParser\\Node\\Stmt\\Break_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Break_.php',
    'PhpParser\\Node\\Stmt\\Case_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Case_.php',
    'PhpParser\\Node\\Stmt\\Catch_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Catch_.php',
    'PhpParser\\Node\\Stmt\\ClassConst' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/ClassConst.php',
    'PhpParser\\Node\\Stmt\\ClassLike' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/ClassLike.php',
    'PhpParser\\Node\\Stmt\\ClassMethod' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/ClassMethod.php',
    'PhpParser\\Node\\Stmt\\Class_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Class_.php',
    'PhpParser\\Node\\Stmt\\Const_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Const_.php',
    'PhpParser\\Node\\Stmt\\Continue_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Continue_.php',
    'PhpParser\\Node\\Stmt\\DeclareDeclare' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/DeclareDeclare.php',
    'PhpParser\\Node\\Stmt\\Declare_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Declare_.php',
    'PhpParser\\Node\\Stmt\\Do_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Do_.php',
    'PhpParser\\Node\\Stmt\\Echo_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Echo_.php',
    'PhpParser\\Node\\Stmt\\ElseIf_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/ElseIf_.php',
    'PhpParser\\Node\\Stmt\\Else_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Else_.php',
    'PhpParser\\Node\\Stmt\\EnumCase' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/EnumCase.php',
    'PhpParser\\Node\\Stmt\\Enum_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Enum_.php',
    'PhpParser\\Node\\Stmt\\Expression' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Expression.php',
    'PhpParser\\Node\\Stmt\\Finally_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Finally_.php',
    'PhpParser\\Node\\Stmt\\For_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/For_.php',
    'PhpParser\\Node\\Stmt\\Foreach_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Foreach_.php',
    'PhpParser\\Node\\Stmt\\Function_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Function_.php',
    'PhpParser\\Node\\Stmt\\Global_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Global_.php',
    'PhpParser\\Node\\Stmt\\Goto_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Goto_.php',
    'PhpParser\\Node\\Stmt\\GroupUse' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/GroupUse.php',
    'PhpParser\\Node\\Stmt\\HaltCompiler' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/HaltCompiler.php',
    'PhpParser\\Node\\Stmt\\If_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/If_.php',
    'PhpParser\\Node\\Stmt\\InlineHTML' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/InlineHTML.php',
    'PhpParser\\Node\\Stmt\\Interface_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Interface_.php',
    'PhpParser\\Node\\Stmt\\Label' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Label.php',
    'PhpParser\\Node\\Stmt\\Namespace_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Namespace_.php',
    'PhpParser\\Node\\Stmt\\Nop' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Nop.php',
    'PhpParser\\Node\\Stmt\\Property' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Property.php',
    'PhpParser\\Node\\Stmt\\PropertyProperty' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/PropertyProperty.php',
    'PhpParser\\Node\\Stmt\\Return_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Return_.php',
    'PhpParser\\Node\\Stmt\\StaticVar' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/StaticVar.php',
    'PhpParser\\Node\\Stmt\\Static_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Static_.php',
    'PhpParser\\Node\\Stmt\\Switch_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Switch_.php',
    'PhpParser\\Node\\Stmt\\TraitUse' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/TraitUse.php',
    'PhpParser\\Node\\Stmt\\TraitUseAdaptation' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/TraitUseAdaptation.php',
    'PhpParser\\Node\\Stmt\\TraitUseAdaptation\\Alias' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/TraitUseAdaptation/Alias.php',
    'PhpParser\\Node\\Stmt\\TraitUseAdaptation\\Precedence' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/TraitUseAdaptation/Precedence.php',
    'PhpParser\\Node\\Stmt\\Trait_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Trait_.php',
    'PhpParser\\Node\\Stmt\\TryCatch' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/TryCatch.php',
    'PhpParser\\Node\\Stmt\\Unset_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Unset_.php',
    'PhpParser\\Node\\Stmt\\UseUse' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/UseUse.php',
    'PhpParser\\Node\\Stmt\\Use_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/Use_.php',
    'PhpParser\\Node\\Stmt\\While_' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/Stmt/While_.php',
    'PhpParser\\Node\\UnionType' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/UnionType.php',
    'PhpParser\\Node\\UseItem' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/UseItem.php',
    'PhpParser\\Node\\VarLikeIdentifier' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/VarLikeIdentifier.php',
    'PhpParser\\Node\\VariadicPlaceholder' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Node/VariadicPlaceholder.php',
    'PhpParser\\Parser' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Parser.php',
    'PhpParser\\ParserAbstract' => $vendorDir . '/nikic/php-parser/lib/PhpParser/ParserAbstract.php',
    'PhpParser\\ParserFactory' => $vendorDir . '/nikic/php-parser/lib/PhpParser/ParserFactory.php',
    'PhpParser\\Parser\\Php7' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Parser/Php7.php',
    'PhpParser\\Parser\\Php8' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Parser/Php8.php',
    'PhpParser\\PhpVersion' => $vendorDir . '/nikic/php-parser/lib/PhpParser/PhpVersion.php',
    'PhpParser\\PrettyPrinter' => $vendorDir . '/nikic/php-parser/lib/PhpParser/PrettyPrinter.php',
    'PhpParser\\PrettyPrinterAbstract' => $vendorDir . '/nikic/php-parser/lib/PhpParser/PrettyPrinterAbstract.php',
    'PhpParser\\PrettyPrinter\\Standard' => $vendorDir . '/nikic/php-parser/lib/PhpParser/PrettyPrinter/Standard.php',
    'PhpParser\\Token' => $vendorDir . '/nikic/php-parser/lib/PhpParser/Token.php',
    'PhpToken' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/PhpToken.php',
    'Plugin_AutoUpdates_Command' => $vendorDir . '/wp-cli/extension-command/src/Plugin_AutoUpdates_Command.php',
    'Plugin_Command' => $vendorDir . '/wp-cli/extension-command/src/Plugin_Command.php',
    'Plugin_Command_Namespace' => $vendorDir . '/wp-cli/checksum-command/src/Plugin_Command_Namespace.php',
    'Plugin_Language_Command' => $vendorDir . '/wp-cli/language-command/src/Plugin_Language_Command.php',
    'Post_Command' => $vendorDir . '/wp-cli/entity-command/src/Post_Command.php',
    'Post_Meta_Command' => $vendorDir . '/wp-cli/entity-command/src/Post_Meta_Command.php',
    'Post_Term_Command' => $vendorDir . '/wp-cli/entity-command/src/Post_Term_Command.php',
    'Post_Type_Command' => $vendorDir . '/wp-cli/entity-command/src/Post_Type_Command.php',
    'Psr\\Container\\ContainerExceptionInterface' => $vendorDir . '/psr/container/src/ContainerExceptionInterface.php',
    'Psr\\Container\\ContainerInterface' => $vendorDir . '/psr/container/src/ContainerInterface.php',
    'Psr\\Container\\NotFoundExceptionInterface' => $vendorDir . '/psr/container/src/NotFoundExceptionInterface.php',
    'Psr\\Http\\Message\\MessageInterface' => $vendorDir . '/psr/http-message/src/MessageInterface.php',
    'Psr\\Http\\Message\\RequestInterface' => $vendorDir . '/psr/http-message/src/RequestInterface.php',
    'Psr\\Http\\Message\\ResponseInterface' => $vendorDir . '/psr/http-message/src/ResponseInterface.php',
    'Psr\\Http\\Message\\ServerRequestInterface' => $vendorDir . '/psr/http-message/src/ServerRequestInterface.php',
    'Psr\\Http\\Message\\StreamInterface' => $vendorDir . '/psr/http-message/src/StreamInterface.php',
    'Psr\\Http\\Message\\UploadedFileInterface' => $vendorDir . '/psr/http-message/src/UploadedFileInterface.php',
    'Psr\\Http\\Message\\UriInterface' => $vendorDir . '/psr/http-message/src/UriInterface.php',
    'Psr\\Log\\AbstractLogger' => $vendorDir . '/psr/log/src/AbstractLogger.php',
    'Psr\\Log\\InvalidArgumentException' => $vendorDir . '/psr/log/src/InvalidArgumentException.php',
    'Psr\\Log\\LogLevel' => $vendorDir . '/psr/log/src/LogLevel.php',
    'Psr\\Log\\LoggerAwareInterface' => $vendorDir . '/psr/log/src/LoggerAwareInterface.php',
    'Psr\\Log\\LoggerAwareTrait' => $vendorDir . '/psr/log/src/LoggerAwareTrait.php',
    'Psr\\Log\\LoggerInterface' => $vendorDir . '/psr/log/src/LoggerInterface.php',
    'Psr\\Log\\LoggerTrait' => $vendorDir . '/psr/log/src/LoggerTrait.php',
    'Psr\\Log\\NullLogger' => $vendorDir . '/psr/log/src/NullLogger.php',
    'React\\Promise\\Deferred' => $vendorDir . '/react/promise/src/Deferred.php',
    'React\\Promise\\Exception\\CompositeException' => $vendorDir . '/react/promise/src/Exception/CompositeException.php',
    'React\\Promise\\Exception\\LengthException' => $vendorDir . '/react/promise/src/Exception/LengthException.php',
    'React\\Promise\\Internal\\CancellationQueue' => $vendorDir . '/react/promise/src/Internal/CancellationQueue.php',
    'React\\Promise\\Internal\\FulfilledPromise' => $vendorDir . '/react/promise/src/Internal/FulfilledPromise.php',
    'React\\Promise\\Internal\\RejectedPromise' => $vendorDir . '/react/promise/src/Internal/RejectedPromise.php',
    'React\\Promise\\Promise' => $vendorDir . '/react/promise/src/Promise.php',
    'React\\Promise\\PromiseInterface' => $vendorDir . '/react/promise/src/PromiseInterface.php',
    'ReturnTypeWillChange' => $vendorDir . '/symfony/polyfill-php81/Resources/stubs/ReturnTypeWillChange.php',
    'Rewrite_Command' => $vendorDir . '/wp-cli/rewrite-command/src/Rewrite_Command.php',
    'Role_Command' => $vendorDir . '/wp-cli/role-command/src/Role_Command.php',
    'Scaffold_Command' => $vendorDir . '/wp-cli/scaffold-command/src/Scaffold_Command.php',
    'Search_Replace_Command' => $vendorDir . '/wp-cli/search-replace-command/src/Search_Replace_Command.php',
    'SebastianBergmann\\CliParser\\AmbiguousOptionException' => $vendorDir . '/sebastian/cli-parser/src/exceptions/AmbiguousOptionException.php',
    'SebastianBergmann\\CliParser\\Exception' => $vendorDir . '/sebastian/cli-parser/src/exceptions/Exception.php',
    'SebastianBergmann\\CliParser\\OptionDoesNotAllowArgumentException' => $vendorDir . '/sebastian/cli-parser/src/exceptions/OptionDoesNotAllowArgumentException.php',
    'SebastianBergmann\\CliParser\\Parser' => $vendorDir . '/sebastian/cli-parser/src/Parser.php',
    'SebastianBergmann\\CliParser\\RequiredOptionArgumentMissingException' => $vendorDir . '/sebastian/cli-parser/src/exceptions/RequiredOptionArgumentMissingException.php',
    'SebastianBergmann\\CliParser\\UnknownOptionException' => $vendorDir . '/sebastian/cli-parser/src/exceptions/UnknownOptionException.php',
    'SebastianBergmann\\CodeCoverage\\BranchAndPathCoverageNotSupportedException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/BranchAndPathCoverageNotSupportedException.php',
    'SebastianBergmann\\CodeCoverage\\CodeCoverage' => $vendorDir . '/phpunit/php-code-coverage/src/CodeCoverage.php',
    'SebastianBergmann\\CodeCoverage\\DeadCodeDetectionNotSupportedException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/DeadCodeDetectionNotSupportedException.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\Driver' => $vendorDir . '/phpunit/php-code-coverage/src/Driver/Driver.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\PathExistsButIsNotDirectoryException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/PathExistsButIsNotDirectoryException.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\PcovDriver' => $vendorDir . '/phpunit/php-code-coverage/src/Driver/PcovDriver.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\PcovNotAvailableException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/PcovNotAvailableException.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\PhpdbgDriver' => $vendorDir . '/phpunit/php-code-coverage/src/Driver/PhpdbgDriver.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\PhpdbgNotAvailableException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/PhpdbgNotAvailableException.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\Selector' => $vendorDir . '/phpunit/php-code-coverage/src/Driver/Selector.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\WriteOperationFailedException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/WriteOperationFailedException.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\WrongXdebugVersionException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/WrongXdebugVersionException.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\Xdebug2Driver' => $vendorDir . '/phpunit/php-code-coverage/src/Driver/Xdebug2Driver.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\Xdebug2NotEnabledException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/Xdebug2NotEnabledException.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\Xdebug3Driver' => $vendorDir . '/phpunit/php-code-coverage/src/Driver/Xdebug3Driver.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\Xdebug3NotEnabledException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/Xdebug3NotEnabledException.php',
    'SebastianBergmann\\CodeCoverage\\Driver\\XdebugNotAvailableException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/XdebugNotAvailableException.php',
    'SebastianBergmann\\CodeCoverage\\Exception' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/Exception.php',
    'SebastianBergmann\\CodeCoverage\\Filter' => $vendorDir . '/phpunit/php-code-coverage/src/Filter.php',
    'SebastianBergmann\\CodeCoverage\\InvalidArgumentException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/InvalidArgumentException.php',
    'SebastianBergmann\\CodeCoverage\\NoCodeCoverageDriverAvailableException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/NoCodeCoverageDriverAvailableException.php',
    'SebastianBergmann\\CodeCoverage\\NoCodeCoverageDriverWithPathCoverageSupportAvailableException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/NoCodeCoverageDriverWithPathCoverageSupportAvailableException.php',
    'SebastianBergmann\\CodeCoverage\\Node\\AbstractNode' => $vendorDir . '/phpunit/php-code-coverage/src/Node/AbstractNode.php',
    'SebastianBergmann\\CodeCoverage\\Node\\Builder' => $vendorDir . '/phpunit/php-code-coverage/src/Node/Builder.php',
    'SebastianBergmann\\CodeCoverage\\Node\\CrapIndex' => $vendorDir . '/phpunit/php-code-coverage/src/Node/CrapIndex.php',
    'SebastianBergmann\\CodeCoverage\\Node\\Directory' => $vendorDir . '/phpunit/php-code-coverage/src/Node/Directory.php',
    'SebastianBergmann\\CodeCoverage\\Node\\File' => $vendorDir . '/phpunit/php-code-coverage/src/Node/File.php',
    'SebastianBergmann\\CodeCoverage\\Node\\Iterator' => $vendorDir . '/phpunit/php-code-coverage/src/Node/Iterator.php',
    'SebastianBergmann\\CodeCoverage\\ParserException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/ParserException.php',
    'SebastianBergmann\\CodeCoverage\\ProcessedCodeCoverageData' => $vendorDir . '/phpunit/php-code-coverage/src/ProcessedCodeCoverageData.php',
    'SebastianBergmann\\CodeCoverage\\RawCodeCoverageData' => $vendorDir . '/phpunit/php-code-coverage/src/RawCodeCoverageData.php',
    'SebastianBergmann\\CodeCoverage\\ReflectionException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/ReflectionException.php',
    'SebastianBergmann\\CodeCoverage\\ReportAlreadyFinalizedException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/ReportAlreadyFinalizedException.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Clover' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Clover.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Cobertura' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Cobertura.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Crap4j' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Crap4j.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Html\\Dashboard' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Html/Renderer/Dashboard.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Html\\Directory' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Html/Renderer/Directory.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Html\\Facade' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Html/Facade.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Html\\File' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Html/Renderer/File.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Html\\Renderer' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Html/Renderer.php',
    'SebastianBergmann\\CodeCoverage\\Report\\PHP' => $vendorDir . '/phpunit/php-code-coverage/src/Report/PHP.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Text' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Text.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\BuildInformation' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/BuildInformation.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Coverage' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Coverage.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Directory' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Directory.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Facade' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Facade.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\File' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/File.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Method' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Method.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Node' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Node.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Project' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Project.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Report' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Report.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Source' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Source.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Tests' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Tests.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Totals' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Totals.php',
    'SebastianBergmann\\CodeCoverage\\Report\\Xml\\Unit' => $vendorDir . '/phpunit/php-code-coverage/src/Report/Xml/Unit.php',
    'SebastianBergmann\\CodeCoverage\\StaticAnalysisCacheNotConfiguredException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/StaticAnalysisCacheNotConfiguredException.php',
    'SebastianBergmann\\CodeCoverage\\StaticAnalysis\\CacheWarmer' => $vendorDir . '/phpunit/php-code-coverage/src/StaticAnalysis/CacheWarmer.php',
    'SebastianBergmann\\CodeCoverage\\StaticAnalysis\\CachingFileAnalyser' => $vendorDir . '/phpunit/php-code-coverage/src/StaticAnalysis/CachingFileAnalyser.php',
    'SebastianBergmann\\CodeCoverage\\StaticAnalysis\\CodeUnitFindingVisitor' => $vendorDir . '/phpunit/php-code-coverage/src/StaticAnalysis/CodeUnitFindingVisitor.php',
    'SebastianBergmann\\CodeCoverage\\StaticAnalysis\\ExecutableLinesFindingVisitor' => $vendorDir . '/phpunit/php-code-coverage/src/StaticAnalysis/ExecutableLinesFindingVisitor.php',
    'SebastianBergmann\\CodeCoverage\\StaticAnalysis\\FileAnalyser' => $vendorDir . '/phpunit/php-code-coverage/src/StaticAnalysis/FileAnalyser.php',
    'SebastianBergmann\\CodeCoverage\\StaticAnalysis\\IgnoredLinesFindingVisitor' => $vendorDir . '/phpunit/php-code-coverage/src/StaticAnalysis/IgnoredLinesFindingVisitor.php',
    'SebastianBergmann\\CodeCoverage\\StaticAnalysis\\ParsingFileAnalyser' => $vendorDir . '/phpunit/php-code-coverage/src/StaticAnalysis/ParsingFileAnalyser.php',
    'SebastianBergmann\\CodeCoverage\\TestIdMissingException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/TestIdMissingException.php',
    'SebastianBergmann\\CodeCoverage\\UnintentionallyCoveredCodeException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/UnintentionallyCoveredCodeException.php',
    'SebastianBergmann\\CodeCoverage\\Util\\DirectoryCouldNotBeCreatedException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/DirectoryCouldNotBeCreatedException.php',
    'SebastianBergmann\\CodeCoverage\\Util\\Filesystem' => $vendorDir . '/phpunit/php-code-coverage/src/Util/Filesystem.php',
    'SebastianBergmann\\CodeCoverage\\Util\\Percentage' => $vendorDir . '/phpunit/php-code-coverage/src/Util/Percentage.php',
    'SebastianBergmann\\CodeCoverage\\Version' => $vendorDir . '/phpunit/php-code-coverage/src/Version.php',
    'SebastianBergmann\\CodeCoverage\\XmlException' => $vendorDir . '/phpunit/php-code-coverage/src/Exception/XmlException.php',
    'SebastianBergmann\\CodeUnitReverseLookup\\Wizard' => $vendorDir . '/sebastian/code-unit-reverse-lookup/src/Wizard.php',
    'SebastianBergmann\\CodeUnit\\ClassMethodUnit' => $vendorDir . '/sebastian/code-unit/src/ClassMethodUnit.php',
    'SebastianBergmann\\CodeUnit\\ClassUnit' => $vendorDir . '/sebastian/code-unit/src/ClassUnit.php',
    'SebastianBergmann\\CodeUnit\\CodeUnit' => $vendorDir . '/sebastian/code-unit/src/CodeUnit.php',
    'SebastianBergmann\\CodeUnit\\CodeUnitCollection' => $vendorDir . '/sebastian/code-unit/src/CodeUnitCollection.php',
    'SebastianBergmann\\CodeUnit\\CodeUnitCollectionIterator' => $vendorDir . '/sebastian/code-unit/src/CodeUnitCollectionIterator.php',
    'SebastianBergmann\\CodeUnit\\Exception' => $vendorDir . '/sebastian/code-unit/src/exceptions/Exception.php',
    'SebastianBergmann\\CodeUnit\\FunctionUnit' => $vendorDir . '/sebastian/code-unit/src/FunctionUnit.php',
    'SebastianBergmann\\CodeUnit\\InterfaceMethodUnit' => $vendorDir . '/sebastian/code-unit/src/InterfaceMethodUnit.php',
    'SebastianBergmann\\CodeUnit\\InterfaceUnit' => $vendorDir . '/sebastian/code-unit/src/InterfaceUnit.php',
    'SebastianBergmann\\CodeUnit\\InvalidCodeUnitException' => $vendorDir . '/sebastian/code-unit/src/exceptions/InvalidCodeUnitException.php',
    'SebastianBergmann\\CodeUnit\\Mapper' => $vendorDir . '/sebastian/code-unit/src/Mapper.php',
    'SebastianBergmann\\CodeUnit\\NoTraitException' => $vendorDir . '/sebastian/code-unit/src/exceptions/NoTraitException.php',
    'SebastianBergmann\\CodeUnit\\ReflectionException' => $vendorDir . '/sebastian/code-unit/src/exceptions/ReflectionException.php',
    'SebastianBergmann\\CodeUnit\\TraitMethodUnit' => $vendorDir . '/sebastian/code-unit/src/TraitMethodUnit.php',
    'SebastianBergmann\\CodeUnit\\TraitUnit' => $vendorDir . '/sebastian/code-unit/src/TraitUnit.php',
    'SebastianBergmann\\Comparator\\ArrayComparator' => $vendorDir . '/sebastian/comparator/src/ArrayComparator.php',
    'SebastianBergmann\\Comparator\\Comparator' => $vendorDir . '/sebastian/comparator/src/Comparator.php',
    'SebastianBergmann\\Comparator\\ComparisonFailure' => $vendorDir . '/sebastian/comparator/src/ComparisonFailure.php',
    'SebastianBergmann\\Comparator\\DOMNodeComparator' => $vendorDir . '/sebastian/comparator/src/DOMNodeComparator.php',
    'SebastianBergmann\\Comparator\\DateTimeComparator' => $vendorDir . '/sebastian/comparator/src/DateTimeComparator.php',
    'SebastianBergmann\\Comparator\\DoubleComparator' => $vendorDir . '/sebastian/comparator/src/DoubleComparator.php',
    'SebastianBergmann\\Comparator\\Exception' => $vendorDir . '/sebastian/comparator/src/exceptions/Exception.php',
    'SebastianBergmann\\Comparator\\ExceptionComparator' => $vendorDir . '/sebastian/comparator/src/ExceptionComparator.php',
    'SebastianBergmann\\Comparator\\Factory' => $vendorDir . '/sebastian/comparator/src/Factory.php',
    'SebastianBergmann\\Comparator\\MockObjectComparator' => $vendorDir . '/sebastian/comparator/src/MockObjectComparator.php',
    'SebastianBergmann\\Comparator\\NumericComparator' => $vendorDir . '/sebastian/comparator/src/NumericComparator.php',
    'SebastianBergmann\\Comparator\\ObjectComparator' => $vendorDir . '/sebastian/comparator/src/ObjectComparator.php',
    'SebastianBergmann\\Comparator\\ResourceComparator' => $vendorDir . '/sebastian/comparator/src/ResourceComparator.php',
    'SebastianBergmann\\Comparator\\RuntimeException' => $vendorDir . '/sebastian/comparator/src/exceptions/RuntimeException.php',
    'SebastianBergmann\\Comparator\\ScalarComparator' => $vendorDir . '/sebastian/comparator/src/ScalarComparator.php',
    'SebastianBergmann\\Comparator\\SplObjectStorageComparator' => $vendorDir . '/sebastian/comparator/src/SplObjectStorageComparator.php',
    'SebastianBergmann\\Comparator\\TypeComparator' => $vendorDir . '/sebastian/comparator/src/TypeComparator.php',
    'SebastianBergmann\\Complexity\\Calculator' => $vendorDir . '/sebastian/complexity/src/Calculator.php',
    'SebastianBergmann\\Complexity\\Complexity' => $vendorDir . '/sebastian/complexity/src/Complexity/Complexity.php',
    'SebastianBergmann\\Complexity\\ComplexityCalculatingVisitor' => $vendorDir . '/sebastian/complexity/src/Visitor/ComplexityCalculatingVisitor.php',
    'SebastianBergmann\\Complexity\\ComplexityCollection' => $vendorDir . '/sebastian/complexity/src/Complexity/ComplexityCollection.php',
    'SebastianBergmann\\Complexity\\ComplexityCollectionIterator' => $vendorDir . '/sebastian/complexity/src/Complexity/ComplexityCollectionIterator.php',
    'SebastianBergmann\\Complexity\\CyclomaticComplexityCalculatingVisitor' => $vendorDir . '/sebastian/complexity/src/Visitor/CyclomaticComplexityCalculatingVisitor.php',
    'SebastianBergmann\\Complexity\\Exception' => $vendorDir . '/sebastian/complexity/src/Exception/Exception.php',
    'SebastianBergmann\\Complexity\\RuntimeException' => $vendorDir . '/sebastian/complexity/src/Exception/RuntimeException.php',
    'SebastianBergmann\\Diff\\Chunk' => $vendorDir . '/sebastian/diff/src/Chunk.php',
    'SebastianBergmann\\Diff\\ConfigurationException' => $vendorDir . '/sebastian/diff/src/Exception/ConfigurationException.php',
    'SebastianBergmann\\Diff\\Diff' => $vendorDir . '/sebastian/diff/src/Diff.php',
    'SebastianBergmann\\Diff\\Differ' => $vendorDir . '/sebastian/diff/src/Differ.php',
    'SebastianBergmann\\Diff\\Exception' => $vendorDir . '/sebastian/diff/src/Exception/Exception.php',
    'SebastianBergmann\\Diff\\InvalidArgumentException' => $vendorDir . '/sebastian/diff/src/Exception/InvalidArgumentException.php',
    'SebastianBergmann\\Diff\\Line' => $vendorDir . '/sebastian/diff/src/Line.php',
    'SebastianBergmann\\Diff\\LongestCommonSubsequenceCalculator' => $vendorDir . '/sebastian/diff/src/LongestCommonSubsequenceCalculator.php',
    'SebastianBergmann\\Diff\\MemoryEfficientLongestCommonSubsequenceCalculator' => $vendorDir . '/sebastian/diff/src/MemoryEfficientLongestCommonSubsequenceCalculator.php',
    'SebastianBergmann\\Diff\\Output\\AbstractChunkOutputBuilder' => $vendorDir . '/sebastian/diff/src/Output/AbstractChunkOutputBuilder.php',
    'SebastianBergmann\\Diff\\Output\\DiffOnlyOutputBuilder' => $vendorDir . '/sebastian/diff/src/Output/DiffOnlyOutputBuilder.php',
    'SebastianBergmann\\Diff\\Output\\DiffOutputBuilderInterface' => $vendorDir . '/sebastian/diff/src/Output/DiffOutputBuilderInterface.php',
    'SebastianBergmann\\Diff\\Output\\StrictUnifiedDiffOutputBuilder' => $vendorDir . '/sebastian/diff/src/Output/StrictUnifiedDiffOutputBuilder.php',
    'SebastianBergmann\\Diff\\Output\\UnifiedDiffOutputBuilder' => $vendorDir . '/sebastian/diff/src/Output/UnifiedDiffOutputBuilder.php',
    'SebastianBergmann\\Diff\\Parser' => $vendorDir . '/sebastian/diff/src/Parser.php',
    'SebastianBergmann\\Diff\\TimeEfficientLongestCommonSubsequenceCalculator' => $vendorDir . '/sebastian/diff/src/TimeEfficientLongestCommonSubsequenceCalculator.php',
    'SebastianBergmann\\Environment\\Console' => $vendorDir . '/sebastian/environment/src/Console.php',
    'SebastianBergmann\\Environment\\OperatingSystem' => $vendorDir . '/sebastian/environment/src/OperatingSystem.php',
    'SebastianBergmann\\Environment\\Runtime' => $vendorDir . '/sebastian/environment/src/Runtime.php',
    'SebastianBergmann\\Exporter\\Exporter' => $vendorDir . '/sebastian/exporter/src/Exporter.php',
    'SebastianBergmann\\FileIterator\\Facade' => $vendorDir . '/phpunit/php-file-iterator/src/Facade.php',
    'SebastianBergmann\\FileIterator\\Factory' => $vendorDir . '/phpunit/php-file-iterator/src/Factory.php',
    'SebastianBergmann\\FileIterator\\Iterator' => $vendorDir . '/phpunit/php-file-iterator/src/Iterator.php',
    'SebastianBergmann\\GlobalState\\CodeExporter' => $vendorDir . '/sebastian/global-state/src/CodeExporter.php',
    'SebastianBergmann\\GlobalState\\Exception' => $vendorDir . '/sebastian/global-state/src/exceptions/Exception.php',
    'SebastianBergmann\\GlobalState\\ExcludeList' => $vendorDir . '/sebastian/global-state/src/ExcludeList.php',
    'SebastianBergmann\\GlobalState\\Restorer' => $vendorDir . '/sebastian/global-state/src/Restorer.php',
    'SebastianBergmann\\GlobalState\\RuntimeException' => $vendorDir . '/sebastian/global-state/src/exceptions/RuntimeException.php',
    'SebastianBergmann\\GlobalState\\Snapshot' => $vendorDir . '/sebastian/global-state/src/Snapshot.php',
    'SebastianBergmann\\Invoker\\Exception' => $vendorDir . '/phpunit/php-invoker/src/exceptions/Exception.php',
    'SebastianBergmann\\Invoker\\Invoker' => $vendorDir . '/phpunit/php-invoker/src/Invoker.php',
    'SebastianBergmann\\Invoker\\ProcessControlExtensionNotLoadedException' => $vendorDir . '/phpunit/php-invoker/src/exceptions/ProcessControlExtensionNotLoadedException.php',
    'SebastianBergmann\\Invoker\\TimeoutException' => $vendorDir . '/phpunit/php-invoker/src/exceptions/TimeoutException.php',
    'SebastianBergmann\\LinesOfCode\\Counter' => $vendorDir . '/sebastian/lines-of-code/src/Counter.php',
    'SebastianBergmann\\LinesOfCode\\Exception' => $vendorDir . '/sebastian/lines-of-code/src/Exception/Exception.php',
    'SebastianBergmann\\LinesOfCode\\IllogicalValuesException' => $vendorDir . '/sebastian/lines-of-code/src/Exception/IllogicalValuesException.php',
    'SebastianBergmann\\LinesOfCode\\LineCountingVisitor' => $vendorDir . '/sebastian/lines-of-code/src/LineCountingVisitor.php',
    'SebastianBergmann\\LinesOfCode\\LinesOfCode' => $vendorDir . '/sebastian/lines-of-code/src/LinesOfCode.php',
    'SebastianBergmann\\LinesOfCode\\NegativeValueException' => $vendorDir . '/sebastian/lines-of-code/src/Exception/NegativeValueException.php',
    'SebastianBergmann\\LinesOfCode\\RuntimeException' => $vendorDir . '/sebastian/lines-of-code/src/Exception/RuntimeException.php',
    'SebastianBergmann\\ObjectEnumerator\\Enumerator' => $vendorDir . '/sebastian/object-enumerator/src/Enumerator.php',
    'SebastianBergmann\\ObjectEnumerator\\Exception' => $vendorDir . '/sebastian/object-enumerator/src/Exception.php',
    'SebastianBergmann\\ObjectEnumerator\\InvalidArgumentException' => $vendorDir . '/sebastian/object-enumerator/src/InvalidArgumentException.php',
    'SebastianBergmann\\ObjectReflector\\Exception' => $vendorDir . '/sebastian/object-reflector/src/Exception.php',
    'SebastianBergmann\\ObjectReflector\\InvalidArgumentException' => $vendorDir . '/sebastian/object-reflector/src/InvalidArgumentException.php',
    'SebastianBergmann\\ObjectReflector\\ObjectReflector' => $vendorDir . '/sebastian/object-reflector/src/ObjectReflector.php',
    'SebastianBergmann\\RecursionContext\\Context' => $vendorDir . '/sebastian/recursion-context/src/Context.php',
    'SebastianBergmann\\RecursionContext\\Exception' => $vendorDir . '/sebastian/recursion-context/src/Exception.php',
    'SebastianBergmann\\RecursionContext\\InvalidArgumentException' => $vendorDir . '/sebastian/recursion-context/src/InvalidArgumentException.php',
    'SebastianBergmann\\ResourceOperations\\ResourceOperations' => $vendorDir . '/sebastian/resource-operations/src/ResourceOperations.php',
    'SebastianBergmann\\Template\\Exception' => $vendorDir . '/phpunit/php-text-template/src/exceptions/Exception.php',
    'SebastianBergmann\\Template\\InvalidArgumentException' => $vendorDir . '/phpunit/php-text-template/src/exceptions/InvalidArgumentException.php',
    'SebastianBergmann\\Template\\RuntimeException' => $vendorDir . '/phpunit/php-text-template/src/exceptions/RuntimeException.php',
    'SebastianBergmann\\Template\\Template' => $vendorDir . '/phpunit/php-text-template/src/Template.php',
    'SebastianBergmann\\Timer\\Duration' => $vendorDir . '/phpunit/php-timer/src/Duration.php',
    'SebastianBergmann\\Timer\\Exception' => $vendorDir . '/phpunit/php-timer/src/exceptions/Exception.php',
    'SebastianBergmann\\Timer\\NoActiveTimerException' => $vendorDir . '/phpunit/php-timer/src/exceptions/NoActiveTimerException.php',
    'SebastianBergmann\\Timer\\ResourceUsageFormatter' => $vendorDir . '/phpunit/php-timer/src/ResourceUsageFormatter.php',
    'SebastianBergmann\\Timer\\TimeSinceStartOfRequestNotAvailableException' => $vendorDir . '/phpunit/php-timer/src/exceptions/TimeSinceStartOfRequestNotAvailableException.php',
    'SebastianBergmann\\Timer\\Timer' => $vendorDir . '/phpunit/php-timer/src/Timer.php',
    'SebastianBergmann\\Type\\CallableType' => $vendorDir . '/sebastian/type/src/type/CallableType.php',
    'SebastianBergmann\\Type\\Exception' => $vendorDir . '/sebastian/type/src/exception/Exception.php',
    'SebastianBergmann\\Type\\FalseType' => $vendorDir . '/sebastian/type/src/type/FalseType.php',
    'SebastianBergmann\\Type\\GenericObjectType' => $vendorDir . '/sebastian/type/src/type/GenericObjectType.php',
    'SebastianBergmann\\Type\\IntersectionType' => $vendorDir . '/sebastian/type/src/type/IntersectionType.php',
    'SebastianBergmann\\Type\\IterableType' => $vendorDir . '/sebastian/type/src/type/IterableType.php',
    'SebastianBergmann\\Type\\MixedType' => $vendorDir . '/sebastian/type/src/type/MixedType.php',
    'SebastianBergmann\\Type\\NeverType' => $vendorDir . '/sebastian/type/src/type/NeverType.php',
    'SebastianBergmann\\Type\\NullType' => $vendorDir . '/sebastian/type/src/type/NullType.php',
    'SebastianBergmann\\Type\\ObjectType' => $vendorDir . '/sebastian/type/src/type/ObjectType.php',
    'SebastianBergmann\\Type\\Parameter' => $vendorDir . '/sebastian/type/src/Parameter.php',
    'SebastianBergmann\\Type\\ReflectionMapper' => $vendorDir . '/sebastian/type/src/ReflectionMapper.php',
    'SebastianBergmann\\Type\\RuntimeException' => $vendorDir . '/sebastian/type/src/exception/RuntimeException.php',
    'SebastianBergmann\\Type\\SimpleType' => $vendorDir . '/sebastian/type/src/type/SimpleType.php',
    'SebastianBergmann\\Type\\StaticType' => $vendorDir . '/sebastian/type/src/type/StaticType.php',
    'SebastianBergmann\\Type\\TrueType' => $vendorDir . '/sebastian/type/src/type/TrueType.php',
    'SebastianBergmann\\Type\\Type' => $vendorDir . '/sebastian/type/src/type/Type.php',
    'SebastianBergmann\\Type\\TypeName' => $vendorDir . '/sebastian/type/src/TypeName.php',
    'SebastianBergmann\\Type\\UnionType' => $vendorDir . '/sebastian/type/src/type/UnionType.php',
    'SebastianBergmann\\Type\\UnknownType' => $vendorDir . '/sebastian/type/src/type/UnknownType.php',
    'SebastianBergmann\\Type\\VoidType' => $vendorDir . '/sebastian/type/src/type/VoidType.php',
    'SebastianBergmann\\Version' => $vendorDir . '/sebastian/version/src/Version.php',
    'Seld\\JsonLint\\DuplicateKeyException' => $vendorDir . '/seld/jsonlint/src/Seld/JsonLint/DuplicateKeyException.php',
    'Seld\\JsonLint\\JsonParser' => $vendorDir . '/seld/jsonlint/src/Seld/JsonLint/JsonParser.php',
    'Seld\\JsonLint\\Lexer' => $vendorDir . '/seld/jsonlint/src/Seld/JsonLint/Lexer.php',
    'Seld\\JsonLint\\ParsingException' => $vendorDir . '/seld/jsonlint/src/Seld/JsonLint/ParsingException.php',
    'Seld\\JsonLint\\Undefined' => $vendorDir . '/seld/jsonlint/src/Seld/JsonLint/Undefined.php',
    'Seld\\PharUtils\\Linter' => $vendorDir . '/seld/phar-utils/src/Linter.php',
    'Seld\\PharUtils\\Timestamps' => $vendorDir . '/seld/phar-utils/src/Timestamps.php',
    'Seld\\Signal\\SignalHandler' => $vendorDir . '/seld/signal-handler/src/SignalHandler.php',
    'Server_Command' => $vendorDir . '/wp-cli/server-command/src/Server_Command.php',
    'Shell_Command' => $vendorDir . '/wp-cli/shell-command/src/Shell_Command.php',
    'Sidebar_Command' => $vendorDir . '/wp-cli/widget-command/src/Sidebar_Command.php',
    'Signup_Command' => $vendorDir . '/wp-cli/entity-command/src/Signup_Command.php',
    'Site_Command' => $vendorDir . '/wp-cli/entity-command/src/Site_Command.php',
    'Site_Meta_Command' => $vendorDir . '/wp-cli/entity-command/src/Site_Meta_Command.php',
    'Site_Option_Command' => $vendorDir . '/wp-cli/entity-command/src/Site_Option_Command.php',
    'Site_Switch_Language_Command' => $vendorDir . '/wp-cli/language-command/src/Site_Switch_Language_Command.php',
    'Smalot\\PdfParser\\Config' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Config.php',
    'Smalot\\PdfParser\\Document' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Document.php',
    'Smalot\\PdfParser\\Element' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Element.php',
    'Smalot\\PdfParser\\Element\\ElementArray' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Element/ElementArray.php',
    'Smalot\\PdfParser\\Element\\ElementBoolean' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Element/ElementBoolean.php',
    'Smalot\\PdfParser\\Element\\ElementDate' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Element/ElementDate.php',
    'Smalot\\PdfParser\\Element\\ElementHexa' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Element/ElementHexa.php',
    'Smalot\\PdfParser\\Element\\ElementMissing' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Element/ElementMissing.php',
    'Smalot\\PdfParser\\Element\\ElementName' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Element/ElementName.php',
    'Smalot\\PdfParser\\Element\\ElementNull' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Element/ElementNull.php',
    'Smalot\\PdfParser\\Element\\ElementNumeric' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Element/ElementNumeric.php',
    'Smalot\\PdfParser\\Element\\ElementString' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Element/ElementString.php',
    'Smalot\\PdfParser\\Element\\ElementStruct' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Element/ElementStruct.php',
    'Smalot\\PdfParser\\Element\\ElementXRef' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Element/ElementXRef.php',
    'Smalot\\PdfParser\\Encoding' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Encoding.php',
    'Smalot\\PdfParser\\Encoding\\AbstractEncoding' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Encoding/AbstractEncoding.php',
    'Smalot\\PdfParser\\Encoding\\EncodingLocator' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Encoding/EncodingLocator.php',
    'Smalot\\PdfParser\\Encoding\\ISOLatin1Encoding' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Encoding/ISOLatin1Encoding.php',
    'Smalot\\PdfParser\\Encoding\\ISOLatin9Encoding' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Encoding/ISOLatin9Encoding.php',
    'Smalot\\PdfParser\\Encoding\\MacRomanEncoding' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Encoding/MacRomanEncoding.php',
    'Smalot\\PdfParser\\Encoding\\PDFDocEncoding' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Encoding/PDFDocEncoding.php',
    'Smalot\\PdfParser\\Encoding\\PostScriptGlyphs' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Encoding/PostScriptGlyphs.php',
    'Smalot\\PdfParser\\Encoding\\StandardEncoding' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Encoding/StandardEncoding.php',
    'Smalot\\PdfParser\\Encoding\\WinAnsiEncoding' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Encoding/WinAnsiEncoding.php',
    'Smalot\\PdfParser\\Exception\\EmptyPdfException' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Exception/EmptyPdfException.php',
    'Smalot\\PdfParser\\Exception\\EncodingNotFoundException' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Exception/EncodingNotFoundException.php',
    'Smalot\\PdfParser\\Exception\\InvalidDictionaryObjectException' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Exception/InvalidDictionaryObjectException.php',
    'Smalot\\PdfParser\\Exception\\MissingCatalogException' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Exception/MissingCatalogException.php',
    'Smalot\\PdfParser\\Exception\\MissingPdfHeaderException' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Exception/MissingPdfHeaderException.php',
    'Smalot\\PdfParser\\Exception\\NotImplementedException' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Exception/NotImplementedException.php',
    'Smalot\\PdfParser\\Font' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Font.php',
    'Smalot\\PdfParser\\Font\\FontCIDFontType0' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Font/FontCIDFontType0.php',
    'Smalot\\PdfParser\\Font\\FontCIDFontType2' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Font/FontCIDFontType2.php',
    'Smalot\\PdfParser\\Font\\FontTrueType' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Font/FontTrueType.php',
    'Smalot\\PdfParser\\Font\\FontType0' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Font/FontType0.php',
    'Smalot\\PdfParser\\Font\\FontType1' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Font/FontType1.php',
    'Smalot\\PdfParser\\Font\\FontType3' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Font/FontType3.php',
    'Smalot\\PdfParser\\Header' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Header.php',
    'Smalot\\PdfParser\\PDFObject' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/PDFObject.php',
    'Smalot\\PdfParser\\Page' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Page.php',
    'Smalot\\PdfParser\\Pages' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Pages.php',
    'Smalot\\PdfParser\\Parser' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/Parser.php',
    'Smalot\\PdfParser\\RawData\\FilterHelper' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/RawData/FilterHelper.php',
    'Smalot\\PdfParser\\RawData\\RawDataParser' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/RawData/RawDataParser.php',
    'Smalot\\PdfParser\\XObject\\Form' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/XObject/Form.php',
    'Smalot\\PdfParser\\XObject\\Image' => $vendorDir . '/smalot/pdfparser/src/Smalot/PdfParser/XObject/Image.php',
    'Stringable' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/Stringable.php',
    'Super_Admin_Command' => $vendorDir . '/wp-cli/super-admin-command/src/Super_Admin_Command.php',
    'Symfony\\Component\\Console\\Application' => $vendorDir . '/symfony/console/Application.php',
    'Symfony\\Component\\Console\\Attribute\\AsCommand' => $vendorDir . '/symfony/console/Attribute/AsCommand.php',
    'Symfony\\Component\\Console\\CI\\GithubActionReporter' => $vendorDir . '/symfony/console/CI/GithubActionReporter.php',
    'Symfony\\Component\\Console\\Color' => $vendorDir . '/symfony/console/Color.php',
    'Symfony\\Component\\Console\\CommandLoader\\CommandLoaderInterface' => $vendorDir . '/symfony/console/CommandLoader/CommandLoaderInterface.php',
    'Symfony\\Component\\Console\\CommandLoader\\ContainerCommandLoader' => $vendorDir . '/symfony/console/CommandLoader/ContainerCommandLoader.php',
    'Symfony\\Component\\Console\\CommandLoader\\FactoryCommandLoader' => $vendorDir . '/symfony/console/CommandLoader/FactoryCommandLoader.php',
    'Symfony\\Component\\Console\\Command\\Command' => $vendorDir . '/symfony/console/Command/Command.php',
    'Symfony\\Component\\Console\\Command\\CompleteCommand' => $vendorDir . '/symfony/console/Command/CompleteCommand.php',
    'Symfony\\Component\\Console\\Command\\DumpCompletionCommand' => $vendorDir . '/symfony/console/Command/DumpCompletionCommand.php',
    'Symfony\\Component\\Console\\Command\\HelpCommand' => $vendorDir . '/symfony/console/Command/HelpCommand.php',
    'Symfony\\Component\\Console\\Command\\LazyCommand' => $vendorDir . '/symfony/console/Command/LazyCommand.php',
    'Symfony\\Component\\Console\\Command\\ListCommand' => $vendorDir . '/symfony/console/Command/ListCommand.php',
    'Symfony\\Component\\Console\\Command\\LockableTrait' => $vendorDir . '/symfony/console/Command/LockableTrait.php',
    'Symfony\\Component\\Console\\Command\\SignalableCommandInterface' => $vendorDir . '/symfony/console/Command/SignalableCommandInterface.php',
    'Symfony\\Component\\Console\\Command\\TraceableCommand' => $vendorDir . '/symfony/console/Command/TraceableCommand.php',
    'Symfony\\Component\\Console\\Completion\\CompletionInput' => $vendorDir . '/symfony/console/Completion/CompletionInput.php',
    'Symfony\\Component\\Console\\Completion\\CompletionSuggestions' => $vendorDir . '/symfony/console/Completion/CompletionSuggestions.php',
    'Symfony\\Component\\Console\\Completion\\Output\\BashCompletionOutput' => $vendorDir . '/symfony/console/Completion/Output/BashCompletionOutput.php',
    'Symfony\\Component\\Console\\Completion\\Output\\CompletionOutputInterface' => $vendorDir . '/symfony/console/Completion/Output/CompletionOutputInterface.php',
    'Symfony\\Component\\Console\\Completion\\Output\\FishCompletionOutput' => $vendorDir . '/symfony/console/Completion/Output/FishCompletionOutput.php',
    'Symfony\\Component\\Console\\Completion\\Output\\ZshCompletionOutput' => $vendorDir . '/symfony/console/Completion/Output/ZshCompletionOutput.php',
    'Symfony\\Component\\Console\\Completion\\Suggestion' => $vendorDir . '/symfony/console/Completion/Suggestion.php',
    'Symfony\\Component\\Console\\ConsoleEvents' => $vendorDir . '/symfony/console/ConsoleEvents.php',
    'Symfony\\Component\\Console\\Cursor' => $vendorDir . '/symfony/console/Cursor.php',
    'Symfony\\Component\\Console\\DataCollector\\CommandDataCollector' => $vendorDir . '/symfony/console/DataCollector/CommandDataCollector.php',
    'Symfony\\Component\\Console\\Debug\\CliRequest' => $vendorDir . '/symfony/console/Debug/CliRequest.php',
    'Symfony\\Component\\Console\\DependencyInjection\\AddConsoleCommandPass' => $vendorDir . '/symfony/console/DependencyInjection/AddConsoleCommandPass.php',
    'Symfony\\Component\\Console\\Descriptor\\ApplicationDescription' => $vendorDir . '/symfony/console/Descriptor/ApplicationDescription.php',
    'Symfony\\Component\\Console\\Descriptor\\Descriptor' => $vendorDir . '/symfony/console/Descriptor/Descriptor.php',
    'Symfony\\Component\\Console\\Descriptor\\DescriptorInterface' => $vendorDir . '/symfony/console/Descriptor/DescriptorInterface.php',
    'Symfony\\Component\\Console\\Descriptor\\JsonDescriptor' => $vendorDir . '/symfony/console/Descriptor/JsonDescriptor.php',
    'Symfony\\Component\\Console\\Descriptor\\MarkdownDescriptor' => $vendorDir . '/symfony/console/Descriptor/MarkdownDescriptor.php',
    'Symfony\\Component\\Console\\Descriptor\\ReStructuredTextDescriptor' => $vendorDir . '/symfony/console/Descriptor/ReStructuredTextDescriptor.php',
    'Symfony\\Component\\Console\\Descriptor\\TextDescriptor' => $vendorDir . '/symfony/console/Descriptor/TextDescriptor.php',
    'Symfony\\Component\\Console\\Descriptor\\XmlDescriptor' => $vendorDir . '/symfony/console/Descriptor/XmlDescriptor.php',
    'Symfony\\Component\\Console\\EventListener\\ErrorListener' => $vendorDir . '/symfony/console/EventListener/ErrorListener.php',
    'Symfony\\Component\\Console\\Event\\ConsoleCommandEvent' => $vendorDir . '/symfony/console/Event/ConsoleCommandEvent.php',
    'Symfony\\Component\\Console\\Event\\ConsoleErrorEvent' => $vendorDir . '/symfony/console/Event/ConsoleErrorEvent.php',
    'Symfony\\Component\\Console\\Event\\ConsoleEvent' => $vendorDir . '/symfony/console/Event/ConsoleEvent.php',
    'Symfony\\Component\\Console\\Event\\ConsoleSignalEvent' => $vendorDir . '/symfony/console/Event/ConsoleSignalEvent.php',
    'Symfony\\Component\\Console\\Event\\ConsoleTerminateEvent' => $vendorDir . '/symfony/console/Event/ConsoleTerminateEvent.php',
    'Symfony\\Component\\Console\\Exception\\CommandNotFoundException' => $vendorDir . '/symfony/console/Exception/CommandNotFoundException.php',
    'Symfony\\Component\\Console\\Exception\\ExceptionInterface' => $vendorDir . '/symfony/console/Exception/ExceptionInterface.php',
    'Symfony\\Component\\Console\\Exception\\InvalidArgumentException' => $vendorDir . '/symfony/console/Exception/InvalidArgumentException.php',
    'Symfony\\Component\\Console\\Exception\\InvalidOptionException' => $vendorDir . '/symfony/console/Exception/InvalidOptionException.php',
    'Symfony\\Component\\Console\\Exception\\LogicException' => $vendorDir . '/symfony/console/Exception/LogicException.php',
    'Symfony\\Component\\Console\\Exception\\MissingInputException' => $vendorDir . '/symfony/console/Exception/MissingInputException.php',
    'Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException' => $vendorDir . '/symfony/console/Exception/NamespaceNotFoundException.php',
    'Symfony\\Component\\Console\\Exception\\RunCommandFailedException' => $vendorDir . '/symfony/console/Exception/RunCommandFailedException.php',
    'Symfony\\Component\\Console\\Exception\\RuntimeException' => $vendorDir . '/symfony/console/Exception/RuntimeException.php',
    'Symfony\\Component\\Console\\Formatter\\NullOutputFormatter' => $vendorDir . '/symfony/console/Formatter/NullOutputFormatter.php',
    'Symfony\\Component\\Console\\Formatter\\NullOutputFormatterStyle' => $vendorDir . '/symfony/console/Formatter/NullOutputFormatterStyle.php',
    'Symfony\\Component\\Console\\Formatter\\OutputFormatter' => $vendorDir . '/symfony/console/Formatter/OutputFormatter.php',
    'Symfony\\Component\\Console\\Formatter\\OutputFormatterInterface' => $vendorDir . '/symfony/console/Formatter/OutputFormatterInterface.php',
    'Symfony\\Component\\Console\\Formatter\\OutputFormatterStyle' => $vendorDir . '/symfony/console/Formatter/OutputFormatterStyle.php',
    'Symfony\\Component\\Console\\Formatter\\OutputFormatterStyleInterface' => $vendorDir . '/symfony/console/Formatter/OutputFormatterStyleInterface.php',
    'Symfony\\Component\\Console\\Formatter\\OutputFormatterStyleStack' => $vendorDir . '/symfony/console/Formatter/OutputFormatterStyleStack.php',
    'Symfony\\Component\\Console\\Formatter\\WrappableOutputFormatterInterface' => $vendorDir . '/symfony/console/Formatter/WrappableOutputFormatterInterface.php',
    'Symfony\\Component\\Console\\Helper\\DebugFormatterHelper' => $vendorDir . '/symfony/console/Helper/DebugFormatterHelper.php',
    'Symfony\\Component\\Console\\Helper\\DescriptorHelper' => $vendorDir . '/symfony/console/Helper/DescriptorHelper.php',
    'Symfony\\Component\\Console\\Helper\\Dumper' => $vendorDir . '/symfony/console/Helper/Dumper.php',
    'Symfony\\Component\\Console\\Helper\\FormatterHelper' => $vendorDir . '/symfony/console/Helper/FormatterHelper.php',
    'Symfony\\Component\\Console\\Helper\\Helper' => $vendorDir . '/symfony/console/Helper/Helper.php',
    'Symfony\\Component\\Console\\Helper\\HelperInterface' => $vendorDir . '/symfony/console/Helper/HelperInterface.php',
    'Symfony\\Component\\Console\\Helper\\HelperSet' => $vendorDir . '/symfony/console/Helper/HelperSet.php',
    'Symfony\\Component\\Console\\Helper\\InputAwareHelper' => $vendorDir . '/symfony/console/Helper/InputAwareHelper.php',
    'Symfony\\Component\\Console\\Helper\\OutputWrapper' => $vendorDir . '/symfony/console/Helper/OutputWrapper.php',
    'Symfony\\Component\\Console\\Helper\\ProcessHelper' => $vendorDir . '/symfony/console/Helper/ProcessHelper.php',
    'Symfony\\Component\\Console\\Helper\\ProgressBar' => $vendorDir . '/symfony/console/Helper/ProgressBar.php',
    'Symfony\\Component\\Console\\Helper\\ProgressIndicator' => $vendorDir . '/symfony/console/Helper/ProgressIndicator.php',
    'Symfony\\Component\\Console\\Helper\\QuestionHelper' => $vendorDir . '/symfony/console/Helper/QuestionHelper.php',
    'Symfony\\Component\\Console\\Helper\\SymfonyQuestionHelper' => $vendorDir . '/symfony/console/Helper/SymfonyQuestionHelper.php',
    'Symfony\\Component\\Console\\Helper\\Table' => $vendorDir . '/symfony/console/Helper/Table.php',
    'Symfony\\Component\\Console\\Helper\\TableCell' => $vendorDir . '/symfony/console/Helper/TableCell.php',
    'Symfony\\Component\\Console\\Helper\\TableCellStyle' => $vendorDir . '/symfony/console/Helper/TableCellStyle.php',
    'Symfony\\Component\\Console\\Helper\\TableRows' => $vendorDir . '/symfony/console/Helper/TableRows.php',
    'Symfony\\Component\\Console\\Helper\\TableSeparator' => $vendorDir . '/symfony/console/Helper/TableSeparator.php',
    'Symfony\\Component\\Console\\Helper\\TableStyle' => $vendorDir . '/symfony/console/Helper/TableStyle.php',
    'Symfony\\Component\\Console\\Input\\ArgvInput' => $vendorDir . '/symfony/console/Input/ArgvInput.php',
    'Symfony\\Component\\Console\\Input\\ArrayInput' => $vendorDir . '/symfony/console/Input/ArrayInput.php',
    'Symfony\\Component\\Console\\Input\\Input' => $vendorDir . '/symfony/console/Input/Input.php',
    'Symfony\\Component\\Console\\Input\\InputArgument' => $vendorDir . '/symfony/console/Input/InputArgument.php',
    'Symfony\\Component\\Console\\Input\\InputAwareInterface' => $vendorDir . '/symfony/console/Input/InputAwareInterface.php',
    'Symfony\\Component\\Console\\Input\\InputDefinition' => $vendorDir . '/symfony/console/Input/InputDefinition.php',
    'Symfony\\Component\\Console\\Input\\InputInterface' => $vendorDir . '/symfony/console/Input/InputInterface.php',
    'Symfony\\Component\\Console\\Input\\InputOption' => $vendorDir . '/symfony/console/Input/InputOption.php',
    'Symfony\\Component\\Console\\Input\\StreamableInputInterface' => $vendorDir . '/symfony/console/Input/StreamableInputInterface.php',
    'Symfony\\Component\\Console\\Input\\StringInput' => $vendorDir . '/symfony/console/Input/StringInput.php',
    'Symfony\\Component\\Console\\Logger\\ConsoleLogger' => $vendorDir . '/symfony/console/Logger/ConsoleLogger.php',
    'Symfony\\Component\\Console\\Messenger\\RunCommandContext' => $vendorDir . '/symfony/console/Messenger/RunCommandContext.php',
    'Symfony\\Component\\Console\\Messenger\\RunCommandMessage' => $vendorDir . '/symfony/console/Messenger/RunCommandMessage.php',
    'Symfony\\Component\\Console\\Messenger\\RunCommandMessageHandler' => $vendorDir . '/symfony/console/Messenger/RunCommandMessageHandler.php',
    'Symfony\\Component\\Console\\Output\\AnsiColorMode' => $vendorDir . '/symfony/console/Output/AnsiColorMode.php',
    'Symfony\\Component\\Console\\Output\\BufferedOutput' => $vendorDir . '/symfony/console/Output/BufferedOutput.php',
    'Symfony\\Component\\Console\\Output\\ConsoleOutput' => $vendorDir . '/symfony/console/Output/ConsoleOutput.php',
    'Symfony\\Component\\Console\\Output\\ConsoleOutputInterface' => $vendorDir . '/symfony/console/Output/ConsoleOutputInterface.php',
    'Symfony\\Component\\Console\\Output\\ConsoleSectionOutput' => $vendorDir . '/symfony/console/Output/ConsoleSectionOutput.php',
    'Symfony\\Component\\Console\\Output\\NullOutput' => $vendorDir . '/symfony/console/Output/NullOutput.php',
    'Symfony\\Component\\Console\\Output\\Output' => $vendorDir . '/symfony/console/Output/Output.php',
    'Symfony\\Component\\Console\\Output\\OutputInterface' => $vendorDir . '/symfony/console/Output/OutputInterface.php',
    'Symfony\\Component\\Console\\Output\\StreamOutput' => $vendorDir . '/symfony/console/Output/StreamOutput.php',
    'Symfony\\Component\\Console\\Output\\TrimmedBufferOutput' => $vendorDir . '/symfony/console/Output/TrimmedBufferOutput.php',
    'Symfony\\Component\\Console\\Question\\ChoiceQuestion' => $vendorDir . '/symfony/console/Question/ChoiceQuestion.php',
    'Symfony\\Component\\Console\\Question\\ConfirmationQuestion' => $vendorDir . '/symfony/console/Question/ConfirmationQuestion.php',
    'Symfony\\Component\\Console\\Question\\Question' => $vendorDir . '/symfony/console/Question/Question.php',
    'Symfony\\Component\\Console\\SignalRegistry\\SignalMap' => $vendorDir . '/symfony/console/SignalRegistry/SignalMap.php',
    'Symfony\\Component\\Console\\SignalRegistry\\SignalRegistry' => $vendorDir . '/symfony/console/SignalRegistry/SignalRegistry.php',
    'Symfony\\Component\\Console\\SingleCommandApplication' => $vendorDir . '/symfony/console/SingleCommandApplication.php',
    'Symfony\\Component\\Console\\Style\\OutputStyle' => $vendorDir . '/symfony/console/Style/OutputStyle.php',
    'Symfony\\Component\\Console\\Style\\StyleInterface' => $vendorDir . '/symfony/console/Style/StyleInterface.php',
    'Symfony\\Component\\Console\\Style\\SymfonyStyle' => $vendorDir . '/symfony/console/Style/SymfonyStyle.php',
    'Symfony\\Component\\Console\\Terminal' => $vendorDir . '/symfony/console/Terminal.php',
    'Symfony\\Component\\Console\\Tester\\ApplicationTester' => $vendorDir . '/symfony/console/Tester/ApplicationTester.php',
    'Symfony\\Component\\Console\\Tester\\CommandCompletionTester' => $vendorDir . '/symfony/console/Tester/CommandCompletionTester.php',
    'Symfony\\Component\\Console\\Tester\\CommandTester' => $vendorDir . '/symfony/console/Tester/CommandTester.php',
    'Symfony\\Component\\Console\\Tester\\Constraint\\CommandIsSuccessful' => $vendorDir . '/symfony/console/Tester/Constraint/CommandIsSuccessful.php',
    'Symfony\\Component\\Console\\Tester\\TesterTrait' => $vendorDir . '/symfony/console/Tester/TesterTrait.php',
    'Symfony\\Component\\Filesystem\\Exception\\ExceptionInterface' => $vendorDir . '/symfony/filesystem/Exception/ExceptionInterface.php',
    'Symfony\\Component\\Filesystem\\Exception\\FileNotFoundException' => $vendorDir . '/symfony/filesystem/Exception/FileNotFoundException.php',
    'Symfony\\Component\\Filesystem\\Exception\\IOException' => $vendorDir . '/symfony/filesystem/Exception/IOException.php',
    'Symfony\\Component\\Filesystem\\Exception\\IOExceptionInterface' => $vendorDir . '/symfony/filesystem/Exception/IOExceptionInterface.php',
    'Symfony\\Component\\Filesystem\\Exception\\InvalidArgumentException' => $vendorDir . '/symfony/filesystem/Exception/InvalidArgumentException.php',
    'Symfony\\Component\\Filesystem\\Exception\\RuntimeException' => $vendorDir . '/symfony/filesystem/Exception/RuntimeException.php',
    'Symfony\\Component\\Filesystem\\Filesystem' => $vendorDir . '/symfony/filesystem/Filesystem.php',
    'Symfony\\Component\\Filesystem\\Path' => $vendorDir . '/symfony/filesystem/Path.php',
    'Symfony\\Component\\Finder\\Comparator\\Comparator' => $vendorDir . '/symfony/finder/Comparator/Comparator.php',
    'Symfony\\Component\\Finder\\Comparator\\DateComparator' => $vendorDir . '/symfony/finder/Comparator/DateComparator.php',
    'Symfony\\Component\\Finder\\Comparator\\NumberComparator' => $vendorDir . '/symfony/finder/Comparator/NumberComparator.php',
    'Symfony\\Component\\Finder\\Exception\\AccessDeniedException' => $vendorDir . '/symfony/finder/Exception/AccessDeniedException.php',
    'Symfony\\Component\\Finder\\Exception\\DirectoryNotFoundException' => $vendorDir . '/symfony/finder/Exception/DirectoryNotFoundException.php',
    'Symfony\\Component\\Finder\\Finder' => $vendorDir . '/symfony/finder/Finder.php',
    'Symfony\\Component\\Finder\\Gitignore' => $vendorDir . '/symfony/finder/Gitignore.php',
    'Symfony\\Component\\Finder\\Glob' => $vendorDir . '/symfony/finder/Glob.php',
    'Symfony\\Component\\Finder\\Iterator\\CustomFilterIterator' => $vendorDir . '/symfony/finder/Iterator/CustomFilterIterator.php',
    'Symfony\\Component\\Finder\\Iterator\\DateRangeFilterIterator' => $vendorDir . '/symfony/finder/Iterator/DateRangeFilterIterator.php',
    'Symfony\\Component\\Finder\\Iterator\\DepthRangeFilterIterator' => $vendorDir . '/symfony/finder/Iterator/DepthRangeFilterIterator.php',
    'Symfony\\Component\\Finder\\Iterator\\ExcludeDirectoryFilterIterator' => $vendorDir . '/symfony/finder/Iterator/ExcludeDirectoryFilterIterator.php',
    'Symfony\\Component\\Finder\\Iterator\\FileTypeFilterIterator' => $vendorDir . '/symfony/finder/Iterator/FileTypeFilterIterator.php',
    'Symfony\\Component\\Finder\\Iterator\\FilecontentFilterIterator' => $vendorDir . '/symfony/finder/Iterator/FilecontentFilterIterator.php',
    'Symfony\\Component\\Finder\\Iterator\\FilenameFilterIterator' => $vendorDir . '/symfony/finder/Iterator/FilenameFilterIterator.php',
    'Symfony\\Component\\Finder\\Iterator\\LazyIterator' => $vendorDir . '/symfony/finder/Iterator/LazyIterator.php',
    'Symfony\\Component\\Finder\\Iterator\\MultiplePcreFilterIterator' => $vendorDir . '/symfony/finder/Iterator/MultiplePcreFilterIterator.php',
    'Symfony\\Component\\Finder\\Iterator\\PathFilterIterator' => $vendorDir . '/symfony/finder/Iterator/PathFilterIterator.php',
    'Symfony\\Component\\Finder\\Iterator\\RecursiveDirectoryIterator' => $vendorDir . '/symfony/finder/Iterator/RecursiveDirectoryIterator.php',
    'Symfony\\Component\\Finder\\Iterator\\SizeRangeFilterIterator' => $vendorDir . '/symfony/finder/Iterator/SizeRangeFilterIterator.php',
    'Symfony\\Component\\Finder\\Iterator\\SortableIterator' => $vendorDir . '/symfony/finder/Iterator/SortableIterator.php',
    'Symfony\\Component\\Finder\\Iterator\\VcsIgnoredFilterIterator' => $vendorDir . '/symfony/finder/Iterator/VcsIgnoredFilterIterator.php',
    'Symfony\\Component\\Finder\\SplFileInfo' => $vendorDir . '/symfony/finder/SplFileInfo.php',
    'Symfony\\Component\\Process\\Exception\\ExceptionInterface' => $vendorDir . '/wp-cli/process/Exception/ExceptionInterface.php',
    'Symfony\\Component\\Process\\Exception\\InvalidArgumentException' => $vendorDir . '/wp-cli/process/Exception/InvalidArgumentException.php',
    'Symfony\\Component\\Process\\Exception\\LogicException' => $vendorDir . '/wp-cli/process/Exception/LogicException.php',
    'Symfony\\Component\\Process\\Exception\\ProcessFailedException' => $vendorDir . '/wp-cli/process/Exception/ProcessFailedException.php',
    'Symfony\\Component\\Process\\Exception\\ProcessTimedOutException' => $vendorDir . '/wp-cli/process/Exception/ProcessTimedOutException.php',
    'Symfony\\Component\\Process\\Exception\\RuntimeException' => $vendorDir . '/wp-cli/process/Exception/RuntimeException.php',
    'Symfony\\Component\\Process\\ExecutableFinder' => $vendorDir . '/wp-cli/process/ExecutableFinder.php',
    'Symfony\\Component\\Process\\InputStream' => $vendorDir . '/wp-cli/process/InputStream.php',
    'Symfony\\Component\\Process\\PhpExecutableFinder' => $vendorDir . '/wp-cli/process/PhpExecutableFinder.php',
    'Symfony\\Component\\Process\\PhpProcess' => $vendorDir . '/wp-cli/process/PhpProcess.php',
    'Symfony\\Component\\Process\\Pipes\\AbstractPipes' => $vendorDir . '/wp-cli/process/Pipes/AbstractPipes.php',
    'Symfony\\Component\\Process\\Pipes\\PipesInterface' => $vendorDir . '/wp-cli/process/Pipes/PipesInterface.php',
    'Symfony\\Component\\Process\\Pipes\\UnixPipes' => $vendorDir . '/wp-cli/process/Pipes/UnixPipes.php',
    'Symfony\\Component\\Process\\Pipes\\WindowsPipes' => $vendorDir . '/wp-cli/process/Pipes/WindowsPipes.php',
    'Symfony\\Component\\Process\\Process' => $vendorDir . '/wp-cli/process/Process.php',
    'Symfony\\Component\\Process\\ProcessBuilder' => $vendorDir . '/wp-cli/process/ProcessBuilder.php',
    'Symfony\\Component\\Process\\ProcessUtils' => $vendorDir . '/wp-cli/process/ProcessUtils.php',
    'Symfony\\Component\\String\\AbstractString' => $vendorDir . '/symfony/string/AbstractString.php',
    'Symfony\\Component\\String\\AbstractUnicodeString' => $vendorDir . '/symfony/string/AbstractUnicodeString.php',
    'Symfony\\Component\\String\\ByteString' => $vendorDir . '/symfony/string/ByteString.php',
    'Symfony\\Component\\String\\CodePointString' => $vendorDir . '/symfony/string/CodePointString.php',
    'Symfony\\Component\\String\\Exception\\ExceptionInterface' => $vendorDir . '/symfony/string/Exception/ExceptionInterface.php',
    'Symfony\\Component\\String\\Exception\\InvalidArgumentException' => $vendorDir . '/symfony/string/Exception/InvalidArgumentException.php',
    'Symfony\\Component\\String\\Exception\\RuntimeException' => $vendorDir . '/symfony/string/Exception/RuntimeException.php',
    'Symfony\\Component\\String\\Inflector\\EnglishInflector' => $vendorDir . '/symfony/string/Inflector/EnglishInflector.php',
    'Symfony\\Component\\String\\Inflector\\FrenchInflector' => $vendorDir . '/symfony/string/Inflector/FrenchInflector.php',
    'Symfony\\Component\\String\\Inflector\\InflectorInterface' => $vendorDir . '/symfony/string/Inflector/InflectorInterface.php',
    'Symfony\\Component\\String\\Inflector\\SpanishInflector' => $vendorDir . '/symfony/string/Inflector/SpanishInflector.php',
    'Symfony\\Component\\String\\LazyString' => $vendorDir . '/symfony/string/LazyString.php',
    'Symfony\\Component\\String\\Slugger\\AsciiSlugger' => $vendorDir . '/symfony/string/Slugger/AsciiSlugger.php',
    'Symfony\\Component\\String\\Slugger\\SluggerInterface' => $vendorDir . '/symfony/string/Slugger/SluggerInterface.php',
    'Symfony\\Component\\String\\TruncateMode' => $vendorDir . '/symfony/string/TruncateMode.php',
    'Symfony\\Component\\String\\UnicodeString' => $vendorDir . '/symfony/string/UnicodeString.php',
    'Symfony\\Contracts\\Service\\Attribute\\Required' => $vendorDir . '/symfony/service-contracts/Attribute/Required.php',
    'Symfony\\Contracts\\Service\\Attribute\\SubscribedService' => $vendorDir . '/symfony/service-contracts/Attribute/SubscribedService.php',
    'Symfony\\Contracts\\Service\\ResetInterface' => $vendorDir . '/symfony/service-contracts/ResetInterface.php',
    'Symfony\\Contracts\\Service\\ServiceCollectionInterface' => $vendorDir . '/symfony/service-contracts/ServiceCollectionInterface.php',
    'Symfony\\Contracts\\Service\\ServiceLocatorTrait' => $vendorDir . '/symfony/service-contracts/ServiceLocatorTrait.php',
    'Symfony\\Contracts\\Service\\ServiceMethodsSubscriberTrait' => $vendorDir . '/symfony/service-contracts/ServiceMethodsSubscriberTrait.php',
    'Symfony\\Contracts\\Service\\ServiceProviderInterface' => $vendorDir . '/symfony/service-contracts/ServiceProviderInterface.php',
    'Symfony\\Contracts\\Service\\ServiceSubscriberInterface' => $vendorDir . '/symfony/service-contracts/ServiceSubscriberInterface.php',
    'Symfony\\Contracts\\Service\\ServiceSubscriberTrait' => $vendorDir . '/symfony/service-contracts/ServiceSubscriberTrait.php',
    'Symfony\\Polyfill\\Ctype\\Ctype' => $vendorDir . '/symfony/polyfill-ctype/Ctype.php',
    'Symfony\\Polyfill\\Intl\\Grapheme\\Grapheme' => $vendorDir . '/symfony/polyfill-intl-grapheme/Grapheme.php',
    'Symfony\\Polyfill\\Intl\\Normalizer\\Normalizer' => $vendorDir . '/symfony/polyfill-intl-normalizer/Normalizer.php',
    'Symfony\\Polyfill\\Mbstring\\Mbstring' => $vendorDir . '/symfony/polyfill-mbstring/Mbstring.php',
    'Symfony\\Polyfill\\Php73\\Php73' => $vendorDir . '/symfony/polyfill-php73/Php73.php',
    'Symfony\\Polyfill\\Php80\\Php80' => $vendorDir . '/symfony/polyfill-php80/Php80.php',
    'Symfony\\Polyfill\\Php80\\PhpToken' => $vendorDir . '/symfony/polyfill-php80/PhpToken.php',
    'Symfony\\Polyfill\\Php81\\Php81' => $vendorDir . '/symfony/polyfill-php81/Php81.php',
    'Taxonomy_Command' => $vendorDir . '/wp-cli/entity-command/src/Taxonomy_Command.php',
    'Term_Command' => $vendorDir . '/wp-cli/entity-command/src/Term_Command.php',
    'Term_Meta_Command' => $vendorDir . '/wp-cli/entity-command/src/Term_Meta_Command.php',
    'Tests\\BaseTestCase' => $baseDir . '/tests/BaseTestCase.php',
    'Tests\\Integration\\OfficeAddinTest' => $baseDir . '/tests/Integration/OfficeAddinTest.php',
    'Tests\\Unit\\CacheManagerTest' => $baseDir . '/tests/Unit/CacheManagerTest.php',
    'Tests\\Unit\\ErrorReporterTest' => $baseDir . '/tests/Unit/ErrorReporterTest.php',
    'Tests\\Unit\\PerformanceMonitorTest' => $baseDir . '/tests/Unit/PerformanceMonitorTest.php',
    'Tests\\Unit\\RateLimiterTest' => $baseDir . '/tests/Unit/RateLimiterTest.php',
    'TheSeer\\Tokenizer\\Exception' => $vendorDir . '/theseer/tokenizer/src/Exception.php',
    'TheSeer\\Tokenizer\\NamespaceUri' => $vendorDir . '/theseer/tokenizer/src/NamespaceUri.php',
    'TheSeer\\Tokenizer\\NamespaceUriException' => $vendorDir . '/theseer/tokenizer/src/NamespaceUriException.php',
    'TheSeer\\Tokenizer\\Token' => $vendorDir . '/theseer/tokenizer/src/Token.php',
    'TheSeer\\Tokenizer\\TokenCollection' => $vendorDir . '/theseer/tokenizer/src/TokenCollection.php',
    'TheSeer\\Tokenizer\\TokenCollectionException' => $vendorDir . '/theseer/tokenizer/src/TokenCollectionException.php',
    'TheSeer\\Tokenizer\\Tokenizer' => $vendorDir . '/theseer/tokenizer/src/Tokenizer.php',
    'TheSeer\\Tokenizer\\XMLSerializer' => $vendorDir . '/theseer/tokenizer/src/XMLSerializer.php',
    'Theme_AutoUpdates_Command' => $vendorDir . '/wp-cli/extension-command/src/Theme_AutoUpdates_Command.php',
    'Theme_Command' => $vendorDir . '/wp-cli/extension-command/src/Theme_Command.php',
    'Theme_Language_Command' => $vendorDir . '/wp-cli/language-command/src/Theme_Language_Command.php',
    'Theme_Mod_Command' => $vendorDir . '/wp-cli/extension-command/src/Theme_Mod_Command.php',
    'Transient_Command' => $vendorDir . '/wp-cli/cache-command/src/Transient_Command.php',
    'UnhandledMatchError' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/UnhandledMatchError.php',
    'User_Application_Password_Command' => $vendorDir . '/wp-cli/entity-command/src/User_Application_Password_Command.php',
    'User_Command' => $vendorDir . '/wp-cli/entity-command/src/User_Command.php',
    'User_Meta_Command' => $vendorDir . '/wp-cli/entity-command/src/User_Meta_Command.php',
    'User_Session_Command' => $vendorDir . '/wp-cli/entity-command/src/User_Session_Command.php',
    'User_Term_Command' => $vendorDir . '/wp-cli/entity-command/src/User_Term_Command.php',
    'ValueError' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/ValueError.php',
    'WP_CLI' => $vendorDir . '/wp-cli/wp-cli/php/class-wp-cli.php',
    'WP_CLI\\Autoloader' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Autoloader.php',
    'WP_CLI\\Bootstrap\\AutoloaderStep' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Bootstrap/AutoloaderStep.php',
    'WP_CLI\\Bootstrap\\BootstrapState' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Bootstrap/BootstrapState.php',
    'WP_CLI\\Bootstrap\\BootstrapStep' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Bootstrap/BootstrapStep.php',
    'WP_CLI\\Bootstrap\\CheckRoot' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Bootstrap/CheckRoot.php',
    'WP_CLI\\Bootstrap\\ConfigureRunner' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Bootstrap/ConfigureRunner.php',
    'WP_CLI\\Bootstrap\\DeclareAbstractBaseCommand' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Bootstrap/DeclareAbstractBaseCommand.php',
    'WP_CLI\\Bootstrap\\DeclareFallbackFunctions' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Bootstrap/DeclareFallbackFunctions.php',
    'WP_CLI\\Bootstrap\\DeclareMainClass' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Bootstrap/DeclareMainClass.php',
    'WP_CLI\\Bootstrap\\DefineProtectedCommands' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Bootstrap/DefineProtectedCommands.php',
    'WP_CLI\\Bootstrap\\IncludeFallbackAutoloader' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Bootstrap/IncludeFallbackAutoloader.php',
    'WP_CLI\\Bootstrap\\IncludeFrameworkAutoloader' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Bootstrap/IncludeFrameworkAutoloader.php',
    'WP_CLI\\Bootstrap\\IncludePackageAutoloader' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Bootstrap/IncludePackageAutoloader.php',
    'WP_CLI\\Bootstrap\\IncludeRequestsAutoloader' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Bootstrap/IncludeRequestsAutoloader.php',
    'WP_CLI\\Bootstrap\\InitializeColorization' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Bootstrap/InitializeColorization.php',
    'WP_CLI\\Bootstrap\\InitializeContexts' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Bootstrap/InitializeContexts.php',
    'WP_CLI\\Bootstrap\\InitializeLogger' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Bootstrap/InitializeLogger.php',
    'WP_CLI\\Bootstrap\\LaunchRunner' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Bootstrap/LaunchRunner.php',
    'WP_CLI\\Bootstrap\\LoadDispatcher' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Bootstrap/LoadDispatcher.php',
    'WP_CLI\\Bootstrap\\LoadExecCommand' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Bootstrap/LoadExecCommand.php',
    'WP_CLI\\Bootstrap\\LoadRequiredCommand' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Bootstrap/LoadRequiredCommand.php',
    'WP_CLI\\Bootstrap\\LoadUtilityFunctions' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Bootstrap/LoadUtilityFunctions.php',
    'WP_CLI\\Bootstrap\\RegisterDeferredCommands' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Bootstrap/RegisterDeferredCommands.php',
    'WP_CLI\\Bootstrap\\RegisterFrameworkCommands' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Bootstrap/RegisterFrameworkCommands.php',
    'WP_CLI\\Bootstrap\\RunnerInstance' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Bootstrap/RunnerInstance.php',
    'WP_CLI\\CommandWithDBObject' => $vendorDir . '/wp-cli/entity-command/src/WP_CLI/CommandWithDBObject.php',
    'WP_CLI\\CommandWithMeta' => $vendorDir . '/wp-cli/entity-command/src/WP_CLI/CommandWithMeta.php',
    'WP_CLI\\CommandWithTerms' => $vendorDir . '/wp-cli/entity-command/src/WP_CLI/CommandWithTerms.php',
    'WP_CLI\\CommandWithTranslation' => $vendorDir . '/wp-cli/language-command/src/WP_CLI/CommandWithTranslation.php',
    'WP_CLI\\CommandWithUpgrade' => $vendorDir . '/wp-cli/extension-command/src/WP_CLI/CommandWithUpgrade.php',
    'WP_CLI\\Completions' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Completions.php',
    'WP_CLI\\ComposerIO' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/ComposerIO.php',
    'WP_CLI\\Configurator' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Configurator.php',
    'WP_CLI\\Context' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Context.php',
    'WP_CLI\\ContextManager' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/ContextManager.php',
    'WP_CLI\\Context\\Admin' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Context/Admin.php',
    'WP_CLI\\Context\\Auto' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Context/Auto.php',
    'WP_CLI\\Context\\Cli' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Context/Cli.php',
    'WP_CLI\\Context\\Frontend' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Context/Frontend.php',
    'WP_CLI\\Core\\CoreUpgrader' => $vendorDir . '/wp-cli/core-command/src/WP_CLI/Core/CoreUpgrader.php',
    'WP_CLI\\Core\\NonDestructiveCoreUpgrader' => $vendorDir . '/wp-cli/core-command/src/WP_CLI/Core/NonDestructiveCoreUpgrader.php',
    'WP_CLI\\DestructivePluginUpgrader' => $vendorDir . '/wp-cli/extension-command/src/WP_CLI/DestructivePluginUpgrader.php',
    'WP_CLI\\DestructiveThemeUpgrader' => $vendorDir . '/wp-cli/extension-command/src/WP_CLI/DestructiveThemeUpgrader.php',
    'WP_CLI\\Dispatcher\\CommandAddition' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Dispatcher/CommandAddition.php',
    'WP_CLI\\Dispatcher\\CommandFactory' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Dispatcher/CommandFactory.php',
    'WP_CLI\\Dispatcher\\CommandNamespace' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Dispatcher/CommandNamespace.php',
    'WP_CLI\\Dispatcher\\CompositeCommand' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Dispatcher/CompositeCommand.php',
    'WP_CLI\\Dispatcher\\RootCommand' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Dispatcher/RootCommand.php',
    'WP_CLI\\Dispatcher\\Subcommand' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Dispatcher/Subcommand.php',
    'WP_CLI\\DocParser' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/DocParser.php',
    'WP_CLI\\Embeds\\Cache_Command' => $vendorDir . '/wp-cli/embed-command/src/Cache_Command.php',
    'WP_CLI\\Embeds\\Embeds_Namespace' => $vendorDir . '/wp-cli/embed-command/src/Embeds_Namespace.php',
    'WP_CLI\\Embeds\\Fetch_Command' => $vendorDir . '/wp-cli/embed-command/src/Fetch_Command.php',
    'WP_CLI\\Embeds\\Handler_Command' => $vendorDir . '/wp-cli/embed-command/src/Handler_Command.php',
    'WP_CLI\\Embeds\\Provider_Command' => $vendorDir . '/wp-cli/embed-command/src/Provider_Command.php',
    'WP_CLI\\Embeds\\oEmbed' => $vendorDir . '/wp-cli/embed-command/src/oEmbed.php',
    'WP_CLI\\Exception\\NonExistentKeyException' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Exception/NonExistentKeyException.php',
    'WP_CLI\\ExitException' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/ExitException.php',
    'WP_CLI\\Extractor' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Extractor.php',
    'WP_CLI\\Fetchers\\Base' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Fetchers/Base.php',
    'WP_CLI\\Fetchers\\Comment' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Fetchers/Comment.php',
    'WP_CLI\\Fetchers\\Plugin' => $vendorDir . '/wp-cli/extension-command/src/WP_CLI/Fetchers/Plugin.php',
    'WP_CLI\\Fetchers\\Post' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Fetchers/Post.php',
    'WP_CLI\\Fetchers\\Signup' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Fetchers/Signup.php',
    'WP_CLI\\Fetchers\\Site' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Fetchers/Site.php',
    'WP_CLI\\Fetchers\\Theme' => $vendorDir . '/wp-cli/extension-command/src/WP_CLI/Fetchers/Theme.php',
    'WP_CLI\\Fetchers\\UnfilteredPlugin' => $vendorDir . '/wp-cli/checksum-command/src/WP_CLI/Fetchers/UnfilteredPlugin.php',
    'WP_CLI\\Fetchers\\User' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Fetchers/User.php',
    'WP_CLI\\FileCache' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/FileCache.php',
    'WP_CLI\\Formatter' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Formatter.php',
    'WP_CLI\\I18n\\BladeCodeExtractor' => $vendorDir . '/wp-cli/i18n-command/src/BladeCodeExtractor.php',
    'WP_CLI\\I18n\\BladeGettextExtractor' => $vendorDir . '/wp-cli/i18n-command/src/BladeGettextExtractor.php',
    'WP_CLI\\I18n\\BlockExtractor' => $vendorDir . '/wp-cli/i18n-command/src/BlockExtractor.php',
    'WP_CLI\\I18n\\CommandNamespace' => $vendorDir . '/wp-cli/i18n-command/src/CommandNamespace.php',
    'WP_CLI\\I18n\\FileDataExtractor' => $vendorDir . '/wp-cli/i18n-command/src/FileDataExtractor.php',
    'WP_CLI\\I18n\\IterableCodeExtractor' => $vendorDir . '/wp-cli/i18n-command/src/IterableCodeExtractor.php',
    'WP_CLI\\I18n\\JedGenerator' => $vendorDir . '/wp-cli/i18n-command/src/JedGenerator.php',
    'WP_CLI\\I18n\\JsCodeExtractor' => $vendorDir . '/wp-cli/i18n-command/src/JsCodeExtractor.php',
    'WP_CLI\\I18n\\JsFunctionsScanner' => $vendorDir . '/wp-cli/i18n-command/src/JsFunctionsScanner.php',
    'WP_CLI\\I18n\\JsonSchemaExtractor' => $vendorDir . '/wp-cli/i18n-command/src/JsonSchemaExtractor.php',
    'WP_CLI\\I18n\\MakeJsonCommand' => $vendorDir . '/wp-cli/i18n-command/src/MakeJsonCommand.php',
    'WP_CLI\\I18n\\MakeMoCommand' => $vendorDir . '/wp-cli/i18n-command/src/MakeMoCommand.php',
    'WP_CLI\\I18n\\MakePhpCommand' => $vendorDir . '/wp-cli/i18n-command/src/MakePhpCommand.php',
    'WP_CLI\\I18n\\MakePotCommand' => $vendorDir . '/wp-cli/i18n-command/src/MakePotCommand.php',
    'WP_CLI\\I18n\\MapCodeExtractor' => $vendorDir . '/wp-cli/i18n-command/src/MapCodeExtractor.php',
    'WP_CLI\\I18n\\PhpArrayGenerator' => $vendorDir . '/wp-cli/i18n-command/src/PhpArrayGenerator.php',
    'WP_CLI\\I18n\\PhpCodeExtractor' => $vendorDir . '/wp-cli/i18n-command/src/PhpCodeExtractor.php',
    'WP_CLI\\I18n\\PhpFunctionsScanner' => $vendorDir . '/wp-cli/i18n-command/src/PhpFunctionsScanner.php',
    'WP_CLI\\I18n\\PotGenerator' => $vendorDir . '/wp-cli/i18n-command/src/PotGenerator.php',
    'WP_CLI\\I18n\\UpdatePoCommand' => $vendorDir . '/wp-cli/i18n-command/src/UpdatePoCommand.php',
    'WP_CLI\\Inflector' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Inflector.php',
    'WP_CLI\\Iterators\\CSV' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Iterators/CSV.php',
    'WP_CLI\\Iterators\\Exception' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Iterators/Exception.php',
    'WP_CLI\\Iterators\\Query' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Iterators/Query.php',
    'WP_CLI\\Iterators\\Table' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Iterators/Table.php',
    'WP_CLI\\Iterators\\Transform' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Iterators/Transform.php',
    'WP_CLI\\JsonManipulator' => $vendorDir . '/wp-cli/package-command/src/WP_CLI/JsonManipulator.php',
    'WP_CLI\\LanguagePackUpgrader' => $vendorDir . '/wp-cli/language-command/src/WP_CLI/LanguagePackUpgrader.php',
    'WP_CLI\\Loggers\\Base' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Loggers/Base.php',
    'WP_CLI\\Loggers\\Execution' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Loggers/Execution.php',
    'WP_CLI\\Loggers\\Quiet' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Loggers/Quiet.php',
    'WP_CLI\\Loggers\\Regular' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Loggers/Regular.php',
    'WP_CLI\\MaintenanceMode\\MaintenanceModeCommand' => $vendorDir . '/wp-cli/maintenance-mode-command/src/MaintenanceModeCommand.php',
    'WP_CLI\\NoOp' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/NoOp.php',
    'WP_CLI\\PackageManagerEventSubscriber' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/PackageManagerEventSubscriber.php',
    'WP_CLI\\Package\\Compat\\Min_Composer_1_10\\NullIOMethodsTrait' => $vendorDir . '/wp-cli/package-command/src/WP_CLI/Package/Compat/Min_Composer_1_10/NullIOMethodsTrait.php',
    'WP_CLI\\Package\\Compat\\Min_Composer_2_3\\NullIOMethodsTrait' => $vendorDir . '/wp-cli/package-command/src/WP_CLI/Package/Compat/Min_Composer_2_3/NullIOMethodsTrait.php',
    'WP_CLI\\Package\\Compat\\NullIOMethodsTrait' => $vendorDir . '/wp-cli/package-command/src/WP_CLI/Package/Compat/NullIOMethodsTrait.php',
    'WP_CLI\\Package\\ComposerIO' => $vendorDir . '/wp-cli/package-command/src/WP_CLI/Package/ComposerIO.php',
    'WP_CLI\\ParsePluginNameInput' => $vendorDir . '/wp-cli/extension-command/src/WP_CLI/ParsePluginNameInput.php',
    'WP_CLI\\ParseThemeNameInput' => $vendorDir . '/wp-cli/extension-command/src/WP_CLI/ParseThemeNameInput.php',
    'WP_CLI\\Process' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Process.php',
    'WP_CLI\\ProcessRun' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/ProcessRun.php',
    'WP_CLI\\RequestsLibrary' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/RequestsLibrary.php',
    'WP_CLI\\Runner' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Runner.php',
    'WP_CLI\\SearchReplacer' => $vendorDir . '/wp-cli/search-replace-command/src/WP_CLI/SearchReplacer.php',
    'WP_CLI\\Shell\\REPL' => $vendorDir . '/wp-cli/shell-command/src/WP_CLI/Shell/REPL.php',
    'WP_CLI\\SynopsisParser' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/SynopsisParser.php',
    'WP_CLI\\SynopsisValidator' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/SynopsisValidator.php',
    'WP_CLI\\Traverser\\RecursiveDataStructureTraverser' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/Traverser/RecursiveDataStructureTraverser.php',
    'WP_CLI\\UpgraderSkin' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/UpgraderSkin.php',
    'WP_CLI\\WpHttpCacheManager' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/WpHttpCacheManager.php',
    'WP_CLI\\WpOrgApi' => $vendorDir . '/wp-cli/wp-cli/php/WP_CLI/WpOrgApi.php',
    'WP_CLI_Command' => $vendorDir . '/wp-cli/wp-cli/php/class-wp-cli-command.php',
    'WP_Export_Base_Writer' => $vendorDir . '/wp-cli/export-command/src/WP_Export_Base_Writer.php',
    'WP_Export_Exception' => $vendorDir . '/wp-cli/export-command/src/WP_Export_Exception.php',
    'WP_Export_File_Writer' => $vendorDir . '/wp-cli/export-command/src/WP_Export_File_Writer.php',
    'WP_Export_Oxymel' => $vendorDir . '/wp-cli/export-command/src/WP_Export_Oxymel.php',
    'WP_Export_Query' => $vendorDir . '/wp-cli/export-command/src/WP_Export_Query.php',
    'WP_Export_Returner' => $vendorDir . '/wp-cli/export-command/src/WP_Export_Returner.php',
    'WP_Export_Split_Files_Writer' => $vendorDir . '/wp-cli/export-command/src/WP_Export_Split_Files_Writer.php',
    'WP_Export_Term_Exception' => $vendorDir . '/wp-cli/export-command/src/WP_Export_Term_Exception.php',
    'WP_Export_WXR_Formatter' => $vendorDir . '/wp-cli/export-command/src/WP_Export_WXR_Formatter.php',
    'WP_Export_XML_Over_HTTP' => $vendorDir . '/wp-cli/export-command/src/WP_Export_XML_Over_HTTP.php',
    'WP_Iterator_Exception' => $vendorDir . '/wp-cli/export-command/src/WP_Iterator_Exception.php',
    'WP_Map_Iterator' => $vendorDir . '/wp-cli/export-command/src/WP_Map_Iterator.php',
    'WP_Post_IDs_Iterator' => $vendorDir . '/wp-cli/export-command/src/WP_Post_IDs_Iterator.php',
    'Widget_Command' => $vendorDir . '/wp-cli/widget-command/src/Widget_Command.php',
    'cli\\Arguments' => $vendorDir . '/wp-cli/php-cli-tools/lib/cli/Arguments.php',
    'cli\\Colors' => $vendorDir . '/wp-cli/php-cli-tools/lib/cli/Colors.php',
    'cli\\Memoize' => $vendorDir . '/wp-cli/php-cli-tools/lib/cli/Memoize.php',
    'cli\\Notify' => $vendorDir . '/wp-cli/php-cli-tools/lib/cli/Notify.php',
    'cli\\Progress' => $vendorDir . '/wp-cli/php-cli-tools/lib/cli/Progress.php',
    'cli\\Shell' => $vendorDir . '/wp-cli/php-cli-tools/lib/cli/Shell.php',
    'cli\\Streams' => $vendorDir . '/wp-cli/php-cli-tools/lib/cli/Streams.php',
    'cli\\Table' => $vendorDir . '/wp-cli/php-cli-tools/lib/cli/Table.php',
    'cli\\Tree' => $vendorDir . '/wp-cli/php-cli-tools/lib/cli/Tree.php',
    'cli\\arguments\\Argument' => $vendorDir . '/wp-cli/php-cli-tools/lib/cli/arguments/Argument.php',
    'cli\\arguments\\HelpScreen' => $vendorDir . '/wp-cli/php-cli-tools/lib/cli/arguments/HelpScreen.php',
    'cli\\arguments\\InvalidArguments' => $vendorDir . '/wp-cli/php-cli-tools/lib/cli/arguments/InvalidArguments.php',
    'cli\\arguments\\Lexer' => $vendorDir . '/wp-cli/php-cli-tools/lib/cli/arguments/Lexer.php',
    'cli\\notify\\Dots' => $vendorDir . '/wp-cli/php-cli-tools/lib/cli/notify/Dots.php',
    'cli\\notify\\Spinner' => $vendorDir . '/wp-cli/php-cli-tools/lib/cli/notify/Spinner.php',
    'cli\\progress\\Bar' => $vendorDir . '/wp-cli/php-cli-tools/lib/cli/progress/Bar.php',
    'cli\\table\\Ascii' => $vendorDir . '/wp-cli/php-cli-tools/lib/cli/table/Ascii.php',
    'cli\\table\\Renderer' => $vendorDir . '/wp-cli/php-cli-tools/lib/cli/table/Renderer.php',
    'cli\\table\\Tabular' => $vendorDir . '/wp-cli/php-cli-tools/lib/cli/table/Tabular.php',
    'cli\\tree\\Ascii' => $vendorDir . '/wp-cli/php-cli-tools/lib/cli/tree/Ascii.php',
    'cli\\tree\\Markdown' => $vendorDir . '/wp-cli/php-cli-tools/lib/cli/tree/Markdown.php',
    'cli\\tree\\Renderer' => $vendorDir . '/wp-cli/php-cli-tools/lib/cli/tree/Renderer.php',
    'eftec\\bladeone\\BladeOne' => $vendorDir . '/eftec/bladeone/lib/BladeOne.php',
    'eftec\\bladeone\\BladeOneCache' => $vendorDir . '/eftec/bladeone/lib/BladeOneCache.php',
    'eftec\\bladeone\\BladeOneCacheRedis' => $vendorDir . '/eftec/bladeone/lib/BladeOneCacheRedis.php',
    'eftec\\bladeone\\BladeOneCustom' => $vendorDir . '/eftec/bladeone/lib/BladeOneCustom.php',
    'eftec\\bladeone\\BladeOneHtml' => $vendorDir . '/eftec/bladeone/lib/BladeOneHtml.php',
    'eftec\\bladeone\\BladeOneHtmlBootstrap' => $vendorDir . '/eftec/bladeone/lib/BladeOneHtmlBootstrap.php',
    'eftec\\bladeone\\BladeOneLang' => $vendorDir . '/eftec/bladeone/lib/BladeOneLang.php',
    'setasign\\Fpdi\\FpdfTpl' => $vendorDir . '/setasign/fpdi/src/FpdfTpl.php',
    'setasign\\Fpdi\\FpdfTplTrait' => $vendorDir . '/setasign/fpdi/src/FpdfTplTrait.php',
    'setasign\\Fpdi\\FpdfTrait' => $vendorDir . '/setasign/fpdi/src/FpdfTrait.php',
    'setasign\\Fpdi\\Fpdi' => $vendorDir . '/setasign/fpdi/src/Fpdi.php',
    'setasign\\Fpdi\\FpdiException' => $vendorDir . '/setasign/fpdi/src/FpdiException.php',
    'setasign\\Fpdi\\FpdiTrait' => $vendorDir . '/setasign/fpdi/src/FpdiTrait.php',
    'setasign\\Fpdi\\GraphicsState' => $vendorDir . '/setasign/fpdi/src/GraphicsState.php',
    'setasign\\Fpdi\\Math\\Matrix' => $vendorDir . '/setasign/fpdi/src/Math/Matrix.php',
    'setasign\\Fpdi\\Math\\Vector' => $vendorDir . '/setasign/fpdi/src/Math/Vector.php',
    'setasign\\Fpdi\\PdfParser\\CrossReference\\AbstractReader' => $vendorDir . '/setasign/fpdi/src/PdfParser/CrossReference/AbstractReader.php',
    'setasign\\Fpdi\\PdfParser\\CrossReference\\CrossReference' => $vendorDir . '/setasign/fpdi/src/PdfParser/CrossReference/CrossReference.php',
    'setasign\\Fpdi\\PdfParser\\CrossReference\\CrossReferenceException' => $vendorDir . '/setasign/fpdi/src/PdfParser/CrossReference/CrossReferenceException.php',
    'setasign\\Fpdi\\PdfParser\\CrossReference\\FixedReader' => $vendorDir . '/setasign/fpdi/src/PdfParser/CrossReference/FixedReader.php',
    'setasign\\Fpdi\\PdfParser\\CrossReference\\LineReader' => $vendorDir . '/setasign/fpdi/src/PdfParser/CrossReference/LineReader.php',
    'setasign\\Fpdi\\PdfParser\\CrossReference\\ReaderInterface' => $vendorDir . '/setasign/fpdi/src/PdfParser/CrossReference/ReaderInterface.php',
    'setasign\\Fpdi\\PdfParser\\Filter\\Ascii85' => $vendorDir . '/setasign/fpdi/src/PdfParser/Filter/Ascii85.php',
    'setasign\\Fpdi\\PdfParser\\Filter\\Ascii85Exception' => $vendorDir . '/setasign/fpdi/src/PdfParser/Filter/Ascii85Exception.php',
    'setasign\\Fpdi\\PdfParser\\Filter\\AsciiHex' => $vendorDir . '/setasign/fpdi/src/PdfParser/Filter/AsciiHex.php',
    'setasign\\Fpdi\\PdfParser\\Filter\\FilterException' => $vendorDir . '/setasign/fpdi/src/PdfParser/Filter/FilterException.php',
    'setasign\\Fpdi\\PdfParser\\Filter\\FilterInterface' => $vendorDir . '/setasign/fpdi/src/PdfParser/Filter/FilterInterface.php',
    'setasign\\Fpdi\\PdfParser\\Filter\\Flate' => $vendorDir . '/setasign/fpdi/src/PdfParser/Filter/Flate.php',
    'setasign\\Fpdi\\PdfParser\\Filter\\FlateException' => $vendorDir . '/setasign/fpdi/src/PdfParser/Filter/FlateException.php',
    'setasign\\Fpdi\\PdfParser\\Filter\\Lzw' => $vendorDir . '/setasign/fpdi/src/PdfParser/Filter/Lzw.php',
    'setasign\\Fpdi\\PdfParser\\Filter\\LzwException' => $vendorDir . '/setasign/fpdi/src/PdfParser/Filter/LzwException.php',
    'setasign\\Fpdi\\PdfParser\\PdfParser' => $vendorDir . '/setasign/fpdi/src/PdfParser/PdfParser.php',
    'setasign\\Fpdi\\PdfParser\\PdfParserException' => $vendorDir . '/setasign/fpdi/src/PdfParser/PdfParserException.php',
    'setasign\\Fpdi\\PdfParser\\StreamReader' => $vendorDir . '/setasign/fpdi/src/PdfParser/StreamReader.php',
    'setasign\\Fpdi\\PdfParser\\Tokenizer' => $vendorDir . '/setasign/fpdi/src/PdfParser/Tokenizer.php',
    'setasign\\Fpdi\\PdfParser\\Type\\PdfArray' => $vendorDir . '/setasign/fpdi/src/PdfParser/Type/PdfArray.php',
    'setasign\\Fpdi\\PdfParser\\Type\\PdfBoolean' => $vendorDir . '/setasign/fpdi/src/PdfParser/Type/PdfBoolean.php',
    'setasign\\Fpdi\\PdfParser\\Type\\PdfDictionary' => $vendorDir . '/setasign/fpdi/src/PdfParser/Type/PdfDictionary.php',
    'setasign\\Fpdi\\PdfParser\\Type\\PdfHexString' => $vendorDir . '/setasign/fpdi/src/PdfParser/Type/PdfHexString.php',
    'setasign\\Fpdi\\PdfParser\\Type\\PdfIndirectObject' => $vendorDir . '/setasign/fpdi/src/PdfParser/Type/PdfIndirectObject.php',
    'setasign\\Fpdi\\PdfParser\\Type\\PdfIndirectObjectReference' => $vendorDir . '/setasign/fpdi/src/PdfParser/Type/PdfIndirectObjectReference.php',
    'setasign\\Fpdi\\PdfParser\\Type\\PdfName' => $vendorDir . '/setasign/fpdi/src/PdfParser/Type/PdfName.php',
    'setasign\\Fpdi\\PdfParser\\Type\\PdfNull' => $vendorDir . '/setasign/fpdi/src/PdfParser/Type/PdfNull.php',
    'setasign\\Fpdi\\PdfParser\\Type\\PdfNumeric' => $vendorDir . '/setasign/fpdi/src/PdfParser/Type/PdfNumeric.php',
    'setasign\\Fpdi\\PdfParser\\Type\\PdfStream' => $vendorDir . '/setasign/fpdi/src/PdfParser/Type/PdfStream.php',
    'setasign\\Fpdi\\PdfParser\\Type\\PdfString' => $vendorDir . '/setasign/fpdi/src/PdfParser/Type/PdfString.php',
    'setasign\\Fpdi\\PdfParser\\Type\\PdfToken' => $vendorDir . '/setasign/fpdi/src/PdfParser/Type/PdfToken.php',
    'setasign\\Fpdi\\PdfParser\\Type\\PdfType' => $vendorDir . '/setasign/fpdi/src/PdfParser/Type/PdfType.php',
    'setasign\\Fpdi\\PdfParser\\Type\\PdfTypeException' => $vendorDir . '/setasign/fpdi/src/PdfParser/Type/PdfTypeException.php',
    'setasign\\Fpdi\\PdfReader\\DataStructure\\Rectangle' => $vendorDir . '/setasign/fpdi/src/PdfReader/DataStructure/Rectangle.php',
    'setasign\\Fpdi\\PdfReader\\Page' => $vendorDir . '/setasign/fpdi/src/PdfReader/Page.php',
    'setasign\\Fpdi\\PdfReader\\PageBoundaries' => $vendorDir . '/setasign/fpdi/src/PdfReader/PageBoundaries.php',
    'setasign\\Fpdi\\PdfReader\\PdfReader' => $vendorDir . '/setasign/fpdi/src/PdfReader/PdfReader.php',
    'setasign\\Fpdi\\PdfReader\\PdfReaderException' => $vendorDir . '/setasign/fpdi/src/PdfReader/PdfReaderException.php',
    'setasign\\Fpdi\\TcpdfFpdi' => $vendorDir . '/setasign/fpdi/src/TcpdfFpdi.php',
    'setasign\\Fpdi\\Tcpdf\\Fpdi' => $vendorDir . '/setasign/fpdi/src/Tcpdf/Fpdi.php',
    'setasign\\Fpdi\\Tfpdf\\FpdfTpl' => $vendorDir . '/setasign/fpdi/src/Tfpdf/FpdfTpl.php',
    'setasign\\Fpdi\\Tfpdf\\Fpdi' => $vendorDir . '/setasign/fpdi/src/Tfpdf/Fpdi.php',
    'thiagoalessio\\TesseractOCR\\Command' => $vendorDir . '/thiagoalessio/tesseract_ocr/src/Command.php',
    'thiagoalessio\\TesseractOCR\\FeatureNotAvailableException' => $vendorDir . '/thiagoalessio/tesseract_ocr/src/FeatureNotAvailableException.php',
    'thiagoalessio\\TesseractOCR\\FriendlyErrors' => $vendorDir . '/thiagoalessio/tesseract_ocr/src/FriendlyErrors.php',
    'thiagoalessio\\TesseractOCR\\ImageNotFoundException' => $vendorDir . '/thiagoalessio/tesseract_ocr/src/ImageNotFoundException.php',
    'thiagoalessio\\TesseractOCR\\NoWritePermissionsForOutputFile' => $vendorDir . '/thiagoalessio/tesseract_ocr/src/NoWritePermissionsForOutputFile.php',
    'thiagoalessio\\TesseractOCR\\Option' => $vendorDir . '/thiagoalessio/tesseract_ocr/src/Option.php',
    'thiagoalessio\\TesseractOCR\\Process' => $vendorDir . '/thiagoalessio/tesseract_ocr/src/Process.php',
    'thiagoalessio\\TesseractOCR\\TesseractNotFoundException' => $vendorDir . '/thiagoalessio/tesseract_ocr/src/TesseractNotFoundException.php',
    'thiagoalessio\\TesseractOCR\\TesseractOCR' => $vendorDir . '/thiagoalessio/tesseract_ocr/src/TesseractOCR.php',
    'thiagoalessio\\TesseractOCR\\TesseractOcrException' => $vendorDir . '/thiagoalessio/tesseract_ocr/src/TesseractOcrException.php',
    'thiagoalessio\\TesseractOCR\\UnsuccessfulCommandException' => $vendorDir . '/thiagoalessio/tesseract_ocr/src/UnsuccessfulCommandException.php',
);
