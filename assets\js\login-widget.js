/**
 * Login Widget JavaScript
 * 
 * Gestisce le interazioni utente per il login widget
 */

(function($) {
    'use strict';
    
    // Variabile per tenere traccia se l'API Google è stata caricata
    let googleApiLoaded = false;
    
    // Inizializza quando il documento è pronto
    $(document).ready(function() {
        initLoginWidget();
    });
    
    /**
     * Inizializza il widget di login
     */
    function initLoginWidget() {
        // Switch tra tabs login e registrazione
        $('.login-tabs .tab').on('click', function() {
            const tabId = $(this).data('tab');
            
            // Attiva il tab con animazione
            $('.login-tabs .tab').removeClass('active');
            $(this).addClass('active');
            
            // Mostra il contenuto relativo con animazione di fade
            $('.tab-content').removeClass('active').fadeOut(100, function() {
                $('#' + tabId + '-tab').addClass('active').fadeIn(300);
            });
        });
        
        // Inizializza form di login
        $('#login-form').on('submit', function(e) {
            e.preventDefault();
            handleLoginSubmit($(this));
        });
        
        // Inizializza form di registrazione
        $('#registration-form').on('submit', function(e) {
            e.preventDefault();
            handleRegistrationSubmit($(this));
        });
        
        // Inizializza form di reset password
        $('#reset-form').on('submit', function(e) {
            e.preventDefault();
            handleResetPasswordSubmit($(this));
        });

        // Inizializza lo strength meter per la password
        $('#register-password').on('keyup', function() {
            updatePasswordStrength($(this).val());
        });

        // Aggiungi effetto di ripple ai pulsanti
        $('.login-button, .register-button').on('mousedown', function(e) {
            const $button = $(this);
            
            // Crea l'elemento ripple solo se non è già in corso
            if (!$button.find('.ripple').length) {
                const btnOffset = $button.offset();
                const xPos = e.pageX - btnOffset.left;
                const yPos = e.pageY - btnOffset.top;
                
                const $ripple = $('<span class="ripple"></span>').css({
                    width: $button.outerWidth(),
                    height: $button.outerHeight(),
                    top: 0,
                    left: 0,
                    background: 'radial-gradient(circle, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0) 70%)',
                    transform: 'scale(0)',
                    borderRadius: '50%',
                    position: 'absolute',
                    'transform-origin': `${xPos}px ${yPos}px`
                });
                
                $button.append($ripple);
                
                // Animazione del ripple
                setTimeout(function() {
                    $ripple.css('transform', 'scale(2.5)');
                    setTimeout(function() {
                        $ripple.fadeOut(300, function() {
                            $(this).remove();
                        });
                    }, 400);
                }, 10);
            }
        });

        // Aggiungi la classe di focus quando i campi di input sono attivi
        $('.form-group input').on('focus', function() {
            $(this).parent('.form-group').addClass('focused');
        }).on('blur', function() {
            $(this).parent('.form-group').removeClass('focused');
        });

        // Verifica se i pulsanti Google sono presenti nella pagina
        if ($('#google-login-button').length > 0 || $('#google-register-button').length > 0) {
            // Controlla se l'API Google è già caricata
            if (typeof google !== 'undefined' && google.accounts) {
                googleApiLoaded = true;
                initGoogleButtons();
            } else {
                // Carica lo script di Google se non è già stato caricato
                loadGoogleScript();
            }
        }
    }

    /**
     * Carica lo script di Google Sign-In
     */
    function loadGoogleScript() {
        if ($('#google-signin-script').length === 0) {
            console.log('Caricamento script Google Sign-In...');
            const script = document.createElement('script');
            script.id = 'google-signin-script';
            script.src = 'https://accounts.google.com/gsi/client';
            script.async = true;
            script.defer = true;
            
            // Imposta il callback quando lo script è caricato
            script.onload = function() {
                console.log('Script Google Sign-In caricato con successo');
                googleApiLoaded = true;
                
                // Attendi brevemente per assicurarsi che l'API sia completamente inizializzata
                setTimeout(function() {
                    initGoogleButtons();
                }, 100);
            };
            
            // Gestisci errori di caricamento
            script.onerror = function(error) {
                console.error('Errore nel caricamento dello script Google Sign-In:', error);
                $('.google-login-button, .google-button-container').hide();
                $('.google-login-error').show().text('Impossibile caricare il login con Google. Riprova più tardi.');
            };
            
            document.body.appendChild(script);
        }
    }

    /**
     * Inizializza i pulsanti di accesso Google
     */
    function initGoogleButtons() {
        // Se l'API Google non è caricata, esci
        if (!googleApiLoaded || typeof google === 'undefined' || !google.accounts) {
            console.warn('API Google non ancora disponibile. Impossibile inizializzare i pulsanti.');
            // Riproviamo dopo un breve periodo
            setTimeout(function() {
                if (typeof google !== 'undefined' && google.accounts) {
                    console.log('API Google rilevata al secondo tentativo');
                    initGoogleButtons();
                }
            }, 1000);
            return;
        }
        
        try {
            console.log('Inizializzazione pulsanti Google...');
            
            // Utilizza il client ID dai parametri globali
            const clientId = typeof googleAuthParams !== 'undefined' ? googleAuthParams.clientId : '';
            
            if (!clientId) {
                console.error('Client ID Google non trovato. Verifica la configurazione.');
                return;
            }

            // Configurazione per l'auto-prompt in caso di errori di autenticazione
            google.accounts.id.cancel();
            
            // Inizializza i pulsanti Google
            $('.google-login-button').each(function() {
                const context = $(this).data('context') || 'signin';
                const buttonId = $(this).attr('id');
                
                console.log('Inizializzazione Google button:', buttonId, 'con client ID:', clientId);
                
                try {
                    // Configura e renderizza il pulsante Google
                    google.accounts.id.initialize({
                        client_id: clientId,
                        callback: context === 'signin' ? handleGoogleLogin : handleGoogleRegister,
                        context: context,
                        ux_mode: 'popup',
                        auto_select: false, // Disabilitare la selezione automatica per evitare problemi
                        cancel_on_tap_outside: true, // Permette di chiudere il popup cliccando fuori
                    });
                    
                    // Assicurati che il contenitore del pulsante sia vuoto prima di renderizzare
                    const buttonContainer = document.getElementById(buttonId);
                    if (buttonContainer) {
                        // Rimuovi eventuali pulsanti esistenti
                        while (buttonContainer.firstChild) {
                            buttonContainer.removeChild(buttonContainer.firstChild);
                        }
                        
                        // Renderizza il nuovo pulsante con larghezza fissa invece di percentuale
                        google.accounts.id.renderButton(
                            buttonContainer,
                            { 
                                type: 'standard', 
                                theme: 'outline', 
                                size: 'large',
                                text: context === 'signin' ? 'signin_with' : 'signup_with',
                                shape: 'rectangular',
                                logo_alignment: 'center',
                                width: 240 // Larghezza fissa invece di percentuale
                            }
                        );
                        
                        // Rendi visibile il pulsante
                        $(buttonContainer).show();
                    } else {
                        console.error('Elemento con ID', buttonId, 'non trovato nel DOM');
                    }
                } catch (error) {
                    console.error('Errore nell\'inizializzazione del pulsante Google:', error);
                    $(this).closest('.google-button-container').append(
                        '<div class="google-error-message">Errore di inizializzazione. <a href="#" class="retry-google-init">Riprova</a></div>'
                    );
                    
                    // Aggiungi la possibilità di riprovare
                    $(this).closest('.google-button-container').find('.retry-google-init').on('click', function(e) {
                        e.preventDefault();
                        $(this).parent().remove();
                        initGoogleButtons();
                    });
                }
            });
            
            // Aggiunge il prompt una tantum per assicurarsi che l'utente sia autenticato
            // Questo aiuta a prevenire errori di token scaduto
            google.accounts.id.prompt((notification) => {
                if (notification.isNotDisplayed() || notification.isSkippedMoment()) {
                    console.log('Google One Tap non visualizzato:', notification.getNotDisplayedReason() || notification.getSkippedReason());
                }
            });
            
        } catch (error) {
            console.error('Errore generale nell\'inizializzazione dei pulsanti Google:', error);
        }
    }
    
    /**
     * Gestisce l'invio del form di login
     * 
     * @param {jQuery} $form Il form di login
     */
    function handleLoginSubmit($form) {
        // Resetta eventuali messaggi di errore
        resetFormMessages($form);
        
        // Valida il form
        const username = $form.find('input[name="username"]').val();
        const password = $form.find('input[name="password"]').val();
        const remember = $form.find('input[name="remember_me"]').is(':checked');
        
        // Validazione base
        if (!username || !password) {
            showFormMessage($form, loginWidgetParams.messages.requiredFields, 'error');
            highlightEmptyFields($form);
            return;
        }
        
        // Controlla se è un'email (opzionale, poiché ora accettiamo entrambi)
        if (username.includes('@') && !isValidEmail(username)) {
            showFormMessage($form, loginWidgetParams.messages.invalidEmail, 'error');
            highlightField($form.find('input[name="username"]'));
            return;
        }
        
        // Disabilita il pulsante durante l'invio
        const $submitButton = $form.find('button[type="submit"]');
        const originalText = $submitButton.text();
        $submitButton.prop('disabled', true).html('<span class="spinner"></span>' + originalText);
        
        // Invia la richiesta AJAX
        $.ajax({
            url: loginWidgetParams.ajaxUrl,
            type: 'POST',
            data: {
                action: 'unified_login',
                username: username,
                password: password,
                remember: remember,
                nonce: loginWidgetParams.nonce
            },            success: function(response) {
                if (response.success) {
                    // Login riuscito
                    // Mostra tutto il contenuto della risposta per debug 
                    showFormMessage($form, response.data, 'success');
                    
                    // Aggiungi classe di successo e mostra effetto di caricamento
                    $form.addClass('login-success');
                    
                    // Salva l'URL di reindirizzamento in una variabile
                    const redirectUrl = response.data.redirect_url;
                    
                    // Aggiungi un pulsante di reindirizzamento manuale
                    $form.find('.form-message').append(
                        '<div class="redirect-controls">' +
                        '<p>Reindirizzamento a: <strong>' + redirectUrl + '</strong></p>' +
                        '<button type="button" class="manual-redirect">Vai ora</button>' +
                        '</div>'
                    );
                    
                    // Gestisci il click sul pulsante di reindirizzamento
                    $form.find('.manual-redirect').on('click', function() {
                        window.location.href = redirectUrl;
                    });
                    
                    // Reindirizza automaticamente dopo 3 secondi
                    setTimeout(function() {
                        window.location.href = redirectUrl;
                    }, 3000);
                } else {
                    // Login fallito
                    showFormMessage($form, response.data.message, 'error');
                    
                    // Animazione shake per il form
                    $form.removeClass('shake');
                    setTimeout(function() {
                        $form.addClass('shake');
                    }, 10);
                    
                    // Se l'account non è attivato, mostra un link per richiedere una nuova email
                    if (response.data.not_activated) {
                        $form.find('.form-message').append(
                            '<p><a href="#" class="resend-activation" data-email="' + username + '">' +
                            'Invia nuovamente email di attivazione</a></p>'
                        );
                        
                        // Inizializza il link per richiedere una nuova email
                        $('.resend-activation').on('click', function(e) {
                            e.preventDefault();
                            requestNewActivationEmail($(this).data('email'));
                        });
                    }
                }
            },
            error: function() {
                showFormMessage($form, loginWidgetParams.messages.ajaxError, 'error');
            },
            complete: function() {
                // Riattiva il pulsante
                $submitButton.prop('disabled', false).text(originalText);
            }
        });
    }
    
    /**
     * Gestisce l'invio del form di registrazione
     * 
     * @param {jQuery} $form Il form di registrazione
     */
    function handleRegistrationSubmit($form) {
        // Resetta eventuali messaggi di errore
        resetFormMessages($form);
        
        // Valida il form
        const email = $form.find('input[name="email"]').val();
        const username = $form.find('input[name="username"]').val();
        const password = $form.find('input[name="password"]').val();
        const confirmPassword = $form.find('input[name="confirm_password"]').val();
        const privacyAccepted = $form.find('input[name="privacy_policy"]').is(':checked');
        
        // Validazione base
        if (!email || !username || !password || !confirmPassword) {
            showFormMessage($form, loginWidgetParams.messages.requiredFields, 'error');
            highlightEmptyFields($form);
            return;
        }
        
        if (!isValidEmail(email)) {
            showFormMessage($form, loginWidgetParams.messages.invalidEmail, 'error');
            highlightField($form.find('input[name="email"]'));
            return;
        }
        
        if (password.length < 8) {
            showFormMessage($form, loginWidgetParams.messages.passwordTooShort, 'error');
            highlightField($form.find('input[name="password"]'));
            return;
        }
        
        if (password !== confirmPassword) {
            showFormMessage($form, loginWidgetParams.messages.passwordMismatch, 'error');
            highlightField($form.find('input[name="confirm_password"]'));
            return;
        }
        
        if (!privacyAccepted) {
            showFormMessage($form, loginWidgetParams.messages.privacyRequired, 'error');
            $form.find('.privacy-policy').addClass('privacy-error').fadeIn(200).fadeOut(200).fadeIn(200);
            return;
        }
        
        // Disabilita il pulsante durante l'invio
        const $submitButton = $form.find('button[type="submit"]');
        const originalText = $submitButton.text();
        $submitButton.prop('disabled', true).html('<span class="spinner"></span>' + originalText);
        
        // Invia la richiesta AJAX
        $.ajax({
            url: loginWidgetParams.ajaxUrl,
            type: 'POST',
            data: {
                action: 'email_registration',
                email: email,
                username: username,
                password: password,
                confirm_password: confirmPassword,
                privacy_policy: privacyAccepted,
                nonce: loginWidgetParams.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Registrazione riuscita
                    $form[0].reset(); // Resetta il form
                    showFormMessage($form, response.data.message, 'success');
                    
                    // Nasconde il form di registrazione e mostra solo il messaggio di successo
                    $form.find('.form-group, .privacy-policy, button').slideUp(300);
                    
                    // Scorri alla posizione del messaggio
                    $('html, body').animate({
                        scrollTop: $form.find('.form-message').offset().top - 100
                    }, 500);
                } else {
                    // Registrazione fallita
                    showFormMessage($form, response.data.message, 'error');
                    
                    // Animazione shake per il form
                    $form.removeClass('shake');
                    setTimeout(function() {
                        $form.addClass('shake');
                    }, 10);
                }
            },
            error: function() {
                showFormMessage($form, loginWidgetParams.messages.ajaxError, 'error');
            },
            complete: function() {
                // Riattiva il pulsante
                $submitButton.prop('disabled', false).text(originalText);
            }
        });
    }
    
    /**
     * Gestisce l'invio del form di reset password
     * 
     * @param {jQuery} $form Il form di reset password
     */
    function handleResetPasswordSubmit($form) {
        // Resetta eventuali messaggi di errore
        resetFormMessages($form);
        
        // Valida il form
        const email = $form.find('input[name="reset_email"]').val();
        
        // Validazione base
        if (!email) {
            showFormMessage($form, loginWidgetParams.messages.requiredFields, 'error');
            highlightEmptyFields($form);
            return;
        }
        
        if (!isValidEmail(email)) {
            showFormMessage($form, loginWidgetParams.messages.invalidEmail, 'error');
            highlightField($form.find('input[name="reset_email"]'));
            return;
        }
        
        // Disabilita il pulsante durante l'invio
        const $submitButton = $form.find('button[type="submit"]');
        const originalText = $submitButton.text();
        $submitButton.prop('disabled', true).html('<span class="spinner"></span>' + originalText);
        
        // Invia la richiesta AJAX
        $.ajax({
            url: loginWidgetParams.ajaxUrl,
            type: 'POST',
            data: {
                action: 'request_password_reset',
                email: email,
                nonce: loginWidgetParams.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Reset password richiesto con successo
                    showFormMessage($form, response.data.message, 'success');
                    $form.find('input').val(''); // Pulisce il form
                } else {
                    // Errore
                    showFormMessage($form, response.data.message, 'error');
                    
                    // Animazione shake per il form
                    $form.removeClass('shake');
                    setTimeout(function() {
                        $form.addClass('shake');
                    }, 10);
                }
            },
            error: function() {
                showFormMessage($form, loginWidgetParams.messages.resetError, 'error');
            },
            complete: function() {
                // Riattiva il pulsante
                $submitButton.prop('disabled', false).text(originalText);
            }
        });
    }
    
    /**
     * Richiede una nuova email di attivazione
     * 
     * @param {string} email L'email da riattivare
     */
    function requestNewActivationEmail(email) {
        const $link = $('.resend-activation');
        const originalText = $link.text();
        
        // Mostra stato di caricamento
        $link.addClass('loading').text('Invio in corso...');
        
        // Invia la richiesta AJAX per richiedere una nuova email
        $.ajax({
            url: loginWidgetParams.ajaxUrl,
            type: 'POST',
            data: {
                action: 'resend_activation_email',
                email: email,
                nonce: loginWidgetParams.nonce
            },
            success: function(response) {
                if (response.success) {
                    $link.text('Email inviata con successo!').addClass('success-text');
                    setTimeout(function() {
                        $link.text(originalText).removeClass('loading success-text');
                    }, 3000);
                } else {
                    $link.text(response.data.message).addClass('error-text');
                    setTimeout(function() {
                        $link.text(originalText).removeClass('loading error-text');
                    }, 3000);
                }
            },
            error: function() {
                $link.text('Si è verificato un errore. Riprova più tardi.').addClass('error-text');
                setTimeout(function() {
                    $link.text(originalText).removeClass('loading error-text');
                }, 3000);
            }
        });
    }
    
    /**
     * Aggiorna l'indicatore di robustezza della password
     * 
     * @param {string} password La password da valutare
     */
    function updatePasswordStrength(password) {
        const $meter = $('.password-strength-meter');
        const $bar = $meter.find('.strength-bar');
        const $text = $meter.find('.strength-text');
        
        // Mostra il meter solo se c'è una password
        if (!password) {
            $meter.fadeOut(200);
            return;
        }
        
        $meter.fadeIn(200);
        
        // Calcola la robustezza
        let strength = 0;
        
        // Lunghezza minima (8 caratteri)
        if (password.length >= 8) {
            strength += 1;
        }
        
        // Contiene lettere minuscole
        if (/[a-z]/.test(password)) {
            strength += 1;
        }
        
        // Contiene lettere maiuscole
        if (/[A-Z]/.test(password)) {
            strength += 1;
        }
        
        // Contiene numeri
        if (/[0-9]/.test(password)) {
            strength += 1;
        }
        
        // Contiene caratteri speciali
        if (/[^a-zA-Z0-9]/.test(password)) {
            strength += 1;
        }
        
        // Aggiorna la UI
        let strengthClass, strengthText, strengthPercentage;
        
        switch (strength) {
            case 0:
            case 1:
                strengthClass = 'weak';
                strengthText = loginWidgetParams.messages.passwordWeak;
                strengthPercentage = 20;
                break;
            case 2:
            case 3:
                strengthClass = 'medium';
                strengthText = loginWidgetParams.messages.passwordMedium;
                strengthPercentage = 50;
                break;
            case 4:
                strengthClass = 'strong';
                strengthText = loginWidgetParams.messages.passwordStrong;
                strengthPercentage = 80;
                break;
            case 5:
                strengthClass = 'very-strong';
                strengthText = loginWidgetParams.messages.passwordVeryStrong;
                strengthPercentage = 100;
                break;
        }
        
        // Aggiorna la barra con animazione
        $bar.removeClass('weak medium strong very-strong')
            .addClass(strengthClass)
            .css('width', '0%')
            .animate({ width: strengthPercentage + '%' }, 300);
        
        // Aggiorna il testo
        $text.text(strengthText);
    }
    
    /**
     * Mostra un messaggio nel form
     * 
     * @param {jQuery} $form Il form
     * @param {string} message Il messaggio da mostrare
     * @param {string} type Il tipo di messaggio (success|error|info)
     */    function showFormMessage($form, message, type) {
        const $messageContainer = $form.find('.form-message');
        
        // Remove existing classes and add new ones
        $messageContainer.removeClass('success error info showing')
                         .addClass(type);
        
        // Prima mostra il contenitore che è nascosto
        $messageContainer.show();
        
        // Check if message is a JSON object or string
        try {
            let jsonObj = null;
            let messageText = '';
            
            // Check if message is already an object
            if (typeof message === 'object' && message !== null) {
                jsonObj = message;
                // Estrai il messaggio di testo se presente
                if (message.message) {
                    messageText = message.message;
                }
            } else if (typeof message === 'string') {
                messageText = message;
                // Try to parse as JSON if it starts with { or [
                if ((message.trim().startsWith('{') && message.trim().endsWith('}')) || 
                    (message.trim().startsWith('[') && message.trim().endsWith(']'))) {
                    try {
                        jsonObj = JSON.parse(message);
                    } catch (e) {
                        // Ignora l'errore di parsing
                    }
                }
            }
            
            // Imposta il contenuto base con il messaggio di testo
            $messageContainer.html('<p>' + messageText + '</p>');
            
            // Aggiungi i dettagli JSON se presenti
            if (jsonObj && type === 'success') {
                // Non mostrare l'oggetto JSON completo per i messaggi di successo, 
                // ma evidenzia solo info utili
                if (jsonObj.redirect_url) {
                    $messageContainer.append('<p class="redirect-info">Redirect URL: ' + jsonObj.redirect_url + '</p>');
                }
                if (jsonObj.user_type) {
                    $messageContainer.append('<p class="user-type-info">Tipo utente: ' + jsonObj.user_type + '</p>');
                }
            } else if (jsonObj) {
                // Per gli errori o altri tipi, mostra il JSON completo
                const formattedJson = JSON.stringify(jsonObj, null, 2);
                $messageContainer.append('<pre>' + formattedJson + '</pre>');
            }
        } catch (e) {
            // In caso di errore, mostra un messaggio semplice
            console.error("Errore nella formattazione del messaggio:", e);
            $messageContainer.html('<p>' + (typeof message === 'string' ? message : 'Si è verificato un errore') + '</p>');
        }
        
        // Use setTimeout to ensure the DOM has updated before adding the animation class
        setTimeout(function() {
            $messageContainer.addClass('showing');
        }, 10);
        
        // If it's a success message, add a subtle checkmark icon
        if (type === 'success') {
            $messageContainer.prepend('<span class="message-icon">✓</span> ');
        } else if (type === 'error') {
            $messageContainer.prepend('<span class="message-icon">!</span> ');
        }
    }
    
    /**
     * Resetta i messaggi nel form
     * 
     * @param {jQuery} $form Il form
     */
    function resetFormMessages($form) {
        const $messageContainer = $form.find('.form-message');
        $messageContainer.removeClass('success error info').empty().hide();
        
        // Rimuovi anche l'evidenziazione dei campi di errore
        $form.find('.input-error').removeClass('input-error');
        $form.find('.privacy-error').removeClass('privacy-error');
        $form.removeClass('shake');
    }
    
    /**
     * Evidenzia i campi vuoti nel form
     * 
     * @param {jQuery} $form Il form
     */
    function highlightEmptyFields($form) {
        $form.find('input[type="text"], input[type="email"], input[type="password"]').each(function() {
            if (!$(this).val()) {
                highlightField($(this));
            }
        });
    }
    
    /**
     * Evidenzia un campo specifico
     * 
     * @param {jQuery} $field Il campo da evidenziare
     */
    function highlightField($field) {
        $field.addClass('input-error').parent('.form-group').addClass('has-error');
        
        // Rimuovi la classe dopo un po' o quando l'utente digita
        $field.one('focus', function() {
            $(this).removeClass('input-error').parent('.form-group').removeClass('has-error');
        });
    }
    
    /**
     * Verifica se un'email è valida
     * 
     * @param {string} email L'email da verificare
     * @return {boolean} True se l'email è valida
     */
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    /**
     * Gestisce il login tramite Google
     * 
     * @param {Object} response Risposta da Google Identity Services
     */
    function handleGoogleLogin(response) {
        if (!response || !response.credential) {
            console.error('Risposta Google non valida');
            showGlobalMessage('Errore durante l\'accesso con Google. Riprova più tardi.', 'error');
            return;
        }
        
        console.log('Login Google in corso...');
        console.log('Token ricevuto, lunghezza: ' + response.credential.length);
        
        // Mostra messaggio di caricamento
        showGlobalMessage('Accesso in corso...', 'info');
        
        // Invia il token ID al server
        $.ajax({
            url: loginWidgetParams.ajaxUrl,
            type: 'POST',
            data: {
                action: 'google_login',
                id_token: response.credential,
                nonce: loginWidgetParams.nonce
            },
            timeout: 30000, // Timeout di 30 secondi
            success: function(response) {
                if (response.success) {
                    showGlobalMessage(response.data.message, 'success');
                    
                    // Reindirizza dopo un breve ritardo
                    setTimeout(function() {
                        window.location.href = response.data.redirect_url;
                    }, 1000);
                } else {
                    showGlobalMessage(response.data.message, 'error');
                    
                    // Se l'utente non esiste, suggerisci la registrazione
                    if (response.data.no_account) {
                        $('#login-tab').removeClass('active');
                        $('#register-tab').addClass('active');
                        $('.login-tabs .tab[data-tab="login"]').removeClass('active');
                        $('.login-tabs .tab[data-tab="register"]').addClass('active');
                        
                        // Suggerisci di usare il pulsante di registrazione Google
                        showFormMessage($('#registration-form'), 'Puoi usare lo stesso account Google per registrarti.', 'info');
                    }
                }
            },
            error: function(xhr, status, error) {
                console.error('Errore AJAX durante login Google:', status, error);
                
                let errorMessage = 'Si è verificato un errore durante l\'elaborazione della richiesta. ';
                
                if (xhr.status === 400 || xhr.status === 401) {
                    errorMessage += 'Il token di autenticazione Google potrebbe non essere valido o scaduto. ';
                    
                    // Aggiunge un pulsante per riprovare l'autenticazione
                    showGlobalMessage(errorMessage + 'Clicca il pulsante per riprovare.', 'error');
                    
                    $('.global-message').append('<p><button type="button" class="retry-google-auth">Riprova autenticazione</button></p>');
                    $('.retry-google-auth').on('click', function() {
                        // Rimuovi il pulsante
                        $(this).parent().remove();
                        
                        // Mostra stato di attesa
                        showGlobalMessage('Riavvio autenticazione Google...', 'info');
                        
                        // Reset e riavvio dell'autenticazione Google
                        if (typeof google !== 'undefined' && google.accounts) {
                            google.accounts.id.cancel();
                            setTimeout(() => {
                                // Pulizia dei token precedenti
                                google.accounts.id.disableAutoSelect();
                                
                                // Reinizializza i pulsanti
                                initGoogleButtons();
                                
                                // Aggiorna il messaggio
                                showGlobalMessage('Autenticazione ripristinata. Per favore, prova di nuovo il login con Google.', 'info');
                            }, 500);
                        } else {
                            // Se l'API Google non è disponibile, ricarica la pagina
                            window.location.reload();
                        }
                    });
                } else {
                    try {
                        if (xhr.responseText) {
                            console.log('Risposta errore 400:', xhr.responseText);
                            var jsonResponse = JSON.parse(xhr.responseText);
                            if (jsonResponse && jsonResponse.data && jsonResponse.data.message) {
                                errorMessage += ' Dettaglio: ' + jsonResponse.data.message;
                            }
                        }
                    } catch(e) {
                        console.error('Errore nel parse della risposta:', e);
                        errorMessage += ' Si è verificato un errore imprevisto. Riprova più tardi o contatta l\'assistenza.';
                        showGlobalMessage(errorMessage, 'error');
                    }
                    
                    showGlobalMessage(errorMessage + 'Riprova più tardi o contatta l\'assistenza.', 'error');
                }
            }
        });
    }
    
    /**
     * Gestisce la registrazione tramite Google
     * 
     * @param {Object} response Risposta da Google Identity Services
     */
    function handleGoogleRegister(response) {
        if (!response || !response.credential) {
            console.error('Risposta Google non valida');
            showGlobalMessage('Errore durante la registrazione con Google. Riprova più tardi.', 'error');
            return;
        }
        
        console.log('Registrazione Google in corso...');
        console.log('Token ricevuto (primi 10 caratteri per sicurezza): ' + response.credential.substring(0, 10) + '...');
        console.log('Lunghezza token: ' + response.credential.length);
        
        // Mostra messaggio di caricamento
        showGlobalMessage('Registrazione in corso...', 'info');
        
        // Invia il token ID al server con alcune opzioni aggiuntive
        $.ajax({
            url: loginWidgetParams.ajaxUrl,
            type: 'POST',
            data: {
                action: 'google_register',
                id_token: response.credential,
                nonce: loginWidgetParams.nonce
            },
            timeout: 60000, // Aumenta il timeout a 60 secondi
            success: function(response) {
                if (response.success) {
                    showGlobalMessage(response.data.message, 'success');
                    
                    // Se è necessaria un'ulteriore conferma
                    if (response.data.needs_confirmation) {
                        // Mostra form di conferma o altre azioni necessarie
                    } else {
                        // Reindirizza dopo un breve ritardo
                        setTimeout(function() {
                            window.location.href = response.data.redirect_url;
                        }, 1000);
                    }
                } else {
                    showGlobalMessage(response.data.message, 'error');
                    
                    // Se l'account esiste già, suggerisci il login
                    if (response.data.account_exists) {
                        $('#register-tab').removeClass('active');
                        $('#login-tab').addClass('active');
                        $('.login-tabs .tab[data-tab="register"]').removeClass('active');
                        $('.login-tabs .tab[data-tab="login"]').addClass('active');
                        
                        // Suggerisci di usare il pulsante di login Google
                        showFormMessage($('#login-form'), 'Puoi usare lo stesso account Google per accedere.', 'info');
                    }
                }
            },
            error: function(xhr, status, error) {
                console.error('Errore nella richiesta AJAX durante la registrazione Google:');
                console.error('Status: ' + status);
                console.error('Error: ' + error);
                console.error('Status code: ' + xhr.status);
                
                let errorMessage = 'Si è verificato un errore durante l\'elaborazione della richiesta. ';
                
                // Gestione specifica dell'errore 400
                if (xhr.status === 400) {
                    errorMessage += 'Il token di autenticazione Google potrebbe non essere valido o scaduto. ';
                    
                    // Prova a ottenere maggiori informazioni sull'errore
                    try {
                        if (xhr.responseText) {
                            console.log('Risposta errore 400:', xhr.responseText);
                            var jsonResponse = JSON.parse(xhr.responseText);
                            if (jsonResponse && jsonResponse.data && jsonResponse.data.message) {
                                errorMessage += ' Dettaglio: ' + jsonResponse.data.message;
                            }
                        }
                    } catch(e) {
                        console.error('Errore nel parse della risposta:', e);
                        errorMessage += ' Si è verificato un errore imprevisto. Riprova più tardi o contatta l\'assistenza.';
                        showGlobalMessage(errorMessage, 'error');
                    }
                    
                    // Aggiungi un pulsante per riprovare l'autenticazione
                    showGlobalMessage(errorMessage, 'error');
                    
                    $('.global-message').append('<p><button type="button" class="retry-google-auth">Riprova autenticazione</button></p>');
                    $('.retry-google-auth').on('click', function() {
                        // Rimuovi il pulsante
                        $(this).parent().remove();
                        
                        // Mostra stato di attesa
                        showGlobalMessage('Riavvio autenticazione Google...', 'info');
                        
                        // Reset e riavvio dell'autenticazione Google
                        if (typeof google !== 'undefined' && google.accounts) {
                            google.accounts.id.cancel();
                            setTimeout(() => {
                                // Pulizia dei token precedenti
                                google.accounts.id.disableAutoSelect();
                                
                                // Reinizializza i pulsanti
                                initGoogleButtons();
                                
                                // Aggiorna il messaggio
                                showGlobalMessage('Autenticazione ripristinata. Per favore, prova di nuovo la registrazione con Google.', 'info');
                            }, 500);
                        } else {
                            // Se l'API Google non è disponibile, ricarica la pagina
                            window.location.reload();
                        }
                    });
                } 
                // Per altri errori HTTP
                else if (xhr.status) {
                    errorMessage += 'Codice di errore: ' + xhr.status;
                    
                    if (xhr.status === 0 && status === 'timeout') {
                        errorMessage = 'La richiesta è scaduta. Verifica la tua connessione internet e riprova.';
                    } else if (xhr.status === 403) {
                        errorMessage = 'Errore di autorizzazione. Il nonce di sicurezza potrebbe essere scaduto. Ricarica la pagina e riprova.';
                    } else if (xhr.status === 500) {
                        errorMessage = 'Errore interno del server. Riprova più tardi o contatta l\'amministratore.';
                    }
                }
                
                showGlobalMessage(errorMessage, 'error');
                
                // Aggiungi un pulsante per ricaricare la pagina
                $('.global-message').append('<p><button type="button" class="reload-page-button">Ricarica pagina</button></p>');
                $('.reload-page-button').on('click', function() {
                    window.location.reload();
                });
            }
        });
    }
    
    /**
     * Mostra un messaggio globale sul widget
     * 
     * @param {string} message Il messaggio da mostrare
     * @param {string} type Il tipo di messaggio (success|error|info)
     */
    function showGlobalMessage(message, type) {
        let $messageContainer = $('.login-widget-container').find('.global-message');
        
        // Se il contenitore non esiste, crealo
        if ($messageContainer.length === 0) {
            $messageContainer = $('<div class="global-message"></div>');
            $('.login-widget-container').prepend($messageContainer);
        }
        
        $messageContainer.removeClass('success error info')
                         .addClass(type)
                         .html('<p>' + message + '</p>')
                         .hide()
                         .slideDown(300);
    }
    
})(jQuery);