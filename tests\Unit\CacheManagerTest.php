<?php

namespace Tests\Unit;

use Tests\BaseTestCase;
use Mockery;

/**
 * Unit tests for Office_Addin_Cache_Manager class
 */
class CacheManagerTest extends BaseTestCase
{
    private $cache_manager;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Include the cache manager class
        require_once __DIR__ . '/../../includes/class-office-addin-cache-manager.php';
        
        $this->cache_manager = new \Office_Addin_Cache_Manager();
    }

    public function testSetAndGetCache(): void
    {
        $key = 'test_key';
        $value = ['data' => 'test_value'];
        $group = 'settings';
        
        // Mock wp_cache_set to return true
        \Brain\Monkey\Functions\when('wp_cache_set')->justReturn(true);
        \Brain\Monkey\Functions\when('wp_cache_get')->justReturn($value);
        
        $set_result = $this->cache_manager->set($key, $value, $group);
        $this->assertTrue($set_result);
        
        $get_result = $this->cache_manager->get($key, $group);
        $this->assertEquals($value, $get_result);
    }

    public function testGetReturnsDefaultWhenCacheMiss(): void
    {
        $key = 'nonexistent_key';
        $default = 'default_value';
        $group = 'settings';
        
        // Mock cache miss
        \Brain\Monkey\Functions\when('wp_cache_get')->justReturn(false);
        
        $result = $this->cache_manager->get($key, $group, $default);
        
        $this->assertEquals($default, $result);
    }

    public function testDeleteRemovesFromCache(): void
    {
        $key = 'test_key';
        $group = 'settings';
        
        \Brain\Monkey\Functions\when('wp_cache_delete')->justReturn(true);
        
        $result = $this->cache_manager->delete($key, $group);
        
        $this->assertTrue($result);
    }

    public function testFlushGroupClearsGroupCache(): void
    {
        $group = 'settings';
        
        \Brain\Monkey\Functions\when('wp_cache_flush_group')->justReturn(true);
        
        $result = $this->cache_manager->flush_group($group);
        
        $this->assertTrue($result);
    }

    public function testInvalidateVersionIncrementsVersion(): void
    {
        \Brain\Monkey\Functions\when('get_option')->justReturn('1');
        \Brain\Monkey\Functions\when('update_option')->justReturn(true);
        
        $result = $this->cache_manager->invalidate('settings');
        
        $this->assertTrue($result);
    }

    public function testCacheKeyGenerationWithVersion(): void
    {
        \Brain\Monkey\Functions\when('get_option')->justReturn('5');
        
        $key = $this->callMethod($this->cache_manager, 'get_versioned_key', ['test_key', 'settings']);
        
        $this->assertEquals('test_key_v5', $key);
    }

    public function testWarmUpPreloadsCommonData(): void
    {
        \Brain\Monkey\Functions\when('get_option')->justReturn('test_value');
        \Brain\Monkey\Functions\when('wp_cache_set')->justReturn(true);
        
        $result = $this->cache_manager->warm_up();
        
        $this->assertTrue($result);
    }

    public function testGetStatsReturnsUsageData(): void
    {
        // Mock cache stats
        \Brain\Monkey\Functions\when('wp_cache_get')
            ->justReturn(['hits' => 100, 'misses' => 20, 'sets' => 80]);
        
        $stats = $this->cache_manager->get_stats();
        
        $this->assertIsArray($stats);
        $this->assertArrayHasKey('hits', $stats);
        $this->assertArrayHasKey('misses', $stats);
        $this->assertArrayHasKey('hit_ratio', $stats);
        
        $this->assertEquals(100, $stats['hits']);
        $this->assertEquals(20, $stats['misses']);
        $this->assertEquals(0.83, $stats['hit_ratio'], '', 0.01); // 100/(100+20)
    }

    public function testSetWithTtlUsesCorrectExpiration(): void
    {
        $key = 'test_key';
        $value = 'test_value';
        $group = 'api_results';
        $ttl = 3600;
        
        \Brain\Monkey\Functions\when('wp_cache_set')
            ->with($key, \Mockery::any(), $group, $ttl)
            ->andReturn(true);
        
        $result = $this->cache_manager->set($key, $value, $group, $ttl);
        
        $this->assertTrue($result);
    }

    public function testIncrementCreatesCounterIfNotExists(): void
    {
        $key = 'counter_key';
        $group = 'counters';
        
        // First get returns false (counter doesn't exist)
        \Brain\Monkey\Functions\when('wp_cache_get')->justReturn(false);
        \Brain\Monkey\Functions\when('wp_cache_set')->justReturn(true);
        
        $result = $this->cache_manager->increment($key, $group);
        
        $this->assertEquals(1, $result);
    }

    public function testIncrementIncrementsExistingCounter(): void
    {
        $key = 'counter_key';
        $group = 'counters';
        
        // Mock existing counter value
        \Brain\Monkey\Functions\when('wp_cache_get')->justReturn(5);
        \Brain\Monkey\Functions\when('wp_cache_set')->justReturn(true);
        
        $result = $this->cache_manager->increment($key, $group, 3);
        
        $this->assertEquals(8, $result); // 5 + 3
    }

    public function testHashKeyGeneratesConsistentHash(): void
    {
        $data = ['param1' => 'value1', 'param2' => 'value2'];
        
        $hash1 = $this->callMethod($this->cache_manager, 'hash_key', [$data]);
        $hash2 = $this->callMethod($this->cache_manager, 'hash_key', [$data]);
        
        $this->assertEquals($hash1, $hash2);
        $this->assertIsString($hash1);
        $this->assertEquals(32, strlen($hash1)); // MD5 hash length
    }

    public function testMultiGetRetrievesMultipleKeys(): void
    {
        $keys = ['key1', 'key2', 'key3'];
        $group = 'settings';
        
        // Mock cache responses
        \Brain\Monkey\Functions\when('wp_cache_get')
            ->andReturnUsing(function($key) {
                return $key === 'key1' ? 'value1' : 
                      ($key === 'key2' ? 'value2' : false);
            });
        
        $results = $this->cache_manager->get_multiple($keys, $group);
        
        $this->assertIsArray($results);
        $this->assertEquals('value1', $results['key1']);
        $this->assertEquals('value2', $results['key2']);
        $this->assertFalse($results['key3']);
    }

    public function testSetMultipleCachesMultipleValues(): void
    {
        $data = [
            'key1' => 'value1',
            'key2' => 'value2',
            'key3' => 'value3'
        ];
        $group = 'settings';
        
        \Brain\Monkey\Functions\when('wp_cache_set')->justReturn(true);
        
        $result = $this->cache_manager->set_multiple($data, $group);
        
        $this->assertTrue($result);
    }

    public function testCleanupRemovesExpiredEntries(): void
    {
        global $wpdb;
        
        // Mock cleanup query
        $wpdb->shouldReceive('query')
            ->once()
            ->andReturn(3); // 3 entries cleaned
        
        $result = $this->cache_manager->cleanup();
        
        $this->assertEquals(3, $result);
    }

    public function testGetSizeEstimatesMemoryUsage(): void
    {
        // Mock cache size calculation
        \Brain\Monkey\Functions\when('wp_cache_get')
            ->justReturn(['memory_usage' => 1048576]); // 1MB
        
        $size = $this->cache_manager->get_size();
        
        $this->assertIsArray($size);
        $this->assertArrayHasKey('bytes', $size);
        $this->assertArrayHasKey('human', $size);
    }

    public function testTaggingSupport(): void
    {
        $key = 'tagged_key';
        $value = 'tagged_value';
        $group = 'settings';
        $tags = ['user_123', 'settings_page'];
        
        \Brain\Monkey\Functions\when('wp_cache_set')->justReturn(true);
        
        $result = $this->cache_manager->set_with_tags($key, $value, $group, $tags);
        
        $this->assertTrue($result);
    }

    public function testInvalidateByTagClearsTaggedEntries(): void
    {
        $tag = 'user_123';
        
        \Brain\Monkey\Functions\when('wp_cache_get')
            ->justReturn(['key1', 'key2', 'key3']);
        \Brain\Monkey\Functions\when('wp_cache_delete')->justReturn(true);
        
        $result = $this->cache_manager->invalidate_by_tag($tag);
        
        $this->assertIsInt($result);
        $this->assertGreaterThanOrEqual(0, $result);
    }

    public function testCompressionForLargeValues(): void
    {
        $large_value = str_repeat('A', 10000); // 10KB string
        $key = 'large_key';
        $group = 'api_results';
        
        // Mock that compression is used for large values
        \Brain\Monkey\Functions\when('wp_cache_set')
            ->andReturnUsing(function($k, $v) {
                // Verify that value might be compressed
                return is_string($v) || is_array($v);
            });
        
        $result = $this->cache_manager->set($key, $large_value, $group);
        
        $this->assertTrue($result);
    }
}
