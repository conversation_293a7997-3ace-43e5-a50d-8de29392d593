<?php
/**
 * Class Enqueue_Scripts
 * 
 * Gestisce il caricamento di script e stili per il plugin Financial Advisor
 */
class Enqueue_Scripts {
    /**
     * Costruttore
     */
    public function __construct() {
        // Aggiungi azioni per il caricamento di script e stili
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
    }
    
    /**
     * Carica script e stili per il frontend
     */
    public function enqueue_frontend_scripts() {
        // Versione del plugin per il cache busting
        $version = defined('DOCUMENT_ADVISOR_VERSION') ? DOCUMENT_ADVISOR_VERSION : '1.0';
        
        // Percorso base per gli asset
        $base_url = plugin_dir_url(dirname(__FILE__));
        
        // Rimossa la funzionalità di aggiornamento in tempo reale delle statistiche
    }
    
    /**
     * Carica script e stili per l'admin
     */
    public function enqueue_admin_scripts($hook) {
        // Versione del plugin per il cache busting
        $version = defined('DOCUMENT_ADVISOR_VERSION') ? DOCUMENT_ADVISOR_VERSION : '1.0';
        
        // Percorso base per gli asset
        $base_url = plugin_dir_url(dirname(__FILE__));
        
        // Carica il polyfill per gli event listener passivi su tutte le pagine admin
        wp_enqueue_script('event-passive-polyfill', $base_url . 'assets/js/event-passive-polyfill.js', array(), $version, true);
        
        // Carica la configurazione TinyMCE prima dell'editor
        if (wp_script_is('tinymce', 'registered')) {
            wp_enqueue_script('tinymce-config', $base_url . 'assets/js/tinymce-config.js', array('tinymce'), $version, true);
        }
        
        // Carica script specifici per la pagina Office Add-in
        if (strpos($hook, 'office-addin-manager') !== false) {
            wp_enqueue_script('office-addin-preview-script', $base_url . 'assets/js/office-addin-preview.js', array('jquery', 'event-passive-polyfill'), $version, true);
            wp_enqueue_style('office-addin-preview-style', $base_url . 'assets/css/office-addin-preview.css', array(), $version);
        }
    }
}

// Inizializza la classe
new Enqueue_Scripts();
