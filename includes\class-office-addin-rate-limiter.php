<?php
/**
 * Office Add-in Rate Limiter
 *
 * This class provides rate limiting functionality for Office Add-in API requests
 * to prevent abuse and ensure system stability.
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

class Office_Addin_Rate_Limiter {
    
    /**
     * Default rate limits (requests per minute)
     */
    private $default_limits = [
        'analyze_excel_data' => 30,      // 30 analysis requests per minute
        'get_settings' => 60,            // 60 settings requests per minute
        'get_queries' => 60,             // 60 query requests per minute
        'test_connection' => 10          // 10 connection tests per minute
    ];
    
    /**
     * Rate limit window in seconds
     */
    private $window_seconds = 60;
    
    /**
     * Cache group for rate limiting
     */
    private $cache_group = 'office_addin_rate_limit';
    
    /**
     * Constructor
     */
    public function __construct() {
        // Initialize object cache group
        wp_cache_add_global_groups([$this->cache_group]);
    }
    
    /**
     * Check if request is allowed based on rate limits
     *
     * @param string $action The action being performed
     * @param string $identifier Client identifier (IP or user ID)
     * @return array Result with success status and remaining requests
     */
    public function is_request_allowed($action, $identifier = null) {
        try {
            // Get client identifier
            if (!$identifier) {
                $identifier = $this->get_client_identifier();
            }
            
            // Get rate limit for this action
            $limit = $this->get_rate_limit($action);
            
            // Generate cache key
            $cache_key = $this->generate_cache_key($action, $identifier);
            
            // Get current request count
            $current_count = wp_cache_get($cache_key, $this->cache_group);
            
            if ($current_count === false) {
                // First request in this window
                wp_cache_set($cache_key, 1, $this->cache_group, $this->window_seconds);
                
                return [
                    'allowed' => true,
                    'remaining' => $limit - 1,
                    'reset_time' => time() + $this->window_seconds,
                    'limit' => $limit
                ];
            }
            
            if ($current_count >= $limit) {
                // Rate limit exceeded
                if (function_exists('dv_debug_log')) {
                    dv_debug_log("Rate limit exceeded for action '{$action}' by identifier '{$identifier}'. Count: {$current_count}, Limit: {$limit}", 'office_addin');
                }
                
                return [
                    'allowed' => false,
                    'remaining' => 0,
                    'reset_time' => time() + $this->window_seconds,
                    'limit' => $limit,
                    'error' => __('Rate limit exceeded. Please try again later.', 'document-viewer-plugin')
                ];
            }
            
            // Increment counter
            wp_cache_incr($cache_key, 1, $this->cache_group);
            
            return [
                'allowed' => true,
                'remaining' => $limit - ($current_count + 1),
                'reset_time' => time() + $this->window_seconds,
                'limit' => $limit
            ];
            
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Rate limiter exception: ' . $e->getMessage(), 'office_addin');
            }
            
            // On error, allow request but log the issue
            return [
                'allowed' => true,
                'remaining' => 0,
                'reset_time' => time() + $this->window_seconds,
                'limit' => 0,
                'error' => 'Rate limiter error'
            ];
        }
    }
    
    /**
     * Get rate limit for specific action
     *
     * @param string $action Action name
     * @return int Rate limit
     */
    private function get_rate_limit($action) {
        // Allow customization via options
        $custom_limits = get_option('office_addin_rate_limits', []);
        
        if (isset($custom_limits[$action])) {
            return intval($custom_limits[$action]);
        }
        
        return isset($this->default_limits[$action]) ? $this->default_limits[$action] : 30;
    }
    
    /**
     * Get client identifier (IP address or user ID)
     *
     * @return string Client identifier
     */
    private function get_client_identifier() {
        // For logged-in users, use user ID
        if (is_user_logged_in()) {
            return 'user_' . get_current_user_id();
        }
        
        // For anonymous users, use IP address
        return 'ip_' . $this->get_client_ip();
    }
    
    /**
     * Get client IP address
     *
     * @return string IP address
     */
    private function get_client_ip() {
        // Check for various headers that might contain the real IP
        $headers = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_X_FORWARDED_FOR',      // Load balancers/proxies
            'HTTP_X_FORWARDED',          // Proxies
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_FORWARDED_FOR',        // Proxies
            'HTTP_FORWARDED',            // Proxies
            'REMOTE_ADDR'                // Standard
        ];
        
        foreach ($headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ip = $_SERVER[$header];
                
                // Handle comma-separated IPs (X-Forwarded-For)
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                // Validate IP address
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        // Fallback to REMOTE_ADDR
        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '127.0.0.1';
    }
    
    /**
     * Generate cache key for rate limiting
     *
     * @param string $action Action name
     * @param string $identifier Client identifier
     * @return string Cache key
     */
    private function generate_cache_key($action, $identifier) {
        $window_start = floor(time() / $this->window_seconds);
        return "rate_limit_{$action}_{$identifier}_{$window_start}";
    }
    
    /**
     * Reset rate limit for specific action and identifier
     *
     * @param string $action Action name
     * @param string $identifier Client identifier (optional)
     * @return bool Success status
     */
    public function reset_rate_limit($action, $identifier = null) {
        try {
            if (!$identifier) {
                $identifier = $this->get_client_identifier();
            }
            
            $cache_key = $this->generate_cache_key($action, $identifier);
            wp_cache_delete($cache_key, $this->cache_group);
            
            if (function_exists('dv_debug_log')) {
                dv_debug_log("Rate limit reset for action '{$action}' and identifier '{$identifier}'", 'office_addin');
            }
            
            return true;
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Rate limit reset exception: ' . $e->getMessage(), 'office_addin');
            }
            return false;
        }
    }
    
    /**
     * Get current rate limit status for an action
     *
     * @param string $action Action name
     * @param string $identifier Client identifier (optional)
     * @return array Rate limit status
     */
    public function get_rate_limit_status($action, $identifier = null) {
        try {
            if (!$identifier) {
                $identifier = $this->get_client_identifier();
            }
            
            $limit = $this->get_rate_limit($action);
            $cache_key = $this->generate_cache_key($action, $identifier);
            $current_count = wp_cache_get($cache_key, $this->cache_group);
            
            if ($current_count === false) {
                $current_count = 0;
            }
            
            return [
                'limit' => $limit,
                'used' => intval($current_count),
                'remaining' => max(0, $limit - intval($current_count)),
                'reset_time' => time() + $this->window_seconds,
                'window_seconds' => $this->window_seconds
            ];
            
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Get rate limit status exception: ' . $e->getMessage(), 'office_addin');
            }
            
            return [
                'limit' => 0,
                'used' => 0,
                'remaining' => 0,
                'reset_time' => time(),
                'window_seconds' => $this->window_seconds,
                'error' => 'Status check failed'
            ];
        }
    }
    
    /**
     * Update rate limits configuration
     *
     * @param array $limits New rate limits
     * @return bool Success status
     */
    public function update_rate_limits($limits) {
        try {
            // Validate limits
            $validated_limits = [];
            
            foreach ($limits as $action => $limit) {
                if (is_numeric($limit) && $limit >= 0) {
                    $validated_limits[sanitize_key($action)] = intval($limit);
                }
            }
            
            update_option('office_addin_rate_limits', $validated_limits);
            
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Rate limits updated: ' . print_r($validated_limits, true), 'office_addin');
            }
            
            return true;
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Update rate limits exception: ' . $e->getMessage(), 'office_addin');
            }
            return false;
        }
    }
    
    /**
     * Clear all rate limit data
     *
     * @return bool Success status
     */
    public function clear_all_rate_limits() {
        try {
            // This would require more complex cache clearing in a production environment
            // For now, we'll rely on the natural expiration of cache entries
            
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Rate limit cache cleared (entries will expire naturally)', 'office_addin');
            }
            
            return true;
        } catch (Exception $e) {
            if (function_exists('dv_debug_log')) {
                dv_debug_log('Clear rate limits exception: ' . $e->getMessage(), 'office_addin');
            }
            return false;
        }
    }
}
