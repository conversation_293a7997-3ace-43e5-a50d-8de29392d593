<?php
/**
 * Subscriber Page Template
 * 
 * Template for subscriber-only pages in Financial Advisor
 * 
 * @package Financial_Advisor
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Check if user has access to this page
if (!fa_user_can_access('frontend')) {
    // User doesn't have access, check if they're logged in at all
    if (!is_user_logged_in() && !isset($_COOKIE['fa_subscriber_login'])) {
        // Not logged in, redirect to login page
        $login_url = get_option('login_page_url', home_url());
        $login_url = add_query_arg('redirect_to', urlencode($_SERVER['REQUEST_URI']), $login_url);
        wp_safe_redirect($login_url);
        exit;
    } else {
        // Logged in but not as a subscriber with access
        echo fa_get_access_denied_message();
        return;
    }
}

// At this point, the user has access
// Check if they are a WordPress admin or a subscriber
$is_admin = current_user_can('manage_options');
$is_subscriber = fa_is_subscriber();
$subscriber_data = fa_get_subscriber_data();
$subscription_type = $is_subscriber ? $subscriber_data['type'] : '';

// You can use the variable $subscription_type to customize content based on subscription
?>

<!-- Content for subscribers will go here -->
<div class="subscriber-content">
    <div class="subscriber-header">
        <?php if ($is_subscriber): ?>
            <h3><?php _e('Benvenuto', 'document-viewer-plugin'); ?> <?php echo esc_html($subscriber_data['name']); ?> <?php echo esc_html($subscriber_data['surname']); ?></h3>
            <p class="subscription-type"><?php _e('Tipo di sottoscrizione:', 'document-viewer-plugin'); ?> <strong><?php echo esc_html($subscription_type); ?></strong></p>
        <?php else: ?>
            <h3><?php _e('Benvenuto', 'document-viewer-plugin'); ?></h3>
            <p class="subscription-type"><?php _e('Utente WordPress', 'document-viewer-plugin'); ?></p>
        <?php endif; ?>
    </div>
    
    <div class="subscriber-body">
        <!-- This area will be filled with the actual page content -->
    </div>
</div>
