/* Help Line CSS - Versione 1.0 */

.help-line-panel {
    position: fixed;
    right: 0;
    top: 40%;
    transform: translateY(-50%);
    z-index: 9999;
    display: flex;
    transition: transform 0.3s ease-in-out;
}

.help-line-toggle {
    background-color: #0073aa;
    padding: 12px;
    border-radius: 50% 0 0 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: -2px 2px 5px rgba(0, 0, 0, 0.2);
    align-self: center;
}

.help-line-toggle:hover {
    background-color: #005a87;
}

.help-icon {
    color: white;
    font-size: 18px;
    font-weight: bold;
    width: 24px;
    height: 24px;
    text-align: center;
    line-height: 24px;
}

.help-line-content {
    width: 300px;
    max-height: 80vh;
    overflow-y: auto;
    background-color: white;
    padding: 20px;
    border-radius: 5px 0 0 5px;
    box-shadow: -2px 2px 10px rgba(0, 0, 0, 0.15);
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;
    display: none; /* Nasconde inizialmente il contenuto */
}

.help-line-panel.open .help-line-content {
    transform: translateX(0);
    display: block; /* Mostra il contenuto quando il pannello è aperto */
}

.help-line-content h3 {
    margin-top: 0;
    color: #0073aa;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

.help-line-body {
    font-size: 0.9rem;
    line-height: 1.6;
    color: #444;
}

.help-line-body h4 {
    margin-top: 20px;
    margin-bottom: 10px;
    color: #23282d;
}

.help-line-body ol,
.help-line-body ul {
    margin-left: 20px;
    margin-bottom: 15px;
}

.help-line-body li {
    margin-bottom: 8px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .help-line-panel {
        bottom: 0;
        top: auto;
        right: 20px;
        transform: none;
    }
    
    .help-line-content {
        position: fixed;
        bottom: 0;
        right: 0;
        width: 100%;
        max-width: 100%;
        border-radius: 10px 10px 0 0;
        max-height: 70vh;
        transform: translateY(100%);
    }
    
    .help-line-panel.open .help-line-content {
        transform: translateY(0);
    }
    
    .help-line-toggle {
        border-radius: 50%;
        position: absolute;
        bottom: 60px;
        right: 0;
    }
}

/* Nasconde qualsiasi colonna dossier a sinistra */
.dossier-column,
.dossier-sidebar,
.dossier-panel,
.left-dossier,
#dossier-container,
#dossier-sidebar,
.left-sidebar-dossier {
    display: none !important;
}