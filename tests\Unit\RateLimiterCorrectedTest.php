<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;

/**
 * Corrected Rate Limiter Tests
 * 
 * Tests Rate Limiter functionality with correct expected return formats
 */
class RateLimiterCorrectedTest extends TestCase
{
    private $rate_limiter;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // Reset superglobals
        $_SERVER['REMOTE_ADDR'] = '*************';
        $_SERVER['HTTP_X_FORWARDED_FOR'] = '';
        $_SERVER['HTTP_X_REAL_IP'] = '';
        
        // Clear test cache
        \TestCache::clear();
        \TestOptions::clear();
        
        // Require the Rate Limiter class
        require_once dirname(dirname(__DIR__)) . '/includes/class-office-addin-rate-limiter.php';
        
        // Create new instance for each test
        $this->rate_limiter = new \Office_Addin_Rate_Limiter();
    }
    
    protected function tearDown(): void
    {
        // Clean up
        \TestCache::clear();
        \TestOptions::clear();
        $this->rate_limiter = null;
        parent::tearDown();
    }
    
    public function testConstructorSetsUpRateLimiter()
    {
        $this->assertInstanceOf(\Office_Addin_Rate_Limiter::class, $this->rate_limiter);
    }
    
    public function testIsRequestAllowedReturnsTrueForNewClient()
    {
        $result = $this->rate_limiter->is_request_allowed('test_action');
        
        $this->assertTrue($result['allowed']);
        $this->assertArrayHasKey('remaining', $result);
        $this->assertArrayHasKey('reset_time', $result);
        $this->assertArrayHasKey('limit', $result);
    }
    
    public function testGetRateLimitStatusReturnsCorrectData()
    {
        // Make a request first
        $this->rate_limiter->is_request_allowed('test_action');
        
        $status = $this->rate_limiter->get_rate_limit_status('test_action');
        
        $this->assertIsArray($status);
        $this->assertArrayHasKey('used', $status);
        $this->assertArrayHasKey('limit', $status);
        $this->assertArrayHasKey('remaining', $status);
        $this->assertArrayHasKey('reset_time', $status);
        $this->assertArrayHasKey('window_seconds', $status);
    }
    
    public function testRateLimitingWorksWithMultipleRequests()
    {
        $action = 'test_action';
        
        // First request should be allowed
        $result1 = $this->rate_limiter->is_request_allowed($action);
        $this->assertTrue($result1['allowed']);
        
        // Second request should also be allowed (within limits)
        $result2 = $this->rate_limiter->is_request_allowed($action);
        $this->assertTrue($result2['allowed']);
        
        // The remaining count should decrease
        $this->assertLessThanOrEqual($result1['remaining'], $result2['remaining']);
    }
    
    public function testResetRateLimitClearsCounters()
    {
        $action = 'test_action';
        
        // Make some requests
        $this->rate_limiter->is_request_allowed($action);
        $this->rate_limiter->is_request_allowed($action);
        
        // Get status before reset
        $status_before = $this->rate_limiter->get_rate_limit_status($action);
        $this->assertGreaterThan(0, $status_before['used']);
        
        // Reset the rate limit (returns boolean)
        $reset_result = $this->rate_limiter->reset_rate_limit($action);
        $this->assertTrue($reset_result);
        
        // Check status after reset
        $status_after = $this->rate_limiter->get_rate_limit_status($action);
        $this->assertEquals(0, $status_after['used']);
    }
    
    public function testUpdateRateLimitsChangesConfiguration()
    {
        $new_limits = [
            'test_action' => 5
        ];
        
        $result = $this->rate_limiter->update_rate_limits($new_limits);
        $this->assertTrue($result);
        
        // Test that the new limit is applied by making requests
        $status = $this->rate_limiter->get_rate_limit_status('test_action');
        $this->assertEquals(5, $status['limit']);
    }
    
    public function testClearAllRateLimitsWorks()
    {
        // Make some requests for different actions
        $this->rate_limiter->is_request_allowed('action1');
        $this->rate_limiter->is_request_allowed('action2');
        
        // Clear all rate limits (returns boolean)
        $result = $this->rate_limiter->clear_all_rate_limits();
        $this->assertTrue($result);
        
        // Note: clear_all_rate_limits() relies on natural cache expiration
        // so we can't immediately test that counters are reset
        $this->assertTrue($result); // Just verify the method call succeeded
    }
    
    public function testDifferentActionTypesAreTrackedSeparately()
    {
        // Make requests for different actions
        $result1 = $this->rate_limiter->is_request_allowed('upload');
        $result2 = $this->rate_limiter->is_request_allowed('download');
        
        $this->assertTrue($result1['allowed']);
        $this->assertTrue($result2['allowed']);
        
        // Get status for each action
        $status_upload = $this->rate_limiter->get_rate_limit_status('upload');
        $status_download = $this->rate_limiter->get_rate_limit_status('download');
        
        // Each should have their own counter
        $this->assertEquals(1, $status_upload['used']);
        $this->assertEquals(1, $status_download['used']);
    }
      public function testGetClientIdentifierUsesUserIdWhenLoggedIn()
    {
        // Use reflection to test the private method
        $reflection = new \ReflectionClass($this->rate_limiter);
        $method = $reflection->getMethod('get_client_identifier');
        $method->setAccessible(true);
        
        $identifier = $method->invoke($this->rate_limiter);
        
        // Since is_user_logged_in() returns true and get_current_user_id() returns 1 in our mocks
        $this->assertTrue(strpos($identifier, 'user_') !== false, 'Identifier should contain user_ prefix when logged in');
        $this->assertTrue(strpos($identifier, '1') !== false, 'Identifier should contain user ID 1');
    }
    
    public function testCacheKeyGenerationIsConsistent()
    {
        // Use reflection to test the private method
        $reflection = new \ReflectionClass($this->rate_limiter);
        $method = $reflection->getMethod('generate_cache_key');
        $method->setAccessible(true);
        
        $key1 = $method->invoke($this->rate_limiter, 'test_action', 'client123');
        $key2 = $method->invoke($this->rate_limiter, 'test_action', 'client123');        $this->assertEquals($key1, $key2);
        $this->assertTrue(strpos($key1, 'rate_limit') !== false, 'Cache key should contain rate_limit');
        $this->assertTrue(strpos($key1, 'test_action') !== false, 'Cache key should contain test_action');
        $this->assertTrue(strpos($key1, 'client123') !== false, 'Cache key should contain client123');
    }
    
    public function testErrorHandlingForInvalidActions()
    {
        // Test with empty action - should still work but may have default behavior
        $result = $this->rate_limiter->is_request_allowed('');
        $this->assertIsArray($result);
        $this->assertArrayHasKey('allowed', $result);
        
        // Test get status for non-existent action
        $status = $this->rate_limiter->get_rate_limit_status('non_existent_action');
        $this->assertIsArray($status);
        $this->assertEquals(0, $status['used']);
    }
    
    public function testRateLimitPerformanceWithManyRequests()
    {
        $action = 'performance_test';
        $start_time = microtime(true);
        
        // Make 10 rapid requests
        for ($i = 0; $i < 10; $i++) {
            $result = $this->rate_limiter->is_request_allowed($action);
            $this->assertIsArray($result);
            $this->assertArrayHasKey('allowed', $result);
        }
        
        $end_time = microtime(true);
        $execution_time = $end_time - $start_time;
        
        // Should complete in reasonable time (less than 1 second)
        $this->assertLessThan(1.0, $execution_time);
        
        // Check final status
        $status = $this->rate_limiter->get_rate_limit_status($action);
        $this->assertEquals(10, $status['used']);
    }
    
    public function testRateLimitEventuallyBlocksRequests()
    {
        $action = 'block_test';
        
        // Make requests until we hit the limit
        $requests_made = 0;
        $blocked = false;
        
        // Default rate limit appears to be 60 requests, so test up to 65
        for ($i = 0; $i < 65; $i++) {
            $result = $this->rate_limiter->is_request_allowed($action);
            $requests_made++;
            
            if (!$result['allowed']) {
                $blocked = true;
                break;
            }
        }
        
        // Should eventually block requests
        $this->assertTrue($blocked, "Rate limiting should eventually block requests after {$requests_made} attempts");
    }
    
    public function testPrivateMethodsExist()
    {
        $reflection = new \ReflectionClass($this->rate_limiter);
        
        // Check that expected private methods exist
        $this->assertTrue($reflection->hasMethod('get_client_identifier'));
        $this->assertTrue($reflection->hasMethod('generate_cache_key'));
        $this->assertTrue($reflection->hasMethod('get_rate_limit'));
        
        // Check that key public methods exist
        $this->assertTrue($reflection->hasMethod('is_request_allowed'));
        $this->assertTrue($reflection->hasMethod('get_rate_limit_status'));
        $this->assertTrue($reflection->hasMethod('reset_rate_limit'));
        $this->assertTrue($reflection->hasMethod('update_rate_limits'));
        $this->assertTrue($reflection->hasMethod('clear_all_rate_limits'));
    }
}
