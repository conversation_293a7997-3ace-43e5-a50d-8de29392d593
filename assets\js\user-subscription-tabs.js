/**
 * User Subscription Tabs Navigation
 * 
 * Handles the tab navigation in the user subscription admin pages.
 * Makes sure that clicking on tabs correctly navigates to the right page.
 */

jQuery(document).ready(function($) {
    console.log('User Subscription Tabs JS initialized');
    
    /**
     * Handle tab navigation for user subscription admin pages
     * This ensures that clicks on the tab navigation elements properly redirect to the correct pages
     */
    
    // Use direct DOM element selection to avoid potential conflicts
    var tabContainer = document.querySelector('.user-subscriptions-admin .nav-tab-wrapper');
    if (tabContainer) {
        console.log('Found tab container:', tabContainer);
        
        // Get all nav-tab elements within the wrapper
        var tabLinks = tabContainer.querySelectorAll('.nav-tab');
        console.log('Found tab links:', tabLinks.length);
        
        // Add click listeners to each tab
        tabLinks.forEach(function(tabLink) {
            // Remove any existing click handlers
            tabLink.removeEventListener('click', handleTabClick);
            
            // Add our click handler
            tabLink.addEventListener('click', handleTabClick);
            
            // Add visual indicator that click is properly bound
            tabLink.style.cursor = 'pointer';
        });
    } else {
        console.error('Tab container not found!');
    }
    
    /**
     * Handle tab click events
     */
    function handleTabClick(e) {
        // Get the href attribute from the tab
        var targetHref = this.getAttribute('href');
        console.log('Tab clicked with href:', targetHref);
        
        // If the href is present, navigate to that URL
        if (targetHref) {
            console.log('Navigating to:', targetHref);
            window.location.href = targetHref;
            
            // Prevent default behavior just in case
            e.preventDefault();
            return false;
        }
    }
});