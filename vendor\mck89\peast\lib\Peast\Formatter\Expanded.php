<?php
/**
 * This file is part of the Peast package
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information refer to the LICENSE file
 * distributed with this source code
 */
namespace Peast\Formatter;

/**
 * Compact formatter.
 * 
 * <AUTHOR> <<EMAIL>>
 */
class Expanded extends Base
{
    /**
     * <PERSON><PERSON><PERSON> that indicates if open curly brackets in code blocks must be
     * on a new line
     * 
     * @var bool
     */
    protected $newLineBeforeCurlyBracket = true;
    
    /**
     * Boolean that indicates if content inside round brackets must be
     * surrounded by spaces
     * 
     * @var bool
     */
    protected $spacesInsideRoundBrackets = true;
}