<?php

namespace Tests;

use PHPUnit\Framework\TestCase;
use Mockery;
use Brain\Monkey;

/**
 * Base test case for Office Add-in tests
 */
abstract class BaseTestCase extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        Monkey\setUp();
        
        // Set up WordPress environment
        $this->setUpWordPress();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        Monkey\tearDown();
        parent::tearDown();
    }

    /**
     * Set up WordPress mocks and globals
     */
    protected function setUpWordPress(): void
    {
        global $wpdb;
        
        if (!isset($wpdb)) {
            $wpdb = Mockery::mock('wpdb');
            $wpdb->prefix = 'wp_';
            $wpdb->options = 'wp_options';
            $wpdb->shouldReceive('prepare')->andReturnUsing(function($query) {
                return $query;
            });
            $wpdb->shouldReceive('get_results')->andReturn([]);
            $wpdb->shouldReceive('insert')->andReturn(true);
            $wpdb->shouldReceive('update')->andReturn(true);
            $wpdb->shouldReceive('delete')->andReturn(true);
            $wpdb->shouldReceive('get_var')->andReturn(1);
            $wpdb->shouldReceive('query')->andReturn(true);
        }

        // Mock WordPress functions
        Monkey\Functions\when('current_time')->justReturn(time());
        Monkey\Functions\when('wp_hash')->justReturn('test_hash');
        Monkey\Functions\when('wp_mail')->justReturn(true);
        Monkey\Functions\when('get_bloginfo')->justReturn('Test Site');
        Monkey\Functions\when('admin_url')->justReturn('http://localhost/wp-admin/');
        Monkey\Functions\when('site_url')->justReturn('http://localhost/');
        Monkey\Functions\when('home_url')->justReturn('http://localhost/');
    }

    /**
     * Create a mock WordPress option
     */
    protected function mockOption(string $option_name, $value): void
    {
        Monkey\Functions\when('get_option')
            ->justReturn($value)
            ->whenHappen(function($name) use ($option_name) {
                return $name === $option_name;
            });
    }

    /**
     * Create a mock user capability check
     */
    protected function mockUserCan(bool $can = true): void
    {
        Monkey\Functions\when('current_user_can')->justReturn($can);
    }

    /**
     * Create a mock nonce verification
     */
    protected function mockNonce(bool $valid = true): void
    {
        Monkey\Functions\when('wp_verify_nonce')->justReturn($valid);
    }

    /**
     * Assert that a WordPress hook was called
     */
    protected function assertHookCalled(string $hook): void
    {
        $this->assertTrue(
            Monkey\Actions\did($hook) || Monkey\Filters\applied($hook),
            "Hook '{$hook}' was not called"
        );
    }

    /**
     * Get reflection property value
     */
    protected function getProperty(object $object, string $property)
    {
        $reflection = new \ReflectionClass($object);
        $property = $reflection->getProperty($property);
        $property->setAccessible(true);
        return $property->getValue($object);
    }

    /**
     * Set reflection property value
     */
    protected function setProperty(object $object, string $property, $value): void
    {
        $reflection = new \ReflectionClass($object);
        $property = $reflection->getProperty($property);
        $property->setAccessible(true);
        $property->setValue($object, $value);
    }

    /**
     * Call protected method
     */
    protected function callMethod(object $object, string $method, array $args = [])
    {
        $reflection = new \ReflectionClass($object);
        $method = $reflection->getMethod($method);
        $method->setAccessible(true);
        return $method->invokeArgs($object, $args);
    }
}
