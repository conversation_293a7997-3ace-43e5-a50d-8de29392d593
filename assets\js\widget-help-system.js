/**
 * Widget Help System - Frontend JavaScript
 * 
 * Gestisce la visualizzazione context-aware degli help per ogni widget.
 * Sostituisce il sistema Help Line globale con uno modulare.
 * 
 * @version 1.0.0
 */

(function($) {
    'use strict';
    
    class WidgetHelpSystem {
        constructor() {
            this.activeWidget = null;
            this.helpModal = null;
            this.registeredWidgets = widgetHelpAjax.widgets || {};
            this.init();
        }
        
        init() {
            this.createHelpModal();
            this.bindGlobalEvents();
            this.autoActivateWidgets();
        }
        
        createHelpModal() {
            const modalHtml = `
                <div id="widget-help-modal" class="widget-help-modal" style="display: none;">
                    <div class="widget-help-overlay"></div>
                    <div class="widget-help-container">
                        <div class="widget-help-header">
                            <h3 class="widget-help-title">Widget Help</h3>
                            <button class="widget-help-close">&times;</button>
                        </div>
                        <div class="widget-help-body">
                            <div class="widget-help-loading">Caricamento...</div>
                            <div class="widget-help-content"></div>
                        </div>
                        <div class="widget-help-footer">
                            <button class="widget-help-btn widget-help-btn-close">Chiudi</button>
                        </div>
                    </div>
                </div>
            `;
            
            $('body').append(modalHtml);
            this.helpModal = $('#widget-help-modal');
            
            // Bind modal events
            this.helpModal.find('.widget-help-close, .widget-help-btn-close, .widget-help-overlay')
                .on('click', () => this.closeHelp());
        }
        
        register(widgetId, config) {
            this.registeredWidgets[widgetId] = {
                triggers: [],
                position: 'right',
                autoShow: false,
                ...config
            };
            
            // Bind triggers
            if (config.triggers && config.triggers.length > 0) {
                config.triggers.forEach(trigger => {
                    $(document).on('click', `${trigger} .widget-help-trigger, ${trigger}[data-help-trigger]`, 
                        (e) => {
                            e.preventDefault();
                            this.activateWidget(widgetId);
                        });
                });
            }
        }
        
        activateWidget(widgetId, section = 'main') {
            const config = this.registeredWidgets[widgetId];
            if (!config || !config.enabled) {
                console.warn(`Widget ${widgetId} not found or disabled`);
                return;
            }
            
            this.activeWidget = widgetId;
            this.showHelp(widgetId, section);
        }
        
        forceActivateWidget(widgetId) {
            this.activateWidget(widgetId);
        }
        
        activateSection(widgetId, section) {
            this.activateWidget(widgetId, section);
        }
        
        showHelp(widgetId, section = 'main') {
            this.helpModal.show();
            this.helpModal.find('.widget-help-loading').show();
            this.helpModal.find('.widget-help-content').hide();
            
            // AJAX call to get help content
            $.ajax({
                url: widgetHelpAjax.ajax_url,
                type: 'POST',
                data: {
                    action: 'get_widget_help',
                    widget_id: widgetId,
                    section: section,
                    nonce: widgetHelpAjax.nonce
                },
                success: (response) => {
                    if (response.success) {
                        this.helpModal.find('.widget-help-title').text(response.data.widget_name);
                        this.helpModal.find('.widget-help-content').html(response.data.content);
                        this.helpModal.find('.widget-help-loading').hide();
                        this.helpModal.find('.widget-help-content').show();
                        
                        // Position modal based on config
                        this.positionModal(response.data.position);
                        
                        // Bind section navigation
                        this.bindSectionNavigation(widgetId);
                    }
                },
                error: () => {
                    this.helpModal.find('.widget-help-loading').hide();
                    this.helpModal.find('.widget-help-content')
                        .html('<p>Errore nel caricamento dell\'help. Riprova.</p>')
                        .show();
                }
            });
        }
        
        showQuickTip(widgetId, tipType) {
            // Implementation for quick tips on hover
            const tipContent = this.getQuickTipContent(widgetId, tipType);
            if (tipContent) {
                this.showTooltip(tipContent);
            }
        }
        
        showActionHelp(widgetId, action) {
            const actionHelp = this.getActionHelpContent(widgetId, action);
            if (actionHelp) {
                this.showInlineHelp(actionHelp);
            }
        }
        
        showTroubleshootingHelp(widgetId, errorType) {
            const troubleshootingContent = this.getTroubleshootingContent(widgetId, errorType);
            if (troubleshootingContent) {
                this.showErrorModal(troubleshootingContent);
            }
        }
        
        showErrorHelp(widgetId, errorType) {
            this.showTroubleshootingHelp(widgetId, errorType);
        }
        
        bindSectionNavigation(widgetId) {
            this.helpModal.find('[data-section]').on('click', (e) => {
                const section = $(e.currentTarget).data('section');
                this.activateSection(widgetId, section);
            });
        }
        
        positionModal(position) {
            const container = this.helpModal.find('.widget-help-container');
            container.removeClass('position-left position-right position-center');
            container.addClass(`position-${position}`);
        }
        
        closeHelp() {
            this.helpModal.hide();
            this.activeWidget = null;
        }
        
        bindGlobalEvents() {
            // Keyboard shortcuts
            $(document).on('keydown', (e) => {
                if (e.key === 'Escape' && this.helpModal.is(':visible')) {
                    this.closeHelp();
                }
                
                // F1 key for context help
                if (e.key === 'F1') {
                    e.preventDefault();
                    this.showContextualHelp();
                }
            });
        }
        
        autoActivateWidgets() {
            // Auto-activate widgets based on configuration
            Object.keys(this.registeredWidgets).forEach(widgetId => {
                const config = this.registeredWidgets[widgetId];
                if (config.autoShow && config.triggers) {
                    config.triggers.forEach(trigger => {
                        $(document).on('DOMNodeInserted', trigger, () => {
                            setTimeout(() => this.activateWidget(widgetId), 1000);
                        });
                    });
                }
            });
        }
        
        showContextualHelp() {
            // Find the closest widget and show its help
            const $focusedWidget = $('.report-viewer-widget:visible').first();
            if ($focusedWidget.length) {
                this.activateWidget('report_viewer');
            }
        }
        
        getQuickTipContent(widgetId, tipType) {
            const tips = {
                'report_viewer': {
                    'filters': 'Usa i filtri per trovare rapidamente i report che cerchi',
                    'export': 'Clicca qui per scaricare il report in vari formati',
                    'versions': 'Visualizza e gestisci le versioni precedenti del report'
                }
            };
            
            return tips[widgetId] && tips[widgetId][tipType] ? tips[widgetId][tipType] : null;
        }
        
        getActionHelpContent(widgetId, action) {
            const actionHelps = {
                'report_viewer': {
                    'create_report': 'Clicca sul pulsante "Nuovo Report" e compila i campi richiesti',
                    'filter_data': 'Usa la barra di ricerca o i filtri laterali per filtrare i dati',
                    'export_pdf': 'Seleziona il formato PDF dal menu export e clicca download'
                }
            };
            
            return actionHelps[widgetId] && actionHelps[widgetId][action] ? actionHelps[widgetId][action] : null;
        }
        
        getTroubleshootingContent(widgetId, errorType) {
            const troubleshooting = {
                'report_viewer': {
                    'export_failed': 'Verifica la connessione internet e riprova. Se il problema persiste, contatta l\'assistenza.',
                    'data_not_loading': 'Controlla i permessi di accesso ai dati e ricarica la pagina.'
                }
            };
            
            return troubleshooting[widgetId] && troubleshooting[widgetId][errorType] ? troubleshooting[widgetId][errorType] : null;
        }
        
        showTooltip(content) {
            // Simple tooltip implementation
            const tooltip = $(`<div class="widget-help-tooltip">${content}</div>`);
            $('body').append(tooltip);
            
            setTimeout(() => tooltip.fadeOut(() => tooltip.remove()), 3000);
        }
        
        showInlineHelp(content) {
            // Show inline help near the active element
            console.log('Inline help:', content);
        }
        
        showErrorModal(content) {
            // Show error-specific help modal
            this.helpModal.find('.widget-help-content').html(`<div class="error-help">${content}</div>`);
            this.helpModal.show();
        }
    }
    
    // Global instance
    window.widgetHelpSystem = new WidgetHelpSystem();
    
    // jQuery plugin for easy activation
    $.fn.activateWidgetHelp = function(widgetId) {
        return this.each(function() {
            window.widgetHelpSystem.activateWidget(widgetId);
        });
    };
    
})(jQuery);
     */
    hideHelp() {
        this.helpContainer.classList.remove('visible');
        this.isVisible = false;
        
        // Nasconde dopo l'animazione
        setTimeout(() => {
            this.helpContainer.style.display = 'none';
            this.helpContainer.innerHTML = '';
        }, 300);
    }
    
    /**
     * Adatta la posizione dell'help
     */
    adjustHelpPosition() {
        if (!this.isVisible) return;
        
        const panel = this.helpContainer.querySelector('.widget-help-panel');
        if (!panel) return;
        
        // Logica per adattamento responsive
        const isMobile = window.innerWidth <= 768;
        
        if (isMobile) {
            panel.classList.add('mobile');
        } else {
            panel.classList.remove('mobile');
        }
    }
    
    /**
     * API pubblica per forzare l'attivazione di un widget
     */
    forceActivateWidget(widgetId) {
        if (this.widgets[widgetId]) {
            this.activateWidget(widgetId);
        }
    }
    
    /**
     * API pubblica per disattivare l'help corrente
     */
    deactivateCurrentWidget() {
        if (this.currentWidget) {
            this.deactivateWidget(this.currentWidget);
        }
    }
    
    /**
     * API pubblica per ottenere il widget attualmente attivo
     */
    getCurrentWidget() {
        return this.currentWidget;
    }
    
    /**
     * Aggiorna la configurazione di un widget
     */
    updateWidgetConfig(widgetId, config) {
        if (this.widgets[widgetId]) {
            this.widgets[widgetId] = { ...this.widgets[widgetId], ...config };
        }
    }
}

// Inizializzazione automatica quando il DOM è pronto
document.addEventListener('DOMContentLoaded', function() {
    // Verifica che la configurazione sia disponibile
    if (typeof widgetHelpConfig !== 'undefined') {
        window.widgetHelpSystem = new WidgetHelpSystem(widgetHelpConfig);
    } else {
        console.warn('Widget Help System: configurazione non trovata');
    }
});

// Compatibilità con jQuery
if (typeof jQuery !== 'undefined') {
    jQuery(document).ready(function($) {
        // Helper jQuery per attivare help
        $.fn.activateWidgetHelp = function(widgetId) {
            if (window.widgetHelpSystem) {
                window.widgetHelpSystem.forceActivateWidget(widgetId);
            }
            return this;
        };
        
        // Helper jQuery per disattivare help
        $.fn.deactivateWidgetHelp = function() {
            if (window.widgetHelpSystem) {
                window.widgetHelpSystem.deactivateCurrentWidget();
            }
            return this;
        };
    });
}
