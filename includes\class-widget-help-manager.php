<?php
/**
 * Widget Help Manager
 * 
 * Gestisce il sistema di Help specifico per ogni widget del plugin Financial Advisor.
 * Sostituisce il sistema Help Line globale con uno modulare e context-aware.
 * 
 * @package Financial_Advisor
 * @version 1.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class Widget_Help_Manager {
    
    /**
     * Istanza singleton
     */
    private static $instance = null;
    
    /**
     * Widget registrati e loro configurazioni help
     */
    private $registered_widgets = array();
    
    /**
     * Widget attualmente attivo
     */
    private $current_widget = null;
    
    /**
     * Costruttore privato per singleton
     */
    private function __construct() {
        $this->init();
    }
    
    /**
     * Ottiene l'istanza singleton
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Inizializzazione del manager
     */
    private function init() {
        // Hook per inizializzazione admin
        add_action('admin_init', array($this, 'admin_init'));
        
        // Hook per frontend
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_footer', array($this, 'render_active_help'));
        
        // Ajax handlers
        add_action('wp_ajax_save_widget_help', array($this, 'save_widget_help'));
        add_action('wp_ajax_get_widget_help', array($this, 'get_widget_help'));
        
        // Registra i widget di default
        $this->register_default_widgets();
    }
    
    /**
     * Registra i widget di default del sistema
     */
    private function register_default_widgets() {
        $default_widgets = array(
            'document_viewer' => array(
                'name' => __('Document Viewer', 'document-viewer-plugin'),
                'description' => __('Widget per l\'analisi e visualizzazione documenti', 'document-viewer-plugin'),
                'default_content' => $this->get_document_viewer_default_help(),
                'triggers' => array('.document-viewer-widget', '[data-widget="document-viewer"]'),
                'position' => 'right',
                'enabled' => true
            ),
            'subscriber_management' => array(
                'name' => __('Subscriber Management', 'document-viewer-plugin'),
                'description' => __('Widget per la gestione abbonamenti utenti', 'document-viewer-plugin'),
                'default_content' => $this->get_subscriber_management_default_help(),
                'triggers' => array('.subscriber-management-widget', '[data-widget="subscriber-management"]'),
                'position' => 'right',
                'enabled' => true
            ),
            'chat_model' => array(
                'name' => __('Chat Model', 'document-viewer-plugin'),
                'description' => __('Widget chat AI per assistenza', 'document-viewer-plugin'),
                'default_content' => $this->get_chat_model_default_help(),
                'triggers' => array('.chat-model-widget', '[data-widget="chat-model"]'),
                'position' => 'bottom',
                'enabled' => true
            ),
            'user_subscription' => array(
                'name' => __('User Subscription', 'document-viewer-plugin'),
                'description' => __('Widget gestione sottoscrizioni utente', 'document-viewer-plugin'),
                'default_content' => $this->get_user_subscription_default_help(),
                'triggers' => array('.user-subscription-widget', '[data-widget="user-subscription"]'),
                'position' => 'right',
                'enabled' => true
            ),
            'login' => array(
                'name' => __('Login Widget', 'document-viewer-plugin'),
                'description' => __('Widget di login e autenticazione', 'document-viewer-plugin'),
                'default_content' => $this->get_login_default_help(),
                'triggers' => array('.login-widget', '[data-widget="login"]'),
                'position' => 'center',
                'enabled' => true
            )
        );
        
        foreach ($default_widgets as $widget_id => $config) {
            $this->register_widget($widget_id, $config);
        }
    }
    
    /**
     * Registra un nuovo widget nel sistema help
     * 
     * @param string $widget_id ID univoco del widget
     * @param array $config Configurazione del widget
     */
    public function register_widget($widget_id, $config) {
        $defaults = array(
            'name' => '',
            'description' => '',
            'default_content' => '',
            'triggers' => array(),
            'position' => 'right',
            'enabled' => true,
            'style' => 'panel'
        );
        
        $this->registered_widgets[$widget_id] = wp_parse_args($config, $defaults);
    }
    
    /**
     * Rimuove un widget dal sistema help
     */
    public function unregister_widget($widget_id) {
        unset($this->registered_widgets[$widget_id]);
    }
    
    /**
     * Ottiene tutti i widget registrati
     */
    public function get_registered_widgets() {
        return $this->registered_widgets;
    }
    
    /**
     * Ottiene la configurazione di un widget specifico
     */
    public function get_widget_config($widget_id) {
        return isset($this->registered_widgets[$widget_id]) ? $this->registered_widgets[$widget_id] : null;
    }
    
    /**
     * Imposta il widget attualmente attivo
     */
    public function set_current_widget($widget_id) {
        if (isset($this->registered_widgets[$widget_id])) {
            $this->current_widget = $widget_id;
        }
    }
    
    /**
     * Ottiene il widget attualmente attivo
     */
    public function get_current_widget() {
        return $this->current_widget;
    }
    
    /**
     * Inizializzazione admin
     */
    public function admin_init() {
        // Aggiungi le impostazioni admin se necessario
        add_action('admin_menu', array($this, 'add_admin_menu'));
    }
    
    /**
     * Aggiunge il menu admin
     */
    public function add_admin_menu() {
        add_submenu_page(
            'options-general.php',
            __('Widget Help System', 'document-viewer-plugin'),
            __('Widget Help', 'document-viewer-plugin'),
            'manage_options',
            'widget-help-system',
            array($this, 'admin_page')
        );
    }
    
    /**
     * Enqueue script e stili
     */
    public function enqueue_scripts() {
        wp_enqueue_style(
            'widget-help-system',
            plugin_dir_url(__FILE__) . '../assets/css/widget-help-system.css',
            array(),
            '1.0.0'
        );
        
        wp_enqueue_script(
            'widget-help-system',
            plugin_dir_url(__FILE__) . '../assets/js/widget-help-system.js',
            array('jquery'),
            '1.0.0',
            true
        );
        
        // Localizza script con configurazioni
        wp_localize_script('widget-help-system', 'widgetHelpConfig', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('widget_help_nonce'),
            'widgets' => $this->get_frontend_widget_configs(),
            'currentWidget' => $this->current_widget
        ));
    }
    
    /**
     * Ottiene le configurazioni widget per il frontend
     */
    private function get_frontend_widget_configs() {
        $frontend_configs = array();
        
        foreach ($this->registered_widgets as $widget_id => $config) {
            if ($config['enabled']) {
                $frontend_configs[$widget_id] = array(
                    'triggers' => $config['triggers'],
                    'position' => $config['position'],
                    'style' => $config['style']
                );
            }
        }
        
        return $frontend_configs;
    }
    
    /**
     * Render help per il widget attivo
     */
    public function render_active_help() {
        // Viene gestito via JavaScript per essere context-aware
        echo '<div id="widget-help-container"></div>';
    }
    
    /**
     * Salva il contenuto help di un widget (AJAX)
     */
    public function save_widget_help() {
        check_ajax_referer('widget_help_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'Permessi insufficienti'));
        }
        
        $widget_id = sanitize_text_field($_POST['widget_id']);
        $content = wp_kses_post($_POST['content']);
        $enabled = isset($_POST['enabled']) ? (bool) $_POST['enabled'] : true;
        $position = sanitize_text_field($_POST['position']);
        
        if (!isset($this->registered_widgets[$widget_id])) {
            wp_send_json_error(array('message' => 'Widget non valido'));
        }
        
        // Salva le configurazioni
        $widget_helps = get_option('widget_help_content', array());
        $widget_helps[$widget_id] = array(
            'content' => $content,
            'enabled' => $enabled,
            'position' => $position,
            'updated_at' => current_time('mysql')
        );
        
        update_option('widget_help_content', $widget_helps);
        
        wp_send_json_success(array('message' => 'Help salvato con successo'));
    }
    
    /**
     * Ottiene il contenuto help di un widget (AJAX)
     */
    public function get_widget_help() {
        check_ajax_referer('widget_help_nonce', 'nonce');
        
        $widget_id = sanitize_text_field($_POST['widget_id']);
        
        if (!isset($this->registered_widgets[$widget_id])) {
            wp_send_json_error(array('message' => 'Widget non valido'));
        }
        
        $widget_helps = get_option('widget_help_content', array());
        $help_data = isset($widget_helps[$widget_id]) ? $widget_helps[$widget_id] : array();
        
        // Usa contenuto di default se non esistente
        if (empty($help_data['content'])) {
            $help_data['content'] = $this->registered_widgets[$widget_id]['default_content'];
        }
        
        wp_send_json_success($help_data);
    }
    
    /**
     * Pagina di amministrazione
     */
    public function admin_page() {
        include plugin_dir_path(__FILE__) . '../templates/admin/widget-help-admin.php';
    }
    
    // ============================================
    // CONTENUTI HELP DI DEFAULT PER OGNI WIDGET
    // ============================================
    
    private function get_document_viewer_default_help() {
        return '<h4>Document Viewer - Guida rapida</h4>
        <ol>
            <li><strong>Caricamento:</strong> Clicca su "Scegli file" e seleziona un PDF o documento Word</li>
            <li><strong>Analisi:</strong> Inserisci la tua domanda nel campo "Descrizione/Domanda"</li>
            <li><strong>Elaborazione:</strong> Clicca "Analizza" per avviare l\'analisi AI</li>
            <li><strong>Risultati:</strong> Visualizza l\'analisi nella sezione risultati</li>
            <li><strong>Export:</strong> Usa il pulsante "Export PDF" per salvare l\'analisi</li>
        </ol>
        <h4>Suggerimenti:</h4>
        <ul>
            <li>Documenti senza password funzionano meglio</li>
            <li>Domande specifiche producono analisi più accurate</li>
            <li>Puoi usare il zoom per leggere meglio i dettagli</li>
        </ul>';
    }
    
    private function get_subscriber_management_default_help() {
        return '<h4>Gestione Abbonamenti - Guida</h4>
        <ol>
            <li><strong>Visualizzazione:</strong> Consulta lo stato del tuo abbonamento nella dashboard</li>
            <li><strong>Upgrade:</strong> Clicca su "Upgrade" per passare a un piano superiore</li>
            <li><strong>Cronologia:</strong> Visualizza le tue transazioni nella sezione "Cronologia"</li>
            <li><strong>Fatturazione:</strong> Gestisci i metodi di pagamento</li>
        </ol>
        <h4>Tipi di abbonamento:</h4>
        <ul>
            <li><strong>Basic:</strong> Analisi limitate mensili</li>
            <li><strong>Pro:</strong> Analisi illimitate + features avanzate</li>
            <li><strong>Enterprise:</strong> Tutti i servizi + supporto dedicato</li>
        </ul>';
    }
    
    private function get_chat_model_default_help() {
        return '<h4>Chat AI - Come utilizzarla</h4>
        <ol>
            <li><strong>Avvio:</strong> Clicca sull\'icona chat per aprire la finestra</li>
            <li><strong>Domande:</strong> Scrivi la tua domanda in linguaggio naturale</li>
            <li><strong>Contesto:</strong> Fai riferimento ai documenti caricati per domande specifiche</li>
            <li><strong>Cronologia:</strong> Le conversazioni vengono salvate per consultazione</li>
        </ol>
        <h4>Esempi di domande:</h4>
        <ul>
            <li>"Spiega il bilancio che ho caricato"</li>
            <li>"Quali sono i punti critici di questo documento?"</li>
            <li>"Come posso migliorare la mia analisi finanziaria?"</li>
        </ul>';
    }
    
    private function get_user_subscription_default_help() {
        return '<h4>Sottoscrizioni Utente - Guida</h4>
        <ol>
            <li><strong>Registrazione:</strong> Crea il tuo account per accedere ai servizi</li>
            <li><strong>Conferma email:</strong> Verifica il tuo indirizzo email</li>
            <li><strong>Selezione piano:</strong> Scegli l\'abbonamento più adatto</li>
            <li><strong>Pagamento:</strong> Completa la transazione in sicurezza</li>
        </ol>
        <h4>Gestione account:</h4>
        <ul>
            <li>Modifica profilo e password</li>
            <li>Gestisci le notifiche email</li>
            <li>Visualizza l\'utilizzo dei servizi</li>
        </ul>';
    }
    
    private function get_login_default_help() {
        return '<h4>Accesso - Informazioni utili</h4>
        <ol>
            <li><strong>Login:</strong> Usa email e password del tuo account</li>
            <li><strong>Password dimenticata:</strong> Clicca "Password dimenticata?" per il reset</li>
            <li><strong>Nuova registrazione:</strong> Clicca "Registrati" se non hai un account</li>
            <li><strong>Social login:</strong> Puoi accedere con Google o LinkedIn se configurato</li>
        </ol>
        <h4>Problemi comuni:</h4>
        <ul>
            <li>Verifica che l\'email sia corretta</li>
            <li>Controlla la cartella spam per email di conferma</li>
            <li>Assicurati che JavaScript sia abilitato</li>
        </ul>';
    }
}

// Inizializza il manager
function widget_help_manager() {
    return Widget_Help_Manager::get_instance();
}

// Avvia il sistema
widget_help_manager();
