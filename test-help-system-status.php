<?php
/**
 * Test rapido per verificare lo stato del sistema Help
 * Da eseguire nel backend WordPress
 */

// Prevenire accesso diretto
if (!defined('ABSPATH')) {
    exit('Accesso diretto non consentito');
}

// Verifica permessi admin
if (!current_user_can('manage_options')) {
    wp_die('Non hai i permessi per accedere a questa pagina.');
}

echo '<div style="font-family: Arial, sans-serif; margin: 20px; background: #f9f9f9; padding: 20px; border-radius: 8px;">';
echo '<h1 style="color: #2271b1;">🔍 Test Rapido Sistema Help</h1>';

// 1. Verifica classi caricate
echo '<h2>📚 Classi Sistema</h2>';
$classes = array(
    'Widget_Help_System',
    'Widget_Help_Admin', 
    'Widget_Help_Manager',
    'Menu_Manager'
);

foreach ($classes as $class) {
    $status = class_exists($class) ? '✅' : '❌';
    echo "<p>$status $class</p>";
}

// 2. Verifica istanza globale
echo '<h2>🌐 Istanza Globale</h2>';
global $widget_help_system;
if ($widget_help_system) {
    echo '<p>✅ $widget_help_system disponibile</p>';
    
    // Test metodi
    if (method_exists($widget_help_system, 'get_all_widgets')) {
        $widgets = $widget_help_system->get_all_widgets();
        echo '<p>📊 Widget registrati: ' . count($widgets) . '</p>';
        
        if (!empty($widgets)) {
            echo '<ul>';
            foreach ($widgets as $id => $config) {
                $enabled = $config['enabled'] ? '🟢' : '🔴';
                echo "<li>$enabled $id: {$config['name']}</li>";
            }
            echo '</ul>';
        }
    }
} else {
    echo '<p>❌ $widget_help_system non disponibile</p>';
    echo '<p>🔧 Tentativo di inizializzazione...</p>';
    
    if (class_exists('Widget_Help_System')) {
        try {
            $widget_help_system = new Widget_Help_System();
            echo '<p>✅ Inizializzazione riuscita</p>';
        } catch (Exception $e) {
            echo '<p>❌ Errore inizializzazione: ' . $e->getMessage() . '</p>';
        }
    }
}

// 3. Verifica funzione helper
echo '<h2>🔧 Funzione Helper</h2>';
if (function_exists('widget_help_system')) {
    $helper_instance = widget_help_system();
    if ($helper_instance) {
        echo '<p>✅ widget_help_system() funziona</p>';
    } else {
        echo '<p>❌ widget_help_system() restituisce null</p>';
    }
} else {
    echo '<p>❌ Funzione widget_help_system() non definita</p>';
}

// 4. Verifica database
echo '<h2>💾 Database</h2>';
$content_option = get_option('widget_help_content', array());
$settings_option = get_option('widget_help_widget_settings', array());

echo '<p>📄 widget_help_content: ' . (empty($content_option) ? '❌ Vuoto' : '✅ ' . count($content_option) . ' widget') . '</p>';
echo '<p>⚙️ widget_help_widget_settings: ' . (empty($settings_option) ? '❌ Vuoto' : '✅ ' . count($settings_option) . ' widget') . '</p>';

// 5. Test inizializzazione contenuti predefiniti
echo '<h2>🚀 Test Inizializzazione</h2>';
if (empty($content_option) && empty($settings_option)) {
    echo '<p>⚠️ Database vuoto - Inizializzazione contenuti predefiniti...</p>';
    
    // Contenuti di test
    $default_content = array(
        'report_viewer' => array(
            'main' => '<h3>Report Viewer</h3><p>Sistema di visualizzazione report finanziari con funzionalità avanzate di analisi e export.</p><ul><li>Visualizzazione interattiva</li><li>Export in PDF/Excel</li><li>Filtri avanzati</li></ul>',
            'advanced' => '<h4>Funzioni Avanzate</h4><p>Strumenti per analisi approfondite:</p><ul><li>Grafici dinamici</li><li>Comparazioni temporali</li><li>Drill-down dei dati</li></ul>'
        ),
        'document_analyzer' => array(
            'main' => '<h3>Document Analyzer</h3><p>Analizzatore intelligente di documenti finanziari con OCR e AI.</p><ul><li>Riconoscimento automatico</li><li>Estrazione dati</li><li>Classificazione documenti</li></ul>',
            'setup' => '<h4>Configurazione</h4><p>Impostazioni per l\'analizzatore:</p><ul><li>Soglie di confidenza</li><li>Formati supportati</li><li>Regole di classificazione</li></ul>'
        )
    );
    
    $default_settings = array(
        'report_viewer' => array('enabled' => true, 'position' => 'right'),
        'document_analyzer' => array('enabled' => true, 'position' => 'left')
    );
    
    // Salva nel database
    update_option('widget_help_content', $default_content);
    update_option('widget_help_widget_settings', $default_settings);
    
    echo '<p>✅ Contenuti predefiniti inizializzati</p>';
    echo '<p>📊 Salvati ' . count($default_content) . ' widget con contenuti</p>';
    echo '<p>⚙️ Salvate ' . count($default_settings) . ' configurazioni widget</p>';
    
    // Aggiorna anche il sistema se disponibile
    if ($widget_help_system && method_exists($widget_help_system, 'load_saved_content')) {
        $widget_help_system->load_saved_content();
        echo '<p>🔄 Sistema aggiornato con nuovi contenuti</p>';
    }
} else {
    echo '<p>✅ Database già popolato</p>';
}

// 6. Verifica menu admin
echo '<h2>🎛️ Menu Admin</h2>';
$admin_url = admin_url('admin.php?page=widget-help-admin');
echo '<p>🔗 URL admin: <a href="' . $admin_url . '" target="_blank">' . $admin_url . '</a></p>';

// Verifica se il menu è registrato
global $submenu;
$menu_found = false;
if (isset($submenu)) {
    foreach ($submenu as $parent => $items) {
        foreach ($items as $item) {
            if (isset($item[2]) && strpos($item[2], 'widget-help') !== false) {
                echo '<p>✅ Menu trovato sotto: ' . $parent . '</p>';
                $menu_found = true;
                break 2;
            }
        }
    }
}

if (!$menu_found) {
    echo '<p>❌ Menu admin non registrato</p>';
}

// 7. Test AJAX
echo '<h2>🔄 Test AJAX</h2>';
if (class_exists('Widget_Help_System')) {
    echo '<p>✅ Handler AJAX disponibili:</p>';
    echo '<ul>';
    echo '<li>wp_ajax_get_widget_help</li>';
    echo '<li>wp_ajax_nopriv_get_widget_help</li>';
    echo '</ul>';
} else {
    echo '<p>❌ Handler AJAX non disponibili</p>';
}

echo '<hr>';
echo '<h2>📋 Riepilogo</h2>';

$issues = array();
if (!class_exists('Widget_Help_System')) $issues[] = 'Classe Widget_Help_System non caricata';
if (!$widget_help_system) $issues[] = 'Istanza globale non disponibile';
if (empty($content_option)) $issues[] = 'Database contenuti vuoto';
if (!$menu_found) $issues[] = 'Menu admin non registrato';

if (empty($issues)) {
    echo '<p style="color: #00a32a; font-weight: bold;">✅ Sistema Help completamente funzionale!</p>';
} else {
    echo '<p style="color: #d63638; font-weight: bold;">❌ Problemi rilevati:</p>';
    echo '<ul>';
    foreach ($issues as $issue) {
        echo '<li>' . $issue . '</li>';
    }
    echo '</ul>';
}

echo '</div>';
?>
