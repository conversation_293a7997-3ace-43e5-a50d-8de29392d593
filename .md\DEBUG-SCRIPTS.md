# Script di Debug per Console Errors - Subscriber Management Widget

## 🔍 SCRIPT DI DEBUG AUTOMATICO

### 1. **Debug Completo Inizializzazione**
```javascript
// Esegui questo script nella console per verificare lo stato completo
function debugSubscriberWidget() {
    const debug = {
        timestamp: new Date().toISOString(),
        environment: {
            jquery: typeof $ !== 'undefined' ? $.fn.jquery : 'NOT LOADED',
            ajaxObject: typeof subscriberManagementAjax !== 'undefined',
            widgetObject: typeof window.subscriberManagementWidget !== 'undefined'
        },
        ajaxConfiguration: {},
        domElements: {},
        functions: {},
        errors: []
    };

    // Verifica configurazione AJAX
    if (typeof subscriberManagementAjax !== 'undefined') {
        debug.ajaxConfiguration = {
            ajax_url: subscriberManagementAjax.ajax_url || 'MISSING',
            nonce: subscriberManagementAjax.nonce ? 'PRESENT' : 'MISSING',
            messages: subscriberManagementAjax.messages ? Object.keys(subscriberManagementAjax.messages) : 'MISSING'
        };
    } else {
        debug.errors.push('subscriberManagementAjax object not found');
    }

    // Verifica elementi DOM critici
    debug.domElements = {
        widget_container: $('.subscriber-management-widget-container').length,
        menu_items: $('.menu-item').length,
        credit_display: $('.credit-value').length,
        recharge_button: $('#proceed-recharge-btn').length,
        amount_buttons: $('.amount-btn').length,
        payment_methods: $('.payment-method').length,
        feedback_message: $('#subscriber-feedback-message').length
    };

    // Verifica funzioni esportate
    if (typeof window.subscriberManagementWidget !== 'undefined') {
        debug.functions = {
            exported: Object.keys(window.subscriberManagementWidget),
            updateCreditDisplay: typeof window.subscriberManagementWidget.updateCreditDisplay,
            showFeedbackMessage: typeof window.subscriberManagementWidget.showFeedbackMessage
        };
    } else {
        debug.errors.push('window.subscriberManagementWidget not exported');
    }

    // Verifica errori comuni
    if (debug.domElements.widget_container === 0) {
        debug.errors.push('Widget container not found - check if widget is loaded');
    }
    
    if (debug.domElements.recharge_button === 0) {
        debug.errors.push('Recharge button not found - check if credits section is active');
    }

    console.group('🔍 SUBSCRIBER WIDGET DEBUG REPORT');
    console.log('Environment:', debug.environment);
    console.log('AJAX Config:', debug.ajaxConfiguration);
    console.log('DOM Elements:', debug.domElements);
    console.log('Functions:', debug.functions);
    
    if (debug.errors.length > 0) {
        console.error('❌ ERRORS FOUND:', debug.errors);
    } else {
        console.log('✅ NO CRITICAL ERRORS DETECTED');
    }
    console.groupEnd();

    return debug;
}

// Esegui debug
debugSubscriberWidget();
```

### 2. **Monitor AJAX Real-Time**
```javascript
// Monitor tutte le richieste AJAX del widget
function monitorWidgetAjax() {
    const originalAjax = $.ajax;
    
    $.ajax = function(options) {
        if (options.data && options.data.action && 
            (options.data.action.includes('subscriber') || 
             options.data.action.includes('recharge') || 
             options.data.action.includes('update_subscriber'))) {
            
            console.group('🌐 WIDGET AJAX REQUEST');
            console.log('Action:', options.data.action);
            console.log('URL:', options.url);
            console.log('Data:', options.data);
            console.log('Timeout:', options.timeout || 'default');
            
            // Wrap success callback
            const originalSuccess = options.success;
            options.success = function(response) {
                console.log('✅ SUCCESS Response:', response);
                console.groupEnd();
                
                // Verifica struttura response
                if (!response || typeof response.success === 'undefined') {
                    console.warn('⚠️ Unexpected response structure:', response);
                }
                
                if (originalSuccess) originalSuccess.apply(this, arguments);
            };
            
            // Wrap error callback
            const originalError = options.error;
            options.error = function(xhr, status, error) {
                console.error('❌ ERROR Response:', {
                    status: status,
                    error: error,
                    xhr: xhr,
                    readyState: xhr.readyState,
                    responseText: xhr.responseText
                });
                console.groupEnd();
                
                if (originalError) originalError.apply(this, arguments);
            };
        }
        
        return originalAjax.call(this, options);
    };
    
    console.log('🎯 AJAX monitoring enabled for subscriber widget');
}

// Attiva monitoring
monitorWidgetAjax();
```

### 3. **Test Sicurezza Response Handling**
```javascript
// Testa la robustezza dell'error handling
function testErrorHandling() {
    console.group('🧪 TESTING ERROR HANDLING');
    
    // Test 1: Response undefined
    console.log('Test 1: Undefined response');
    try {
        const response = undefined;
        const errorMessage = (response && response.data && response.data.message) 
            ? response.data.message 
            : 'Fallback message';
        console.log('✅ Handled undefined response:', errorMessage);
    } catch (e) {
        console.error('❌ Failed undefined response test:', e);
    }
    
    // Test 2: Response.data undefined
    console.log('Test 2: Undefined response.data');
    try {
        const response = { success: false };
        const errorMessage = (response && response.data && response.data.message) 
            ? response.data.message 
            : 'Fallback message';
        console.log('✅ Handled undefined response.data:', errorMessage);
    } catch (e) {
        console.error('❌ Failed response.data test:', e);
    }
    
    // Test 3: subscriberManagementAjax undefined
    console.log('Test 3: Missing AJAX object');
    try {
        const backupAjax = window.subscriberManagementAjax;
        window.subscriberManagementAjax = undefined;
        
        const errorMessage = (subscriberManagementAjax && subscriberManagementAjax.messages && subscriberManagementAjax.messages.recharge_error)
            ? subscriberManagementAjax.messages.recharge_error
            : 'Final fallback';
        console.log('✅ Handled missing AJAX object:', errorMessage);
        
        window.subscriberManagementAjax = backupAjax;
    } catch (e) {
        console.error('❌ Failed AJAX object test:', e);
    }
    
    console.groupEnd();
}

// Esegui test
testErrorHandling();
```

### 4. **Simulatore Scenari Errore**
```javascript
// Simula vari scenari di errore per testare robustezza
function simulateErrorScenarios() {
    console.group('🎭 SIMULATING ERROR SCENARIOS');
    
    // Scenario 1: Server timeout
    function simulateTimeout() {
        console.log('Simulating server timeout...');
        $.ajax({
            url: subscriberManagementAjax.ajax_url,
            type: 'POST',
            timeout: 1, // 1ms timeout for instant failure
            data: {
                action: 'recharge_credits',
                nonce: subscriberManagementAjax.nonce,
                subscriber_id: 'test',
                amount: 10,
                method: 'test'
            },
            success: function(response) {
                console.log('Unexpected success:', response);
            },
            error: function(xhr, status, error) {
                console.log('✅ Timeout handled correctly:', status);
            }
        });
    }
    
    // Scenario 2: Invalid URL
    function simulateInvalidUrl() {
        console.log('Simulating invalid URL...');
        $.ajax({
            url: 'https://invalid-url-test.local/admin-ajax.php',
            type: 'POST',
            timeout: 5000,
            data: { action: 'test' },
            success: function(response) {
                console.log('Unexpected success:', response);
            },
            error: function(xhr, status, error) {
                console.log('✅ Invalid URL handled correctly:', {status, error});
            }
        });
    }
    
    // Scenario 3: Malformed response
    function simulateMalformedResponse() {
        console.log('Testing malformed response handling...');
        const malformedResponses = [
            null,
            undefined,
            {},
            { success: true }, // Missing data
            { data: {} }, // Missing success
            { success: false, data: null }
        ];
        
        malformedResponses.forEach((response, index) => {
            try {
                const errorMessage = (response && response.data && response.data.message) 
                    ? response.data.message 
                    : 'Fallback for malformed response';
                console.log(`✅ Test ${index + 1} passed:`, errorMessage);
            } catch (e) {
                console.error(`❌ Test ${index + 1} failed:`, e);
            }
        });
    }
    
    // Esegui tutti i test
    simulateTimeout();
    setTimeout(simulateInvalidUrl, 1000);
    setTimeout(simulateMalformedResponse, 2000);
    
    console.groupEnd();
}

// Avvia simulazione
simulateErrorScenarios();
```

### 5. **Verifica Performance e Memory**
```javascript
// Monitor performance e memory usage
function monitorPerformance() {
    console.group('📊 PERFORMANCE MONITORING');
    
    // Memory usage
    if (performance.memory) {
        console.log('Memory usage:', {
            used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + ' MB',
            total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + ' MB',
            limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024) + ' MB'
        });
    }
    
    // Event listeners count
    const elementsCounts = {
        'menu-item': $('.menu-item').length,
        'amount-btn': $('.amount-btn').length,
        'payment-method': $('.payment-method').length,
        'forms': $('form').length
    };
    console.log('Elements with event listeners:', elementsCounts);
    
    // Timing performance per click handlers
    let clickTimes = [];
    
    function measureClickPerformance() {
        $('.amount-btn').off('click.perf').on('click.perf', function() {
            const start = performance.now();
            
            // Simula il processing
            setTimeout(() => {
                const end = performance.now();
                const duration = end - start;
                clickTimes.push(duration);
                
                if (duration > 16) { // 60fps = 16ms max
                    console.warn(`⚠️ Slow click handler: ${duration.toFixed(2)}ms`);
                } else {
                    console.log(`✅ Fast click handler: ${duration.toFixed(2)}ms`);
                }
                
                // Report average dopo 5 clicks
                if (clickTimes.length >= 5) {
                    const avg = clickTimes.reduce((a, b) => a + b) / clickTimes.length;
                    console.log(`Average click response: ${avg.toFixed(2)}ms`);
                    clickTimes = [];
                }
            }, 0);
        });
    }
    
    measureClickPerformance();
    console.log('✅ Performance monitoring enabled - click buttons to test');
    console.groupEnd();
}

// Attiva monitoring
monitorPerformance();
```

### 6. **Auto-Fix Script per Errori Comuni**
```javascript
// Script che tenta di auto-correggere errori comuni
function autoFixCommonErrors() {
    console.group('🔧 AUTO-FIX COMMON ERRORS');
    
    let fixesApplied = 0;
    
    // Fix 1: AJAX object mancante
    if (typeof subscriberManagementAjax === 'undefined') {
        console.log('Attempting to fix missing AJAX object...');
        window.subscriberManagementAjax = {
            ajax_url: '/wp-admin/admin-ajax.php',
            nonce: 'fallback-nonce',
            messages: {
                recharge_error: 'Errore durante la ricarica',
                update_error: 'Errore durante l\'aggiornamento'
            }
        };
        console.log('✅ Created fallback AJAX object');
        fixesApplied++;
    }
    
    // Fix 2: Widget object mancante
    if (typeof window.subscriberManagementWidget === 'undefined') {
        console.log('Attempting to fix missing widget object...');
        window.subscriberManagementWidget = {
            showFeedbackMessage: function(message, type) {
                console.log(`Fallback message (${type}):`, message);
                alert(`${type.toUpperCase()}: ${message}`);
            },
            updateCreditDisplay: function(newCredit) {
                console.log('Fallback credit update:', newCredit);
                $('.credit-value').text('€' + newCredit);
            }
        };
        console.log('✅ Created fallback widget object');
        fixesApplied++;
    }
    
    // Fix 3: Button stuck disabled
    const $stuckButtons = $('button:disabled').filter(function() {
        return !$(this).hasClass('processing') && $(this).text().indexOf('spinner') === -1;
    });
    
    if ($stuckButtons.length > 0) {
        console.log(`Found ${$stuckButtons.length} stuck buttons, fixing...`);
        $stuckButtons.prop('disabled', false);
        console.log('✅ Fixed stuck buttons');
        fixesApplied++;
    }
    
    // Fix 4: Missing critical CSS classes
    if ($('.subscriber-management-widget-container').length === 0 && $('.subscriber-widget').length > 0) {
        console.log('Attempting to fix missing CSS classes...');
        $('.subscriber-widget').addClass('subscriber-management-widget-container');
        console.log('✅ Added missing CSS classes');
        fixesApplied++;
    }
    
    console.log(`🏁 Auto-fix completed. ${fixesApplied} fixes applied.`);
    console.groupEnd();
    
    return fixesApplied;
}

// Esegui auto-fix
autoFixCommonErrors();
```

### 7. **Monitor Errori Global**
```javascript
// Cattura tutti gli errori JavaScript nella pagina
function setupGlobalErrorMonitoring() {
    // Counter errori
    let errorCount = 0;
    let widgetErrors = [];
    
    // Handler errori globali
    window.addEventListener('error', function(e) {
        errorCount++;
        
        // Filtra errori relativi al widget
        if (e.filename && 
            (e.filename.includes('subscriber-management-widget') || 
             e.message.includes('subscriberManagement') ||
             e.message.includes('recharge') ||
             e.message.includes('updateCredit'))) {
            
            const errorInfo = {
                timestamp: new Date().toISOString(),
                message: e.message,
                filename: e.filename,
                line: e.lineno,
                column: e.colno,
                stack: e.error ? e.error.stack : 'No stack trace'
            };
            
            widgetErrors.push(errorInfo);
            
            console.group('🚨 WIDGET ERROR DETECTED');
            console.error('Error details:', errorInfo);
            console.log('Total widget errors:', widgetErrors.length);
            console.groupEnd();
            
            // Auto-tentativo fix per errori comuni
            if (e.message.includes('undefined')) {
                console.log('Attempting auto-fix for undefined error...');
                autoFixCommonErrors();
            }
        }
    });
    
    // Handler promise rejections
    window.addEventListener('unhandledrejection', function(e) {
        if (e.reason && e.reason.toString().includes('subscriber')) {
            console.error('🚨 Unhandled promise rejection in widget:', e.reason);
        }
    });
    
    console.log('🛡️ Global error monitoring enabled for subscriber widget');
    
    // Return function per ottenere statistiche
    return function getErrorStats() {
        return {
            totalErrors: errorCount,
            widgetErrors: widgetErrors.length,
            latestErrors: widgetErrors.slice(-5)
        };
    };
}

// Attiva monitoring globale
const getErrorStats = setupGlobalErrorMonitoring();

// Funzione per ottenere report errori
function getErrorReport() {
    const stats = getErrorStats();
    console.group('📊 ERROR REPORT');
    console.log('Statistics:', stats);
    console.groupEnd();
    return stats;
}
```

## 🚀 USO DEGLI SCRIPT

### Quick Debug (1 comando)
```javascript
// Esegui tutto in una volta
(function() {
    debugSubscriberWidget();
    monitorWidgetAjax();
    setupGlobalErrorMonitoring();
    console.log('🔍 Complete debugging setup enabled');
})();
```

### Reset Completo
```javascript
// Reset tutto e riavvia monitoring
(function() {
    // Disabilita monitoring precedenti
    $(document).off('.debug');
    
    // Re-inizializza tutto
    autoFixCommonErrors();
    debugSubscriberWidget();
    monitorWidgetAjax();
    
    console.log('🔄 Debug environment reset and reinitialized');
})();
```

Questi script ti permetteranno di identificare e risolvere rapidamente qualsiasi errore console nel widget di gestione sottoscrittori.
