/**
 * jQuery UI Dialog Fix
 * Funzioni per migliorare il comportamento dei jQuery UI Dialog
 */

(function($) {
    'use strict';

    // Funzione per forzare la corretta chiusura dei dialog
    window.forceCloseDialog = function(selector) {
        try {
            // Tenta di chiudere normalmente
            $(selector).dialog('close');
        } catch (e) {
            console.warn('Error in normal dialog close:', e);
            
            // Fallback: rimuovi manualmente
            $(selector).closest('.ui-dialog').remove();
            $('.ui-widget-overlay').remove();
            $('body').removeClass('ui-dialog-open');
        }
        
        // Pulisci sempre gli errori
        $('.form-errors').text('');
    };

    // Funzione per aggiungere classi utili al body
    function setupDialogBodyClasses() {
        $(document).on('dialogopen', '.ui-dialog-content', function() {
            $('body').addClass('ui-dialog-open');
        });
        
        $(document).on('dialogclose', '.ui-dialog-content', function() {
            // Controlla se ci sono ancora dialog aperti prima di rimuovere la classe
            if ($('.ui-dialog-content:visible').length === 0) {
                $('body').removeClass('ui-dialog-open');
            }
        });
        
        // Aggiungi gestore globale per la chiusura dei dialog con ESC
        $(document).on('keydown', function(e) {
            // ESC key
            if (e.keyCode === 27) {
                // Se c'è un dialog aperto, chiudilo
                if ($('.ui-dialog:visible').length) {
                    $('.ui-dialog-content:visible').each(function() {
                        try {
                            $(this).dialog('close');
                        } catch(e) {
                            console.warn('Error closing dialog with ESC:', e);
                        }
                    });
                    $('.form-errors').text('');
                    $('.ui-widget-overlay').remove();
                }
            }
        });
    }

    // Esegui al DOM ready
    $(function() {
        setupDialogBodyClasses();
        
        // Aggiungi classe per facilitare lo styling di form-errors 
        // solo all'interno di dialog
        if (!$('#jquery-ui-dialog-extra-fix').length) {
            $('head').append(`
                <style id="jquery-ui-dialog-extra-fix">
                    /* Assicura che form-errors sia nascosto quando non è visibile un dialog */
                    body:not(.ui-dialog-open) .form-errors {
                        display: none !important;
                    }
                    
                    /* Fix per dialoghi che non si chiudono correttamente */
                    .ui-dialog-titlebar-close {
                        opacity: 1 !important;
                        visibility: visible !important;
                    }
                </style>
            `);
        }
    });

})(jQuery);
