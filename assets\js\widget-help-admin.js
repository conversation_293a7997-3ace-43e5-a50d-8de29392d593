(function($) {
    'use strict';
    
    class WidgetHelpAdmin {
        constructor() {
            this.currentWidget = 'report_viewer';
            this.currentSection = 'main';
            this.init();
        }
        
        init() {
            this.bindEvents();
            this.initContentEditor();
            this.initAnalytics();
        }
        
        bindEvents() {
            // Widget selection change
            $('#widget-select').on('change', (e) => {
                this.currentWidget = $(e.target).val();
                this.loadWidgetContent();
            });
            
            // Section tab clicks
            $('.section-tab').on('click', (e) => {
                e.preventDefault();
                const section = $(e.target).data('section');
                this.switchSection(section);
            });
            
            // Save content button
            $('.save-help-content').on('click', () => {
                this.saveContent();
            });
            
            // Preview content button
            $('.preview-help-content').on('click', () => {
                this.previewContent();
            });
            
            // Widget enabled toggle
            $('.widget-enabled-toggle').on('change', (e) => {
                const widgetId = $(e.target).data('widget');
                const enabled = $(e.target).is(':checked') ? 1 : 0;
                this.toggleWidget(widgetId, enabled);
            });
            
            // Position change with immediate save
            $('.widget-position-select').on('change', (e) => {
                const widgetId = $(e.target).data('widget');
                const position = $(e.target).val();
                this.updateWidgetPosition(widgetId, position);
            });
            
            // Test widget help
            $('.test-widget-help').on('click', (e) => {
                e.preventDefault();
                const widgetId = $(e.target).data('widget');
                this.testWidgetHelp(widgetId);
            });
            
            // Analytics refresh
            $('.refresh-analytics').on('click', (e) => {
                e.preventDefault();
                this.refreshAnalytics();
            });
        }
        
        initContentEditor() {
            // Initialize any additional editor features
            if (typeof tinymce !== 'undefined') {
                tinymce.init({
                    selector: '#help_content_editor',
                    plugins: 'lists link image table code',
                    toolbar: 'undo redo | formatselect | bold italic | alignleft aligncenter alignright | bullist numlist | link image | code',
                    height: 400
                });
            }
        }
        
        switchSection(section) {
            // Save current content before switching
            this.saveCurrentContent();
            
            // Update UI
            $('.section-tab').removeClass('active');
            $(`.section-tab[data-section="${section}"]`).addClass('active');
            
            this.currentSection = section;
            this.loadSectionContent(section);
        }
        
        loadWidgetContent() {
            const url = new URL(window.location);
            url.searchParams.set('widget', this.currentWidget);
            window.history.pushState({}, '', url);
            
            this.loadSectionContent(this.currentSection);
        }
        
        loadSectionContent(section) {
            $.ajax({
                url: widgetHelpAdmin.ajax_url,
                type: 'POST',
                data: {
                    action: 'get_widget_help',
                    widget_id: this.currentWidget,
                    section: section,
                    nonce: widgetHelpAdmin.nonce
                },
                success: (response) => {
                    if (response.success) {
                        if (typeof tinymce !== 'undefined' && tinymce.get('help_content_editor')) {
                            tinymce.get('help_content_editor').setContent(response.data.content || '');
                        } else {
                            $('#help_content_editor').val(response.data.content || '');
                        }
                    }
                },
                error: () => {
                    this.showNotice('Errore nel caricamento del contenuto', 'error');
                }
            });
        }
        
        saveCurrentContent() {
            let content = '';
            if (typeof tinymce !== 'undefined' && tinymce.get('help_content_editor')) {
                content = tinymce.get('help_content_editor').getContent();
            } else {
                content = $('#help_content_editor').val();
            }
            
            if (content) {
                this.saveContentAjax(this.currentWidget, this.currentSection, content, false);
            }
        }
        
        saveContent() {
            let content = '';
            if (typeof tinymce !== 'undefined' && tinymce.get('help_content_editor')) {
                content = tinymce.get('help_content_editor').getContent();
            } else {
                content = $('#help_content_editor').val();
            }
            
            this.saveContentAjax(this.currentWidget, this.currentSection, content, true);
        }
        
        saveContentAjax(widgetId, section, content, showNotice = true) {
            $.ajax({
                url: widgetHelpAdmin.ajax_url,
                type: 'POST',
                data: {
                    action: 'save_widget_help_content',
                    widget_id: widgetId,
                    section: section,
                    content: content,
                    nonce: widgetHelpAdmin.nonce
                },
                success: (response) => {
                    if (response.success && showNotice) {
                        this.showNotice(widgetHelpAdmin.strings.saved, 'success');
                    }
                },
                error: () => {
                    if (showNotice) {
                        this.showNotice(widgetHelpAdmin.strings.error, 'error');
                    }
                }
            });
        }
        
        previewContent() {
            let content = '';
            if (typeof tinymce !== 'undefined' && tinymce.get('help_content_editor')) {
                content = tinymce.get('help_content_editor').getContent();
            } else {
                content = $('#help_content_editor').val();
            }
            
            // Open preview in new window
            const preview = window.open('', 'preview', 'width=600,height=400,scrollbars=yes');
            preview.document.write(`
                <html>
                <head>
                    <title>Preview Help Content</title>
                    <style>
                        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; padding: 20px; }
                        h3, h4 { color: #2c3e50; }
                        ul { padding-left: 20px; }
                        li { margin-bottom: 8px; }
                    </style>
                </head>
                <body>
                    ${content}
                </body>
                </html>
            `);
            preview.document.close();
        }
        
        toggleWidget(widgetId, enabled) {
            $.ajax({
                url: widgetHelpAdmin.ajax_url,
                type: 'POST',
                data: {
                    action: 'toggle_widget_help',
                    widget_id: widgetId,
                    enabled: enabled,
                    nonce: widgetHelpAdmin.nonce
                },
                success: (response) => {
                    if (response.success) {
                        this.showNotice('Widget aggiornato', 'success');
                    }
                },
                error: () => {
                    this.showNotice('Errore nell\'aggiornamento', 'error');
                }
            });
        }
        
        updateWidgetPosition(widgetId, position) {
            $.ajax({
                url: widgetHelpAdmin.ajax_url,
                type: 'POST',
                data: {
                    action: 'update_widget_position',
                    widget_id: widgetId,
                    position: position,
                    nonce: widgetHelpAdmin.nonce
                },
                success: (response) => {
                    if (response.success) {
                        this.showNotice('Posizione aggiornata', 'success');
                        
                        // Update visual indicator
                        const $row = $(`.widget-position-select[data-widget="${widgetId}"]`).closest('tr');
                        $row.addClass('updated-row');
                        setTimeout(() => $row.removeClass('updated-row'), 2000);
                    }
                },
                error: () => {
                    this.showNotice('Errore nell\'aggiornamento posizione', 'error');
                }
            });
        }
        
        testWidgetHelp(widgetId) {
            // Simulate help activation in a popup
            const testWindow = window.open('', 'test-help', 'width=500,height=600,scrollbars=yes');
            
            $.ajax({
                url: widgetHelpAdmin.ajax_url,
                type: 'POST',
                data: {
                    action: 'get_widget_help',
                    widget_id: widgetId,
                    section: 'main',
                    nonce: widgetHelpAdmin.nonce
                },
                success: (response) => {
                    if (response.success) {
                        testWindow.document.write(`
                            <html>
                            <head>
                                <title>Test Help - ${response.data.widget_name}</title>
                                <style>
                                    body { 
                                        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; 
                                        padding: 20px; 
                                        background: #f0f6fc;
                                    }
                                    .help-container {
                                        background: white;
                                        border-radius: 8px;
                                        padding: 20px;
                                        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                                    }
                                    .help-header {
                                        background: #2271b1;
                                        color: white;
                                        padding: 15px;
                                        margin: -20px -20px 20px -20px;
                                        border-radius: 8px 8px 0 0;
                                    }
                                    h3, h4 { color: #2c3e50; }
                                    ul { padding-left: 20px; }
                                    li { margin-bottom: 8px; line-height: 1.5; }
                                    strong { color: #2980b9; }
                                    .test-note {
                                        background: #fff3cd;
                                        border: 1px solid #ffeaa7;
                                        padding: 10px;
                                        border-radius: 4px;
                                        margin-bottom: 20px;
                                        font-size: 14px;
                                    }
                                </style>
                            </head>
                            <body>
                                <div class="help-container">
                                    <div class="help-header">
                                        <h2>🧪 Test Help: ${response.data.widget_name}</h2>
                                    </div>
                                    <div class="test-note">
                                        <strong>Modalità Test:</strong> Questa è un'anteprima di come apparirà l'help agli utenti.
                                    </div>
                                    ${response.data.content}
                                </div>
                            </body>
                            </html>
                        `);
                        testWindow.document.close();
                    } else {
                        testWindow.document.write('<p>Errore nel caricamento del contenuto help.</p>');
                        testWindow.document.close();
                    }
                },
                error: () => {
                    testWindow.document.write('<p>Errore nella connessione.</p>');
                    testWindow.document.close();
                }
            });
        }
        
        initAnalytics() {
            // Initialize analytics features
            this.updateUsageBars();
        }
        
        updateUsageBars() {
            $('.fa-usage-fill').each(function() {
                const width = $(this).css('width');
                $(this).css('width', '0').animate({ width: width }, 1000);
            });
        }
        
        refreshAnalytics() {
            // Simulate analytics refresh
            this.showNotice('Analytics aggiornate', 'info');
            this.updateUsageBars();
        }
        
        showNotice(message, type = 'info') {
            const notice = $(`
                <div class="notice notice-${type} is-dismissible">
                    <p>${message}</p>
                    <button type="button" class="notice-dismiss">
                        <span class="screen-reader-text">Dismiss this notice.</span>
                    </button>
                </div>
            `);
            
            $('.wrap h1').after(notice);
            
            // Auto dismiss after 3 seconds
            setTimeout(() => {
                notice.fadeOut(() => notice.remove());
            }, 3000);
        }
    }
    
    // Initialize on document ready
    $(document).ready(() => {
        new WidgetHelpAdmin();
        
        // Add visual feedback for updated rows
        $('<style>')
            .prop('type', 'text/css')
            .html(`
                .updated-row {
                    background-color: #d1ecf1 !important;
                    transition: background-color 2s ease;
                }
                .fa-help-header .description {
                    font-size: 14px;
                    line-height: 1.6;
                }
            `)
            .appendTo('head');
    });
    
})(jQuery);
