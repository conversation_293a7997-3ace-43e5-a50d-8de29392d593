<?php
/**
 * Test script per verificare il funzionamento del database per il Widget Help System
 * 
 * Questo script verifica:
 * 1. Il salvataggio dei contenuti nelle opzioni WordPress
 * 2. Il recupero dei contenuti salvati
 * 3. La consistenza della struttura dati
 * 4. La sincronizzazione tra le diverse chiavi di opzione
 */

// Prevenire accesso diretto
if (!defined('ABSPATH')) {
    // Se eseguito standalone, simulare WordPress
    define('ABSPATH', dirname(__FILE__) . '/../../../../');
    
    // Funzioni simulate per test standalone
    function get_option($option_name, $default = false) {
        echo "DEBUG: get_option('$option_name')\n";
        
        // Simulare dati di test
        if ($option_name === 'widget_help_content') {
            return array(
                'report_viewer' => array(
                    'main' => '<h3>Report Viewer Test Content</h3><p>Contenuto di test salvato.</p>'
                ),
                'document_analyzer' => array(
                    'main' => '<h3>Document Analyzer Test Content</h3><p>Contenuto di test per l\'analizzatore.</p>'
                )
            );
        }
        
        if ($option_name === 'widget_help_widget_settings') {
            return array(
                'report_viewer' => array('enabled' => true, 'position' => 'right'),
                'document_analyzer' => array('enabled' => true, 'position' => 'left')
            );
        }
        
        return $default;
    }
    
    function update_option($option_name, $value) {
        echo "DEBUG: update_option('$option_name', " . print_r($value, true) . ")\n";
        return true;
    }
    
    function wp_parse_args($args, $defaults) {
        return array_merge($defaults, (array) $args);
    }
    
    function __($text, $domain = 'default') {
        return $text;
    }
}

echo "<h1>Test Database Widget Help System</h1>\n";

// Includere le classi necessarie
if (file_exists('includes/class-widget-help-system.php')) {
    require_once('includes/class-widget-help-system.php');
}

/**
 * Test 1: Verifica struttura dati nelle opzioni WordPress
 */
function test_database_structure() {
    echo "<h2>Test 1: Verifica Struttura Database</h2>\n";
    
    // Verificare le opzioni esistenti
    $content_option = get_option('widget_help_content', array());
    $settings_option = get_option('widget_help_widget_settings', array());
    $legacy_option = get_option('widget_help_contents', array());
    
    echo "<h3>Opzione 'widget_help_content':</h3>\n";
    echo "<pre>" . print_r($content_option, true) . "</pre>\n";
    
    echo "<h3>Opzione 'widget_help_widget_settings':</h3>\n";
    echo "<pre>" . print_r($settings_option, true) . "</pre>\n";
    
    echo "<h3>Opzione 'widget_help_contents' (legacy):</h3>\n";
    echo "<pre>" . print_r($legacy_option, true) . "</pre>\n";
    
    // Controllare inconsistenze
    if (!empty($legacy_option) && empty($content_option)) {
        echo "<p style='color: orange;'>⚠️ Rilevata inconsistenza: dati nella chiave legacy ma non nella nuova.</p>\n";
    }
    
    return array($content_option, $settings_option, $legacy_option);
}

/**
 * Test 2: Verifica del salvataggio di contenuti
 */
function test_content_saving() {
    echo "<h2>Test 2: Verifica Salvataggio Contenuti</h2>\n";
    
    // Creare contenuti di test
    $test_widgets = array(
        'report_viewer' => array(
            'main' => '<h3>Report Viewer</h3><p>Contenuto di test per il visualizzatore report.</p>',
            'advanced' => '<h4>Funzioni Avanzate</h4><ul><li>Export PDF</li><li>Grafici interattivi</li></ul>'
        ),
        'document_analyzer' => array(
            'main' => '<h3>Document Analyzer</h3><p>Guida per l\'analisi documenti.</p>',
            'setup' => '<h4>Configurazione</h4><p>Impostazioni iniziali dell\'analizzatore.</p>'
        )
    );
    
    // Simulare il salvataggio
    echo "<h3>Simulazione salvataggio contenuti:</h3>\n";
    foreach ($test_widgets as $widget_id => $sections) {
        foreach ($sections as $section => $content) {
            echo "<p>Salvataggio: $widget_id -> $section</p>\n";
            
            // Simulare l'operazione di salvataggio
            $saved_content = get_option('widget_help_content', array());
            if (!isset($saved_content[$widget_id])) {
                $saved_content[$widget_id] = array();
            }
            $saved_content[$widget_id][$section] = $content;
            update_option('widget_help_content', $saved_content);
        }
    }
    
    return $test_widgets;
}

/**
 * Test 3: Verifica del recupero di contenuti
 */
function test_content_retrieval() {
    echo "<h2>Test 3: Verifica Recupero Contenuti</h2>\n";
    
    // Recuperare i contenuti salvati
    $saved_content = get_option('widget_help_content', array());
    
    echo "<h3>Contenuti recuperati:</h3>\n";
    echo "<pre>" . print_r($saved_content, true) . "</pre>\n";
    
    // Test di recupero specifico
    $test_cases = array(
        array('widget_id' => 'report_viewer', 'section' => 'main'),
        array('widget_id' => 'document_analyzer', 'section' => 'setup'),
        array('widget_id' => 'nonexistent', 'section' => 'main')
    );
    
    foreach ($test_cases as $test) {
        $widget_id = $test['widget_id'];
        $section = $test['section'];
        
        if (isset($saved_content[$widget_id][$section])) {
            $content = $saved_content[$widget_id][$section];
            echo "<p>✓ Recuperato: $widget_id/$section = " . substr($content, 0, 50) . "...</p>\n";
        } else {
            echo "<p>✗ Non trovato: $widget_id/$section</p>\n";
        }
    }
}

/**
 * Test 4: Verifica consistenza con Widget Help System
 */
function test_widget_help_system_integration() {
    echo "<h2>Test 4: Integrazione Widget Help System</h2>\n";
    
    if (class_exists('Widget_Help_System')) {
        echo "<p>✓ Classe Widget_Help_System disponibile</p>\n";
        
        // Test di inizializzazione
        try {
            global $widget_help_system;
            if (!$widget_help_system) {
                $widget_help_system = new Widget_Help_System();
            }
            
            echo "<p>✓ Istanza Widget_Help_System creata</p>\n";
            
            // Test di recupero widget registrati
            $widgets = $widget_help_system->get_all_widgets();
            echo "<h3>Widget registrati (" . count($widgets) . "):</h3>\n";
            foreach ($widgets as $widget_id => $config) {
                echo "<p>- $widget_id: {$config['name']}</p>\n";
            }
            
            // Test di recupero contenuto
            $test_content = $widget_help_system->get_help_content('report_viewer', 'main');
            if (!empty($test_content)) {
                echo "<p>✓ Contenuto recuperato per report_viewer/main</p>\n";
            } else {
                echo "<p>⚠️ Nessun contenuto per report_viewer/main (usando default)</p>\n";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ Errore nell'inizializzazione: " . $e->getMessage() . "</p>\n";
        }
    } else {
        echo "<p style='color: red;'>✗ Classe Widget_Help_System non disponibile</p>\n";
    }
}

/**
 * Test 5: Migrazione dati legacy
 */
function test_legacy_migration() {
    echo "<h2>Test 5: Migrazione Dati Legacy</h2>\n";
    
    $legacy_data = get_option('widget_help_contents', array());
    $current_data = get_option('widget_help_content', array());
    
    if (!empty($legacy_data) && empty($current_data)) {
        echo "<p>⚠️ Rilevati dati legacy che necessitano migrazione</p>\n";
        echo "<h3>Dati legacy da migrare:</h3>\n";
        echo "<pre>" . print_r($legacy_data, true) . "</pre>\n";
        
        // Simulare migrazione
        echo "<h3>Simulazione migrazione:</h3>\n";
        $migrated_data = array();
        foreach ($legacy_data as $widget_id => $content) {
            if (is_string($content)) {
                $migrated_data[$widget_id] = array('main' => $content);
            } else {
                $migrated_data[$widget_id] = $content;
            }
        }
        
        echo "<p>Migrazione di " . count($legacy_data) . " widget completata</p>\n";
        update_option('widget_help_content', $migrated_data);
        
    } else {
        echo "<p>✓ Nessuna migrazione necessaria</p>\n";
    }
}

// Eseguire tutti i test
echo "<div style='font-family: Arial, sans-serif; margin: 20px;'>\n";

$structure_data = test_database_structure();
$saved_data = test_content_saving();
test_content_retrieval();
test_widget_help_system_integration();
test_legacy_migration();

echo "<h2>Riepilogo Test</h2>\n";
echo "<p>✓ Test di struttura database completato</p>\n";
echo "<p>✓ Test di salvataggio contenuti completato</p>\n";
echo "<p>✓ Test di recupero contenuti completato</p>\n";
echo "<p>✓ Test di integrazione sistema completato</p>\n";
echo "<p>✓ Test di migrazione legacy completato</p>\n";

echo "</div>\n";

// Se eseguito in WordPress, aggiungere anche verifica tramite AJAX
if (defined('DOING_AJAX') || (defined('ABSPATH') && function_exists('wp_ajax_nopriv_test_widget_help_db'))) {
    add_action('wp_ajax_test_widget_help_db', 'ajax_test_widget_help_database');
    add_action('wp_ajax_nopriv_test_widget_help_db', 'ajax_test_widget_help_database');
    
    function ajax_test_widget_help_database() {
        ob_start();
        include __FILE__;
        $output = ob_get_clean();
        
        wp_send_json_success(array(
            'message' => 'Test database completato',
            'output' => $output
        ));
    }
}
?>
