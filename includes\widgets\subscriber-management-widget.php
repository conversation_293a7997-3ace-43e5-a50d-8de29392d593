<?php
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Subscriber Management Widget
 *
 * Widget per la gestione dei dati sottoscrittori con menu sinistro per:
 * 1. Dati di Accesso
 * 2. Consumi e Statistiche
 * 3. Ricarica Crediti
 */
class Subscriber_Management_Widget extends WP_Widget {

    public function __construct() {
        parent::__construct(
            'subscriber_management_widget',
            __('Subscriber Management Widget', 'document-viewer-plugin'),
            array('description' => __('Widget per la gestione dei dati sottoscrittori con statistiche e ricarica crediti.', 'document-viewer-plugin'))
        );

        // Enqueue scripts and styles - FIXED: Properly hooked
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));

        // AJAX handlers
        add_action('wp_ajax_update_subscriber_data', array($this, 'update_subscriber_data'));
        add_action('wp_ajax_nopriv_update_subscriber_data', array($this, 'update_subscriber_data'));
        add_action('wp_ajax_recharge_credits', array($this, 'recharge_credits'));
        add_action('wp_ajax_nopriv_recharge_credits', array($this, 'recharge_credits'));
        add_action('wp_ajax_get_current_credit', array($this, 'get_current_credit'));
        add_action('wp_ajax_nopriv_get_current_credit', array($this, 'get_current_credit'));
    }

    /**
     * Enqueue scripts and styles
     */
    public function enqueue_scripts() {
        // Carica il CSS principale del widget
        wp_enqueue_style('subscriber-management-widget-css',
            plugin_dir_url(dirname(dirname(__FILE__))) . 'assets/css/subscriber-management-widget.css',
            array(), '1.0.2');

        wp_enqueue_script('subscriber-management-widget-js',
            plugin_dir_url(dirname(dirname(__FILE__))) . 'assets/js/subscriber-management-widget.js',
            array('jquery'), '1.0.2', true);

        wp_localize_script('subscriber-management-widget-js', 'subscriberManagementAjax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('subscriber_management_nonce'),
            'messages' => array(
                'update_success' => __('Dati aggiornati con successo!', 'document-viewer-plugin'),
                'update_error' => __('Errore durante l\'aggiornamento dei dati.', 'document-viewer-plugin'),
                'recharge_success' => __('Crediti ricaricati con successo!', 'document-viewer-plugin'),
                'recharge_error' => __('Errore durante la ricarica dei crediti.', 'document-viewer-plugin'),
                'login_required' => __('Devi essere loggato per accedere a questa funzione.', 'document-viewer-plugin')
            )
        ));
    }

    /**
     * Widget form in admin
     */
    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : __('Gestione Sottoscrittore', 'document-viewer-plugin');
        ?>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('title')); ?>"><?php _e('Titolo:', 'document-viewer-plugin'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('title')); ?>"
                   name="<?php echo esc_attr($this->get_field_name('title')); ?>" type="text"
                   value="<?php echo esc_attr($title); ?>">
        </p>
        <?php
    }

    /**
     * Update widget settings
     */
    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? sanitize_text_field($new_instance['title']) : '';
        return $instance;
    }

    /**
     * Front-end display del widget
     */
    public function widget($args, $instance) {
        // Debug: verifica cookie e stato autenticazione
        $debug_info = array();
        $debug_info['cookie_exists'] = isset($_COOKIE['fa_subscriber_login']);
        $debug_info['cookie_value'] = isset($_COOKIE['fa_subscriber_login']) ? substr($_COOKIE['fa_subscriber_login'], 0, 50) . '...' : 'N/A';
        $debug_info['wp_user_logged'] = is_user_logged_in();

        if (class_exists('FA_Access_Control') && function_exists('fa_access_control')) {
            $access_control = fa_access_control();
            $debug_info['is_subscriber'] = $access_control->is_current_user_subscriber();
        }

        // Prova anche con le funzioni helper
        if (function_exists('fa_is_subscriber')) {
            $debug_info['fa_is_subscriber'] = fa_is_subscriber();
        }

        if (function_exists('fa_get_subscriber_data')) {
            $helper_data = fa_get_subscriber_data();
            $debug_info['helper_data_exists'] = !empty($helper_data);
        }

        // Verifica se l'utente è loggato come sottoscrittore
        $user_data = $this->get_current_subscriber_data();

        // Se non trova dati, prova con la funzione helper
        if (!$user_data && function_exists('fa_get_subscriber_data')) {
            $helper_data = fa_get_subscriber_data();
            if ($helper_data && isset($helper_data['id'])) {
                global $wpdb;
                $user_data = $wpdb->get_row($wpdb->prepare(
                    "SELECT * FROM wpcd_user_subscription WHERE id = %d",
                    intval($helper_data['id'])
                ), ARRAY_A);
            }
        }

        if (!$user_data) {
            echo $args['before_widget'];
            echo '<div class="subscriber-management-widget-container">';
            echo '<div class="login-required-message">';
            echo '<h3>' . __('Accesso Richiesto', 'document-viewer-plugin') . '</h3>';
            echo '<p>' . __('Devi essere loggato come sottoscrittore per accedere a questa sezione.', 'document-viewer-plugin') . '</p>';

            // Debug temporaneo
            echo '<div style="background: #f0f0f0; padding: 10px; margin: 10px 0; font-size: 12px;">';
            echo '<strong>Debug Info:</strong><br>';
            foreach ($debug_info as $key => $value) {
                echo $key . ': ' . (is_bool($value) ? ($value ? 'true' : 'false') : $value) . '<br>';
            }
            echo '</div>';

            echo '</div>';
            echo '</div>';
            echo $args['after_widget'];
            return;
        }

        echo $args['before_widget'];

        if (!empty($instance['title'])) {
            echo $args['before_title'] . apply_filters('widget_title', $instance['title']) . $args['after_title'];
        }

        $this->render_widget_content($user_data);

        echo $args['after_widget'];
    }

    /**
     * Ottieni i dati del sottoscrittore corrente
     */
    private function get_current_subscriber_data() {
        // Prima verifica usando la classe FA_Access_Control se disponibile
        if (class_exists('FA_Access_Control') && function_exists('fa_access_control')) {
            $access_control = fa_access_control();
            if ($access_control->is_current_user_subscriber()) {
                $subscriber_data = $access_control->get_current_subscriber_data();
                if ($subscriber_data && isset($subscriber_data['id'])) {
                    global $wpdb;
                    $user_data = $wpdb->get_row($wpdb->prepare(
                        "SELECT * FROM wpcd_user_subscription WHERE id = %d",
                        intval($subscriber_data['id'])
                    ), ARRAY_A);

                    if ($user_data) {
                        return $user_data;
                    }
                }
            }
        }

        // Fallback: verifica tramite cookie direttamente (con decodifica base64)
        if (isset($_COOKIE['fa_subscriber_login']) && !empty($_COOKIE['fa_subscriber_login'])) {
            try {
                // Decodifica il cookie come nel resto del sistema
                $cookie_data = stripslashes($_COOKIE['fa_subscriber_login']);
                $subscriber_data = json_decode(base64_decode($cookie_data), true);

                if ($subscriber_data && isset($subscriber_data['id'])) {
                    global $wpdb;
                    $user_data = $wpdb->get_row($wpdb->prepare(
                        "SELECT * FROM wpcd_user_subscription WHERE id = %d",
                        intval($subscriber_data['id'])
                    ), ARRAY_A);

                    if ($user_data) {
                        return $user_data;
                    }
                }
            } catch (Exception $e) {
                // Log dell'errore se disponibile
                if (function_exists('dv_debug_log')) {
                    dv_debug_log('Errore decodifica cookie subscriber management: ' . $e->getMessage());
                }
            }
        }

        // Verifica se è un utente WordPress con dati nella tabella sottoscrittori
        if (is_user_logged_in()) {
            $current_user = wp_get_current_user();
            global $wpdb;
            $user_data = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM wpcd_user_subscription WHERE email = %s",
                $current_user->user_email
            ), ARRAY_A);

            if ($user_data) {
                return $user_data;
            }
        }

        // Fallback: dati di test per utenti loggati senza dati nella tabella sottoscrittori
        if (is_user_logged_in()) {
            $current_user = wp_get_current_user();
            return array(
                'id' => 999,
                'username' => $current_user->user_login,
                'email' => $current_user->user_email,
                'name' => $current_user->first_name ?: 'Nome',
                'surname' => $current_user->last_name ?: 'Cognome',
                'phone' => '+39 ************',
                'tipo_subscription' => 'Premium',
                'analysis_count' => 15,
                'tokens_used' => 8500,
                'tot_cost' => 85.50,
                'actual_cost' => 12.30,
                'credit' => 150.00,
                'created_at' => '2024-01-01 10:00:00',
                'updated_at' => date('Y-m-d H:i:s')
            );
        }

        return null;
    }

    /**
     * Renderizza il contenuto del widget
     */
    private function render_widget_content($user_data) {
        $subscriber_id = isset($user_data['id']) ? $user_data['id'] : 999;
        ?>
        <div class="subscriber-management-widget-container" data-subscriber-id="<?php echo esc_attr($subscriber_id); ?>">
            <!-- Menu laterale -->
            <div class="subscriber-menu-column">
                <div class="subscriber-menu">
                    <div class="menu-item active" data-section="access-data">
                        <i class="fas fa-user"></i>
                        <span><?php _e('Dati di Accesso', 'document-viewer-plugin'); ?></span>
                    </div>
                    <div class="menu-item" data-section="consumption">
                        <i class="fas fa-chart-bar"></i>
                        <span><?php _e('Consumi', 'document-viewer-plugin'); ?></span>
                    </div>
                    <div class="menu-item" data-section="recharge">
                        <i class="fas fa-credit-card"></i>
                        <span><?php _e('Ricarica Crediti', 'document-viewer-plugin'); ?></span>
                    </div>
                </div>
            </div>

            <!-- Contenuto principale -->
            <div class="subscriber-content-column">
                
                <!-- Breadcrumb Navigation -->
                <div class="section-breadcrumb">
                    <?php _e('Dashboard', 'document-viewer-plugin'); ?> <span class="separator">›</span> <span class="current"><?php _e('Dati di Accesso', 'document-viewer-plugin'); ?></span>
                </div>
                
                <!-- Sezione Dati di Accesso -->
                <div class="content-section active" id="access-data-section">
                    <?php $this->render_access_data_section($user_data); ?>
                </div>

                <!-- Sezione Consumi -->
                <div class="content-section" id="consumption-section">
                    <?php $this->render_consumption_section($user_data); ?>
                </div>

                <!-- Sezione Ricarica Crediti -->
                <div class="content-section" id="recharge-section">
                    <?php $this->render_recharge_section($user_data); ?>
                </div>
            </div>
        </div>

        <!-- Messaggi di feedback -->
        <div id="subscriber-feedback-message" class="feedback-message" style="display: none;"></div>

        <script>
            // Funzione di ripristino grid in caso di sovrascritture
            document.addEventListener('DOMContentLoaded', function() {
                // Funzione per garantire il layout grid
                function ensureGridLayout() {
                    var usageDataGrid = document.getElementById('subscriber-usage-data-grid');
                    if (usageDataGrid) {
                        var computedStyle = window.getComputedStyle(usageDataGrid);
                        if (computedStyle.display !== 'grid') {
                            console.log('Ripristino forzato layout grid per subscriber usage data');
                            usageDataGrid.classList.add('js-ensure-grid');

                            // Forza il repaint applicando inline style
                            usageDataGrid.style.display = 'grid';
                            usageDataGrid.style.gridTemplateColumns = 'repeat(auto-fit, minmax(200px, 1fr))';
                            usageDataGrid.style.gap = '20px';
                        }
                    }
                }

                // Esegui immediatamente
                ensureGridLayout();

                // Ricontrolla dopo che la pagina è completamente caricata
                window.addEventListener('load', ensureGridLayout);

                // E periodicamente per sicurezza
                setTimeout(ensureGridLayout, 500);
                setTimeout(ensureGridLayout, 1000);
            });
        </script>

        <div class="consumption-details">
        <?php
    }

    /**
     * Renderizza la sezione dati di accesso
     */
    private function render_access_data_section($user_data) {
        ?>
        <div class="section-header">
            <h3><?php _e('Dati di Accesso', 'document-viewer-plugin'); ?></h3>
            <p><?php _e('Visualizza e modifica i tuoi dati personali', 'document-viewer-plugin'); ?></p>
        </div>

        <form id="access-data-form" class="subscriber-form">
            <div class="form-row">
                <div class="form-group">
                    <label for="subscriber-username"><?php _e('Username', 'document-viewer-plugin'); ?></label>
                    <input type="text" id="subscriber-username" name="username"
                           value="<?php echo esc_attr($user_data['username']); ?>" readonly>
                </div>
                <div class="form-group">
                    <label for="subscriber-email"><?php _e('Email', 'document-viewer-plugin'); ?></label>
                    <input type="email" id="subscriber-email" name="email"
                           value="<?php echo esc_attr($user_data['email']); ?>" readonly>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="subscriber-name"><?php _e('Nome', 'document-viewer-plugin'); ?></label>
                    <input type="text" id="subscriber-name" name="name"
                           value="<?php echo esc_attr($user_data['name']); ?>">
                </div>
                <div class="form-group">
                    <label for="subscriber-surname"><?php _e('Cognome', 'document-viewer-plugin'); ?></label>
                    <input type="text" id="subscriber-surname" name="surname"
                           value="<?php echo esc_attr($user_data['surname']); ?>">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="subscriber-phone"><?php _e('Telefono', 'document-viewer-plugin'); ?></label>
                    <input type="tel" id="subscriber-phone" name="phone"
                           value="<?php echo esc_attr($user_data['phone']); ?>">
                </div>
                <div class="form-group">
                    <label for="subscriber-subscription"><?php _e('Tipo Sottoscrizione', 'document-viewer-plugin'); ?></label>
                    <input type="text" id="subscriber-subscription" name="tipo_subscription"
                           value="<?php echo esc_attr($user_data['tipo_subscription']); ?>" readonly>
                </div>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn-primary">
                    <i class="fas fa-save"></i>
                    <?php _e('Aggiorna Dati', 'document-viewer-plugin'); ?>
                </button>
            </div>

            <input type="hidden" name="subscriber_id" value="<?php echo esc_attr($user_data['id']); ?>">
        </form>
        <?php
    }

    /**
     * Renderizza la sezione consumi
     */
    private function render_consumption_section($user_data) {
        // Calcola alcuni dati aggiuntivi per la visualizzazione
        $days_active = round((time() - strtotime($user_data['created_at'])) / 86400);
        $avg_tokens_per_analysis = $user_data['analysis_count'] > 0 ? round($user_data['tokens_used'] / $user_data['analysis_count']) : 0;
        ?>
        <div class="section-header">
            <h3><?php _e('Statistiche Consumi', 'document-viewer-plugin'); ?> <span class="header-badge"><?php _e('Dashboard', 'document-viewer-plugin'); ?></span></h3>
            <p><?php _e('Monitora il tuo utilizzo del servizio e visualizza le statistiche dettagliate in tempo reale', 'document-viewer-plugin'); ?></p>
        </div>

        <!-- Contenitore griglia utilizzo dati con classi uniche per subscriber management -->
        <div id="subscriber-usage-data-grid" class="subscriber-usage-wrapper consumption-data-grid" data-widget-type="subscriber-management" data-layout="grid">
            <div class="usage-data-card">
                <div class="usage-data-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="usage-data-content">
                    <div class="usage-data-value"><?php echo esc_html($user_data['analysis_count'] ?: 0); ?></div>
                    <div class="usage-data-label"><?php _e('Analisi Effettuate', 'document-viewer-plugin'); ?></div>
                </div>
            </div>

            <div class="usage-data-card">
                <div class="usage-data-icon">
                    <i class="fas fa-coins"></i>
                </div>
                <div class="usage-data-content">
                    <div class="usage-data-value"><?php echo esc_html(number_format($user_data['tokens_used'] ?: 0)); ?></div>
                    <div class="usage-data-label"><?php _e('Token Utilizzati', 'document-viewer-plugin'); ?></div>
                </div>
            </div>

            <div class="usage-data-card">
                <div class="usage-data-icon">
                    <i class="fas fa-euro-sign"></i>
                </div>
                <div class="usage-data-content">
                    <div class="usage-data-value">€<?php echo esc_html(number_format($user_data['tot_cost'] ?: 0, 2, ',', '.')); ?></div>
                    <div class="usage-data-label"><?php _e('Costo Totale', 'document-viewer-plugin'); ?></div>
                </div>
            </div>

            <div class="usage-data-card credit-card">
                <div class="usage-data-icon">
                    <i class="fas fa-wallet"></i>
                </div>
                <div class="usage-data-content">
                    <div class="usage-data-value" id="consumption-credit-display">€<?php echo esc_html(number_format($user_data['credit'] ?: 0, 2, ',', '.')); ?></div>
                    <div class="usage-data-label"><?php _e('Credito Disponibile', 'document-viewer-plugin'); ?></div>
                </div>
            </div>
        </div>

        <div class="consumption-details">
            <h4><?php _e('Dettagli Utilizzo', 'document-viewer-plugin'); ?></h4>
            <div class="detail-row">
                <span class="detail-label"><?php _e('Ultima Analisi:', 'document-viewer-plugin'); ?></span>
                <span class="detail-value"><?php echo esc_html(date('d/m/Y H:i', strtotime($user_data['updated_at']))); ?></span>
            </div>
            <div class="detail-row">
                <span class="detail-label"><?php _e('Registrazione:', 'document-viewer-plugin'); ?></span>
                <span class="detail-value"><?php echo esc_html(date('d/m/Y', strtotime($user_data['created_at']))); ?></span>
            </div>
            <div class="detail-row">
                <span class="detail-label"><?php _e('Giorni di attività:', 'document-viewer-plugin'); ?></span>
                <span class="detail-value"><?php echo esc_html($days_active); ?> <?php _e('giorni', 'document-viewer-plugin'); ?></span>
            </div>
            <div class="detail-row">
                <span class="detail-label"><?php _e('Costo Attuale:', 'document-viewer-plugin'); ?></span>
                <span class="detail-value">€<?php echo esc_html(number_format($user_data['actual_cost'] ?: 0, 2, ',', '.')); ?></span>
            </div>
            <div class="detail-row">
                <span class="detail-label"><?php _e('Media Token/Analisi:', 'document-viewer-plugin'); ?></span>
                <span class="detail-value"><?php echo esc_html(number_format($avg_tokens_per_analysis)); ?> tokens</span>
            </div>
            <div class="detail-row">
                <span class="detail-label"><?php _e('Tipo Sottoscrizione:', 'document-viewer-plugin'); ?></span>
                <span class="detail-value subscription-type"><?php echo esc_html($user_data['tipo_subscription']); ?></span>
            </div>
        </div>
        <?php
    }

    /**
     * Renderizza la sezione ricarica crediti
     */
    private function render_recharge_section($user_data) {
        ?>
        <div class="section-header">
            <h3><?php _e('Ricarica Crediti', 'document-viewer-plugin'); ?></h3>
            <p><?php _e('Aggiungi crediti al tuo account per continuare ad utilizzare il servizio', 'document-viewer-plugin'); ?></p>
        </div>

        <!-- Contenitore con classe wrapper per isolamento stile -->
        <div class="subscriber-recharge-wrapper current-credit-display">
            <div class="credit-amount">
                <span class="credit-label"><?php _e('Credito Attuale:', 'document-viewer-plugin'); ?></span>
                <span class="credit-value" id="recharge-credit-display">€<?php echo esc_html(number_format($user_data['credit'] ?: 0, 2, ',', '.')); ?></span>
            </div>
        </div>

        <div class="recharge-options">
            <h4><?php _e('Seleziona Importo', 'document-viewer-plugin'); ?></h4>
            <div class="amount-buttons">
                <button type="button" class="amount-btn" data-amount="10">€10</button>
                <button type="button" class="amount-btn" data-amount="25">€25</button>
                <button type="button" class="amount-btn" data-amount="50">€50</button>
                <button type="button" class="amount-btn" data-amount="100">€100</button>
            </div>

            <div class="custom-amount">
                <label for="custom-amount-input"><?php _e('Importo Personalizzato:', 'document-viewer-plugin'); ?></label>
                <div class="amount-input-group">
                    <span class="currency-symbol">€</span>
                    <input type="number" id="custom-amount-input" min="5" max="500" step="0.01" placeholder="0.00">
                </div>
            </div>
        </div>

        <div class="payment-methods">
            <h4><?php _e('Metodi di Pagamento', 'document-viewer-plugin'); ?></h4>
            <div class="payment-options">
                <div class="payment-method" data-method="paypal">
                    <i class="fab fa-paypal"></i>
                    <span><?php _e('PayPal', 'document-viewer-plugin'); ?></span>
                </div>
                <div class="payment-method" data-method="stripe">
                    <i class="fas fa-credit-card"></i>
                    <span><?php _e('Carta di Credito', 'document-viewer-plugin'); ?></span>
                </div>
            </div>
        </div>

        <div class="recharge-actions">
            <button type="button" id="proceed-recharge-btn" class="btn-primary" disabled>
                <i class="fas fa-shopping-cart"></i>
                <?php _e('Procedi con la Ricarica', 'document-viewer-plugin'); ?>
            </button>
        </div>

        <input type="hidden" id="selected-amount" value="">
        <input type="hidden" id="selected-method" value="">
        <input type="hidden" id="subscriber-id" value="<?php echo esc_attr($user_data['id']); ?>">
        <?php
    }

    /**
     * AJAX handler per aggiornare i dati del sottoscrittore
     */
    public function update_subscriber_data() {
        // Verifica nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'subscriber_management_nonce')) {
            wp_send_json_error(array('message' => __('Verifica di sicurezza fallita.', 'document-viewer-plugin')));
            return;
        }

        $subscriber_id = intval($_POST['subscriber_id']);
        $name = sanitize_text_field($_POST['name']);
        $surname = sanitize_text_field($_POST['surname']);
        $phone = sanitize_text_field($_POST['phone']);

        if (!$subscriber_id) {
            wp_send_json_error(array('message' => __('ID sottoscrittore non valido.', 'document-viewer-plugin')));
            return;
        }

        global $wpdb;
        $result = $wpdb->update(
            'wpcd_user_subscription',
            array(
                'name' => $name,
                'surname' => $surname,
                'phone' => $phone,
                'updated_at' => current_time('mysql')
            ),
            array('id' => $subscriber_id),
            array('%s', '%s', '%s', '%s'),
            array('%d')
        );

        if ($result !== false) {
            wp_send_json_success(array('message' => __('Dati aggiornati con successo!', 'document-viewer-plugin')));
        } else {
            wp_send_json_error(array('message' => __('Errore durante l\'aggiornamento dei dati.', 'document-viewer-plugin')));
        }
    }

    /**
     * AJAX handler per la ricarica crediti
     */
    public function recharge_credits() {
        // Verifica nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'subscriber_management_nonce')) {
            wp_send_json_error(array('message' => __('Verifica di sicurezza fallita.', 'document-viewer-plugin')));
            return;
        }

        $subscriber_id = intval($_POST['subscriber_id']);
        $amount = floatval($_POST['amount']);
        $method = sanitize_text_field($_POST['method']);

        if (!$subscriber_id || $amount <= 0) {
            wp_send_json_error(array('message' => __('Dati non validi per la ricarica.', 'document-viewer-plugin')));
            return;
        }

        // Per ora simuliamo il pagamento - in futuro integrare con gateway reali
        global $wpdb;

        // Ottieni il credito attuale
        $current_credit = $wpdb->get_var($wpdb->prepare(
            "SELECT credit FROM wpcd_user_subscription WHERE id = %d",
            $subscriber_id
        ));

        if ($current_credit === null) {
            wp_send_json_error(array('message' => __('Sottoscrittore non trovato.', 'document-viewer-plugin')));
            return;
        }

        $new_credit = $current_credit + $amount;

        // Aggiorna il credito
        $result = $wpdb->update(
            'wpcd_user_subscription',
            array(
                'credit' => $new_credit,
                'updated_at' => current_time('mysql')
            ),
            array('id' => $subscriber_id),
            array('%f', '%s'),
            array('%d')
        );

        if ($result !== false) {
            // Log della transazione (opzionale - creare tabella transazioni)
            $this->log_credit_transaction($subscriber_id, $amount, $method);

            wp_send_json_success(array(
                'message' => sprintf(__('Crediti ricaricati con successo! Nuovo saldo: €%.2f', 'document-viewer-plugin'), $new_credit),
                'new_credit' => number_format($new_credit, 2, ',', '.')
            ));
        } else {
            wp_send_json_error(array('message' => __('Errore durante la ricarica dei crediti.', 'document-viewer-plugin')));
        }
    }

    /**
     * AJAX handler per ottenere il valore attuale del credito
     */
    public function get_current_credit() {
        // Verifica nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'subscriber_management_nonce')) {
            wp_send_json_error(array('message' => __('Verifica di sicurezza fallita.', 'document-viewer-plugin')));
            return;
        }

        // Ottieni i dati del sottoscrittore corrente
        $user_data = $this->get_current_subscriber_data();

        if (!$user_data) {
            wp_send_json_error(array('message' => __('Sottoscrittore non trovato.', 'document-viewer-plugin')));
            return;
        }

        // Restituisci il credito corrente
        wp_send_json_success(array(
            'credit' => number_format($user_data['credit'] ?: 0, 2, '.', ''),
            'formatted_credit' => number_format($user_data['credit'] ?: 0, 2, ',', '.'),
            'subscriber_id' => $user_data['id']
        ));
    }

    /**
     * Log delle transazioni di credito
     */
    private function log_credit_transaction($subscriber_id, $amount, $method) {
        global $wpdb;

        // Usa la tabella esistente creata dalla funzione database-setup
        $table_name = $wpdb->prefix . 'credit_transactions';

        // Inserisci la transazione usando la struttura completa
        $result = $wpdb->insert(
            $table_name,
            array(
                'subscriber_id' => $subscriber_id,
                'amount' => $amount,
                'transaction_type' => 'recharge',
                'payment_method' => $method,
                'status' => 'completed',
                'transaction_id' => null, // Per ora null, in futuro potrà contenere l'ID della transazione del gateway
                'notes' => sprintf(__('Credit recharge via %s from subscriber widget', 'document-viewer-plugin'), $method)
            ),
            array('%d', '%f', '%s', '%s', '%s', '%s', '%s')
        );

        if ($result === false) {
            error_log('Error inserting credit transaction: ' . $wpdb->last_error);
        }
    }
}

/**
 * Registra il widget
 */
function register_subscriber_management_widget() {
    register_widget('Subscriber_Management_Widget');
}
add_action('widgets_init', 'register_subscriber_management_widget');

/**
 * Shortcode per il widget di gestione sottoscrittori
 */
function subscriber_management_shortcode($atts) {
    $atts = shortcode_atts(array(
        'title' => __('Gestione Sottoscrittore', 'document-viewer-plugin'),
    ), $atts);

    // Inizia l'output del buffer
    ob_start();

    // Crea un'istanza del widget
    $widget = new Subscriber_Management_Widget();

    // Visualizza il contenuto del widget
    echo '<div class="subscriber-management-shortcode">';
    $widget->widget(array('before_widget' => '', 'after_widget' => '', 'before_title' => '<h3>', 'after_title' => '</h3>'), $atts);
    echo '</div>';

    // Restituisci il contenuto del buffer
    return ob_get_clean();
}
add_shortcode('subscriber_management', 'subscriber_management_shortcode');
