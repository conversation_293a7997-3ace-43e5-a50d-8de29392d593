# Financial Advisor Plugin - Documentazione Generale

## 📋 Panoramica

Il **Financial Advisor Plugin** è un plugin WordPress complesso e modulare progettato per fornire servizi di analisi finanziaria avanzata utilizzando l'intelligenza artificiale. Il plugin integra funzionalità di analisi documentale, chat AI, gestione utenti, gateway di pagamento e add-in Office.

### Informazioni Plugin
- **Nome**: Financial Advisor Plugin
- **Versione**: 1.1
- **Autore**: Inventyx
- **File Principale**: `document-advisor-plugin.php`

---

## 🔧 Architettura del Plugin

### Struttura delle Cartelle
```
financial-advisor-V4/
├── assets/                          # Risorse statiche
│   ├── css/                        # Fogli di stile
│   ├── js/                         # JavaScript
│   ├── images/                     # Immagini
│   └── logs/                       # Log di sistema
├── includes/                       # Classi core
│   ├── widgets/                    # Widget WordPress
│   ├── class-*.php                 # Classi principali
│   └── *.php                       # File di utilità
├── templates/                      # Template frontend
├── tests/                          # Test automatici
├── vendor/                         # Dipendenze Composer
└── *.php                          # File di configurazione
```

### Classi Principali
1. **Document_Viewer_Plugin** - Classe principale del plugin
2. **Financial_Advisor_Menu_Manager** - Gestione menu amministrazione
3. **Document_Viewer_Settings** - Impostazioni e configurazione
4. **Payment_Gateway_Admin** - Gestione gateway di pagamento
5. **User_Subscription_Admin** - Gestione abbonamenti utenti

---

## 🎛️ Sistema di Menu WordPress

### Menu Principale: "Financial Advisor"
Il plugin registra un menu principale nell'admin di WordPress con i seguenti sottomenu:

#### 1. **Settings** (Impostazioni)
- **URL**: `/wp-admin/admin.php?page=document-viewer-settings`
- **Funzione**: Configurazione API e impostazioni generali
- **Classe**: `Document_Viewer_Settings`

#### 2. **Financial Academy**
- **URL**: `/wp-admin/admin.php?page=financial-academy-manager`
- **Funzione**: Gestione domande finanziarie predefinite
- **Caratteristiche**: Database di domande per chat AI

#### 3. **Office Add-in**
- **URL**: `/wp-admin/admin.php?page=office-addin-manager`
- **Funzione**: Gestione add-in per Microsoft Office
- **Caratteristiche**: Generazione manifest, configurazione

#### 4. **Payment Gateways**
- **URL**: `/wp-admin/admin.php?page=payment-gateways`
- **Funzione**: Configurazione PayPal e Stripe
- **Classe**: `Payment_Gateway_Admin`

#### 5. **User Subscriptions**
- **URL**: `/wp-admin/admin.php?page=user-subscriptions`
- **Funzione**: Gestione utenti e abbonamenti
- **Classe**: `User_Subscription_Admin`

---

## 🔧 Sezione Settings (Backend)

### Configurazione API Primaria

La sezione impostazioni gestisce la configurazione centrale del plugin:

#### **Tab API Settings**
```php
// Opzioni principali
document_viewer_api_key         // Chiave API OpenRouter
document_viewer_api_endpoint    // Endpoint API
document_viewer_model          // Modello AI selezionato
```

#### **Funzionalità Disponibili**
1. **Test Connessione API**
   - Verifica chiave API
   - Test endpoint
   - Validazione modello

2. **Gestione Modelli**
   - Lista modelli disponibili
   - Configurazione modello predefinito
   - Salvataggio rapido

3. **Debug e Logging**
   - Visualizzazione log in tempo reale
   - Cancellazione log
   - Monitoraggio errori

#### **AJAX Handlers**
```php
wp_ajax_test_api_connection      // Test connessione
wp_ajax_save_api_key            // Salva chiave API
wp_ajax_test_api_model          // Test modello specifico
wp_ajax_clear_debug_log         // Cancella log debug
wp_ajax_refresh_debug_log       // Aggiorna log
```

### Gestione Query Predefinite

Il sistema include un gestore per domande finanziarie predefinite:

```php
wp_ajax_add_preset_query        // Aggiungi query
wp_ajax_update_preset_query     // Modifica query
wp_ajax_delete_preset_query     // Elimina query
wp_ajax_add_financial_query_set // Aggiungi set di query
```

---

## 🎨 Sistema Widget

Il plugin include 5 widget WordPress principali:

### 1. **Chat Model Widget** (`Chat_Model_Widget`)

#### **Funzionalità**
- Chat interattiva con modelli AI
- Menu domande finanziarie predefinite
- Verifica configurazione API automatica
- Supporto typing effect

#### **File Associati**
- **PHP**: `includes/widgets/chat-model-widget.php`
- **CSS**: `assets/css/chat-model-widget.css`
- **JS**: `assets/js/chat-model-widget.js`

#### **Configurazione Widget**
```php
// Costruttore widget
public function __construct() {
    parent::__construct(
        'chat_model_widget',
        __('Chat Model Widget', 'document-viewer-plugin'),
        array('description' => __('A widget to chat with AI models.'))
    );
}
```

#### **Controlli Sicurezza**
- Verifica configurazione API prima del rendering
- Messaggi differenziati per admin/utenti
- Protezione XSS su output

### 2. **Document Viewer Widget** (`Document_Viewer_Widget`)

#### **Funzionalità**
- Upload e visualizzazione documenti
- Analisi AI di documenti
- Zoom e navigazione documenti
- Integrazione statistiche utente

#### **File Associati**
- **PHP**: `includes/widgets/document-viewer-widget.php`
- **CSS**: `assets/css/document-viewer.css`
- **JS**: `assets/js/document-viewer.js`

#### **Shortcode Support**
```php
// Utilizzabile come shortcode
echo do_shortcode('[document_viewer title="Analisi Documento"]');
```

### 3. **User Subscription Widget** (`User_Subscription_Widget`)

#### **Funzionalità**
- Form di registrazione utenti
- Validazione dati client-side
- Integrazione con sistema pagamenti
- UI responsiva con validazione in tempo reale

#### **File Associati**
- **PHP**: `includes/widgets/user-subscription-widget.php`
- **CSS**: `assets/css/user-subscription-widget.css`
- **JS**: `assets/js/user-subscription-widget.js`

#### **Validazioni Implementate**
```javascript
// Validazioni JavaScript
- Email format validation
- Password strength checker
- Password confirmation match
- Required fields validation
- Phone number validation
```

#### **Caratteristiche Sicurezza**
- Sanitizzazione input server-side
- Nonce verification
- Prevenzione XSS
- Validazione CSRF

### 4. **Login Widget** (`Unified_Login_Widget`)

#### **Funzionalità**
- Login unificato (WordPress + Subscriber)
- Reset password
- Dual authentication system
- Gestione cookie personalizzati

#### **File Associati**
- **PHP**: `includes/widgets/login-widget.php`
- **CSS**: `assets/css/login-widget.css`
- **JS**: `assets/js/login-widget.js`

#### **AJAX Handlers**
```php
wp_ajax_nopriv_unified_login          // Login utenti
wp_ajax_nopriv_request_password_reset // Reset password
```

#### **Sistema Autenticazione**
- Supporto utenti WordPress standard
- Sistema subscriber personalizzato
- Gestione cookie `fa_subscriber_login`
- Redirect personalizzabili

### 5. **Subscriber Management Widget** (`Subscriber_Management_Widget`)

#### **Funzionalità**
- Gestione dati sottoscrittore
- Visualizzazione statistiche utilizzo
- Sistema ricarica crediti
- Dashboard personalizzata

#### **File Associati**
- **PHP**: `includes/widgets/subscriber-management-widget.php`
- **CSS**: `assets/css/subscriber-management-widget.css`
- **JS**: `assets/js/subscriber-management-widget.js`

#### **Sezioni Interface**
1. **Dati di Accesso**
   - Visualizzazione informazioni utente
   - Modifica dati personali
   - Gestione password

2. **Consumi e Statistiche**
   - Crediti rimanenti
   - Analisi effettuate
   - Token utilizzati
   - Costi sostenuti

3. **Ricarica Crediti**
   - Integrazione gateway pagamento
   - Selezione importi predefiniti
   - Storico transazioni

#### **Debug e Monitoring**
```php
// Debug info disponibile
$debug_info['cookie_exists']     // Presenza cookie
$debug_info['cookie_value']      // Valore cookie (mascherato)
$debug_info['wp_user_logged']    // Stato login WordPress
$debug_info['is_subscriber']     // Stato subscriber
```

---

## 💾 Sistema Database

### Tabelle Principali

#### **wp_document_analysis**
```sql
CREATE TABLE wp_document_analysis (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    filename VARCHAR(255),
    analysis_text LONGTEXT,
    tokens_used INT,
    analysis_date DATETIME,
    INDEX(user_id),
    INDEX(analysis_date)
);
```

#### **wp_preset_queries**
```sql
CREATE TABLE wp_preset_queries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    question_text TEXT,
    category VARCHAR(100),
    created_at DATETIME,
    INDEX(category)
);
```

#### **Payment Gateway Tables**
```sql
-- PayPal Configuration
CREATE TABLE wp_paypal_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client_id VARCHAR(255),
    client_secret VARCHAR(255),
    sandbox_mode BOOLEAN,
    webhook_url VARCHAR(255),
    created_at DATETIME,
    updated_at DATETIME
);

-- Stripe Configuration
CREATE TABLE wp_stripe_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    publishable_key VARCHAR(255),
    secret_key VARCHAR(255),
    webhook_secret VARCHAR(255),
    test_mode BOOLEAN,
    created_at DATETIME,
    updated_at DATETIME
);
```

#### **User Subscription Tables**
```sql
-- Users (subscriber esterni)
CREATE TABLE wp_fa_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE,
    name VARCHAR(100),
    surname VARCHAR(100),
    email VARCHAR(100) UNIQUE,
    phone VARCHAR(20),
    password_hash VARCHAR(255),
    subscription_type_id INT,
    credit DECIMAL(10,2),
    analysis_count INT DEFAULT 0,
    tokens_used BIGINT DEFAULT 0,
    actual_cost DECIMAL(10,4) DEFAULT 0.0000,
    total_cost DECIMAL(10,2) DEFAULT 0.00,
    created_at DATETIME,
    last_update DATETIME,
    INDEX(username),
    INDEX(email),
    INDEX(subscription_type_id)
);

-- Subscription Types
CREATE TABLE wp_fa_subscription_types (
    id INT AUTO_INCREMENT PRIMARY KEY,
    type_sub VARCHAR(100),
    link_redirect VARCHAR(255),
    cost_per_token DECIMAL(10,6) DEFAULT 0.001000,
    created_at DATETIME
);
```

---

## 🚀 Roadmap di Sviluppo e Test per Widget

### 1. Widget Report Viewer Professionale
- **Documentazione Specifica**: `docs/widget-report-viewer.md`
- **Specifiche Tecniche**: `docs/WIDGET-REPORT-VIEWER-SPECS.md`
- **Fasi di Sviluppo**:
    1. **Definizione struttura dati**:
        - **Prompt Sviluppo**: Implementa la struttura dati e le API CRUD. Scrivi test unitari per validazione e persistenza dati.
        - **Test**: Creazione, salvataggio, recupero report via API. Mock dati demo.
    2. **Implementazione UI lista report**:
        - **Prompt Sviluppo**: Crea la UI lista report con ricerca e filtri. Collega alle API.
        - **Test**: Ricerca, filtro per azienda/periodo, paginazione.
    3. **Dettaglio report e temi**:
        - **Prompt Sviluppo**: Implementa la UI dettaglio report e il cambio tema. Aggiungi export PDF/Excel.
        - **Test**: Visualizzazione dettagliata, cambio tema, export file.
    4. **Tagging e note**:
        - **Prompt Sviluppo**: Aggiungi gestione tag/note e ricerca per tag.
        - **Test**: Aggiunta, modifica, ricerca per tag/note.
    5. **Versioning e cronologia**:
        - **Prompt Sviluppo**: Implementa versioning e storico modifiche.
        - **Test**: Visualizzazione versioni, ripristino report.
- **Checklist validazione fase**: Ogni fase deve essere testabile singolarmente (mock/demo). Test unitari e funzionali completati. Documentazione aggiornata.

### 2. Widget Caricamento Excel & Riclassificazione
- **Documentazione Specifica**: `docs/widget-caricamento-excel-riclassificazione.md`
- **Fasi di Sviluppo**:
    1. **Upload e parsing**:
        - **Prompt Sviluppo**: Implementa upload e parsing file Excel. Scrivi test unitari su parsing e validazione formato.
        - **Test**: Upload file, parsing colonne, errori formato.
    2. **UI mapping conti**:
        - **Prompt Sviluppo**: Crea UI mapping conti drag&drop. Gestisci salvataggio mapping.
        - **Test**: Mapping manuale/automatico, correzione mapping.
    3. **Riclassificazione automatica**:
        - **Prompt Sviluppo**: Implementa algoritmo di riclassificazione automatica.
        - **Test**: Output riclassificato, verifica correttezza.
    4. **Salvataggio e storico**:
        - **Prompt Sviluppo**: Salva report riclassificato e storico import.
        - **Test**: Visualizzazione storico, ripristino import.
    5. **Validazione e gestione errori**:
        - **Prompt Sviluppo**: Implementa validazione dati e gestione errori.
        - **Test**: Errori simulati, messaggi utente.
- **Checklist validazione fase**: Ogni fase deve essere testabile singolarmente (mock/demo). Test unitari e funzionali completati. Documentazione aggiornata.

### 3. Widget Analisi Bilancio Riclassificato
- **Documentazione Specifica**: `docs/widget-analisi-bilancio-riclassificato.md`
- **Fasi di Sviluppo**:
    1. **Calcolo indici**:
        - **Prompt Sviluppo**: Implementa calcolo indici e API. Scrivi test unitari su formule.
        - **Test**: Calcolo indici, confronto manuale.
    2. **Grafici dinamici**:
        - **Prompt Sviluppo**: Crea UI grafici dinamici e export immagini.
        - **Test**: Visualizzazione grafici, export immagini.
    3. **Analisi AI e suggerimenti**:
        - **Prompt Sviluppo**: Integra AI per generazione commenti e alert.
        - **Test**: Output AI, verifica coerenza, alert soglie.
    4. **Benchmark settore**:
        - **Prompt Sviluppo**: Implementa confronto automatico con benchmark.
        - **Test**: Visualizzazione confronto, alert gap.
    5. **Export e notifiche**:
        - **Prompt Sviluppo**: Implementa export e notifiche.
        - **Test**: Download, ricezione notifiche.
- **Checklist validazione fase**: Ogni fase deve essere testabile singolarmente (mock/demo). Test unitari e funzionali completati. Documentazione aggiornata.

### 4. Widget Gestione Report e Riclassificazione Aziendale
- **Documentazione Specifica**: `docs/widget-gestione-report-riclassificazione-aziendale.md`
- **Fasi di Sviluppo**:
    1. **Archivio e dashboard azienda**:
        - **Prompt Sviluppo**: Implementa archivio report e dashboard azienda.
        - **Test**: Navigazione, filtri, dashboard.
    2. **Riclassificazione manuale/automatica**:
        - **Prompt Sviluppo**: Implementa UI revisione mapping e versioning.
        - **Test**: Modifica mapping, versioning, ripristino.
    3. **Workflow revisione e approvazione**:
        - **Prompt Sviluppo**: Implementa workflow revisione, commenti e firma digitale.
        - **Test**: Cambio stato, commenti, firma.
    4. **Sicurezza e permessi**:
        - **Prompt Sviluppo**: Implementa gestione ruoli e permessi granulari.
        - **Test**: Accesso utenti, restrizioni.
- **Checklist validazione fase**: Ogni fase deve essere testabile singolarmente (mock/demo). Test unitari e funzionali completati. Documentazione aggiornata.

---

## 🔐 Sistema di Sicurezza

### Controllo Accessi

#### **FA Access Control System**
```php
// Funzione principale controllo accesso
function fa_user_can_access($level = 'subscriber') {
    // Implementa logica controllo accesso
    // Supporta livelli: subscriber, admin, custom
}

// Utilizzo nei widget e admin
if (!fa_user_can_access('admin')) {
    wp_die(__('You do not have sufficient permissions to access this page.'));
}
```

### Nonce e CSRF Protection
```php
// Generazione nonce
wp_create_nonce('document_viewer_nonce')
wp_create_nonce('subscriber_management_nonce')
wp_create_nonce('unified_access_nonce')

// Verifica nonce
wp_verify_nonce($_POST['nonce'], 'document_viewer_nonce')
```

### Sanitizzazione Dati
```php
// Input sanitization
sanitize_text_field($input)
sanitize_email($email)
sanitize_url($url)

// Output escaping
esc_html($text)
esc_attr($attribute)
esc_url($url)
```

---

## ⚙️ Sistema Configurazione

### Gestione Opzioni WordPress
```php
// Opzioni principali
get_option('document_viewer_api_key')
get_option('document_viewer_api_endpoint')
get_option('document_viewer_model')

// Registrazione settings
register_setting('document_viewer_settings', 'document_viewer_api_key')
```

### Configurazione Multi-Environment
- Supporto modalità sandbox/test
- Configurazione separata per sviluppo/produzione
- Debug logging configurabile

---

## 🔌 Sistema AJAX

### Handler Principali

#### **Analisi Documenti**
```php
wp_ajax_analyze_document          // Analisi per utenti loggati
wp_ajax_nopriv_analyze_document   // Analisi per non loggati
wp_ajax_save_document_analysis    // Salvataggio analisi
wp_ajax_export_analysis_pdf       // Export PDF
```

#### **Chat AI**
```php
wp_ajax_chat_model               // Chat per utenti loggati
wp_ajax_nopriv_chat_model        // Chat per non loggati
wp_ajax_settings_chat_model      // Chat da impostazioni
```

#### **Gestione Utenti**
```php
wp_ajax_unified_login                    // Login unificato
wp_ajax_update_subscriber_data           // Aggiorna dati subscriber
wp_ajax_recharge_credits                 // Ricarica crediti
wp_ajax_get_user_subscription_stats      // Statistiche utilizzo
```

#### **Gateway Pagamento**
```php
wp_ajax_save_paypal_config         // Salva config PayPal
wp_ajax_save_stripe_config         // Salva config Stripe
wp_ajax_test_paypal_config         // Test PayPal
wp_ajax_test_stripe_config         // Test Stripe
wp_ajax_run_gateway_checklist      // Test completo gateway
```

---

## 📊 Sistema di Logging e Debug

### Debug Logging
```php
// Funzioni debug disponibili
dv_debug_log($message, $context)     // Log messaggio
dv_clear_shared_log($context)        // Cancella log
dv_get_shared_log($context)          // Recupera log

// Contesti logging
'default'         // Log generale
'payment'         // Log pagamenti
'api'            // Log API calls
'widget'         // Log widget
'ajax'           // Log AJAX
```

### Error Monitoring
- Sistema di monitoraggio errori in tempo reale
- Log strutturati per categoria
- Interface admin per visualizzazione log
- Export report debug

---

## 🎨 Sistema di Styling

### CSS Architecture
```
assets/css/
├── admin-style.css                 # Stili admin generali
├── payment-gateway-admin.css       # Stili gateway pagamento
├── user-subscriptions-admin.css    # Stili gestione utenti
├── chat-model-widget.css          # Stili chat widget
├── document-viewer.css            # Stili document viewer
├── login-widget.css               # Stili login widget
├── subscriber-management-widget.css # Stili subscriber widget
└── user-subscription-widget.css   # Stili subscription widget
```

### JavaScript Architecture
```
assets/js/
├── admin-enhanced.js              # Enhancement admin UI
├── payment-gateway-admin.js       # Gestione gateway
├── user-subscriptions-admin.js    # Admin utenti
├── chat-model-widget.js          # Funzionalità chat
├── document-viewer.js            # Document viewer
├── login-widget.js               # Login functionality
├── subscriber-management-widget.js # Subscriber management
└── user-subscription-widget.js   # Subscription form
```

---

## 🔄 Sistema di Aggiornamento

### Version Control
- Constant `DOCUMENT_ADVISOR_VERSION` per cache busting
- Sistema di migrazione database automatico
- Backward compatibility maintenance

### Database Migrations
```php
// Hook attivazione plugin
add_action('activate_financial-advisor-V4/document-advisor-plugin.php', 
    array($this, 'initialize_database_tables'));

// Funzione migrazione
public function initialize_database_tables() {
    // Crea/aggiorna tabelle database
    // Gestisce versioning schema
}
```

---

## 📋 API Integration

### OpenRouter API Integration
- Supporto modelli AI multipli
- Rate limiting e error handling
- Token usage tracking
- Cost calculation

### Office Integration
- Microsoft Office Add-in support
- Manifest generation automatica
- Excel data extraction
- API bridge WordPress-Office

---

## 🚀 Performance Optimization

### Caching Strategy
- Asset versioning per cache busting
- Lazy loading per widget pesanti
- Database query optimization
- AJAX response caching

### Resource Management
- Conditional script loading
- CSS/JS minification support
- CDN fallback per librerie esterne
- Database index optimization

---

## 🛠️ Maintenance e Troubleshooting

### Common Issues Resolution

#### **API Connection Problems**
1. Verificare chiave API in Settings
2. Controllare endpoint configurato
3. Testare connessione da debug panel

#### **Widget Non Funzionanti**
1. Verificare registrazione widget
2. Controllare dipendenze JavaScript
3. Verificare permessi utente

#### **Database Issues**
1. Verificare tabelle create correttamente
2. Controllare permessi database
3. Eseguire migrazione manuale se necessario

### Debug Commands
```php
// Verifica stato plugin
$plugin = new Document_Viewer_Plugin();
$plugin->get_plugin_status();

// Test database connectivity
$db_manager = dv_database_manager();
$db_manager->test_connection();

// Reset configuration
$settings = new Document_Viewer_Settings();
$settings->reset_to_defaults();
```

---

## 📞 Support e Sviluppo

### Developer Hooks
```php
// Hook personalizzati disponibili
do_action('fa_user_login', $user_id);
do_action('save_document_analysis', $analysis_data);
apply_filters('fa_user_can_access', $access, $level);
apply_filters('fa_login_redirect', $redirect_to);
```

### Extensibility Points
- Sistema di hook personalizzati
- API pubblica per estensioni
- Widget inheritance support
- Admin page extension framework

---

## 📚 Conclusione

Il Financial Advisor Plugin rappresenta una soluzione completa per servizi di consulenza finanziaria AI-powered in WordPress. La sua architettura modulare, il sistema di sicurezza robusto e l'interfaccia utente moderna lo rendono adatto sia per installazioni semplici che per implementazioni enterprise complesse.

### Caratteristiche Chiave
- ✅ 5 widget specializzati
- ✅ Sistema admin completo
- ✅ Integrazione pagamenti
- ✅ Chat AI avanzata
- ✅ Office Add-in integration
- ✅ Sistema debug completo
- ✅ Sicurezza enterprise-grade
- ✅ Architettura estensibile

Per supporto tecnico o personalizzazioni, contattare il team di sviluppo Inventyx.

---

*Documentazione generata per Financial Advisor Plugin v1.1*  
*Ultimo aggiornamento: Giugno 2025*
