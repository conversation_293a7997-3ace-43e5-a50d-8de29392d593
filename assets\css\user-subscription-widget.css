/**
 * User Subscription Widget CSS
 * Stile allineato con il subscriber management widget per coerenza visiva
 * Selettori specifici per evitare conflitti con document-viewer.css
 */

/* Container principale con specificità alta */
.widget.user_subscription_widget .user-subscription-widget-container,
.user-subscription-widget-container {
    max-width: 100% !important;
    margin: 20px 0 !important;
    padding: 0 !important;
    background: #fff !important;
    border: 1px solid #e1e1e1 !important;
    border-radius: 8px !important;
    box-shadow: 0 3px 10px rgba(0,0,0,0.08) !important;
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 0 !important;
    position: relative !important;
    overflow: hidden !important;
    min-height: 500px !important;
}

/* Titoli generali con specificità alta */
.widget.user_subscription_widget .user-subscription-widget-container h3,
.user-subscription-widget-container h3 {
    margin: 0 0 20px 0 !important;
    font-size: 1.4rem !important;
    font-weight: 600 !important;
    color: #333 !important;
    letter-spacing: 0.02em !important;
    position: relative !important;
    width: auto !important;
    text-align: left !important;
    flex-basis: auto !important;
}

.widget.user_subscription_widget .user-subscription-widget-container h3:after,
.user-subscription-widget-container h3:after {
    content: '' !important;
    display: block !important;
    width: 60px !important;
    height: 3px !important;
    background: #0073aa !important;
    margin: 8px 0 0 0 !important;
    border-radius: 2px !important;
}

/* Form styling moderno con specificità alta */
.widget.user_subscription_widget .user-subscription-form,
.user-subscription-widget-container .user-subscription-form {
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: space-between !important;
    width: 100% !important;
    padding: 0 !important;
    background: transparent !important;
    border: none !important;
    border-radius: 0 !important;
}

/* Form groups con layout migliorato */
.widget.user_subscription_widget .user-subscription-form .form-group,
.user-subscription-widget-container .user-subscription-form .form-group {
    margin-bottom: 20px !important;
    position: relative !important;
    width: 48% !important;
    box-sizing: border-box !important;
    flex-shrink: 0 !important;
}

/* Classi specifiche per il layout a due colonne */
.user-subscription-form .column-left {
    width: 48%;
    margin-right: 2%;
    clear: left;
}

.user-subscription-form .column-right {
    width: 48%;
    margin-left: 2%;
    clear: right;
}

/* Label styling moderno */
.user-subscription-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 0.95rem;
    line-height: 1.4;
}

.user-subscription-form .required {
    color: #e74c3c;
    font-weight: bold;
}

/* Input styling moderno */
.user-subscription-form input[type="text"],
.user-subscription-form input[type="email"],
.user-subscription-form input[type="tel"],
.user-subscription-form input[type="password"],
.user-subscription-form select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e1e1;
    border-radius: 6px;
    font-size: 0.95rem;
    color: #333;
    background-color: #fff;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

/* Stati di focus e interazione */
.user-subscription-form input:focus,
.user-subscription-form select:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 3px rgba(0, 115, 170, 0.1);
    outline: none;
    transform: translateY(-1px);
}

.user-subscription-form input.error,
.user-subscription-form select.error {
    border-color: #e74c3c;
    border-width: 2px;
    animation: errorShake 0.3s 1;
}

@keyframes errorShake {
    0%, 100% {transform: translateX(0);}
    25% {transform: translateX(-5px);}
    75% {transform: translateX(5px);}
}

/* Messaggi di errore */
.user-subscription-form .error-message {
    color: #e74c3c;
    font-size: 0.85rem;
    margin-top: 8px;
    display: block;
    background-color: transparent;
    padding: 0;
    border: none;
    font-weight: 500;
}

/* Gruppo termini e condizioni */
.user-subscription-form .terms-group {
    display: flex;
    align-items: flex-start;
    width: 100%;
    margin-bottom: 25px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.user-subscription-form .terms-group input[type="checkbox"] {
    width: auto;
    margin-right: 12px;
    margin-top: 2px;
    transform: scale(1.2);
}

.user-subscription-form .terms-group label {
    margin-bottom: 0;
    font-weight: 500;
    font-size: 0.9rem;
    line-height: 1.5;
    flex: 1;
}

/* Elementi che devono occupare l'intera larghezza */
.user-subscription-form .form-submit,
.user-subscription-form .response-message,
.user-subscription-form .form-group.full-width {
    width: 100%;
}

/* Pulsante di submit moderno */
.user-subscription-form .submit-button {
    padding: 15px 25px;
    background: linear-gradient(135deg, #0073aa 0%, #005177 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(0, 115, 170, 0.3);
    width: 100%;
    position: relative;
    overflow: hidden;
}

.user-subscription-form .submit-button:hover {
    background: linear-gradient(135deg, #005177 0%, #003d5c 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 115, 170, 0.4);
}

.user-subscription-form .submit-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(0, 115, 170, 0.3);
}

/* Messaggi di risposta */
.user-subscription-form .response-message {
    padding: 15px 20px;
    border-radius: 6px;
    text-align: center;
    font-weight: 500;
    margin-top: 25px;
    background-color: #ffffff;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    border: 1px solid #e1e1e1;
}

.user-subscription-form .response-message.success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
    border-left: 4px solid #28a745;
}

.user-subscription-form .response-message.error {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
    border-left: 4px solid #dc3545;
}

/* Password strength meter migliorato */
.user-subscription-form .password-strength-meter {
    margin-top: 12px;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    position: relative;
    overflow: hidden;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
}

.user-subscription-form .password-strength-meter .strength-bar {
    height: 100%;
    width: 0;
    border-radius: 4px;
    transition: width 0.4s ease, background-color 0.4s ease;
    position: relative;
}

.user-subscription-form .password-strength-meter .strength-text {
    display: block;
    font-size: 0.85rem;
    margin-top: 8px;
    color: #666;
    font-weight: 500;
}

.user-subscription-form .password-strength-meter.weak .strength-bar {
    background: linear-gradient(90deg, #e74c3c 0%, #c0392b 100%);
    width: 25%;
}

.user-subscription-form .password-strength-meter.medium .strength-bar {
    background: linear-gradient(90deg, #f39c12 0%, #e67e22 100%);
    width: 60%;
}

.user-subscription-form .password-strength-meter.strong .strength-bar {
    background: linear-gradient(90deg, #27ae60 0%, #2ecc71 100%);
    width: 100%;
}

/* Layout a tre colonne moderno con specificità alta */
.widget.user_subscription_widget .dossier-column,
.user-subscription-widget-container .dossier-column {
    flex: 0 0 250px !important;
    min-width: 250px !important;
    max-width: 280px !important;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    padding: 25px 20px !important;
    border-right: 1px solid #e1e1e1 !important;
    position: relative !important;
}

.widget.user_subscription_widget .dossier-column h3,
.user-subscription-widget-container .dossier-column h3 {
    margin-top: 0 !important;
    margin-bottom: 20px !important;
    text-align: left !important;
    color: #333 !important;
    padding-bottom: 15px !important;
    border-bottom: 2px solid #0073aa !important;
    font-size: 1.2rem !important;
    width: auto !important;
    flex-basis: auto !important;
}

.widget.user_subscription_widget .dossier-column h3:after,
.user-subscription-widget-container .dossier-column h3:after {
    display: none !important; /* Rimuove la linea decorativa globale per questa sezione */
}

.dossier-content {
    color: #555;
    line-height: 1.6;
}

.dossier-content p {
    margin-bottom: 15px;
    font-size: 0.95rem;
}

.dossier-content ul {
    margin: 15px 0;
    padding-left: 20px;
}

.dossier-content li {
    margin-bottom: 8px;
    font-size: 0.9rem;
    color: #666;
}

.widget.user_subscription_widget .document-form-column,
.user-subscription-widget-container .document-form-column {
    flex: 2 !important;
    min-width: 400px !important;
    position: relative !important;
    background-color: #fff !important;
    padding: 30px !important;
    border-right: 1px solid #e1e1e1 !important;
    max-width: none !important;
}

.widget.user_subscription_widget .document-form-column h3,
.user-subscription-widget-container .document-form-column h3 {
    text-align: left !important;
    margin-bottom: 25px !important;
    color: #333 !important;
    font-size: 1.3rem !important;
    width: auto !important;
    flex-basis: auto !important;
}

.widget.user_subscription_widget .document-display-column,
.user-subscription-widget-container .document-display-column {
    flex: 1 !important;
    min-width: 280px !important;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    padding: 25px 20px !important;
    position: relative !important;
    max-width: none !important;
}

.widget.user_subscription_widget .document-display-column h3,
.user-subscription-widget-container .document-display-column h3 {
    margin-top: 0 !important;
    margin-bottom: 20px !important;
    text-align: left !important;
    color: #333 !important;
    padding-bottom: 15px !important;
    border-bottom: 2px solid #0073aa !important;
    font-size: 1.2rem !important;
    width: auto !important;
    flex-basis: auto !important;
}

.widget.user_subscription_widget .document-display-column h3:after,
.user-subscription-widget-container .document-display-column h3:after {
    display: none !important; /* Rimuove la linea decorativa globale per questa sezione */
}

.subscription-benefits ul {
    margin: 15px 0;
    padding-left: 20px;
}

.subscription-benefits li {
    margin-bottom: 12px;
    font-size: 0.95rem;
    color: #555;
    position: relative;
}

.subscription-benefits li:before {
    content: "✓";
    color: #28a745;
    font-weight: bold;
    position: absolute;
    left: -20px;
}

.subscription-benefits p {
    margin-top: 20px;
    font-size: 0.9rem;
    color: #666;
    font-style: italic;
}

/* Design responsivo migliorato */
@media (max-width: 1200px) {
    .user-subscription-widget-container {
        flex-direction: column;
    }

    .dossier-column,
    .document-form-column,
    .document-display-column {
        width: 100%;
        max-width: 100%;
        border-right: none;
        border-bottom: 1px solid #e1e1e1;
    }

    .document-display-column {
        border-bottom: none;
    }
}

@media (max-width: 768px) {
    .user-subscription-widget-container {
        margin: 10px 0;
        border-radius: 6px;
    }

    .dossier-column,
    .document-form-column,
    .document-display-column {
        padding: 20px;
    }

    .user-subscription-form .form-group,
    .user-subscription-form .column-left,
    .user-subscription-form .column-right {
        width: 100%;
        margin-left: 0;
        margin-right: 0;
        margin-bottom: 20px;
    }

    .user-subscription-form input,
    .user-subscription-form select {
        font-size: 16px; /* Previene lo zoom su iOS */
        padding: 15px;
    }

    .user-subscription-form .submit-button {
        padding: 18px 25px;
        font-size: 16px;
    }
}

/* Miglioramenti per accessibilità */
.user-subscription-form input:focus,
.user-subscription-form select:focus,
.user-subscription-form .submit-button:focus {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

/* Animazioni di caricamento */
.user-subscription-form .submit-button.loading {
    pointer-events: none;
    opacity: 0.7;
}

.user-subscription-form .submit-button.loading:after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
