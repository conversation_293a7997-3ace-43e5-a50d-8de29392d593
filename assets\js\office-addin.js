/**
 * Office Add-in JavaScript
 * 
 * This file contains the client-side functionality for the Excel add-in.
 * It handles the interaction between Excel and the WordPress plugin.
 */

// Aggiungi gli stili CSS per l'animazione di evidenziazione
(function() {
    if (!document.getElementById('office-addin-highlight-styles')) {
        var style = document.createElement('style');
        style.id = 'office-addin-highlight-styles';
        style.textContent = `
            @keyframes highlight-animation {
                0% { background-color: #ffffff; }
                30% { background-color: #ffffd0; }
                100% { background-color: #ffffff; }
            }
            
            .textarea-highlight {
                animation: highlight-animation 1.5s ease-in-out;
                border-color: #4a90e2 !important;
            }
        `;
        document.head.appendChild(style);
    }
})();

(function($) {
    'use strict';

    // Store API settings
    var apiSettings = {
        apiKey: '',
        apiEndpoint: '',
        model: ''
    };

    // Store predefined queries
    var predefinedQueries = [];

    // Initialize the Office Add-in
    function initializeOfficeAddin() {
        // Set up event handlers
        setupEventHandlers();
        
        // Load API settings
        loadApiSettings();
        
        // Load predefined queries
        loadPredefinedQueries();
    }

    // Set up event handlers
    function setupEventHandlers() {
        $('#extract-text').on('click', extractSelectedText);
        $('#analyze-button').on('click', analyzeData);
        $('#predefined-query').on('change', handlePredefinedQueryChange);
    }

    // Handle predefined query change
    function handlePredefinedQueryChange() {
        var queryId = $('#predefined-query').val();
        
        if (queryId) {
            // Find the selected query
            var selectedQuery = predefinedQueries.find(function(query) {
                return query.id == queryId;
            });
            
            if (selectedQuery) {
                // Copy the query_text (extended question) to the custom query field
                $('#custom-query').val(selectedQuery.query_text);
                
                // Add highlight animation
                $('#custom-query').addClass('textarea-highlight');
                
                // Remove the highlight class after animation completes
                setTimeout(function() {
                    $('#custom-query').removeClass('textarea-highlight');
                }, 1500);
                
                // Set focus on the textarea for immediate editing
                $('#custom-query').focus();
            }
        }
    }

    // Load API settings from WordPress
    function loadApiSettings() {
        $.ajax({
            url: document_viewer_ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'office_addin_get_settings',
                nonce: document_viewer_ajax_object.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Store the API settings
                    apiSettings = response.data;
                    
                    // Update the UI
                    $('#selected-model').text(apiSettings.model || 'Not selected');
                    $('#api-status').text(apiSettings.api_configured ? 'Connected' : 'Not Connected');
                } else {
                    $('#api-status').text('Settings load failed');
                    console.error('Failed to load API settings:', response.data.message);
                }
            },
            error: function(xhr, status, error) {
                $('#api-status').text('Settings load error');
                console.error('Error loading API settings:', error);
            }
        });
    }

    // Load predefined queries from WordPress
    function loadPredefinedQueries() {
        $.ajax({
            url: document_viewer_ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'office_addin_get_queries',
                nonce: document_viewer_ajax_object.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Store the predefined queries
                    predefinedQueries = response.data;
                    
                    // Populate the dropdown
                    var $select = $('#predefined-query');
                    $select.empty();
                    $select.append($('<option></option>').attr('value', '').text('-- Select a query --'));
                    
                    if (predefinedQueries && predefinedQueries.length > 0) {
                        $.each(predefinedQueries, function(index, query) {
                            $select.append($('<option></option>').attr('value', query.id).text(query.title));
                        });
                    } else {
                        $select.append($('<option></option>').attr('value', '').text('No queries available'));
                    }
                } else {
                    console.error('Failed to load predefined queries:', response.data.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading predefined queries:', error);
            }
        });
    }

    // Extract selected text from Excel
    function extractSelectedText() {
        // Check if Office.js is available
        if (typeof Office === 'undefined' || typeof Excel === 'undefined') {
            alert('Office.js is not loaded. This function only works in Excel.');
            return;
        }
        
        // Use Excel API to get selected range
        Excel.run(function(context) {
            var range = context.workbook.getSelectedRange();
            range.load('text');
            
            return context.sync().then(function() {
                var text = '';
                
                // Convert the 2D array to a string
                for (var i = 0; i < range.text.length; i++) {
                    for (var j = 0; j < range.text[i].length; j++) {
                        if (range.text[i][j]) {
                            text += range.text[i][j] + ' ';
                        }
                    }
                    text += '\n';
                }
                
                // Display the extracted text
                $('#extracted-text').text(text);
                $('#extracted-text-container').show();
            });
        }).catch(function(error) {
            console.error('Error extracting text:', error);
            alert('Error extracting text: ' + error.message);
        });
    }

    // Analyze the extracted data
    function analyzeData() {
        var extractedText = $('#extracted-text').text();
        var queryId = $('#predefined-query').val();
        var customQuery = $('#custom-query').val();
        
        // Validate inputs
        if (!extractedText) {
            alert('Please extract text from Excel first.');
            return;
        }
        
        if (!queryId && !customQuery) {
            alert('Please select a predefined query or enter a custom question.');
            return;
        }
        
        // Show loading indicator
        $('#analysis-results').html('<p>Analyzing data...</p>');
        
        // Send the analysis request
        $.ajax({
            url: document_viewer_ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'office_addin_analyze',
                nonce: document_viewer_ajax_object.nonce,
                text: extractedText,
                query_id: queryId,
                custom_query: customQuery
            },
            success: function(response) {
                if (response.success) {
                    // Display the analysis results
                    $('#analysis-results').html('<div>' + response.data.result + '</div>');
                } else {
                    // Display error message
                    $('#analysis-results').html('<p class="error">Error: ' + response.data.message + '</p>');
                }
            },
            error: function(xhr, status, error) {
                $('#analysis-results').html('<p class="error">Error connecting to the server.</p>');
                console.error('Analysis error:', error);
            }
        });
    }

    // Initialize when document is ready
    $(document).ready(function() {
        // Check if we're in an Office context
        if (typeof Office !== 'undefined') {
            // Initialize when Office is ready
            Office.onReady(function(info) {
                if (info.host === Office.HostType.Excel) {
                    initializeOfficeAddin();
                }
            });
        } else {
            // Initialize directly for testing outside of Office
            initializeOfficeAddin();
        }
    });

})(jQuery);
